(function () {

  angular.module('printty')

    .directive('componentLoading', function ($timeout, $compile) {
      
      return {
        restrict: 'A',
        link: link,
        scope: {
          loadingType: '=componentLoading'
        }
      };

      function link(scope, element) {
        
        var loader = angular.element($compile('<div class="component-loading"><md-progress-circular md-diameter="64" md-mode="indeterminate"></md-progress-circular></div>')(scope)).appendTo(element.parents('md-sidenav'));
        
        var last = null;
        
        scope.$watch('loadingType', function (loadingType) {
          
          switch (loadingType) {
            case 'full':
              loader.removeClass('partial stopped').addClass('full');
              break;
            case 'partial':
              loader.removeClass('full stopped').addClass('partial');
              break;
            case 'stopped':
              $timeout(function() {
                loader.removeClass('full partial').addClass('stopped');
              }, last === 'partial' ? 0 : 250);
              break;
            default:
              loader.removeClass('full partial').addClass('stopped');
          }

          last = loadingType;
          
        });

      }

    });

})();