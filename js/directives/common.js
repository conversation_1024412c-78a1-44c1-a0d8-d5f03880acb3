(function () {

  angular.module('printty')
    
    .directive('focusableSearch', ['$timeout', function($timeout) {
      return {
        restrict: 'A',
        link: link
      };

      function link(scope, element) {
        
        var input = element.find('input');
        
        element.find('i.icons8-search').on('click', function ($event) {
          element.addClass('has-focus');
          input.focus();
          $event.stopPropagation();
        });
        
        input.on('focusin', function () {
          $timeout(function () {
            element.addClass('has-focus');
          });
          
        });
        input.on('focusout', function () {
          $timeout(function () {
            element.removeClass('has-focus');
          }, 150);
          
        });

      }

    }])

    .directive('virtualRepeatScrollTop', ['$timeout', function($timeout) {
      return {
        restrict: 'A',
        link: link
      };
  
      function link(scope, element) {
        
        var scroller = element.siblings('.md-virtual-repeat-container').find('.md-virtual-repeat-scroller');
        var top = element.parent().offset().top;

        element.css({
          top: top
        });
        
        var height = 2000;
        
        // find out scroller height after digest cycle
        $timeout(function () {
          height = scroller.innerHeight() * 2;
        }, 0);
        
        element.click(function () {
          scroller.scrollTop(0);
        });
        
        scroller.on('scroll', function () {
  
          if (scroller.scrollTop() > height) {
            element.addClass('shown');
          } else {
            element.removeClass('shown');
          }
  
        });
  
      }
  
    }])
    .directive('virtualRepeatScrollTopCustom', ['$timeout', function($timeout) {
      return {
        restrict: 'A',
        link: link
      };

      function link(scope, element) {

        var scroller = $('.md-virtual-repeat-scroller');


        var height = 2000;

        // find out scroller height after digest cycle
        $timeout(function () {
          height = scroller.innerHeight() * 2;
        }, 0);

        element.click(function () {
          scroller.scrollTop(0);
        });

          element.addClass('shown');
      }

    }])
      .directive('virtualRepeatScrollBottomCustom', ['$timeout', function($timeout) {
        return {
          restrict: 'A',
          link: link
        };

        function link(scope, element) {

          var scroller = $('.md-virtual-repeat-scroller');


          var height = 2000;

          // find out scroller height after digest cycle
          $timeout(function () {
            height = scroller.innerHeight() * 2;
          }, 0);

          element.click(function () {
            scroller.scrollTop(scroller[0].scrollHeight);
          });

          element.addClass('shown');
        }

      }])
    
    // todo: remake. add binding - emphasis text (to make this directive dynamic)
    .directive('statusEmphasis', function() {
      return {
        restrict: 'A',
        link: link,
        scope: {
          text: '=statusEmphasis'
        }
      };

      function link(scope, element) {

        scope.$watch('text', function () {
          
          if (scope.text === '支払い待ち') {
            element.addClass('red');
          } else if ( element.hasClass('red') ) {
            element.removeClass('red');
          }
          
        })

      }

    })
    
    .directive('pickDateHeader', function() {
      return {
        restrict: 'A',
        link: link
      };

      function link(scope, element) {

        element.addClass('pick-date-header');
        var div = element.find('> div');
        var pickerBtn = element.find('.md-datepicker-button');
        
        div.on('click', function () {
          pickerBtn.click();
        });

      }

    })

    .directive('inputColor', function() {
      return {
        restrict: 'A',
        link: link,
        scope: {
          color: '=inputColor'
        }
      };

      function link(scope) {
        
        var colors = scope.$parent.colors = []; 
        
        scope.$watch('color', function (color) {
          
          if (!color) {
            colors = scope.$parent.colors = [];
            return;
          }

          colors = scope.$parent.colors = [];

          var colorArr = color.replace(/ /g,'').split(',');

          colorArr.forEach(function (color) {
            
            if (color.match(/[0-9A-Fa-f]{6}/g)) {
              colors.push(color);
            }
            
          });
          
        });

      }

    })

    .directive('stringToNumber', function() {
      return {
        require: 'ngModel',
        link: function(scope, element, attrs, ngModel) {
          ngModel.$parsers.push(function(value) {
            return '' + value;
          });
          ngModel.$formatters.push(function(value) {
            return parseFloat(value, 10);
          });
        }
      };
    })
    .directive('evalAttrAsExpr', function evalAttrAsExpr() {
      /*
       * This directive is a workaround for the md-component-id attribute of the
       * mdSidenav directive.
       *
       * The mdSidenav directive, in its controller function, registers the element
       * using the md-component-id attribute. If we need this value to be an
       * expression to be evaluated in the scope, we can't do
       *
       * <md-sidenav md-component-id="{{ expr }}" [...]>
       *
       * because the curly braces are replaced in a subsequent stage. To work around
       * this, this directive replace the value of md-component-id with the value of
       * that expression in the scope. So the previous example becomes
       *
       * <md-sidenav md-component-id="expr" eval-attr-as-expr="mdComponentId" [...]>
       */
      return {
        restrict: 'A',
        controller: function($scope, $element, $attrs) {
          var attrToEval = $attrs.evalAttrAsExpr;
          $attrs[attrToEval] = $scope.$eval($attrs[attrToEval]);
        },
        priority: 9999
      };
    });

})();