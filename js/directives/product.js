(function () {
  
  function ProductSidesCtrl($element, $attrs) {

    "ngInject";
    
    var ctrl = this;
    var activeSide = 0;
    
    if ($attrs.mode) {
      $element.children().addClass('mode-min');
    }
    
    ctrl.getActiveSrc = function () {
      if (ctrl.sides && ctrl.sides.length) {
        return ctrl.sides[activeSide].image_url;
      }
      return null;
    };
    

    ctrl.isActiveSide = function (index) {
      return activeSide === index;
    };
    
    ctrl.setActive = function (index) {
      activeSide = index;
      ctrl.onSelect({
        active: ctrl.sides[activeSide]
      })
    };
    
    ctrl.$onChanges = function (changes) {
      if (changes.sides && angular.isArray(changes.sides.currentValue)) {
        ctrl.activeSide = 0;
        ctrl.onSelect({
          active: ctrl.sides[activeSide]
        })
      }
    }
    
  }
  
  angular.module('printty')
    
    .component('productSidesImages', {
      bindings: {
        sides: '<',
        onSelect: '&'
      },
      controller: ProductSidesCtrl,
      controllerAs: 'ps',
      templateUrl: 'views/templates/product-sides-directive.html'
    });

})();