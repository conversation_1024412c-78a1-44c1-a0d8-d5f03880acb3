(function () {
    function TotalRandomCtrl(utils, Auth, $state, TotalRandomApi, $interval, $mdDialog) {

        "ngInject";

        if (!Auth.isAuthorized('totalrandom')) {
            return $state.go( Auth.defaultRoute );
        }


        var ctrl = this;
        ctrl.data = [];
        ctrl.date = moment().toDate();
        ctrl.quantity = 0;

        utils.appLoaded();

        ctrl.isHide = false;
        ctrl.state = {

            name: 'scan',
            history: [],
            isInfo: false,


            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

        ctrl.$onInit = function () {
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getTotalRandomData(date);
            /*interval = $interval(function() {
                date = moment(ctrl.date).format('YYYY-MM-DD');
                getTotalRandomData(date);
            }, 60000);*/
        };

        ctrl.$onDestroy = function () {
            $interval.cancel(interval);
        }

        ctrl.onDateChange = function(){
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getTotalRandomData(date);
        }

        function getTotalRandomData(date){
            TotalRandomApi.getTotalRandomData(date).then(function (data) {
                ctrl.data = data;
            });
        }

        var tick = function() {
            ctrl.clock = Date.now();
        };

        tick();
        $interval(tick, 1000);

    }

    angular.module('printty')

        .component('totalrandomComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: TotalRandomCtrl,
            controllerAs: 'rc',
            templateUrl: 'views/totalrandom/totalrandom.html'
        });
})();