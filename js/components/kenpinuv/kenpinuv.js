(function () {
  function KenpinUVCtrl(utils, Auth, $state) {

    "ngInject";

    if (!Auth.isAuthorized('kenpinuv')) {
      return $state.go( Auth.defaultRoute );
    }

    var ctrl = this;

      utils.appLoaded();

      ctrl.isHide = false;
    ctrl.state = {

      name: 'scan',
      history: [],
      isInfo: false,


      is: function () {

        if (!arguments.length) return;

        if (arguments.length === 1) {
          return arguments[0] === this.name;
        }

        if (arguments.length > 1) {

          for (var i = 0; i < arguments.length; i++) {

            if (arguments[i] === this.name) {
              return true;
            }

          }

          return false

        }

      },
      back: function () {

        var last = this.history[this.history.length - 1];

        this.set(last, true);

        if (last == 'scan') {
          this.history = [];
        } else {
          this.history.pop();
        }

      },
      set: function (name, forget) {

        if (!forget && this.name != name) {
          this.history.push(this.name);
        }

        this.name = name;

        // emit event
        this._onChangeCallbacks.forEach(function(callback) {
          callback();
        });

      },

      _onChangeCallbacks: [],
      onChange: function(callback) {
        if (angular.isFunction(callback)) {
          this._onChangeCallbacks.push(callback);
        }
      }

    };

  }

  angular.module('printty')

    .component('kenpinuvComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: KenpinUVCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpinuv/kenpinuv.html'
    });


  /******************
   *******************
   * ******* LEFT ******
   ****************/


  function KenpinUVLeftCtrl(Store) {

    "ngInject";

    var ctrl = this;
        // subscription;

    ctrl.$onInit = function () {

      ctrl.state = ctrl.kenpinUVCtrl.state;

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
        }
      });

    };

    // subscribe to print task change
    Store.data('KenpinUVTask').subscribe(function (data) {

      if (!data) return;

      if (ctrl && ctrl.state)
      {
          //ctrl.isMultiple = data.taskType === 'Multiple';
          ctrl.task = data;
          ctrl.state.isInfo = true;
      }
      else
      {
          ctrl.state = {

              name: 'scan',
              history: [],
              isInfo: true,

              is: function () {

                  if (!arguments.length) return;

                  if (arguments.length === 1) {
                      return arguments[0] === this.name;
                  }

                  if (arguments.length > 1) {

                      for (var i = 0; i < arguments.length; i++) {

                          if (arguments[i] === this.name) {
                              return true;
                          }

                      }

                      return false

                  }

              },
              back: function () {

                  var last = this.history[this.history.length - 1];

                  this.set(last, true);

                  if (last == 'scan') {
                      this.history = [];
                  } else {
                      this.history.pop();
                  }

              },
              set: function (name, forget) {

                  if (!forget && this.name != name) {
                      this.history.push(this.name);
                  }

                  this.name = name;

                  // emit event
                  this._onChangeCallbacks.forEach(function(callback) {
                      callback();
                  });

              },

              _onChangeCallbacks: [],
              onChange: function(callback) {
                  if (angular.isFunction(callback)) {
                      this._onChangeCallbacks.push(callback);
                  }
              }

          };
      }

    });

    // ctrl.$onDestroy = function () {
    //   subscription.unsubscribe();
    // };

    /******** helpers ***********/
    function setDefault() {
      ctrl.isMultiple = false;
      ctrl.task = null;
    }

  }

  angular.module('printty')

    .component('kenpinuvLeft', {
      require: {
        kenpinUVCtrl: '^^kenpinuvComponent'
      },
      controller: KenpinUVLeftCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpinuv/kenpinuv-left.html'
    });


  /******************
   *******************
   * ******* RIGHT ******
   ****************/


  function KenpinUVRightCtrl($element, KenpinUVApi, Store, electron, $timeout, utils, ElectronUtils) {

    "ngInject";

    var ctrl = this;

    ctrl.isElectron = !!electron;

    ctrl.scannerInput = null;
    ctrl.qrCodeInput = null;

    ctrl.taskInfo = null;
    ctrl.task = null;
    ctrl.isKenpinStep = true;


    ctrl.$onInit = function () {
      ctrl.state = ctrl.kenpinUVCtrl.state;

      ctrl.focusScanField();

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
          $element.removeClass('single multiple');
        }

        if (ctrl.state.is('scan')) {
          ctrl.focusScanField();
        }
      });

    };

    ctrl.findQr = function () {
      if (!ctrl.qrCodeInput) return;
      search(ctrl.qrCodeInput);
    };

    ctrl.focusScanField = function () {
      $timeout(function () {
        $('#scanner-field').focus();
      });
    };

    ctrl.findQrScan = function () {
      utils.delay(function () {
        if (!ctrl.scannerInput) return;
        search(ctrl.scannerInput);
      }, 250);
    };

    ctrl.clearInput = function () {
      ctrl.qrCodeInput = null;
      ctrl.errorText = null;
    };

    ctrl.onDownload = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.onKenpin = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.downloadItemImage = function (item) {

      utils.globalLoading.show();

      KenpinUVApi.fetchMultipleTaskSource({
        task_group_step_id: ctrl.taskInfo.TaskGroupStep.id,
        item_id: item.TaskStepItem.id
      })
        .then(function (data) {
          ElectronUtils.getFile(data.TaskSource.image_url)
            .finally(function () {
              utils.globalLoading.hide();
            })
        })
        .catch(function () {
          utils.globalLoading.hide();
        })

    };

    ctrl.press = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.kenpinSuccess = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

      if (ctrl.isMultiple) {
        processMultiple();
      } else {
        processSingle();
      }

    };

    ctrl.kenpinFail = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

      if (ctrl.isMultiple) {
        ctrl.state.set('fail', true);
      } else {
        failSingle();
      }

    };

    ctrl.retryKenpin = function () {

      if (ctrl.isMultiple) {
        retryMultiple();
      } else {
        retrySingle();
      }

    };

    ctrl.failMultiple = function () {

      var itemsFailed = [];

      _.forEach(ctrl.task.current_step.items, function (item) {

        if (item.selected) {

          itemsFailed.push({
            TaskStepItem: {
              id: item.TaskStepItem.id
            }
          });

        }

      });

      KenpinUVApi.failMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id,
        failedItems: itemsFailed
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    };

    ctrl.cancelFail = function () {

      _.forEach(ctrl.task.current_step.items, function (item) {
        item.selected = false;
      });

      ctrl.state.set('finish', true);

    };

    /******** helpers ***********/
    function setDefault() {
      ctrl.scannerInput = null;

      ctrl.taskInfo = null;
      ctrl.task = null;

      ctrl.isMultiple = false;
      ctrl.isKenpinStep = true;
      ctrl.errorText = null;
    }

    function search(input) {

        ctrl.errorText = '';

      KenpinUVApi.search(input).then(function (data) {

        ctrl.taskInfo = data;

        ctrl.isMultiple = data.type === 'Multiple';

        if (ctrl.isMultiple) {
          $element.removeClass('single').addClass('multiple');
        } else {
          $element.addClass('single').removeClass('multiple');
        }

        // task found => fetch task
        if (ctrl.isMultiple) {
          fetchMultiple(data, false);
        } else {
          fetchSingle(data, false);
        }

      }, function (error) {

        if (error.status === 400 && angular.isDefined(error.data.message)) {
          ctrl.errorText = error.data.message;
        }

        ctrl.scannerInput = null;

      });
    }

    function fetchSingle(taskData, forget) {

      KenpinUVApi.fetchSingleTask({
        task_id: taskData.Task.id,
        step_id: taskData.TaskStep.id
      }).then(function (data) {

        ctrl.errorText = null;

        //data[0].taskType = 'Single';

        Store.data('KenpinUVTask').update(data);

        //ctrl.task = data;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });

    }

    function fetchMultiple(taskData, forget) {

      KenpinUVApi.fetchMultipleTask({
          step_id: taskData.TaskStep.id
      }).then(function (data) {

        ctrl.errorText = null;

        //data.taskType = 'Multiple';

        Store.data('KenpinUVTask').update(data);

        // ctrl.isKenpinStep = data.current_step.TaskStepType.code === 'kenpinuv';

        ctrl.task = data;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });

    }

    function processSingle() {

      KenpinUVApi.processSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function processMultiple() {

      KenpinUVApi.processMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function failSingle() {

      KenpinUVApi.failSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function retrySingle() {

      KenpinUVApi.retrySingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          fetchSingle(ctrl.taskInfo, true);
        });

    }

    function retryMultiple() {

      KenpinUVApi.retryMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          fetchMultiple(ctrl.taskInfo, true);
        });

    }

  }

  angular.module('printty')

    .component('kenpinuvRight', {
      require: {
        kenpinUVCtrl: '^^kenpinuvComponent'
      },
      controller: KenpinUVRightCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpinuv/kenpinuv-right.html'
    });


  // Kenpin info
    function KenpinUVInfoCtrl(Store, $mdDialog, KenpinUVApi, Auth, $state, utils) {

        "ngInject";

        var ctrl = this,
            subscription;

        ctrl.subscriptions = [];
        ctrl.isShowReasion = false;
        ctrl.taskId = null;
        ctrl.taskIds = null;
        ctrl.loadingType = 'full';
        ctrl.taskTitle = ['おもて', 'うら', 'ひだり', 'みぎ'];
        ctrl.numberCheck = 0;
        ctrl.isDisabled = false;
        ctrl.statusOk = 55;
        var allSelected = false;
        ctrl.has_include = false;

        ctrl.closeKenpin = function () {
            ctrl.state.isInfo= false;
            ctrl.state.back();
            $state.go( 'kenpinuv' );
            clearInterval(intervalTime);
        };

        ctrl.$onInit = function () {

            ctrl.subscriptions = [];

            var kenpinData = {
                typeId: 3
            };
            KenpinUVApi.fetchKenpinStatusByType(kenpinData)
                .then(function (data) {
                    ctrl.statusOk = data[0].KenpinStatus.id;
                })
                .catch(function () {
                    ctrl.statusOk = 55;
                });

            ctrl.state = ctrl.kenpinUVCtrl.state;

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                }
            });

            //get numbercheck
            getNumberChecked(ctrl.taskIds);

            checkCurrentStatus();

            _.forEach(ctrl.task, function (item) {
                if(item.current_step && item.current_step.detail.is_include){
                    ctrl.has_include = true;
                }
            });

            if(ctrl.task['countdown'].inspection_countdown !== 0 && !ctrl.task['countdown'].all_checked) {
                startTime(ctrl.task['countdown'].inspection_countdown);
            }

        };

        var z = parseInt(document.getElementById("count-down").innerHTML);
        var intervalTime;
        ctrl.clickChange = function (step,keyStep) {
            setTimeout(function() {
                var checkOK = document.getElementById("OK-" + keyStep);
                var checkNG = document.getElementById("NG-" + keyStep);
                var checkedOK = checkOK.classList.contains('md-checked');
                var checkedNG = checkNG.classList.contains('md-checked');
                var checked = checkItemUncheck();

                if(!checkedNG && !checkedOK) allSelected = false;
                if((!checkedNG && !checkedOK) || !checked) {
                    startTime (step.current_step.detail.inspection_countdown);
                }else {
                    clearInterval(intervalTime);
                    document.getElementById("count-down").style.display = 'none';
                }
            },10);
        }

        function startTime (s) {
            z = parseInt(s);
            var sec = z - z + parseInt(s);
            if (intervalTime) clearInterval(intervalTime);
            intervalTime = setInterval(function () {
                document.getElementById("count-down").innerHTML = sec;
                console.log('sec',sec);
                if (0 <= sec && sec <= 5) {
                    document.getElementById("count-down").style.display = 'block';
                    document.getElementById("count-down").style.color = 'red';
                } else if (sec > 5) {
                    document.getElementById("count-down").style.display = 'block';
                    document.getElementById("count-down").style.color = 'green';
                }
                if (sec === 0) {
                    clearInterval(intervalTime);
                } else if (sec === 0) {
                    sec = s;
                }
                sec--;
            }, 1000);
        }

        function checkItemUncheck(){
            var check = true;
            angular.forEach(ctrl.task,function(item,key) {
                if(typeof item.current_step !== 'undefined' && (key != 'countdown' || key != 'default' || key != 'detail' )) {
                    if ((typeof (item.selected) == 'undefined' && typeof (item.okSelected) == 'undefined') ||
                        (!item.selected && typeof (item.okSelected) == 'undefined') ||
                        (!item.okSelected && typeof (item.selected) == 'undefined') ||
                        (!item.selected && !item.okSelected)) {
                        check = false;
                    }

                }
            });

            return check;
        }

        function getNumberChecked(taskId) {
            KenpinUVApi.getOrderCheck(taskId)
                .then(function (data) {
                    if(data) {
                        ctrl.numberCheck = data;
                    } else {
                        ctrl.numberCheck = 0;
                    }
                })
                .catch(function () {
                    ctrl.numberCheck = 0;
                });
        }

        ctrl.confirmDelete = function (ev) {
            if(ctrl.subscriptions.length == 0) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('オプションを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                ctrl.subscriptions = [];
            });

        };

        ctrl.save = function () {
            ctrl.isDisabled = true;
            clearInterval(intervalTime);
            document.getElementById("count-down").style.display = 'none';
            if(ctrl.subscriptions.length == 0) {
                return;
            }

            if(ctrl.has_include){
                if(!checkSelectedInclude()){
                    alert('同梱オプションを確認して下さい。')
                    return;
                }
            }

            // setCheckboxDefault();

            var kpData = {
                kenpin: ctrl.subscriptions,
                orderItemId: ctrl.orderItemId
            };
            ctrl.loadingType = 'partial';
            KenpinUVApi.createKenpin(kpData)
                .then(function (data) {
                    ctrl.subscriptions = [];
                    ctrl.numberCheck = ctrl.numberCheck + 1;
                    ctrl.loadingType = 'stopped';
                    ctrl.isDisabled = false;
                    ctrl.state.set('scan', true);
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                    ctrl.isDisabled = false;
                });

        };

        ctrl.selectOKAll = function (side) {
            _.forEach(ctrl.task, function(item) {
                if(typeof(item.current_step) != "undefined" && item.current_step !== null) {
                    if(typeof(item.current_step.TaskStep) != "undefined" && item.current_step.TaskStep !== null && item.current_step.TaskStep.title == side) {
                        item.okSelected = !allSelected;
                        item.selected = false;
                    }
                }
            });

            allSelected = !allSelected;
            _.forEach(ctrl.task, function(item) {
                if(typeof(item.current_step) != "undefined" && item.current_step !== null) {
                    if(typeof(item.current_step.TaskStep) != "undefined" && item.current_step.TaskStep !== null) {
                        ctrl.updateSubscribe(item);
                    }
                }
            });
            if(allSelected) {
                clearInterval(intervalTime);
                document.getElementById("count-down").style.display = 'none';
            }else{
                var timer;
                _.forEach(ctrl.task,function(itemStep,keyStep) {
                    if(typeof itemStep.current_step !== 'undefined' && (keyStep != 'countdown' || keyStep != 'default' || keyStep != 'detail' )) {
                        console.log('itemStep',itemStep);
                        if (!itemStep.selected && !itemStep.okSelected) {
                            timer = itemStep.current_step.detail.inspection_countdown;
                        }
                    }
                });
                startTime(timer);
            }
        };

        function setCheckboxDefault(){
            _.forEach(ctrl.task, function (item) {
                if (item.selected) {
                    item.selected = false;
                }
                if (item.okSelected) {
                    item.okSelected = false;
                }
            });
        }

        ctrl.updateSubscribe = function(item){
            var taskId = item.current_step.TaskStep.id;
            var orderItemId = item.current_step.detail.order_item_id;
            for(var i = 0 ; i < ctrl.subscriptions.length; i ++) {
                if(ctrl.subscriptions[i].taskId === taskId) {
                    ctrl.subscriptions.splice(i,1);
                }
            }
            if(item.okSelected){
                data = {
                    taskId: taskId,
                    status: [
                        {
                            id: ctrl.statusOk,
                            quantity: 1
                        }
                    ],
                    orderItemId: orderItemId,
                };
                ctrl.subscriptions.push(data);
            }
        };

        Store.data('ItemStatus').subscribe(function (data) {
            var count = 1;
            for(var i = 0 ; i < ctrl.subscriptions.length; i ++) {
                if(ctrl.subscriptions[i] !== null && ctrl.subscriptions[i].taskId === data.taskId) {
                    ctrl.subscriptions.splice(i, 1, data);
                    count--;
                }
            }
            if (count === 1 && data) {
                ctrl.subscriptions.push(data);
            }
        });

        subscription = Store.data('KenpinUVTask').subscribe(function (data) {

            if (!data) return;

            //ctrl.isMultiple = data[0].taskType === 'Multiple';
            ctrl.task = data;
            ctrl.taskIds = data['default']['data'].taskIds;
        });

        ctrl.$onDestroy = function () {
            subscription.unsubscribe();
        };

        ctrl.range = function(min, max, step) {
            step = step || 1;
            var input = [];
            for (var i = min; i <= max; i += step) {
                input.push(i);
            }
            return input;
        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.isMultiple = false;
            //ctrl.task = null;
            ctrl.state.isInfo = false;
        }

        ctrl.viewReasion = function (task) {
            var categoryId = task.current_step.TaskStep.category_id;
            var taskId = task.current_step.TaskStep.id;
            var order_item_id = task.current_step.detail.order_item_id;
            if(task.selected) {
                Store.data('Type').update([4, taskId, order_item_id]);
                ctrl.isShowReasion = true;
            } else {
                for(var i = 0 ; i < ctrl.subscriptions.length; i ++) {
                    if(ctrl.subscriptions[i].taskId === taskId) {
                        ctrl.subscriptions.splice(i,1);
                    }
                }
            }
        };

        ctrl.onDetailsClose = function () {
            ctrl.isShowReasion = false;
        };

        ctrl.showLargeImage = function (ev, item) {
            var confirm = $mdDialog.show({
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                    $scope.image_large = item.current_step.TaskStep.large_image_url;
                }],
                templateUrl: 'views/kenpinuv/kenpinuv-dialog.html',
                clickOutsideToClose:true,
            });
        };

        function checkCurrentStatus() {
            _.forEach(ctrl.task, function (item) {
                try{
                    var status = item.current_step.detail.current_status;
                    if(typeof(status) !== 'undefined') {
                        if (status === 1) {
                            item.okSelected = true;
                        }
                        if (status === 2) {
                            item.selected = true;
                        }
                    }

                    if(ctrl.task.default.data.has_kenpin && item.current_step.detail.is_include){
                        item.check_include = true;
                    }
                } catch(e){

                }
            });
        }

        function checkSelectedInclude(){
            var check = true;
            _.forEach(ctrl.task, function (item) {
                if(item.current_step){
                    if((item.okSelected && typeof(item.check_include === 'undefined')) || (item.check_include && typeof(item.okSelected === 'undefined'))) {
                        check = false;
                    }
                    if((item.okSelected && item.check_include) || item.selected) {
                        check = true;
                    }
                }
            });

            return check;
        }

        ctrl.showIncludeOption = function (ev, item) {
            if(item.check_include){
                $mdDialog.show({
                    controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                        $scope.cancelDelete = function () {
                            $mdDialog.cancel();
                        };
                        $scope.confirmDelete = function () {
                            $mdDialog.hide();
                        };

                        $scope.include_options = item.current_step.detail.include_option;
                    }],
                    templateUrl: 'views/kenpinuv/include-option.html',
                    clickOutsideToClose:true,
                });
            }
        };

        ctrl.selectOKOptionAll = function (){
            _.forEach(ctrl.task, function (item) {
                if(item.current_step){
                    item.check_include = true;
                }
            });
        }
    }
    angular.module('printty')

        .component('kenpinuvInfo', {
            require: {
                kenpinUVCtrl: '^^kenpinuvComponent'
            },
            controller: KenpinUVInfoCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/kenpinuv/kenpinuv-info.html'
        });

})();