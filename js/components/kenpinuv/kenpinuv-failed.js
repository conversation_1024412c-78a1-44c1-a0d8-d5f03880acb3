(function() {

    angular.module('printty')

        .component('kenpinuvFailed', {
            bindings: {
                isShown: '<',
                onClose: '&'
            },
            controller: KenpinUVFailedCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/kenpinuv/kenpinuv-failed.html'
        });

    function KenpinUVFailedCtrl($scope, $timeout, $mdSidenav, $mdDialog, Product, ProductLinkedCode, Store, utils, ProductSize, ProductColor, KenpinUVApi) {

        'ngInject';

        /****** variables ******/
        var ctrl = this;
        ctrl.loadingType = 'full';
        ctrl.showMode = true;
        ctrl.categoryId = null;
        ctrl.categories = null;
        ctrl.taskId = null;
        ctrl.orderItemId = null;

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.categories, function (category) {
                if (category.selected && category.KenpinStatus.quantity > 0) {
                    selectedItems.push(
                        {
                            id: category.KenpinStatus.id,
                            quantity: category.KenpinStatus.quantity
                        });
                }
            });
            return selectedItems;

        }

        var subscriptions = [],
            productForm,
            sidenav = {
                open: function () {
                    $mdSidenav('edit-product').open();
                },
                close: function () {
                    $mdSidenav('edit-product').close();
                }
            },
            loadCount = 4;

        /****** methods ******/
        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('Type').subscribe(function (data) {
                    if(data) {
                        ctrl.getKenpinStatusByType(data[0],data[1]);
                        ctrl.taskId = data[1];
                        ctrl.orderItemId = data[2];
                    }
                })
            );

        };

        ctrl.getKenpinStatusByType = function (id, task_id) {
            ctrl.loadingType = 'full';
            KenpinUVApi.fetchKenpinStatusByType({
                typeId: id,
                taskId: task_id
            })
                .then(function (data) {
                    ctrl.categories = data;
                    ctrl.loadingType = 'stopped';
                });
        }

        ctrl.$postLink = function () {

            productForm = $scope.editProductForm;

            $mdSidenav('edit-product').onClose(function () {
                $timeout(function () { ctrl.onClose(); }, 400);
            });

        };

        ctrl.$onChanges = function (changes) {

            if ( !('isShown' in changes) ) {
                return;
            }

            if (ctrl.isShown) {
                openComponent();
            } else {
                setDefault();
            }

        };

        ctrl.close = function () {
            sidenav.close();
        };

        ctrl.save = function () {

            var statuses = getSelected();

            if(statuses.length == 0) {
                ctrl.close();
                return ;
            }

            var itemStatus = {
                'taskId': ctrl.taskId,
                'status' : statuses,
                'orderItemId': ctrl.orderItemId
            };
            Store.data('ItemStatus').update(itemStatus);
            ctrl.close();

        };

        /******** private **********/

        function openComponent() {
            sidenav.open();
        }

        function setDefault() {
            ctrl.showMode = true;
            ctrl.loadingType = 'stopped';
            setFormPristine();
        }

        function setFormPristine() {
            if (productForm) {
                productForm.$setPristine();
                productForm.$setUntouched();
            }
        }

    }

})();
