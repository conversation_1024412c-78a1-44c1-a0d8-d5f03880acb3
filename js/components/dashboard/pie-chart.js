(function () {

  function PieCtrl($element) {
  
    "ngInject";
    
    var ctrl = this;
    
    ctrl.size = parseInt(ctrl.size, 10);
  
    var randomColorFactor = function() {
      return Math.round(Math.random() * 255);
    };
    var randomColor = function(opacity) {
      return 'rgba(' + randomColorFactor() + ',' + randomColorFactor() + ',' + randomColorFactor() + ',' + (opacity || '.5') + ')';
    };
    
    var color = ctrl.color ? ctrl.color: randomColor(1);
    
    var config = {
      type: 'doughnut',
  
      data: {
        labels: [
          "Red",
          "Blue"
        ],
        datasets: [{
          data: [ctrl.size, 100 - ctrl.size],
          backgroundColor: [
            color,
            "#e0e0e0"
          ],
          borderWidth: 0
        }]
      },
      options: {
        cutoutPercentage: 88,
        animation: {
          animateRotate: false
        },
        responsive: false,
        legend: {
          display: false
        },
        tooltips: {
          enabled: false
        }
      }
    };
  
    var ctx = $element.find('canvas').get(0).getContext("2d");
    var pieChart = new Chart(ctx, config);
  
    $element.find('.pie-text').text(ctrl.size + '%').css('color', color);
    
  }
  
  angular.module('printty')
  
    .component('dashboardPie', {
      bindings: {
        size: '@',
        color: '@'
      },
      controller: PieCtrl,
      controllerAs: 'vm',
      template: '<canvas width=100 height=100></canvas><div class="pie-text"></div>'
    });

})();