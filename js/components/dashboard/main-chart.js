(function () {

  function ChartCtrl($element) {

    "ngInject";

    var ctrl = this;

    var daysNames = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];

    // colors
    var colors = ['rgba(255, 172, 100, 0.9)', 'rgba(88, 188, 116, 0.9)'];
    var colorsReturned = 0;
    var randomColorFactor = function() {
      return Math.round(Math.random() * 255);
    };
    var randomColor = function(opacity) {

      if (colors.length >= colorsReturned + 1) {
        return colors[ colorsReturned++ ];
      }

      return 'rgba(' + randomColorFactor() + ',' + randomColorFactor() + ',' + randomColorFactor() + ',' + (opacity || '.5') + ')';
    };

    var setOpacity = function (color, opacity) {
      var colorArr = color.slice(5, -1).split(', ');
      colorArr[3] = opacity;

      return 'rgba(' + colorArr.join(', ') + ')'
    };

    var config = {
      type: 'line',
      data: {
        labels: daysNames,
        datasets: [
          {
            label: "1",
            data: [350, 260, 290, 215, 310, 270, 225]
          },
          {
            label: "2",
            data: [250, 150, 235, 320, 250, 100, 60]
          }
        ]
      },
      options: {
        responsive: true,
        legend: {
          display: false
        },
        hover: {
          mode: 'label'
        },
        elements: {
          point: {
            radius: 5
          }
        },
        scales: {
          xAxes: [{ display: true }],
          yAxes: [{ display: true }]
        }
      }
    };

    $.each(config.data.datasets, function(i, dataset) {
      var background = randomColor(0.8);
      dataset.borderColor = background;
      dataset.backgroundColor = setOpacity(background, 0.2);
      dataset.pointBorderColor = '#fff';
      dataset.pointBackgroundColor = background;
      dataset.pointBorderWidth = 2;
    });

    var ctx = $element.find('canvas').get(0).getContext("2d");
    var lineChart = new Chart(ctx, config);

  }

  angular.module('printty')

    .component('dashboardMainChart', {
      controller: ChartCtrl,
      controllerAs: 'vm',
      template: '<canvas width=670 height=200"></canvas>'
    });
  
})();