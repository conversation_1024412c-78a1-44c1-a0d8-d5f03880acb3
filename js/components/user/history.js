(function () {

  /*****
   *
   * User history component
   *
   * *****/
  
  function UserHistoryCtrl(Auth, $state, Store) {

    "ngInject";
    
    var ctrl = this;

    // data
    ctrl.orders = [
      {
        number: 'BL98389A',
        order_date: '2016.09.30',
        billing_month: '2016年9月',
        price: '20 860円',
        status: '予定',
        created_at: '2016-06-06 14:00',
        selected: true
      },
      {
        number: 'BL61244A',
        order_date: '2016.08.31',
        billing_month: '2016年8月',
        price: '15 300円',
        status: '支払い待ち',
        created_at: '2016-06-06 14:00',
        selected: false
      },
      {
        number: 'BL65054B',
        order_date: '2016.07.29',
        billing_month: '2016年7月',
        price: '10 008円',
        status: '支払い済み',
        created_at: '2016-06-06 14:00',
        selected: false
      },
      {
        number: 'BL88701A',
        order_date: '2016.07.01',
        billing_month: '2016年6月',
        price: '27 330円',
        status: '支払い済み',
        created_at: '2016-06-06 14:00',
        selected: false
      }
    ];

    for (var i = 0; i <= 20; i++) {
      ctrl.orders = ctrl.orders.concat(
        [
          {
            number: 'BL98389A',
            order_date: '2016.09.30',
            billing_month: '2016年9月',
            price: '20 860円',
            status: '予定',
            created_at: '2016-06-06 14:00',
            selected: false
          },
          {
            number: 'BL61244A',
            order_date: '2016.08.31',
            billing_month: '2016年8月',
            price: '15 300円',
            status: '支払い待ち',
            created_at: '2016-06-06 14:00',
            selected: false
          },
          {
            number: 'BL65054B',
            order_date: '2016.07.29',
            billing_month: '2016年7月',
            price: '10 008円',
            status: '支払い済み',
            created_at: '2016-06-06 14:00',
            selected: false
          },
          {
            number: 'BL88701A',
            order_date: '2016.07.01',
            billing_month: '2016年6月',
            price: '27 330円',
            status: '支払い済み',
            created_at: '2016-06-06 14:00',
            selected: false
          }
        ]
      );
    }
    
    var allSelected = false;
    
    ctrl.selectAll = function () {
      
      ctrl.orders.forEach(function (order) {
        order.selected = !allSelected;
      });

      allSelected = !allSelected;
      
    };
    
    // will be overriden by child component through binding
    ctrl.showHistoryInfo = function () {};
    
    // statuses
    ctrl.statuses = [
      {
        id: 0,
        title: 'すべて'
      },
      {
        id: 1,
        title: '予定'
      },
      {
        id: 2,
        title: '支払い待ち'
      },
      {
        id: 3,
        title: '支払い済み'
      }
    ];
    
    ctrl.$onInit = function() {
      Store.data('statuses').update(ctrl.statuses);
    };
    
  }

  angular.module('printty')

    .component('userHistory', {
      controller: UserHistoryCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/history.html'
    });


  /*****
   *
   * User history information (like sidenav)
   *
   * *****/
  
  function UserHistoryInfoCtrl($mdSidenav, Auth) {

    "ngInject";
    
    var ctrl = this;
    var sidenav = {
      open: function () {
        $mdSidenav('user-history-info').open();
      },
      close: function () {
        $mdSidenav('user-history-info').close();
      }
    };

    ctrl.close = sidenav.close;
    
    ctrl.showHistoryInfo = function (index) {
      sidenav.open();
    };
    
    ctrl.orders = [
      {
        number: 'AE341Q92',
        order_date: '2016.12.31',
        price: '20 860円',
        status: '印刷待ち'
      },
      {
        number: 'AA682W24',
        order_date: '2016.10.31',
        price: '15 300円',
        status: '印刷待ち'
      },
      {
        number: 'AA984R14',
        order_date: '2016.09.31',
        price: '20 008円',
        status: '印刷待ち'
      },
      {
        number: 'BG147T39',
        order_date: '2016.08.31',
        price: '27 330円',
        status: '印刷待ち'
      }
    ];
    
  }
  
  angular.module('printty')

    .component('userHistoryInfo', {
      bindings: {
        showHistoryInfo: '=showFunc'
      },
      controller: UserHistoryInfoCtrl,
      controllerAs: 'hi',
      templateUrl: 'views/user/sidenavs/history-info.html'
    });
  
})();