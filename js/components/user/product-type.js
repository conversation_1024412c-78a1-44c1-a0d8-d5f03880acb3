(function () {

  function ProductTypeCtrl($mdSidenav, Drawer, $timeout, utils) {

    "ngInject";

    var ctrl = this,
        activeCategory = {},
        sidenav = {
          open: function () {
            $mdSidenav('product-type-select').open();
          },
          close: function () {
            $mdSidenav('product-type-select').close();
          }
        };

    ctrl.close = sidenav.close;
    
    ctrl.open = function () {
      
      setDefault();

      Drawer.categories().then(function (data) {
        ctrl.types = data.types;
        
        ctrl.searchFailedTypes = !ctrl.types.length;
        
        // select all by default
        if (!ctrl.searchFailedTypes) {
          ctrl.selectType();
        }
      });
      
      sidenav.open();
    };

    ctrl.$postLink = function () {
      // set default after closing (in order to destroy all bindings)

      $mdSidenav('product-type-select').onClose(function () {
        $timeout(function () {
          setDefault();
        }, 400)
      });
    };
    
    ctrl.search = {
      searchQuery: null,
      lastSearch: null,
      search: function () {
        utils.delay(function() {
          if (ctrl.search.lastSearch === ctrl.search.searchQuery) return;
          ctrl.search.lastSearch = ctrl.search.searchQuery;

          ctrl.selectType(activeCategory.type_id, activeCategory.category_id);
        }, 350 );
      }
    };
    
    ctrl.selectType = function (typeId, categoryId) {

      Drawer.products({
        type_id: angular.isDefined(typeId) ? typeId : null,
        category_id: angular.isDefined(categoryId) ? categoryId : null,
        conditions_keywords: ctrl.search.searchQuery,
        paging_size: 100, // todo: make paging
        paging_offset: 0
      }).then(function (data) {

        ctrl.searchFailed = !data.products.length;
        
        data.products.forEach(function (product) {
          product.sides = _.flatMap(product.sides, function (side) {
            return side.ProductColorSide;
          });
        });

        activeCategory.type_id = angular.isDefined(typeId) ? typeId : null;
        activeCategory.category_id = angular.isDefined(categoryId) ? categoryId : null;
        
        ctrl.products = data.products;
        
      });
      
    };
    
    ctrl.isActive = function (type, category) {
      
      if (type) {

        if (category) {
          return (activeCategory.type_id === type.ProductType.id) && (activeCategory.category_id === category.ProductCategory.id);
        } else {
          return (activeCategory.type_id === type.ProductType.id) && (activeCategory.category_id === null);
        }
        
      } else {

        return (activeCategory.type_id === null) && (activeCategory.category_id === null);
        
      }
      
    };
    
    ctrl.selectProduct = function (product) {
      ctrl.onSelect(_.cloneDeep(product));
      ctrl.close();
    };
    
    /*** helpers ***/
    function setDefault() {
      ctrl.types = null;
      ctrl.products = null;
      
      ctrl.searchFailed = false;
      ctrl.searchFailedTypes = false;
      
      activeCategory = {};
    }
    
  }

  angular.module('printty')

    .component('productTypeSelect', {
      bindings: {
        open: '=',
        onSelect: '='
      },
      controller: ProductTypeCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/product-type.html'
    });
  
})();