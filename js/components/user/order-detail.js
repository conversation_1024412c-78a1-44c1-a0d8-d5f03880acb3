(function () {

  angular.module('printty')
    .component('userOrderDetail', {
      bindings: {
        open: '=openOn',
        viewMode: '<',
        onDelete: '&',
        onItemUpdate: '&'
      },
      controller: UserOrderDetailCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/order-detail.html'
    });
  function UserOrderDetailCtrl($element, $timeout, OrderItem, Order, Store, $mdDialog, utils) {

    "ngInject";
    
    var ctrl = this,
        $body = angular.element('body'),
        subscription,
        component = {
          show: function () {
            this.sidebar.scrollTop(0);
            $element.addClass('shown');
            $body.addClass('not-scrollable-x');
          },
          hide: function () {
            $element.removeClass('shown').addClass('hiding');
            $timeout(function () {
              $element.removeClass('hiding');
              $body.removeClass('not-scrollable-x');
              ctrl.onClose();
            }, 300)
          },
          sidebar: $element.find('.sidebar')
        },
        loadCount = 2;

    ctrl.order = null;
    ctrl.deliveryService = null;
    ctrl.orderId = null;
    ctrl.periods = [];
    ctrl.items = [];
    ctrl.viewMode = !!ctrl.viewMode;
    ctrl.loadingType = 'full';

    var pagination = {
      perPage: 40,
      current: 0,
      
      searchFailed: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;

    subscription = Store.data('DeliveryPeriods').subscribe(function (data) {
      ctrl.periods = data;
    });
    
    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };
    
    ctrl.open = function (order) {

      startLoading();

      ctrl.orderId = order.Order.id;
      ctrl.moreBilling = false;
      
      fetchOrder();
      
      ctrl.infiniteItems.numLoaded_ = 0;
      ctrl.infiniteItems.toLoad_ = 0;
      ctrl.infiniteItems.getItemAtIndex(1);
      
      component.show();
    };

    ctrl.infiniteItems = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.items[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {

        if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

        pagination.busy = true;

        this.toLoad_ += pagination.perPage;

        pagination.current++;

        OrderItem.fetchItems(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            order_id: ctrl.orderId
          }
        ).then(
          function (data) {
            pagination.searching = false;

            ctrl.infiniteItems.numLoaded_ = ctrl.infiniteItems.toLoad_ - pagination.perPage + data.items.length;
            ctrl.items = ctrl.items.concat(data.items);

            pagination.searchFailed = !ctrl.items.length;
            pagination.end = ctrl.items.length >= data.total_count;
            pagination.busy = false;

            stopLoading(250);
            
          }
        );

      }
    };
    
    ctrl.close = function () {
      component.hide();
    };

    ctrl.onClose = function () {
      setDefault();
    };
    
    ctrl.getDeliveryPeriodById = function (deliveryId) {
      
      var deliveryPeriod = _.find(ctrl.periods, function (period) {
        return period.OrderDeliveryPeriod.id == deliveryId;
      });
      
      return deliveryPeriod ? deliveryPeriod.OrderDeliveryPeriod.title : null;
    };
    
    ctrl.confirmDelete = function (ev) {
      
      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('注文を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteOrder);
    };
    
    ctrl.onOrderItemDelete = function (itemId) {

      startLoading();
      
      _.remove(ctrl.items, function (item) {
        return item.OrderItem.id == itemId;
      });

      fetchOrder();
      
      ctrl.onItemUpdate();
      
    };
    
    /******* private *********/
    
    function setDefault() {
      ctrl.order = null;
      ctrl.orderId = null;
      ctrl.items = [];

      loadCount = 2;
      
      pagination.end = false;
      pagination.busy = false;
      pagination.current = 0;
    }
    
    function startLoading(type) {
      
      switch (type) {
        case 'partial':
          ctrl.loadingType = 'partial';
          break;
        case 'full':
        default:
          ctrl.loadingType = 'full'
      }
      
    }
    
    function stopLoading(defer) {
      
      if (loadCount > 1) {
        loadCount--;
        return;
      }
      
      $timeout(function () {
        ctrl.loadingType = 'stopped'
      }, defer || 0);
      
    }
    
    function deleteOrder() {

      ctrl.loadingType = 'partial';
      
      Order.deleteOrder(ctrl.orderId)
        .then(function () {

          ctrl.onDelete({
            orderId: ctrl.orderId
          });

          ctrl.close();
        })
        .catch(function () {
          ctrl.close();
        });
      
    }
    
    function fetchOrder() {
      Order.fetchOrder(ctrl.orderId)
        .then(function (data) {
          ctrl.order = data.Order;
          ctrl.deliveryService = data.DeliveryServiceJoin;
          ctrl.moreBilling = !ctrl.order.is_billing_shipping_same;
          ctrl.key = data.Key;
          stopLoading(250);

        });
    }
    
  }
  
  
  /**** 
   * 
   * User detail sidenavs 
   * 
   * *****/
  
  angular.module('printty')
    .component('userOrderProductDetailSidenav', {
      require: {
        ordersDetailCtrl: '^^userOrderDetail'
      },
      bindings: {
        onDelete: '&'
      },
      controller: userOrderProductDetailSidenavCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/sidenavs/product-detail.html'
    });
  
  function userOrderProductDetailSidenavCtrl($mdSidenav, OrderItem, $mdDialog, utils) {

    "ngInject";
    
    // variables
    var ctrl = this;
    var sidenav = {
      open: function () {
        $mdSidenav('user-order-product-detail').open();
      },
      close: function () {
        $mdSidenav('user-order-product-detail').close();
        
      }
    };
    
    ctrl.item = null;
    ctrl.loadingType = 'stopped';
    ctrl.close = sidenav.close;
    
    ctrl.viewItemDetail = function (item) {

      ctrl.loadingType = 'full';
      
      ctrl.item = null;
      ctrl.sides = [];
      
      OrderItem.fetchItem(item.OrderItem.id)
        .then(function (itemData) {
          ctrl.item = itemData;

          ctrl.sides = _.map(ctrl.item.tasks, function (task) {
            
            return {
              title: task.ProductColorSide.title,
              image_url: task.OrderItemTask.image_url
            };
            
          });

          ctrl.loadingType = 'stopped';
          
        });
      
      sidenav.open();
    };
    
    ctrl.confirmDelete = function (ev) {
      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('注文アイテムを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteOrderItem);
    };
    
    /**** show mode *****/
    ctrl.isShow = true;
    
    ctrl.toggleEditMode = function () {
      ctrl.isShow = !ctrl.isShow;
    };

    /**** modify parent controller ****/
    ctrl.$onInit = function() {
      ctrl.ordersDetailCtrl.viewItemDetail = ctrl.viewItemDetail;
      ctrl.viewMode = ctrl.ordersDetailCtrl.viewMode;
    };
    
    /******** private *********/
    function deleteOrderItem() {

      ctrl.loadingType = 'partial';

      OrderItem.deleteItem(ctrl.item.OrderItem.id)
        .then(function () {

          ctrl.onDelete({
            itemId: ctrl.item.OrderItem.id
          });

          ctrl.close();
        })
        .catch(function () {
          ctrl.close();
        });
      
    }

    ctrl.BodyProcurementFail = function () {
      var confirm = $mdDialog.confirm({
        template: utils.inputReasonForFail(),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelInputReason = function () {
            $mdDialog.hide();
          };

          $scope.confirmInputReason = function () {
            angular.element('.global-loading').show();
            var reason = $('#input-reason').val();
            if(reason.length > 0) {
              ctrl.reason = reason;

            } else {
              $('#input-error').html('error');
            }

            var params = {
              reason: ctrl.reason,
              item_id: ctrl.item.OrderItem.id,
            };

            OrderItem.updateReasonForFail(params).then(function (data){
              angular.element('.global-loading').hide();
              ctrl.item.OrderItem.body_procurement_failure = data.OrderItem.body_procurement_failure;
              $mdDialog.hide();
            })
            .catch(function () {
              angular.element('.global-loading').hide();
            });
          };
        }],
        onComplete : function(scope, element){
          var input_reason = ctrl.item.OrderItem.body_procurement_failure;
          $('#input-reason').val(input_reason);
        },
        clickOutsideToClose: true
      });

      $mdDialog.show(confirm);
    };
    
  }

})();