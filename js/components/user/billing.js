(function () {

    function UserBillingCtrl(Order, utils) {

        "ngInject";

        var ctrl = this;

        ctrl.isShown = false;
        ctrl.pagination = {
            start_date: null,
            end_date: null,

            reload: false,

            loading: false,
        };

        ctrl.billing = [];

        ctrl.clickSearch = false;

        var start_date = moment().startOf('month').format('YYYY-MM-DD');
        var end_date = moment().format('YYYY-MM-DD');

        ctrl.startDate = moment(start_date, 'YYYY-MM-DD').toDate();
        ctrl.endDate = moment(end_date, 'YYYY/MM/DD').toDate();

        ctrl.reload = function() {
            ctrl.pagination.reload = true;

            ctrl.billing = [];
            fetchBilling();
        };

        /****** private ******/
        function fetchBilling() {
            utils.listLoading.show();

            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');

            var params = {
                start_date: ctrl.pagination.start_date,
                end_date: ctrl.pagination.end_date,
            };
            Order.fetchBilling(params)
                .then(function (data) {
                    ctrl.billing = data;
                    ctrl.pagination.loading = false;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();
                })
                .catch(function () {
                    utils.listLoading.hide();
                });
        }

        /*** private ****/

        ctrl.onDateChange = function () {
            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');
            ctrl.reload();
        };

        ctrl.$onInit = function () {
            ctrl.pagination.start_date = moment(ctrl.startDate).format('YYYY-MM-DD');
            ctrl.pagination.end_date = moment(ctrl.endDate).format('YYYY-MM-DD');

            fetchBilling();

            utils.appLoaded();
        };

        ctrl.CSVdownloadBilling = function () {
            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime),
                'YYYY-MM-DD')
                .format('YYYY-MM-DD');

            utils.globalLoading.show();

            var params = {
                start_date: ctrl.pagination.start_date,
                end_date: ctrl.pagination.end_date,
            };

            Order.downloadCSVBilling(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });
        };

    }

    angular.module('printty')

        .component('userBilling', {
            controller: UserBillingCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/user/billing.html'
        });

})();