(function () {
  
  function UserCtrl($scope, utils, Auth, Store) {

    "ngInject";
    
    var ctrl = this;
    
    ctrl.isAuthorized = Auth.isAuthorized;
    ctrl.statuses = [];

    var subscription;

    $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });
    
    ctrl.$onInit = function () {
      utils.appLoaded();
      Store.data('statuses').update(ctrl.statuses);
      subscription = Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); });
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    ctrl.showNav = function (title){
      var found = false;
      Store.data('Mode').subscribe(function (data){
        if(data && data.mode_sub){
          found = Auth.contains(data.mode_sub,title);
        }
      });
      return found;
    }

    /*** private ****/
    function onStatusesChange(statuses) {
      ctrl.statuses = statuses;
    }
    
  }
  
  angular.module('printty')
    
    .component('userComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: UserCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/index.html'
    });

})();