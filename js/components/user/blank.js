(function () {

    angular.module('printty')
        .component('userBlank', {
            require: {
                parentCtrl: '^^userComponent'
            },
            controller: <PERSON>lankController,
            controllerAs: 'vm',
            templateUrl: 'views/admin/blank.html'
        });

    function BlankController(Auth, $state, Order, utils, Store, $mdDialog) {

        "ngInject";

        if (!Auth.isAuthorized('orders')) {
            return $state.go( Auth.defaultRoute );
        }

        utils.appLoaded();

        var ctrl = this;
        ctrl.date = moment().toDate();

        ctrl.pagination = {
            init: true,
            perPage: 40,
            current: 0,
            status_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };


        var pagination = ctrl.pagination;

        pagination.reload = function () {
            this.init = true;
            this.end = false;
            this.busy = false;
            this.current = 0;
            ctrl.orders = [];
        };

        function getStatuses() {
            Order.statusesAll().then(function (data) {
                var statuses = _.flatMap(data.statuses, function (status) {
                    return status.OrderStatus;
                });

                Store.data('statuses').update(statuses);
            });
        }

        ctrl.$onInit = function() {
            getStatuses();
            ctrl.parentCtrl.newOrder = ctrl.newOrder;

            ctrl.parentCtrl.downloadCSV = ctrl.downloadCSV;

            subscription = Store.event('sidebarActiveStatus')
                .subscribe(function (status) {
                    onStatusChange(status)
                });
        };

        function onStatusChange(status) {
            pagination.status_id = status;
            pagination.reload();
        }
        ctrl.downloadCSV = function () {
            ctrl.startTime = moment(ctrl.date, 'YYYY/MM/DD').toDate();
            ctrl.endTime = moment(ctrl.date, 'YYYY-MM-DD').toDate();

            var confirm = $mdDialog.confirm({
                template: utils.downloadCSVOrderTemplate(),

                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.getStartDate = function(date){
                        ctrl.startTime = date;
                    };
                    $scope.getEndDate = function(date){
                        ctrl.endTime = date;
                    };
                    $scope.download = function () {
                        angular.element('.global-loading').show();
                        var time_start = moment(moment(ctrl.startTime), 'YYYY-MM-DD').format('YYYY-MM-DD');
                        var time_end = moment(moment(ctrl.endTime), 'YYYY-MM-DD').format('YYYY-MM-DD');
                        Order.downloadOrderCSV({
                            start: time_start,
                            end: time_end,
                            status_id: pagination.status_id,
                        })
                            .then(function (){
                                $mdDialog.cancel();
                                angular.element('.global-loading').hide();
                            })
                            .catch(function () {
                                $mdDialog.cancel();
                                angular.element('.global-loading').hide();
                            });
                    };
                }],

                clickOutsideToClose: true
            });

            $mdDialog.show(confirm);
        };
    }

})();