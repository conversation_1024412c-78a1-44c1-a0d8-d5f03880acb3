(function () {
  
  function NewOrderCtrl($element, $timeout, Upload, utils, $scope, Drawer, DrawToolUtils) {

    "ngInject";

    $element.addClass('new-order-wrapper');
    
    var ctrl = this,
        $body = angular.element('body'),
        component = {
          show: function () {
            $element.addClass('shown');
            $body.addClass('not-scrollable-x');
            
            this.afterShow.forEach(function (callback) {
              callback();
            });
          },
          hide: function () {
            
            var _self = this;
            
            $element.removeClass('shown').addClass('hiding');
            
            $timeout(function () {
              $element.removeClass('hiding');
              $body.removeClass('not-scrollable-x');
              
              _self.afterHide.forEach(function (callback) {
                callback();
              });
              
            }, 300);
            
          },
          
          // callbacks
          afterShow: [],
          afterHide: []
        };
    
    ctrl.open = function () {
      component.show();
      
      // get default settings
      Drawer.default().then(function (data) {
        ctrl.productSelected(data.default);
      });
      
    };
    ctrl.close = function() {
      component.hide();
    };

    // initialize DrawTool after component is shown
    component.afterShow.push(function () {
      DrawTool.initialize(container, {});
    });

    // clear everything after component hide
    component.afterHide.push(function () {
      ctrl.products = null;
      ctrl.product = null;

      ctrl.active.setDefault();
    });

    ctrl.makeColorArr = function (hexArr) {
      if (hexArr && angular.isString(hexArr)) {
        return hexArr.replace(/ /g,'').split(',');
      }
    };
    
    // todo: make as a standalone component
    // -------------------------------
    // ********* Drawing tool *********
    // --------------------------------
    
    // some vars
    var container = $element.find('.drawtool-container').get(0),
        previewContainer = $element.find('.drawtool-preview');
    
    var itemPosition = {
      // defaults
      DEFAULT_FONT_SIZE: 12,
      COLOR: '#ffffff',
      BACKGROUND: '#648CC8',
      FONT_FAMILY: 'Arial',
      
      // positions
      x1: null,
      y1: null,
      x2: null,
      y2: null,
      
      rendered: false,
      borderSize: null, // store border size
      
      init: function () {
        
        this.hide();
        
        this.x1 = new fabric.Text('', { 
          left: 100, 
          top: 100, 
          fontSize: this.DEFAULT_FONT_SIZE,
          backgroundColor: this.BACKGROUND,
          fill: this.COLOR,
          fontFamily: this.FONT_FAMILY,
          lineHeight: 1.1,
          originX: 'right',
          angle: -90
        });
        this.y1 = new fabric.Text('', { 
          left: 100, 
          top: 150, 
          fontSize: this.DEFAULT_FONT_SIZE,
          backgroundColor: this.BACKGROUND,
          fill: this.COLOR,
          fontFamily: this.FONT_FAMILY,
          lineHeight: 1.1
        });
        this.x2 = new fabric.Text('', { 
          left: 100, 
          top: 150, 
          fontSize: this.DEFAULT_FONT_SIZE,
          backgroundColor: this.BACKGROUND,
          fill: this.COLOR,
          fontFamily: this.FONT_FAMILY,
          lineHeight: 1.1,
          angle: -90,
          originX: 'left',
          originY: 'bottom'
        });
        this.y2 = new fabric.Text('', { 
          left: 100, 
          top: 150, 
          fontSize: this.DEFAULT_FONT_SIZE,
          originX: 'right',
          backgroundColor: this.BACKGROUND,
          fill: this.COLOR,
          fontFamily: this.FONT_FAMILY,
          lineHeight: 1.1
        });
      },
      
      update: function (position, size) {
        
        var itemPosLT = DrawTool.sides.selected.items.selected.item.getPointByOrigin('left', 'top');
        var itemPosRB = DrawTool.sides.selected.items.selected.item.getPointByOrigin('right', 'bottom');
        
        this.x1.set({
          text: ' ' + DrawToolUtils.trailingZero( Math.round10( position.left * 10, -1) ) + ' mm ',
          top: itemPosLT.y,
          left: itemPosLT.x - this.DEFAULT_FONT_SIZE - 4
        });
        
        this.y1.set({
          text: ' ' + DrawToolUtils.trailingZero( Math.round10( position.top * 10, -1) ) + ' mm ',
          top: itemPosLT.y - this.DEFAULT_FONT_SIZE - 4,
          left: itemPosLT.x
        });

        this.x2.set({
          text: ' ' + DrawToolUtils.trailingZero( Math.round10( (this.borderSize.width - ( position.left + size.width ) ) * 10, -1) ) + ' mm ',
          top: itemPosRB.y,
          left: itemPosRB.x + this.DEFAULT_FONT_SIZE + 4
        });

        this.y2.set({
          text: ' ' + DrawToolUtils.trailingZero( Math.round10( (this.borderSize.height - ( position.top + size.height ) ) * 10, -1) ) + ' mm ',
          top: itemPosRB.y + 4,
          left: itemPosRB.x
        });

        if (!this.rendered) {
          DrawTool.sides.selected.FabricCanvas.add(this.x1);
          DrawTool.sides.selected.FabricCanvas.add(this.y1);
          DrawTool.sides.selected.FabricCanvas.add(this.x2);
          DrawTool.sides.selected.FabricCanvas.add(this.y2);
          this.rendered = true;
        }

        DrawTool.sides.selected.FabricCanvas.renderAll();
        
      },
      
      hide: function () {
        if (this.x1 && this.x2 && this.y1 && this.y2) {
          this.x1.remove();
          this.y1.remove();
          this.x2.remove();
          this.y2.remove();
        }

        this.rendered = false;
      },
      visible: function () {
        
        if (this.x1 && this.x2 && this.y1 && this.y2) {
          this.x1.setVisible(true);
          this.y1.setVisible(true);
          this.x2.setVisible(true);
          this.y2.setVisible(true);
          DrawTool.sides.selected.FabricCanvas.renderAll();
        }
      },
      invisible: function () {
        if (this.x1 && this.x2 && this.y1 && this.y2) {
          this.x1.setVisible(false);
          this.y1.setVisible(false);
          this.x2.setVisible(false);
          this.y2.setVisible(false);
        }
      }
    };

    // preview mode
    ctrl.showMode = false;
    ctrl.toggleShowMode = function () {

      if (ctrl.showMode) { // show draw tool
        DrawTool.sides.selected.workspaceGrid.enable(true);
        
        itemPosition.visible();
        previewContainer.empty();

        $element.removeClass('show-mode');
      } else { // show product preview
        DrawTool.sides.selected.workspaceGrid.enable(false);
        
        itemPosition.invisible();
        
        // show preview image
        var img = new Image();
        img.src = DrawToolUtils.getPreviewSide(DrawTool.sides.selected, 0.9);
        previewContainer.append(img);

        $element.addClass('show-mode');
      }

      ctrl.showMode = !ctrl.showMode;
    };
    
    // object containing info about items coordinates (top center inputs in view)
    ctrl.coordinates = {
      x: 0,
      y: 0,
      size_x: 0,
      size_y: 0,
      doNotTrigger: false,
      clear: function () {
        var _self = this;
        
        $timeout(
          function () {
            _self.x = _self.y = _self.size_x = _self.size_y = 0;
          },
          0
        );
      },
      update: function (e, type) {
        
        // trigger update only if for 500ms nothing was entered
        utils.delay(function() {
          this.doNotTrigger = true;
          
          var sizes = DrawToolUtils.getSize(DrawTool.sides.selected.items.selected);
          
          if (type === 'x') {
            DrawTool.sides.selected.items.selected.position({left: e.currentTarget.value / 10 + sizes.width / 2 });
          } else if (type === 'y') {
            DrawTool.sides.selected.items.selected.position({top: e.currentTarget.value / 10 + sizes.height / 2});
          } else if (type === 'size_x') {
            DrawTool.sides.selected.items.selected.size({width: e.currentTarget.value / 10});
          } else if (type === 'size_y') {
            DrawTool.sides.selected.items.selected.size({height: e.currentTarget.value / 10});
          }
        }, 500 );
        
      }
    };
    
    // object containing info about active color, side, size
    ctrl.active = {
      color: null,
      side: null,
      size: null,
      setDefault: function () {
        this.color = null;
        this.side = null;
        this.size = null;
      },
      _setSidesInactive: function () {
        
        if (!this.color) {
          return;
        }
        
        this.color.sides.forEach(function (side) {
          if (angular.isDefined(side.active)) {
            side.active = false;
          }
        });
      },
      setSize: function (size) {
        this.size = size;
      }
    };
    
    ctrl.active.setSide = function (side) {
      
      if (side.active) {
        return;
      }
      
      // mark this side as active
      this.side = side;
      
      itemPosition.hide();
      
      // set other sides as inactive
      this._setSidesInactive();
      
      var sideDraw = DrawTool.sides.getSide(side.content.id);
      
      itemPosition.borderSize = {
        width: side.content.border.cm.width,
        height: side.content.border.cm.height
      };
      
      side.active = true;
      
      // select side from DrawTool
      ctrl.coordinates.clear();
      DrawTool.sides.select(side.content.id);
      
      sideDraw.layers.list.forEach(function (item) {
        sideDraw.layers.bringToFront( item.index );
      });

      sideDraw.FabricCanvas.on('object:moving', updateCoordinates);
      sideDraw.FabricCanvas.on('object:scaling', updateCoordinates);
      
    };
    
    ctrl.active.setColor = function (color) {
      
      // set all sides inactive (if was previously set active)
      this._setSidesInactive();
      
      var _self = this;

      _self.color = color;
      
      // create sides with borders + create preview
      color.sides.forEach(function (side, index) {
        
        var last = (index === color.sides.length - 1);

        var sideDraw = DrawTool.sides.getSide(side.content.id);
        
        if (!sideDraw) {
          sideDraw = DrawTool.sides.addSide(side.content.id);

          sideDraw.padding = 100;
        }
        
        sideDraw.FabricCanvas.off('object:moving');
        sideDraw.FabricCanvas.off('object:scaling');
        
        sideDraw.setImage(side.content.imageUrl + '?_', side.content.size.cm)
          .then(function() {
            
            sideDraw.setBorder(side.content.border.cm);
            sideDraw.backdrop.opacity = 1;

            sideDraw.FabricCanvas.backgroundColor = '#fafafa';
            
            sideDraw.initGrid();
            sideDraw.initWorkspaceGrid();
            sideDraw.workspaceGrid.enable(true);
            sideDraw.workspaceGrid.sendToBack();
            
            sideDraw.layers.list.forEach(function (item) {
              sideDraw.layers.bringToFront( item.index );
            });
            
            if (last) {
              updateProductPreviews(color.sides);
              _self.setSide(color.sides[0]);
            }

          });
        
      });
      
    };
    
    // select product
    ctrl.productSelected = function (product) {
      
      // fetch product details
      Drawer.product(product.Product.id).then(function (data) {
        
        ctrl.product = data;
        
        ctrl.colors = data.colors;
        
        // parse side content into valid js object + retrieve sides (get rid of ProductColorSide param)
        ctrl.colors.forEach(function (color) {

          color.sides = _.flatMap(color.sides, function (side) {
            side.ProductColorSide.content = DrawToolUtils.parseContentJSON(side.ProductColorSide.content);
            return side.ProductColorSide;
          });
          
        });
        
        // set default size
        var activeSize = _.find(ctrl.product.sizes, function (size) {
          return !!+size.ProductSize.is_main;
        });
        activeSize = activeSize ? activeSize : ctrl.product.sizes[0];
        
        ctrl.active.setSize(activeSize);
        
        // set default color
        var activeColor = _.find(ctrl.colors, function (color) {
          return !!+color.ProductColor.is_main;
        });

        activeColor = activeColor ? activeColor : ctrl.colors[0];
        ctrl.active.setColor(activeColor);
        
      });
      
    };
    
    //=============================================
    //======= Draw Tool Actions ===================
    //=============================================
    ctrl.addImage = function ($files, $file) {
      
      if (!$file) return;
        
      var fileType = DrawToolUtils.filterFiles($file);
      
      if (fileType !== 'image' && fileType !== 'svg') {
        return;
      }
      
      // convert image data to blob
      Upload.dataUrl($file).then(function (data) {
        
        if (!data) return;
        
        if (fileType === 'image') {
          DrawTool.sides.selected.items.addImage(data);
        } else if (fileType === 'svg') {
          DrawTool.sides.selected.items.addSVG(data);
        }

        updatePreview();
        
      });
      
    };
    
    ctrl.rotateLeft = function () {
      var current = DrawTool.sides.selected.items.selected.rotation();
      DrawTool.sides.selected.items.selected.rotation(current - 30);
      updateCoordinates(true);
      updatePreview(50);
    };
    ctrl.rotateRight = function () {
      var current = DrawTool.sides.selected.items.selected.rotation();
      DrawTool.sides.selected.items.selected.rotation(current + 30);
      updateCoordinates(true);
      updatePreview(50);
    };
    
    // align
    ctrl.align = function (dir) {
      DrawTool.sides.selected.items.selected[dir]();
      updateCoordinates(true);
      updatePreview(50);
    };
    
    ctrl.bringTo = function (pos) {

      DrawTool.sides.selected.layers.update().then(function (data) {
        
        if (pos === 'top') {
          
          DrawTool.sides.selected.layers.bringToFront( DrawTool.sides.selected.items.selected.item.uuid );
          
        } else if (pos === 'back') {

          data.forEach(function (obj) {
            
            if (obj.index !== DrawTool.sides.selected.items.selected.item.uuid) {
              DrawTool.sides.selected.layers.bringForward( obj.index );
            }
            
          });
          
        }

        updatePreview(50);
        
      });
      
    };
    
    ctrl.deleteLayer = function () {
      DrawTool.sides.selected.items.selected.remove();
      updatePreview(50);
    };
    
    ctrl.move = {
      toggle: function () {
        
        if (this['class'] === 'move-layer') {
          this['class'] = 'move-canvas';
          DrawTool.sides.selected.panning = true;
        } else {
          this['class'] = 'move-layer';
          DrawTool.sides.selected.panning = false;
        }
        
      },
      'class': 'move-layer'
    };

    //=============================================
    //======== Draw Tool Events ===================
    //=============================================
    DrawTool.on('object:added', function () {
      updatePreview(350);
    });
    
    DrawTool.on('object:selected', function() {
      itemPosition.init();
      updateCoordinates();
    });
    
    DrawTool.on('object:modified', function() {
      updatePreview(50);
    });
    
    DrawTool.on('selection:cleared', function () {
      itemPosition.hide();
      DrawTool.sides.selected.FabricCanvas.renderAll();
      ctrl.coordinates.clear();
    });
    
    /**** Update functions **********/
    function updateCoordinates() {

      var fixDigest = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      
      if (ctrl.coordinates.doNotTrigger) {
        ctrl.coordinates.doNotTrigger = false;
        return;
      }
      
      if (!DrawTool.sides.selected.items.selected.item) return;
      
      var position = DrawToolUtils.positionByOrigin(DrawTool.sides.selected.items.selected, 'left', 'top');
      var sizes = DrawToolUtils.getSize(DrawTool.sides.selected.items.selected);
      
      if (fixDigest) {
        $timeout(setCoors, 0);
      } else {
        setCoors();
        $scope.$apply();
      }

      itemPosition.update(position, sizes);
      
      function setCoors() {
        ctrl.coordinates.x = DrawToolUtils.trailingZero( Math.round10( position.left * 10, -1) );
        ctrl.coordinates.y = DrawToolUtils.trailingZero( Math.round10( position.top * 10, -1) );
        ctrl.coordinates.size_x = Math.round10(sizes.width * 10, -1);
        ctrl.coordinates.size_y = Math.round10(sizes.height * 10, -1);
      }

    }
    
    function updatePreview() {

      var latency = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 150;
      
      $timeout(function () {
        
        if (DrawTool.sides.selected && DrawTool.sides.selected.items._collection.length) {
          
          itemPosition.invisible();

          var src = DrawToolUtils.getPreviewSide(DrawTool.sides.selected, 0.9);
          
          itemPosition.visible();

          ctrl.active.side.image_url = src;
          
        }
      
      }, latency);
      
    }
    
    function updateProductPreviews(sides) {
      var latency = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 150;

      $timeout(function () {

        sides.forEach(function (side) {

          var sideDraw = DrawTool.sides.getSide(side.content.id);
          side.image_url = DrawToolUtils.getPreviewSide(sideDraw, 0.5);
          
        })

      }, latency);
    }
    
  }

  angular.module('printty')

    .component('newOrder', {
      bindings: {
        open: '=open'
      },
      controller: NewOrderCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/user/new-order.html'
    });
  
})();