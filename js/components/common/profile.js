(function () {

  /*****************************
   * ------- user manage -----
   ****************************/

  angular.module('printty')

    .component('userManage', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      templateUrl: 'views/common/user-info.html',
      controller: function ($state, Store, Auth, Order, utils) {

        'ngInject';

        var ctrl = this;
        ctrl.isAuthorized = Auth.isAuthorized;
        var subscription;

        ctrl.appBlocks = {
          options: {
            admin: '管理',
            user: '発注',
            // dashboard: 'Dashboard',
            print: '印刷',
            dtftranscription: 'DTF転写',
            storage: '在庫',
            kenpin: '検品',
            kenpini: '検品(入れ込み)',
            kenpinuv: '検品(SCP)',
            // random: 'アンドンG',
            // newrandom: 'newあんどんGP',
            andonkai: 'あんどん',
            // seirenrandom: 'あんどん（倉庫精練)',
            totalrandom: 'トータルあんどん',
            randomuv: 'アンドンS',
            andonstatistical: 'あんどん（時間計測）',
            reference: 'レファレンス',
            delivery: '配送準備',
            image: '画像生成プロセスの監視',
            import:'マスター一括登録',
            quality: '品質管理',
            repair: '補修',
            business: 'CSV注文',
            barcode: '発送バーコード排出機能',
            shipping: 'まとめて発送',
            factoryorder: '工場発注',
            printoriginal: '印刷(処理前画像)',
          },
          get current() {

            if ($state.includes('admin')) {
              return 'admin'
            }

            if ($state.includes('user')) {
              return 'user'
            }

            // if ($state.includes('dashboard')) {
            //   return 'dashboard'
            // }

            if ($state.includes('print')) {
              return 'print'
            }

            if ($state.includes('dtftranscription')) {
              return 'dtftranscription'
            }

            if ($state.includes('printoriginal')) {
              return 'printoriginal'
            }

            if ($state.includes('storage')) {
              return 'storage'
            }

            if ($state.includes('kenpin')) {
              return 'kenpin'
            }

            if ($state.includes('kenpini')) {
              return 'kenpini'
            }

              if ($state.includes('kenpinuv')) {
                  return 'kenpinuv'
              }

            if($state.includes('random')) {
                return 'random'
            }

            if($state.includes('newrandom')) {
              return 'newrandom'
            }

            if($state.includes('andonkai')) {
              return 'andonkai'
            }

            if($state.includes('seirenrandom')) {
              return 'seirenrandom'
            }

            if($state.includes('totalrandom')) {
              return 'totalrandom'
            }

              if($state.includes('randomuv')) {
                  return 'randomuv'
              }
            if($state.includes('andonstatistical')) {
              return 'andonstatistical'
            }

            if ($state.includes('reference')) {
              return 'reference'
            }

            if ($state.includes('delivery')) {
              return 'delivery'
            }

            if ($state.includes('image')) {
                return 'image'
            }

            if ($state.includes('import')) {
                return 'import'
            }

            if ($state.includes('quality')) {
                return 'quality'
            }

            if ($state.includes('repair')) {
                return 'repair'
            }

            if ($state.includes('business')) {
              return 'business'
            }

            if ($state.includes('barcode')) {
              return 'barcode'
            }

            if ($state.includes('shipping')) {
              return 'shipping'
            }

            if ($state.includes('factoryorder')) {
              return 'factoryorder'
            }

          },
          set: function (block,title) {
            var mode_sub = null;
            Store.data('Mode').subscribe(function (data){
              if(data && data.mode_sub){
                mode_sub = redirect_to(data,title);
              }
            })
            $state.go(mode_sub ? mode_sub.key : block);

          },
          isAuthorized: function (option) {

            if (option === 'admin') {
              return Auth.isAuthorized('users') || Auth.isAuthorized('production') || Auth.isAuthorized('customers');
            }

            if (option === 'user') {
              return Auth.isAuthorized('orders');
            }

            if (option === 'print') {
              return Auth.isAuthorized('print');
            }

            if (option === 'dtftranscription') {
              return Auth.isAuthorized('dtftranscription');
            }

            if (option === 'printoriginal') {
              return Auth.isAuthorized('printoriginal');
            }

            if (option === 'storage') {
              return Auth.isAuthorized('storage');
            }

            if (option === 'kenpin') {
              return Auth.isAuthorized('kenpin');
            }

            if (option === 'kenpini') {
              return Auth.isAuthorized('kenpini');
            }

              if (option === 'kenpinuv') {
                  return Auth.isAuthorized('kenpinuv');
              }

            if(option === 'random'){
                return Auth.isAuthorized('random');
            }

            if(option === 'newrandom'){
              return Auth.isAuthorized('newrandom');
            }

            if(option === 'preprocess'){
              return Auth.isAuthorized('preprocess');
            }

            if(option === 'andonkai'){
              return Auth.isAuthorized('andonkai');
            }

            if(option === 'andonwr'){
              return Auth.isAuthorized('andonwr');
            }

            if(option === 'andonsf'){
              return Auth.isAuthorized('andonsf');
            }

            if(option === 'seirenrandom'){
              return Auth.isAuthorized('seirenrandom');
            }

            if(option === 'totalrandom'){
              return Auth.isAuthorized('totalrandom');
            }

              if(option === 'randomuv'){
                  return Auth.isAuthorized('randomuv');
              }

            if (option === 'reference') {
              return Auth.isAuthorized('reference');
            }

            if (option === 'delivery') {
              return Auth.isAuthorized('delivery');
            }

            if (option === 'image') {
                return Auth.isAuthorized('image');
            }

            if (option === 'import') {
                return Auth.isAuthorized('import');
            }

            if (option === 'quality') {
                return Auth.isAuthorized('quality');
            }

            if (option === 'repair') {
                return Auth.isAuthorized('repair');
            }

            if (option === 'business') {
              return Auth.isAuthorized('business');
            }

            if (option === 'barcode') {
              return Auth.isAuthorized('barcode');
            }

            if (option === 'shipping') {
              return Auth.isAuthorized('bulkshipping');
            }

            if (option === 'factoryorder') {
              return Auth.isAuthorized('factoryorder');
            }

          }
        };

        ctrl.$onInit = function() {
          Store.data('Mode').subscribe(function (data){
              if(data && data.mode){
                  for (var key in ctrl.appBlocks.options) {
                    var found = data.mode.some(function(item) {
                      return item.title === ctrl.appBlocks.options[key];
                    });
                    if (!found) {
                      delete ctrl.appBlocks.options[key];
                    }
                  }
              }
          })
          subscription = Store.data('account').subscribe(function (data) {
            if(data){
              ctrl.account = data;
              if(typeof ctrl.account.Account.subcontractor_code !== "undefined" && ctrl.account.Account.subcontractor_code !== null){
                ctrl.appBlocks.options.delivery = '配送準備' + ctrl.account.Account.subcontractor_code;
              }
            }
          });
        };

        ctrl.$onDestroy = function () {
          subscription.unsubscribe();
        };

        ctrl.logout = function () {
          Auth.signout().then(
            function () {
              $state.go('auth.signin');
            }
          );
        };

        ctrl.downloadItemInfoCSV = function () {
          utils.globalLoading.show();

          Order.downloadItemInfo()
              .then(function () {
                utils.globalLoading.hide();
              })
              .catch(function () {
                utils.globalLoading.hide();
              });
        }

        function redirect_to(data,title){
          var mode_sub = null;
          var mode_sub_tab = null;
          var mode = data.mode.find(function(item) {
            return item.title === title;
          });
          if(mode){
            mode_sub = data.mode_sub.find(function(item) {
              return item.mode_id === mode.id;
            });
          }
          if(mode_sub){
            mode_sub_tab = data.mode_sub_tab.find(function(item) {
              return item.mode_sub_id === mode_sub.id;
            });
          }
          return mode_sub_tab ? mode_sub_tab : mode_sub;
        }

      },
      controllerAs: 'vm'
    });

    /*****************************
     * ------ profile edit ------
     ****************************/

    function profileEditCtrl($scope, $mdSidenav, Store, Auth) {

      "ngInject";

      var ctrl = this,
          sidenav = {
            open: function () {
              $mdSidenav('profile-edit').open();
            },
            close: function () {
              $mdSidenav('profile-edit').close();
            }
          };

      ctrl.loadingType = 'stopped';
      ctrl.isAuthorized = Auth.isAuthorized;

      ctrl.editProfile = function () {

        if (!ctrl.isAuthorized('account')) {
          return;
        }

        $scope.editAccountForm.$setPristine();
        $scope.editAccountForm.$setUntouched();

        ctrl.account = _.cloneDeep( Store.data('account').get() );

        sidenav.open();
      };

      ctrl.saveAccount = function () {

        if (!ctrl.isAuthorized('account')) return;

        if ( angular.equals(ctrl.account, Store.data('account').get()) ) {
          return sidenav.close();
        }

        $scope.editAccountForm.$setSubmitted();

        if ($scope.editAccountForm.$invalid) {
          return;
        }

        if (ctrl.account.Account.password || ctrl.account.Account.passwordConf) {
          if (ctrl.account.Account.password !== ctrl.account.Account.passwordConf) {
            alert('passwords do not match!');
            return;
          }
        }

        var data = angular.copy(ctrl.account);

        ctrl.loadingType = 'partial';

        Auth.updateAccount(data).then(
          function () {
            ctrl.loadingType = 'stopped';
            sidenav.close();
          },
          function () {
            ctrl.loadingType = 'stopped';
            sidenav.close();
          }
        );

      };

      ctrl.close = sidenav.close;

    }

    angular.module('printty')
      .component('profileEdit', {
        bindings: {
          editProfile: '=openOn'
        },
        controller: profileEditCtrl,
        controllerAs: 'vm',
        templateUrl: 'views/common/sidenavs/profile-edit.html'
      });

    /*****************************
     * ------ profile edit ------
     ****************************/

    function ProfileApiCtrl($mdSidenav, $timeout, Store, Auth, $scope) {

      "ngInject";

      var ctrl = this;
      var sidenav = {
        open: function () {
          $mdSidenav('profile-api').open();
        },
        close: function () {
          $mdSidenav('profile-api').close();
        }
      };

      ctrl.clipSuccessMsg = false;
      ctrl.loadingType = 'stopped';

      ctrl.isAuthorized = Auth.isAuthorized;
      ctrl.close = sidenav.close;

      ctrl.optionBlock = {
        isShown: false,
        active: {}
      };

      ctrl.viewApi = function () {

        ctrl.settings = _.cloneDeep( Store.data('settings').get() );

        sidenav.open();
      };

      ctrl.saveSettings = function () {

        if (!ctrl.isAuthorized('account')) return;

        if ( angular.equals(ctrl.settings, Store.data('settings').get()) ) {
          return sidenav.close();
        }

        $scope.editSettingsForm.$setSubmitted();

        if ($scope.editSettingsForm.$invalid) return;

        ctrl.loadingType = 'partial';

        Auth.updateSettings({
          AccountSettings: {
            billing: ctrl.settings.AccountSettings.billing
          }
        }).then(
          function () {
            Store.data('settings').update(ctrl.settings);
            ctrl.loadingType = 'stopped';
            sidenav.close();
          },
          function () {
            ctrl.loadingType = 'stopped';
            sidenav.close();
          }
        );

      };

      ctrl.onSuccessCopy = function(e) {
        e.clearSelection();

        ctrl.clipSuccessMsg = true;
        $timeout(function () {
          ctrl.clipSuccessMsg = false;
        }, 500);
      };

      // options block

      ctrl.optionBlock.show = function (key, value) {
        ctrl.optionBlock.type = 'details';
        ctrl.optionBlock.active = {
          key: key,
          value: value,
          object: ctrl.settings.AccountSettings.api_callback_options
        };
        ctrl.optionBlock.isShown = true;
      };

      ctrl.optionBlock.add = function () {
        ctrl.optionBlock.type = 'add';
        ctrl.optionBlock.active = {
          key: null,
          value: null,
          object: ctrl.settings.AccountSettings.api_callback_options
        };
        ctrl.optionBlock.isShown = true;
      };

      ctrl.optionBlock.onClose = function () {
        ctrl.optionBlock.isShown = false;
      };

      ctrl.optionBlock.onCreate = function (key, value) {
        ctrl.settings.AccountSettings.api_callback_options[key] = value;

        Auth.updateSettings(ctrl.settings)
          .then(function () {
            Store.data('settings').update(ctrl.settings);
          })
          .catch(function () {
            delete ctrl.settings.AccountSettings.api_callback_options[key];
          })
          .finally(function () {
            ctrl.loadingType = 'stopped';
            ctrl.optionBlock.onClose();
          });

      };

      ctrl.optionBlock.onUpdate = function (original, key, value) {

        if (original.key === key) {
          ctrl.settings.AccountSettings.api_callback_options[key] = value;
        } else {
          ctrl.settings.AccountSettings.api_callback_options[key] = value;
          delete ctrl.settings.AccountSettings.api_callback_options[original.key];
        }

        Auth.updateSettings(ctrl.settings)
          .then(function () {
            Store.data('settings').update(ctrl.settings);
          })
          .catch(function () {
            if (original.key === key) {
              ctrl.settings.AccountSettings.api_callback_options[key] = original.value;
            } else {
              delete ctrl.settings.AccountSettings.api_callback_options[key];
              ctrl.settings.AccountSettings.api_callback_options[original.key] = original.value;
            }
          })
          .finally(function () {
            ctrl.loadingType = 'stopped';
            ctrl.optionBlock.onClose();
          });
      };

      ctrl.optionBlock.onDelete = function (key) {

        var original = {
          key: key,
          value: ctrl.settings.AccountSettings.api_callback_options[key]
        };

        delete ctrl.settings.AccountSettings.api_callback_options[key];

        Auth.updateSettings(ctrl.settings)
          .then(function () {
            Store.data('settings').update(ctrl.settings);
          })
          .catch(function () {
            ctrl.settings.AccountSettings.api_callback_options[original.key] = original.value;
          })
          .finally(function () {
            ctrl.loadingType = 'stopped';
            ctrl.optionBlock.onClose();
          });

      };

    }

    angular.module('printty')
      .component('profileApi', {
        bindings: {
          viewApi: '=openOn'
        },
        controller: ProfileApiCtrl,
        controllerAs: 'vm',
        templateUrl: 'views/common/sidenavs/profile-api.html'
      })

})();