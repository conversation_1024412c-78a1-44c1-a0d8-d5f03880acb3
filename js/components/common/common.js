(function () {
  
  angular.module('printty')
    .component('sidebarSecondaryMenu', {
      bindings: {
        statuses: '<'
      },
      controller: SidebarMenuCtrl,
      controllerAs: 'vm',
      template: '<ul>' + 
                  '<li ng-repeat="status in vm.statuses" ng-class="{active: vm.isActive(status)}"><a href ng-click="vm.setActive(status)">{{ status.title }}</a></li>' +
                '</ul>'
    });
  
  function SidebarMenuCtrl(Store) {
    
    "ngInject";

    var ctrl = this;

    ctrl.activeStatus = 0;

      ctrl.$onInit = function() {

          subscription = Store.event('activeTypeId').subscribe(function (type_id) {
              var status = {id: type_id};
              ctrl.setActive(status)
          });

      };

    ctrl.$onChanges = function () {

      if (window.location.href.indexOf("/admin/production/") > -1) {
          if (localStorage.getItem('type_id') == '0'
              || (window.location.href.indexOf("/admin/production/waiting") > -1)
          ) {
              ctrl.activeStatus = parseInt(localStorage.getItem('type_id'));
          }
          else {
              ctrl.activeStatus = localStorage.getItem('type_id');
          }
      } else {
          ctrl.activeStatus = 0;
      }
    };

    ctrl.setActive = function (status) {
      ctrl.activeStatus = status.id;
      localStorage.setItem("type_id", this.activeStatus)
      Store.event('sidebarActiveStatus').emit(this.activeStatus);
    };
    
    ctrl.isActive = function (status) {
      return status.id === ctrl.activeStatus;
    };
    
  }

    angular.module('printty')
        .component('sidebarSecondaryMenuCustomer', {
            bindings: {
                customers: '<'
            },
            controller: SidebarMenuCustomerCtrl,
            controllerAs: 'vm',
            template: '<ul>' +
            '<li ng-repeat="customer in vm.customers" ng-class="{active: vm.isActive(customer)}"><a href ng-click="vm.setActive(customer)">{{ customer.Customer.title }}</a></li>' +
            '</ul>'
        });

    function SidebarMenuCustomerCtrl(Store) {

        "ngInject";

        var ctrl = this;

        ctrl.activeStatus = 0;

        ctrl.$onChanges = function () {
            ctrl.activeStatus = 0;
        };

        ctrl.setActive = function (customer) {
            ctrl.activeStatus = customer.Customer.id;
            Store.event('sidebarActiveCustomers').emit(this.activeStatus);
        };

        ctrl.isActive = function (customer) {
            return customer.Customer.id === ctrl.activeStatus;
        };

    }

    angular.module('printty')
        .component('sidebarSecondaryMenuType', {
            bindings: {
                types: '<'
            },
            controller: SidebarMenuTypeCtrl,
            controllerAs: 'vm',
            template: '<ul>' +
            '<li ng-repeat="type in vm.types" ng-class="{active: vm.isActive(type)}"><a href ng-click="vm.setActive(type)">{{ type.title }}</a></li>' +
            '</ul>'
        });

    function SidebarMenuTypeCtrl(Store) {

        "ngInject";

        var ctrl = this;

        ctrl.activeStatus = 0;

        ctrl.$onChanges = function () {
            ctrl.activeStatus = 0;
        };

        ctrl.setActive = function (type) {
            ctrl.activeStatus = type.id;
            Store.event('sidebarActiveTypes').emit(this.activeStatus);
        };

        ctrl.isActive = function (type) {
            return type.id === ctrl.activeStatus;
        };

    }

})();