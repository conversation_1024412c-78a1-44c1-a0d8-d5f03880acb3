(function () {

  function OrderDetailCtrl($element, $timeout) {

    "ngInject";
    
    var ctrl = this;
    
    ctrl.isShown = false;

    var $body = angular.element('body');
    
    var component = {
      show: function () {
        this.sidebar.scrollTop(0);
        $element.addClass('shown');
        $body.addClass('not-scrollable-x');
        ctrl.isShown = true;
      },
      hide: function () {
        $element.removeClass('shown').addClass('hiding');
        $timeout(function () {
          $element.removeClass('hiding');
          $body.removeClass('not-scrollable-x');
          ctrl.isShown = false;
        }, 300)
      },
      sidebar: $element.find('.sidebar')
    };
    
    ctrl.viewDetail = function () {
      ctrl.moreBilling = false;
      component.show();
    };
    ctrl.close = function () {
      component.hide();
    };

    if (!ctrl.viewMode) {
      ctrl.viewMode = false;
    }

    ctrl.products = [
      {
        title: 'バック  •  M  •  グレー  •  10枚',
        images: [
          'img/tmp/items/shirt7.png'
        ],
        comm_price: '1 500円',
        print_price: '500円',
        total_price: '20 000円',
        status: '印刷待ち',
        created_at: '2016-06-06 14:00'
      },
      {
        title: 'Poloシャッツ  •  S  •  グレー  •  4枚',
        images: [
          'img/tmp/items/shirt.png'
        ],
        comm_price: '1 700円',
        print_price: '600円',
        total_price: '8 000円',
        status: '印刷中',
        created_at: '2016-06-06 14:00'
      },
      {
        title: 'Tシャッツ  •  L  •  グレー  •  15枚',
        images: [
          'img/tmp/items/shirt2.png',
          'img/tmp/items/shirt.png',
          'img/tmp/items/shirt3.png',
          'img/tmp/items/shirt4.png'
        ],
        comm_price: '1 450円',
        print_price: '790円',
        total_price: '30 000円',
        status: '印刷待ち',
        created_at: '2016-06-06 14:00'
      },
      {
        title: 'Poloシャッツ  •  S  •  グレー  •  20枚',
        images: [
          'img/tmp/items/shirt5.png',
          'img/tmp/items/shirt6.png'
        ],
        comm_price: '2 000円',
        print_price: '495円',
        total_price: '40 000円',
        status: '送信済み',
        created_at: '2016-06-06 14:00'
      },
      {
        title: 'バック  •  M  •  グレー  •  8枚',
        images: [
          'img/tmp/items/shirt7.png'
        ],
        comm_price: '1 200円',
        print_price: '380円',
        total_price: '16 000円',
        status: '送信済み',
        created_at: '2016-06-06 14:00'
      }
    ];

  }

  /**** user detail component ******/
  angular.module('printty')
    .component('orderDetail', {
      bindings: {
        viewDetail: '=viewDetail',
        viewMode: '<'
      },
      controller: OrderDetailCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/common/orders/order-detail.html'
    });


  /****
   *
   * User detail sidenavs
   *
   * *****/

  function OrderProductDetailSidenavCtrl($mdSidenav) {

    "ngInject";
    
    // variables
    var ctrl = this;
    var sidenav = {
      open: function () {
        $mdSidenav('order-product-detail').open();
      },
      close: function () {
        $mdSidenav('order-product-detail').close();
      }
    };

    ctrl.close = sidenav.close;
    ctrl.viewProductDetail = function () {
      sidenav.open();
    };

    ctrl.sides = [
      {
        title: '表',
        image_url: 'img/tmp/sides/side1.png'
      },
      {
        title: '裏',
        image_url: 'img/tmp/sides/side2.png'
      },
      {
        title: '右袖',
        image_url: 'img/tmp/sides/side3.png'
      }
    ];

    /**** show mode *****/
    ctrl.isShow = true;

    ctrl.toggleEditMode = function () {
      ctrl.isShow = !ctrl.isShow;
    };

    /**** modify parent controller ****/
    ctrl.$onInit = function() {
      ctrl.ordersDetailCtrl.viewProductDetail = ctrl.viewProductDetail;
      ctrl.viewMode = ctrl.ordersDetailCtrl.viewMode;
    };

  }

  // detail sidenav
  angular.module('printty')
    .component('orderProductDetailSidenav', {
      require: {
        ordersDetailCtrl: '^^orderDetail'
      },
      controller: OrderProductDetailSidenavCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/common/orders/sidenavs/product-detail.html'
    });


})();