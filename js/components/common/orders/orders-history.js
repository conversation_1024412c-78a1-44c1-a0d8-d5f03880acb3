(function() {

  function OrdersHistoryCtrl($mdSidenav) {

    "ngInject";
    
    var ctrl = this;
    var sidenav = {
      open: function () {
        $mdSidenav('orders-history').open();
      },
      close: function () {
        $mdSidenav('orders-history').close();
      }
    };

    ctrl.close = sidenav.close;

    ctrl.open = function () {
      sidenav.open();
    };

    ctrl.orders = [
      {
        number: 'BL98389A',
        order_date: '2016.09.30',
        billing_month: '2016年9月',
        price: '20 860円',
        status: '予定',
        created_at: '2016-06-06 14:00',
        selected: true
      },
      {
        number: 'BL61244A',
        order_date: '2016.08.31',
        billing_month: '2016年8月',
        price: '15 300円',
        status: '支払い待ち',
        created_at: '2016-06-06 14:00',
        selected: false
      },
      {
        number: 'BL65054B',
        order_date: '2016.07.29',
        billing_month: '2016年7月',
        price: '10 008円',
        status: '支払い済み',
        created_at: '2016-06-06 14:00',
        selected: false
      },
      {
        number: 'BL88701A',
        order_date: '2016.07.01',
        billing_month: '2016年6月',
        price: '27 330円',
        status: '支払い済み',
        created_at: '2016-06-06 14:00',
        selected: false
      }
    ];

    var allSelected = false;
    ctrl.selectAll = function () {

      ctrl.orders.forEach(function (order) {
        order.selected = !allSelected;
      });

      allSelected = !allSelected;

    };

  }

  angular.module('printty')

    .component('ordersHistory', {
      bindings: {
        open: '=open'
      },
      controller: OrdersHistoryCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/common/orders/sidenavs/orders-history.html'
    });

})();
