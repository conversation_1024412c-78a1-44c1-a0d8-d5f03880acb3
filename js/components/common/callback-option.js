(function () {

  angular.module('printty')
    .component('callbackOption', {
      controller: CallbackOptionCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/common/callback-option.html',
      bindings: {
        isShown: '<ngShow',
        type: '<',
        active: '<',
        
        loadingType: '=',
        
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&'
      }
    });

  function CallbackOptionCtrl($scope, utils, $mdDialog) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.key = null;
    ctrl.value = null;

    ctrl.isAdd = true;
    ctrl.showMode = true;

    var optionForm,
      keyClone,
      valueClone;

    /****** methods ******/
    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      optionForm = $scope.optionForm;
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        keyClone = ctrl.key;
        valueClone = ctrl.value;
        ctrl.showMode = false;
      } else {
        ctrl.key = keyClone;
        ctrl.value = valueClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {
      
      optionForm.$setSubmitted();

      if (optionForm.$invalid) return;

      if  ( ctrl.key === keyClone && ctrl.value === valueClone ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      if (keyClone !== ctrl.key && _.has(ctrl.active.object, ctrl.key) ) {
        alert('Key already exists!');
        return;
      }

      ctrl.loadingType = 'partial';
      
      ctrl.onUpdate({
        original: {
          key: keyClone,
          value: valueClone
        },
        key: ctrl.key,
        value: ctrl.value
      });

    };

    ctrl.save = function () {

      optionForm.$setSubmitted();

      if (optionForm.$invalid) return;
      
      if (_.has(ctrl.active.object, ctrl.key)) {
        alert('Key already exists!');
        return;
      }
      
      ctrl.loadingType = 'partial';
      
      ctrl.onCreate({
        key: ctrl.key,
        value: ctrl.value
      });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('オプションを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteOption);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd) {
        ctrl.key = ctrl.active.key;
        ctrl.value = ctrl.active.value;
      }

    }

    function updateOriginal() {
      var sizeData = _.cloneDeep(ctrl.size);

      ctrl.onUpdate({
        size: sizeData
      });
    }

    function deleteOption() {

      ctrl.loadingType = 'partial';

      ctrl.onDelete({
        key: ctrl.key
      });

    }

    function setDefault() {
      ctrl.isAdd = true;
      
      ctrl.key = null;
      ctrl.value = null;

      setFormPristine();
    }

    function setFormPristine() {
      if (optionForm) {
        optionForm.$setPristine();
        optionForm.$setUntouched();
      }
    }
  }

})();