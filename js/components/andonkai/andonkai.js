(function () {
    function AndonKaiCtrl(AndonApi, utils, Auth, $state,  $interval, $mdDialog,Factory) {

        "ngInject";

        if (!Auth.isAuthorized('andonkai')) {
            return $state.go( Auth.defaultRoute );
        }


        var ctrl = this;
        ctrl.data = [];
        ctrl.date1 = moment().toDate();
        ctrl.date2 = moment().toDate();
        ctrl.date3 = moment().toDate();
        ctrl.quantity = 0;

        utils.appLoaded();

        ctrl.isHide = false;
        ctrl.state = {

            name: 'scan',
            history: [],
            isInfo: false,


            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

        ctrl.$onInit = function () {
            var date1 = moment(ctrl.date1).format('YYYY-MM-DD');
            var date2 = moment(ctrl.date2).format('YYYY-MM-DD');
            var date3 = moment(ctrl.date3).format('YYYY-MM-DD');
            getData(date1,date2,date3);
            interval = $interval(function() {
                date1 = moment(ctrl.date1).format('YYYY-MM-DD');
                date2 = moment(ctrl.date2).format('YYYY-MM-DD');
                date3 = moment(ctrl.date3).format('YYYY-MM-DD');
                getData(date1,date2,date3);
            }, 600000);
        };

        ctrl.$onDestroy = function () {
            $interval.cancel(interval);
        }

        ctrl.onChange = function(){
            var date1 = moment(ctrl.date1).format('YYYY-MM-DD');
            var date2 = moment(ctrl.date2).format('YYYY-MM-DD');
            var date3 = moment(ctrl.date3).format('YYYY-MM-DD');
            getData(date1,date2,date3);
        }
        function getData(date1,date2,date3){
            if(ctrl.factory){
                getAndonData(date1,date2,date3);
            }else{
                Factory.fetchFactoryUser().then(function (data) {
                    ctrl.factory = data.factorySelected;
                    ctrl.factories = data.factories;
                    getAndonData(date1,date2,date3);
                });
            }
        }
        function getAndonData(date1,date2,date3){
            AndonApi.getAndonData(
                {
                    date1: date1,
                    date2: date2,
                    date3: date3,
                    factory_id: ctrl.factory
                }
            ).then(function (data) {
                delete(data.customers.customers_data.customers.remaining);
                delete(data.customers.customers_data.customers.nontmix);
                ctrl.data = data;
            });
        }

        var tick = function() {
            ctrl.clock = Date.now();
        };

        tick();
        $interval(tick, 1000);

    }

    angular.module('printty')

        .component('andonkaiComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: AndonKaiCtrl,
            controllerAs: 'rc',
            templateUrl: 'views/andonkai/andonkai.html'
        });
})();