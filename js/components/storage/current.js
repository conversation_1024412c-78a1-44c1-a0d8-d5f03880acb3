(function () {

  function StorageCurrentCtrl(Storage, utils, Auth, Store, Supplier, StorageActivityContent, Factory) {

    "ngInject";
    
    utils.appLoaded();
    
    var ctrl = this;

    ctrl.products = [];

    ctrl.STOCK_STATUS_IN_STOCK = 1;
    ctrl.STOCK_STATUS_WARNING = 2;
    ctrl.STOCK_STATUS_OUT_STOCK = 3;
    ctrl.STOCK_STATUS_OUT_BALANCE = 4;
    ctrl.STOCK_STATUS_OUT_BALANCE_AND_WARNING = 5;
    ctrl.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK = 6;

      ctrl.date = moment().toDate();
      ctrl.startDate = moment().toDate();
      ctrl.endDate = moment().toDate();

      ctrl.storageContent = [];

      ctrl.storageFilter = [];
      ctrl.activeSize = {
          'title' : ''
      };
      ctrl.activeColor = {
          'title' : ''
      };

    var subscriptions = [],
        allSelected = false;
    
    var pagination = {
      init: true,
      perPage: 40,
      current: 0,
      type_id: null,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false,
      noPermission: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;

    pagination.reload = function () {

      this.init = true;
      this.end = false;
      this.busy = false;
      this.current = 0;

      ctrl.products = [];

      ctrl.infiniteProducts.numLoaded_ = 0;
      ctrl.infiniteProducts.toLoad_ = 0;
      ctrl.infiniteProducts.getItemAtIndex(1);
      // getGarmentInfo(ctrl.date);
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 1000 );
    };
    
    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };
    
    // todo: вынести в отдельный класс (?)
    ctrl.infiniteProducts = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.products[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        if(!ctrl.factory) return;
        if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

        pagination.busy = true;

        if (pagination.init) {
          utils.listLoading.show(!pagination.searching);
        } else {
          utils.moreItemsLoad.show()
        }

        this.toLoad_ += pagination.perPage;

        pagination.current++;

        Storage.list(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            conditions_keywords: pagination.searchQuery,
            product_type_id: pagination.product_type_id,
            type_id: pagination.type_id,
            size_title: ctrl.activeSize.title,
            color_title: ctrl.activeColor.title,
            factory_id: ctrl.factory,
          }
        ).then(
          function (data) {
            pagination.init = false;
            pagination.searching = false;

            ctrl.infiniteProducts.numLoaded_ = ctrl.infiniteProducts.toLoad_ - pagination.perPage + data.products.length;
            ctrl.products = ctrl.products.concat(data.products);

            pagination.searchFailed = !ctrl.products.length;
            pagination.end = ctrl.products.length >= data.total_count;
            pagination.busy = false;
            pagination.noPermission = data.no_permission;

            utils.listLoading.hide();
            utils.moreItemsLoad.hide();
          }
        );

      }
    };

    ctrl.$onInit = function () {
      ctrl.matrix = ctrl.parentCtrl.matrix;
      getFactories();

      getStatuses();

      getTypes();

      getSuppliers();

      getStorageContent();

      getStorageFilter();

      subscriptions.push(Store.event('sidebarActiveStatus')
        .subscribe(function (status) {
          onStatusChange(status)
        }));

        subscriptions.push(

            Store.event('sidebarActiveTypes')
                .subscribe(function (type) {
                    pagination.type_id = type;
                    pagination.reload();
                })

        );

        $('#fileInput').on('change', function (e) {
            ctrl.CSVupload(e.target.files[0]);
        }).on('click', function () {
            this.value = null;
        });
    };

    function getSuppliers() {
        Supplier.fetchSuppliers({}).then(function (data) {
            Store.data('Suppliers').update(data.suppliers);
        });
    }

      function getStorageContent() {
          StorageActivityContent.fetchContents({}).then(function (data) {
              ctrl.storageContent = data;
          });
      }

      function getStorageFilter() {
          Storage.fetchFilters().then(function (data) {
              ctrl.storageFilter = data;
          });
      }

      function getFactories() {
          Factory.fetchFactoryUser().then(function(data){
              ctrl.factories = data.factories;
              ctrl.factory = data.factorySelected;
              Store.data('FactorySelect').update(ctrl.factory);
              Store.data('FactorySelect').subscribe(function () {
                  getGarmentInfo();
              })
          });
      }

      function getTypes() {
          var types = [
              {id : 0, title: 'すべて'},
              {id : ctrl.STOCK_STATUS_OUT_STOCK, title: '在庫無し'},
              {id : ctrl.STOCK_STATUS_WARNING, title: '在庫が少ない'},
              {id : ctrl.STOCK_STATUS_OUT_BALANCE, title: '在庫切れ連絡'},
              {id : ctrl.STOCK_STATUS_IN_STOCK, title: '在庫有り'}
          ];
          Store.data('types').update(types);
      }

    ctrl.$onDestroy = function () {
        _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };
    
    ctrl.selectAll = function () {

      ctrl.products.forEach(function (product) {
        product.selected = !allSelected;
      });

      allSelected = !allSelected;

    };

    /*** private ****/
    function getStatuses() {
      Storage.typesAll().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.ProductType;
        });

        Store.data('statuses').update(statuses);
      });
    }

    function onStatusChange(status) {
      pagination.product_type_id = status;
      pagination.reload();
    }

    ctrl.onPicking = function() {
        pagination.reload();
    };

      ctrl.onCertificate = function() {
          pagination.reload();
      };

    ctrl.shipmentCsvDownload = function() {
        var start_date = moment(ctrl.startDate).format('YYYY-MM-DD');
        var end_date = moment(ctrl.endDate).format('YYYY-MM-DD');

        utils.globalLoading.show();

        var params = {
            conditions_keywords : pagination.searchQuery,
            product_type_id : pagination.product_type_id,
            type_id : pagination.type_id,
            start_date : start_date,
            end_date : end_date,
            size_title: ctrl.activeSize.title,
            color_title: ctrl.activeColor.title,
            factory_id: ctrl.factory,
        };

        Storage.csv.downloadShipment(params)
            .then(function () {
                utils.globalLoading.hide();
            })
            .catch(function () {
                utils.globalLoading.hide();
            });
    };

      ctrl.CSVdownload = function () {

          var date = moment(ctrl.date).format('YYYY-MM-DD');

          utils.globalLoading.show();

          var params = {
              conditions_keywords : pagination.searchQuery,
              product_type_id : pagination.product_type_id,
              type_id : pagination.type_id,
              date : date,
              size_title: ctrl.activeSize.title,
              color_title: ctrl.activeColor.title,
              factory_id: ctrl.factory,
          };

          Storage.csv.download(params)
              .then(function () {
                  utils.globalLoading.hide();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

      };

      ctrl.CSVupload = function (file) {

          if (!file) {
              $('#fileInput').click();
              return;
          }

          var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

          if ( fileType !== 'csv' ) {
              alert('File.UnexpectedFormat');
              return;
          }

          utils.globalLoading.show();

          var params = {
              csv : file,
              factory_id: ctrl.factory,
          };

          Storage.csv.upload(params).then(function(){
              utils.globalLoading.hide();
              pagination.reload();
          },function () {
              utils.globalLoading.hide();
          });
      };

      ctrl.setActiveSize = function (size) {
          ctrl.activeSize = (size.ProductSize.title !== 'すべて') ? size.ProductSize : '';
          pagination.reload();
      };

      ctrl.setActiveColor = function (color) {
          ctrl.activeColor = (color.ProductColor.title !== 'すべて') ? color.ProductColor : '';
          pagination.reload();
      };

      ctrl.onChange = function (){
          pagination.reload();
          Store.data('FactorySelect').update(ctrl.factory);
      }

      ctrl.onDateChange = function () {
            getGarmentInfo(ctrl.date);
        };

      function getGarmentInfo(date){
        if(!ctrl.factory) return;
        var date = moment(date).format('YYYY-MM-DD');
        var params = {
            date : date,
            factory_id: ctrl.factory
        };
        StorageActivityContent.totalGarment(params).then(function(data) {
            ctrl.total_garment = data;
        });
    }
    
  }

  angular.module('printty')

    .component('storageCurrent', {
      require: {
        parentCtrl: '^^storageComponent'
      },
      controller: StorageCurrentCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/current.html'
    });

})();