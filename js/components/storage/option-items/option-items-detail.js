(function() {

  function StorageOptionItemsDetailCtrl($mdSidenav, $mdToast, Storage, $timeout, $scope, Store, StorageOption) {

    "ngInject";
    
    var ctrl = this,
        originalProduct,
        productClone,
        sidenav = {
          open: function () {
            $mdSidenav('storage-option-items-detail').open();
          },
          close: function () {
            $mdSidenav('storage-option-items-detail').close();
          }
        };

      ctrl.suppliers = [];
      ctrl.storageContent = [];
      ctrl.showMode = true;
      ctrl.factory = null;

      ctrl.STOCK_STATUS_IN_STOCK = 1;
      ctrl.STOCK_STATUS_WARNING = 2;
      ctrl.STOCK_STATUS_OUT_STOCK = 3;
      ctrl.STOCK_STATUS_OUT_BALANCE = 4;
      ctrl.STOCK_STATUS_OUT_BALANCE_AND_WARNING = 5;
      ctrl.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK = 6;

    setDefault();

    ctrl.open = function (product) {
      setDefault();

      originalProduct = product;

      StorageOption.details({
        option_item_id: product.OptionItem.id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        ctrl.product = data.product;
        
        ctrl.sides = data.sides;

        ctrl.loadingType = 'stopped';
        
      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
      sidenav.open();
    };
    
    ctrl.close = sidenav.close;

    ctrl.$onInit = function () {
        Store.data('Suppliers').subscribe(function (data) {
            ctrl.suppliers = data;
        })
        Store.data('FactorySelect').subscribe(function (data) {
            ctrl.factory = data;
        })
    };
    
    ctrl.$postLink = function () {
      
      // set default after closing (in order to destroy all bindings)
      
      $mdSidenav('storage-option-items-detail').onClose(function () {
        $timeout(function () {
          setDefault();
        }, 400)
      });
      
    };

    // add/remove from storage
    ctrl.updateHistory = angular.noop; // will be updated by inner component

      ctrl.$onDestroy = function () {
          Store.data('Suppliers').update([]);
      };
    
    ctrl.addToStorage = function (content_id) {

      $scope.addRemoveStorageForm.$setSubmitted();
      if ($scope.addRemoveStorageForm.$invalid) return;
      
      var num = _.toInteger(ctrl.movement);
      if (num <= 0) return;

      ctrl.loadingType = 'partial';
      
      StorageOption.productQuantity.add({
        Product: {
            id: ctrl.product.OptionItem.id
        },
        Storage: {
          quantity: num
        },
        content_id : content_id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        $mdToast.show(
          $mdToast.simple()
            .textContent(num + 'を入庫しました')
            .position('top left')
            .toastClass('printty-toast')
            .hideDelay(2000)
        );

        if(data.update) {
            ctrl.product.StorageOptionJoin.balance = parseInt(ctrl.product.StorageOptionJoin.balance) + parseInt(ctrl.movement);
            ctrl.product.StorageOptionJoin.updated_at = data.updated_at;
            checkStatus();
            if (ctrl.updateOriginal) updateOriginal();
        }

        ctrl.movement = null;
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();

        if(data.update) {
            ctrl.updateHistory();
        }
        ctrl.loadingType = 'stopped';
        
      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
    };

    function checkStatus(){
        return true;
        // if(ctrl.product.StorageOptionJoin.total_order == 0){
        //     // in stock
        //     ctrl.product.Storage.status = ctrl.STOCK_STATUS_IN_STOCK;
        // } else {
        //     // get more 10% of total order
        //     var max_range = Math.ceil(ctrl.product.Storage.total_order + (ctrl.product.Storage.total_order * 0.1));
        //
        //     if(ctrl.product.Storage.quantity < ctrl.product.Storage.total_order) {
        //         //out stock
        //         ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_STOCK;
        //     } else if (ctrl.product.Storage.quantity >= ctrl.product.Storage.total_order && ctrl.product.Storage.quantity <= max_range) {
        //         // warning
        //         ctrl.product.Storage.status = ctrl.STOCK_STATUS_WARNING;
        //     } else {
        //         // in stock
        //         ctrl.product.Storage.status = ctrl.STOCK_STATUS_IN_STOCK;
        //     }
        //
        //     // out balance
        //     if (ctrl.product.Storage.min_quantity != null && ctrl.product.Storage.quantity < ctrl.product.Storage.min_quantity) {
        //         switch(ctrl.product.Storage.status) {
        //             case ctrl.STOCK_STATUS_IN_STOCK:
        //                 ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE;
        //                 break;
        //             case ctrl.STOCK_STATUS_WARNING:
        //                 ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE_AND_WARNING;
        //                 break;
        //             case ctrl.STOCK_STATUS_OUT_STOCK:
        //                 ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK;
        //                 break;
        //             default:
        //                 ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE;
        //         }
        //     }
        // }
    }

    ctrl.removeFromStorage = function (content_id) {

      $scope.addRemoveStorageForm.$setSubmitted();
      if ($scope.addRemoveStorageForm.$invalid) return;
      
      var num = _.toInteger(ctrl.movement);
      if (num <= 0) return;

      ctrl.loadingType = 'partial';
      
      StorageOption.productQuantity.remove({
        Product: {
          id: ctrl.product.OptionItem.id
        },
        Storage: {
          quantity: num
        },
        content_id : content_id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        $mdToast.show(
          $mdToast.simple()
            .textContent(num + 'を出庫しました')
            .position('top left')
            .toastClass('printty-toast')
            .hideDelay(2000)
        );

        if(data.update) {
            ctrl.product.StorageOptionJoin.balance = parseInt(ctrl.product.StorageOptionJoin.balance) - parseInt(ctrl.movement);
            ctrl.product.StorageOptionJoin.updated_at = data.updated_at;
            checkStatus();
            if (ctrl.updateOriginal) updateOriginal();
        }

        ctrl.movement = null;
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();

        if(data.update) {
            ctrl.updateHistory();
        }

        ctrl.loadingType = 'stopped';

      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
    };
    
    /*** helpers ***/
    function setDefault() {
      ctrl.product = null;
      ctrl.sides = null;
      ctrl.showMode = true;
      ctrl.movement = null;
      
      ctrl.loadingType = 'full';
      
      if ($scope.addRemoveStorageForm) {
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();
      }

        if ($scope.updateStorageForm) {
            $scope.updateStorageForm.$setPristine();
            $scope.updateStorageForm.$setUntouched();
        }
    }
    
    function updateOriginal() {
      originalProduct.StorageOption.balance = ctrl.product.StorageOptionJoin.balance;
      originalProduct.StorageOption.updated_at = ctrl.product.StorageOptionJoin.updated_at;
    }

      ctrl.edit = function () {

          if (ctrl.showMode) {
              productClone = _.cloneDeep(ctrl.product);
              ctrl.showMode = false;
          } else {
              ctrl.product = productClone;
              ctrl.showMode = true;
          }

      };
    
  }

  angular.module('printty')
    .component('storageOptionItemsDetail', {
      bindings: {
        open: '=',
        updateOriginal: '<',
        storageContent: '<',
        factory: '='
      },
      controller: StorageOptionItemsDetailCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/option-items/option-items-detail.html'
    });
  
})();