(function () {

  function StorageCtrl($scope, $state, Auth, Store, utils) {

    "ngInject";
    
    var ctrl = this;

    if (!Auth.isAuthorized('storage')) {
      return $state.go( Auth.defaultRoute );
    }
    
    ctrl.matrix = {
      show: function () {}
    };

    ctrl.state = $state;
    ctrl.statuses = [];
    ctrl.types = null;

    var subscriptions = [];

    $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });

    ctrl.$onInit = function () {
      utils.appLoaded();
      Store.data('statuses').update(ctrl.statuses);
        subscriptions.push(
            Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); })
        );
        subscriptions.push(
            Store.data('types').subscribe(function (types) { onTypesChange(types); })
        );
    };

    ctrl.$onDestroy = function () {
        _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.showNav = function (title){
      var found = false;
      Store.data('Mode').subscribe(function (data){
        if(data && data.mode_sub){
          found =  Auth.contains(data.mode_sub,title);
        }
      });
      return found;
    }
    
    /*** private ****/
    function onStatusesChange(statuses) {
      ctrl.statuses = statuses;
    }
    function onTypesChange(types) {
        ctrl.types = types;
    }

      ctrl.addSupplier = function () {
          Store.event('storage:create:supplier').emit();
      };

      ctrl.addStorageItem = function () {
        Store.event('storage:create:storage-item').emit();
      };
    
  }

  angular.module('printty')

    .component('storageComponent', {
      controller: StorageCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/storage.html'
    });

})();