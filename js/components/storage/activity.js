(function () {

  function StorageActivityCtrl(Storage, utils, Auth, Store, $mdDialog, Supplier, StorageActivityContent, Factory) {

    "ngInject";
    
    var ctrl = this;

    ctrl.products = [];
    
    var pagination = {
      init: true,
      perPage: 40,
      current: 0,
      type_id: null,
      
      length: 0,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;
      var allSelected = false;

    var subscription;

      ctrl.storageContent = [];

    ctrl.$onInit = function () {
      utils.appLoaded();

        Store.data('types').update([]);
        getFactories();
        getStatuses();

        getSuppliers();

        getStorageContent();

      subscription = Store.event('sidebarActiveStatus')
        .subscribe(function (status) {
          onStatusChange(status)
        });
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    pagination.reload = function () {

      this.init = true;
      this.end = false;
      this.busy = false;
      this.current = 0;
      this.length = 0;

      ctrl.products = [];

      ctrl.infiniteProducts.setDefault();
      ctrl.infiniteProducts.getItemAtIndex(1);
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 1000 );
    };
    
    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };
    
    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };
    
    ctrl.infiniteProducts = {
      numLoaded_: 0,
      lastDate: null,

      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_();
          return null;
        }

        return ctrl.products[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      setDefault: function () {
        this.numLoaded_ = 0;
        this.lastDate = null;
      },
      fetchMoreItems_: function() {
        if(!ctrl.factory) return;
        if (pagination.end || pagination.busy) return;

        pagination.busy = true;

        if (pagination.init) {
          utils.listLoading.show(!pagination.searching);
        } else {
          utils.moreItemsLoad.show();
        }

        pagination.current++;

        Storage.activity(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            conditions_keywords: pagination.searchQuery,
            product_type_id: pagination.type_id,
            factory_id: ctrl.factory,
          }
        ).then(
          function (data) {
              allSelected = false;
            pagination.init = false;
            pagination.searching = false;
            
            pagination.length += data.productsLength;
            
            // if the first date in received data is the last date of previous data => remove date row
            if ( data.products.length && (ctrl.infiniteProducts.lastDate === data.products[0].date) ) {
              data.products.shift();
            }
            
            ctrl.infiniteProducts.numLoaded_ += data.products.length;
            
            ctrl.infiniteProducts.lastDate = data.lastDate;
            
            ctrl.products = ctrl.products.concat(data.products);

            pagination.searchFailed = !pagination.length;
            pagination.end = pagination.length >= data.total_count;
            pagination.busy = false;

            utils.listLoading.hide();
            utils.moreItemsLoad.hide();
          }, function() {
                utils.listLoading.hide();
                utils.moreItemsLoad.hide();
            }
        );

      }
    };
    
    ctrl.onStorageUpdate = function () {
      pagination.reload();
    };

      ctrl.onChange = function (){
          pagination.reload();
      }

    /*** private ****/
    function getStatuses() {
      Storage.typesAll().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.ProductType;
        });

        Store.data('statuses').update(statuses);
      });
    }

      function getFactories() {
          Factory.fetchFactoryUser().then(function(data){
              ctrl.factories = data.factories;
              ctrl.factory = data.factorySelected;
          });
      }

      function getSuppliers() {
          Supplier.fetchSuppliers({}).then(function (data) {
              Store.data('Suppliers').update(data.suppliers);
          });
      }

      function getStorageContent() {
          StorageActivityContent.fetchContents({}).then(function (data) {
              ctrl.storageContent = data;
          });
      }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }

      function getSelected() {

          var selectedItems = [];

          _.forEach(ctrl.products, function (product) {

              if (product.selected) {
                  selectedItems.push(product);
              }

          });

          return selectedItems;

      }

      ctrl.selectAll = function () {

          _.forEach(ctrl.products, function (product) {
              if(product.mode !== 'date') product.selected = !allSelected;
          });

          allSelected = !allSelected;

      };

    ctrl.deleteActivity = function(ev){
        var selected;

        selected = getSelected();

        if (_.isArray(selected) && !selected.length) {
            return;
        }

        var confirm = $mdDialog.confirm({
            template: utils.deleteTemplate('アクティビティを削除しますか？'),
            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                $scope.cancelDelete = function () {
                    $mdDialog.cancel();
                };
                $scope.confirmDelete = function () {
                    $mdDialog.hide();
                };
            }],
            clickOutsideToClose:true,
        }).targetEvent(ev);

        $mdDialog.show(confirm).then(function () {
            var params = {
                activities : selected,
                factory_id: ctrl.factory,
            };

            utils.listLoading.show(true);

            Storage.deleteActivity(params).then(function() {

                pagination.reload();

                utils.listLoading.hide();
            }, function () {
                utils.listLoading.hide();
            });
        });
    }
    
  }

  angular.module('printty')

    .component('storageActivity', {
      controller: StorageActivityCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/activity.html'
    });

})();