(function() {

    function ActivityDetailsCtrl($scope, $mdSidenav, $mdDialog, Storage, Store, utils, $timeout) {

        "ngInject";

        var ctrl = this;

        ctrl.activities = [];

        ctrl.isAdd = true;
        ctrl.showMode = true;
        ctrl.empty = false;

        ctrl.pagination = {
            current: 0,
            perPage: 50,
            empty: false,
            disabled: true
        };

        ctrl.loadingType = 'full';

        var sidenav = {
            open: function () {
                $mdSidenav('activity-details').open();
            },
            close: function () {
                $mdSidenav('activity-details').close();
            }
        };
        setDefault();

        ctrl.$onInit = function() {
            Store.data('FactorySelect').subscribe(function (data) {
                ctrl.factory_id = data;
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            $mdSidenav('activity-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd) {
                fetchActivities();
            }

            sidenav.open();
        }

        ctrl.loadMoreItems = function () {
            fetchActivities();
        };

        function fetchActivities() {
            ctrl.loadingType = 'partial';
            ctrl.pagination.disabled = true;

            Storage.fetchActivities({
                paging_size: ctrl.pagination.perPage,
                paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
                factory_id: ctrl.factory_id,
            })
                .then(function (data) {
                    ctrl.pagination.current++;
                    ctrl.activities = ctrl.activities.concat(data.activities);
                    ctrl.pagination.empty = !ctrl.activities.length;
                    $timeout(function () {
                        ctrl.pagination.disabled = !data.activities.length || (ctrl.activities.length >= data.total_count);
                    });

                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });
        }


        /*** helpers ***/
        function setDefault() {
            ctrl.isAdd = true;
            ctrl.activities = [];
            ctrl.pagination.disabled = true;
            ctrl.pagination.current = 0;
            ctrl.showMode = true;

            ctrl.loadingType = 'stopped';
        }
    }

    angular.module('printty')
        .component('activityDetails', {
            controller: ActivityDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/activity-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

})();
