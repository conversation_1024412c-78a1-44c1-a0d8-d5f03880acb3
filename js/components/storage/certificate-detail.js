(function() {

    function CertificateDetailsCtrl($scope, $mdSidenav, $mdDialog, Storage, Store, utils, $filter, $timeout) {

        "ngInject";

        var ctrl = this;

        ctrl.stamp = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';
        ctrl.uploadFile = null;

        var certificateForm,
            certificateClone;
        var subscriptions = [];

        var sidenav = {
            open: function () {
                $mdSidenav('certificate-detail').open();
            },
            close: function () {
                $mdSidenav('certificate-detail').close();
            }
        };

        // fetch types and colors
        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('ColorSizeSide').subscribe(function (data) {
                    ctrl.details = data;
                }),
                Store.data('FactorySelect').subscribe(function (data) {
                    ctrl.factory_id = data;
                })
            );

            $('#imgInputUpdate').on('change', function (e) {
                ctrl.uploadCertificateDetailImage(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            certificateForm = $scope.certificateFrameForm;

            $mdSidenav('certificate-detail').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function(){
            if (ctrl.showMode) {
                certificateClone = _.cloneDeep(ctrl.stamp);
                ctrl.showMode = false;
            } else {
                ctrl.stamp = certificateClone;
                setFormPristine();
                ctrl.showMode = true;
            }
        };

        ctrl.update = function (){
            certificateForm.$setSubmitted();

            if( certificateForm.$invalid){
                return;
            }

            ctrl.loadingType = 'partial';

            Storage.updateCertificate({
                data: ctrl.stamp.CertificateStamp,
                image: ctrl.image_file
            }).then(function () {
                ctrl.onCreate();
                ctrl.close();
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        };


        ctrl.save = function () {
            certificateForm.$setSubmitted();

            if (certificateForm.$invalid) {
                return;
            }

            ctrl.loadingType = 'partial';

            Storage.createCertificate({
                title: ctrl.stamp.CertificateStamp.title,
                quantity: ctrl.stamp.CertificateStamp.quantity,
                image: ctrl.image_file,
                factory_id: ctrl.factory_id,
            }).then(function () {
                ctrl.onUpdate();
                ctrl.close();
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        };

        ctrl.uploadCertificateDetailImage = function (file) {
            ctrl.image_file = null;
            if (!file) {
                $('#imgInputUpdate').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'png' && fileType !== 'jpg') {
                alert('Image.UnexpectedFormat');
                return;
            }

            ctrl.image_file = file;

            $('#imageNameUpdate').text(file.name);

        };

        ctrl.delete = function (ev) {
// debugger
            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('証紙"タイトル"を削除します。よろしいですか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function() {

                ctrl.loadingType = 'partial';
                Storage.deleteCertificate(ctrl.stamp.CertificateStamp.id).then(
                    function () {
                        // call onDelete event
                        ctrl.onDelete({
                            stampId: ctrl.stamp.CertificateStamp.id
                        });
                        // close sidenav
                        ctrl.close();

                    }, function () {
                        ctrl.loadingType = 'stopped';
                    }
                );

            });

        };

        ctrl.close = function () {
            sidenav.close();
        };

        ctrl.fetchSidesOfColor = function (color_id) {
            fetchSides(color_id);
        };

        /******** private **********/
        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.certificateId) {
                fetchDetail();
            }

            sidenav.open();
        }


        function fetchDetail() {
            ctrl.loadingType = 'full';
// debugger
            Storage.detailsCertificate(ctrl.active.certificateId)
                .then(function (data) {
                    ctrl.stamp = data;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        /*** helpers ***/
        function setDefault() {
            ctrl.isAdd = true;
            ctrl.stamp = null;

            originalPrice = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (certificateForm) {
                certificateForm.$setPristine();
                certificateForm.$setUntouched();
            }
        }

    }

    angular.module('printty')
        .component('certificateDetail', {
            controller: CertificateDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/certificate-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

})();
