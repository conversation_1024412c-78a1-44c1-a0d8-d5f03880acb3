(function() {

  function storageProductHistoryCtrl($element, Storage) {

    "ngInject";

    $element.addClass('order-history-wrapper hide');
    
    var ctrl = this;
    
    var divider = '<md-divider class="hide"></md-divider>';
    
    if (ctrl.divider) {
      divider = angular.element(divider).insertBefore($element);
    }
    
    var pagination = {
      current: 1,
      perPage: 20,
      
      busy: true,
      end: false
    };

    ctrl.pagination = pagination;
    
    ctrl.activity = [];

    pagination.changePage = function (update) {

      pagination.busy = true;

      Storage.productActivity(
        {
          product_id: ctrl.product.Product.id,
          product_color_id: ctrl.product.ProductColor.id,
          product_size_id: ctrl.product.ProductSize.id,
          paging_size: pagination.perPage,
          paging_offset: (pagination.current - 1) * pagination.perPage,
          factory_id: ctrl.product.Factory.id,
        }).then(
        function (data) {
          
          if (!data) {
            pagination.end = true;
            return;
          }
          
          if (update) {
            ctrl.activity = data;
          } else {
            ctrl.activity = ctrl.activity.concat(data);
          }

          if (ctrl.activity.length) {
            $element.removeClass('hide');
            if (ctrl.divider) divider.removeClass('hide');
          } else {
            $element.addClass('hide');
            if (ctrl.divider) divider.addClass('hide');
          }
          
          pagination.busy = false;
          
        }
      );

    };

    pagination.init = function (update) {
      this.current = 1;
      
      // make update look seamless
      if (!update) {
        ctrl.activity = [];
        
        $element.addClass('hide');
        if (ctrl.divider) divider.addClass('hide');
      }
      
      this.end = false;
      
      pagination.changePage(update);
    };

    pagination.loadMore = function () {
      pagination.current++;
      pagination.changePage();
    };
    
    ctrl.$onChanges = function () {
      
      if (ctrl.product) {
        pagination.init();
      } else { // set defaults
        pagination.end = true;
        pagination.busy = true;
        ctrl.activity = [];
        
        $element.addClass('hide');
        if (ctrl.divider) divider.addClass('hide');
      }
      
    };
    
    ctrl.update = function () {
      pagination.init(true);
    };
    
  }

  angular.module('printty')
    .component('storageProductHistory', {
      bindings: {
        product: '<',
        divider: '<',
        update: '='
      },
      controller: storageProductHistoryCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/product-history.html'
    });

})();