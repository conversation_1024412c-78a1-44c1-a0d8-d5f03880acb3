(function() {

  function StorageCurrentDetailCtrl($mdSidenav, $mdToast, Storage, $timeout, $scope, Store) {

    "ngInject";
    
    var ctrl = this,
        originalProduct,
        productClone,
        sidenav = {
          open: function () {
              ctrl.checkSaftyStock = false;
              ctrl.checkVendorOrderPoint = false;
              ctrl.checkAdjustVendorOrder = false;
              ctrl.checkPickingQuantity = false;
              ctrl.checkColorSize = false;
            $mdSidenav('storage-current-detail').open();
          },
          close: function () {
            $mdSidenav('storage-current-detail').close();
          }
        };

      ctrl.suppliers = [];
      ctrl.storageContent = [];
      ctrl.checkSaftyStock = false;
      ctrl.checkVendorOrderPoint = false;
      ctrl.checkAdjustVendorOrder = false;
      ctrl.checkPickingQuantity = false;
      ctrl.showMode = true;
      ctrl.factory = null;

      ctrl.STOCK_STATUS_IN_STOCK = 1;
      ctrl.STOCK_STATUS_WARNING = 2;
      ctrl.STOCK_STATUS_OUT_STOCK = 3;
      ctrl.STOCK_STATUS_OUT_BALANCE = 4;
      ctrl.STOCK_STATUS_OUT_BALANCE_AND_WARNING = 5;
      ctrl.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK = 6;

    setDefault();

    ctrl.open = function (product) {
      
      setDefault();

      originalProduct = product;
      
      Storage.details({
        product_id: product.Product.id,
        product_size_id: product.ProductSize.id,
        product_color_id: product.ProductColor.id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        ctrl.product = data;
        
        ctrl.sides = _.flatMap(ctrl.product.sides, function (side) {
          return side.ProductColorSide;
        });

        ctrl.loadingType = 'stopped';
        
      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
      sidenav.open();
    };
    
    ctrl.close = sidenav.close;

    ctrl.$onInit = function () {
        Store.data('Suppliers').subscribe(function (data) {
            ctrl.suppliers = data;
        })
        Store.data('FactorySelect').subscribe(function (data) {
            ctrl.factory = data;
        })
    };
    
    ctrl.$postLink = function () {
      
      // set default after closing (in order to destroy all bindings)
      
      $mdSidenav('storage-current-detail').onClose(function () {
        $timeout(function () {
          setDefault();
        }, 400)
      });
      
    };
    ctrl.changeProductPicking = function (product_id){
        ctrl.product.Storage.size_color = null;
        if(product_id){
            ctrl.loadingType = 'partial';
            Storage.fetchSizeColorPicking({
                product_id: product_id,
            }).then(function (data) {
                ctrl.product.list_size_color = data.list_size_color;
                ctrl.loadingType = 'stopped';
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        }else{
            ctrl.product.list_size_color = [];
            ctrl.checkColorSize = false;
        }
    }
    
    // add/remove from storage
    ctrl.updateHistory = angular.noop; // will be updated by inner component

      ctrl.$onDestroy = function () {
          Store.data('Suppliers').update([]);
      };
    
    ctrl.addToStorage = function (content_id) {

      $scope.addRemoveStorageForm.$setSubmitted();
      if ($scope.addRemoveStorageForm.$invalid) return;
      
      var num = _.toInteger(ctrl.movement);
      if (num <= 0) return;

      ctrl.loadingType = 'partial';
      
      Storage.productQuantity.add({
        Product: {
          id: ctrl.product.Product.id
        },
        ProductSize: {
          id: ctrl.product.ProductSize.id
        },
        ProductColor: {
          id: ctrl.product.ProductColor.id
        },
        Storage: {
          quantity: num
        },
        content_id : content_id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        $mdToast.show(
          $mdToast.simple()
            .textContent(num + 'を入庫しました')
            .position('top left')
            .toastClass('printty-toast')
            .hideDelay(2000)
        );

        if(data.update) {
            ctrl.product.Storage.quantity += ctrl.movement;
            checkStatus();
            if (ctrl.updateOriginal) updateOriginal();
        }

        ctrl.movement = null;
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();

        if(data.update) {
            ctrl.updateHistory();
        }
        ctrl.loadingType = 'stopped';
        
      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
    };

    function checkStatus(){
        if(ctrl.product.Storage.total_order == 0){
            // in stock
            ctrl.product.Storage.status = ctrl.STOCK_STATUS_IN_STOCK;
        } else {
            // get more 10% of total order
            var max_range = Math.ceil(ctrl.product.Storage.total_order + (ctrl.product.Storage.total_order * 0.1));

            if(ctrl.product.Storage.quantity < ctrl.product.Storage.total_order) {
                //out stock
                ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_STOCK;
            } else if (ctrl.product.Storage.quantity >= ctrl.product.Storage.total_order && ctrl.product.Storage.quantity <= max_range) {
                // warning
                ctrl.product.Storage.status = ctrl.STOCK_STATUS_WARNING;
            } else {
                // in stock
                ctrl.product.Storage.status = ctrl.STOCK_STATUS_IN_STOCK;
            }

            // out balance
            if (ctrl.product.Storage.min_quantity != null && ctrl.product.Storage.quantity < ctrl.product.Storage.min_quantity) {
                switch(ctrl.product.Storage.status) {
                    case ctrl.STOCK_STATUS_IN_STOCK:
                        ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE;
                        break;
                    case ctrl.STOCK_STATUS_WARNING:
                        ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE_AND_WARNING;
                        break;
                    case ctrl.STOCK_STATUS_OUT_STOCK:
                        ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK;
                        break;
                    default:
                        ctrl.product.Storage.status = ctrl.STOCK_STATUS_OUT_BALANCE;
                }
            }
        }
    }

    ctrl.removeFromStorage = function (content_id) {

      $scope.addRemoveStorageForm.$setSubmitted();
      if ($scope.addRemoveStorageForm.$invalid) return;
      
      var num = _.toInteger(ctrl.movement);
      if (num <= 0) return;

      ctrl.loadingType = 'partial';
      
      Storage.productQuantity.remove({
        Product: {
          id: ctrl.product.Product.id
        },
        ProductSize: {
          id: ctrl.product.ProductSize.id
        },
        ProductColor: {
          id: ctrl.product.ProductColor.id
        },
        Storage: {
          quantity: num
        },
        content_id : content_id,
        factory_id: ctrl.factory
      }).then(function (data) {
        
        $mdToast.show(
          $mdToast.simple()
            .textContent(num + 'を出庫しました')
            .position('top left')
            .toastClass('printty-toast')
            .hideDelay(2000)
        );

        if(data.update) {
            ctrl.product.Storage.quantity -= ctrl.movement;
            checkStatus();
            if (ctrl.updateOriginal) updateOriginal();
        }

        ctrl.movement = null;
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();

        if(data.update) {
            ctrl.updateHistory();
        }

        ctrl.loadingType = 'stopped';

      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
    };
    
    /*** helpers ***/
    function setDefault() {
      ctrl.product = null;
      ctrl.sides = null;
      ctrl.showMode = true;
      ctrl.movement = null;
      
      ctrl.loadingType = 'full';
      
      if ($scope.addRemoveStorageForm) {
        $scope.addRemoveStorageForm.$setPristine();
        $scope.addRemoveStorageForm.$setUntouched();
      }

        if ($scope.updateStorageForm) {
            $scope.updateStorageForm.$setPristine();
            $scope.updateStorageForm.$setUntouched();
        }
    }
    
    function updateOriginal() {
      originalProduct.Storage.quantity = ctrl.product.Storage.quantity;
      originalProduct.Storage.unit_price = ctrl.product.Storage.unit_price;
      originalProduct.Storage.design_fee = ctrl.product.Storage.design_fee;
      originalProduct.Storage.total_order = ctrl.product.Storage.total_order;
      originalProduct.Storage.status = ctrl.product.Storage.status;
      originalProduct.Storage.min_quantity = ctrl.product.Storage.min_quantity;
      originalProduct.Storage.picking_mode = ctrl.product.Storage.picking_mode;
      originalProduct.Storage.safty_stock = ctrl.product.Storage.safty_stock;
      originalProduct.Storage.vendor_order_point = ctrl.product.Storage.vendor_order_point;
      originalProduct.Storage.adjust_vendor_order = ctrl.product.Storage.adjust_vendor_order;

      var supplier = _.find(ctrl.suppliers, function (supplier) {
          return supplier.Supplier.id == ctrl.product.Storage.supplier_id;
      });

      if(supplier) {
          originalProduct.Supplier.title = supplier.Supplier.title;
      }
    }

      ctrl.edit = function () {

          if (ctrl.showMode) {
              productClone = _.cloneDeep(ctrl.product);
              ctrl.showMode = false;
          } else {
              ctrl.product = productClone;
              ctrl.showMode = true;
          }

      };

      ctrl.update = function(){
          $scope.updateStorageForm.$setSubmitted();
          ctrl.checkSaftyStock = false;
          ctrl.checkVendorOrderPoint = false;
          ctrl.checkAdjustVendorOrder = false;
          ctrl.checkPickingQuantity = false;
          ctrl.error = false;
          ctrl.checkColorSize = false;

          if (typeof $scope.updateStorageForm.safty_stock.$viewValue == "undefined" || $scope.updateStorageForm.safty_stock.$viewValue === '' || isNaN($scope.updateStorageForm.safty_stock.$viewValue) || $scope.updateStorageForm.safty_stock.$viewValue == null || $scope.updateStorageForm.safty_stock.$viewValue <= 0) {
              ctrl.checkSaftyStock = true;
              ctrl.error = true;
          }

          if (typeof $scope.updateStorageForm.vendor_order_point.$viewValue == "undefined" || $scope.updateStorageForm.vendor_order_point.$viewValue === '' || isNaN($scope.updateStorageForm.vendor_order_point.$viewValue) || $scope.updateStorageForm.vendor_order_point.$viewValue == null || $scope.updateStorageForm.vendor_order_point.$viewValue <= 0) {
              ctrl.checkVendorOrderPoint = true;
              ctrl.error = true;
          }

          if (typeof $scope.updateStorageForm.adjust_vendor_order.$viewValue == "undefined" || $scope.updateStorageForm.adjust_vendor_order.$viewValue === '' || isNaN($scope.updateStorageForm.adjust_vendor_order.$viewValue) || $scope.updateStorageForm.adjust_vendor_order.$viewValue == null || $scope.updateStorageForm.adjust_vendor_order.$viewValue <= 0) {
              ctrl.checkAdjustVendorOrder = true;
              ctrl.error = true;
          }
          if(!ctrl.product.Storage || !ctrl.product.Storage.picking_unit_quantity || ctrl.product.Storage.picking_unit_quantity < 1){
              ctrl.checkPickingQuantity = true;
              ctrl.error = true;
          }
          if (ctrl.product.list_picking && ctrl.product.list_picking.length && ctrl.product.Storage.product_picking && !ctrl.product.Storage.size_color) {
              ctrl.checkColorSize = true;
              ctrl.error = true;
          }

          if (!ctrl.error) {
              var unit_price = _.toInteger(ctrl.product.Storage.unit_price);
              var design_fee = _.toInteger(ctrl.product.Storage.design_fee);
              var total_order = _.toInteger(ctrl.product.Storage.total_order);
              var min_quantity = _.toInteger(ctrl.product.Storage.min_quantity);
              var picking_mode = _.toInteger(ctrl.product.Storage.picking_mode);
              var product_picking = ctrl.product.Storage.product_picking;
              var safty_stock = _.toInteger(ctrl.product.Storage.safty_stock);
              var vendor_order_point = _.toInteger(ctrl.product.Storage.vendor_order_point);
              var adjust_vendor_order = _.toInteger(ctrl.product.Storage.adjust_vendor_order);
              if (ctrl.product.list_picking && ctrl.product.list_picking.length && ctrl.product.Storage.product_picking) {
                  var picking = ctrl.product.Storage.size_color.split('/');
                  var product_color_picking = picking[0];
                  var product_size_picking = picking[1];
              }

              if (unit_price < 0 || design_fee < 0 || total_order < 0) return;

              ctrl.loadingType = 'partial';

              Storage.storage({
                  Product: {
                      id: ctrl.product.Product.id
                  },
                  ProductSize: {
                      id: ctrl.product.ProductSize.id
                  },
                  ProductColor: {
                      id: ctrl.product.ProductColor.id
                  },
                  Storage: {
                      unit_price: unit_price,
                      design_fee: design_fee,
                      total_order: total_order,
                      min_quantity: min_quantity,
                      supplier_id: ctrl.product.Storage.supplier_id,
                      picking_mode: picking_mode,
                      product_picking: product_picking,
                      safty_stock: safty_stock,
                      vendor_order_point: vendor_order_point,
                      adjust_vendor_order: adjust_vendor_order,
                      is_stock_alert_notifice: ctrl.product.Storage.is_stock_alert_notifice,
                      picking_unit_quantity: ctrl.product.Storage.picking_unit_quantity,
                      product_color_picking : product_color_picking ? product_color_picking : null,
                      product_size_picking : product_size_picking ? product_size_picking : null,
                  },
                  factory_id: ctrl.factory
              }).then(function () {
                  ctrl.product.Storage.quantity -= ctrl.movement;
                  ctrl.product.Storage.min_quantity = _.toInteger(ctrl.product.Storage.min_quantity);
                  checkStatus();
                  if (ctrl.updateOriginal) updateOriginal();

                  $scope.updateStorageForm.$setPristine();
                  $scope.updateStorageForm.$setUntouched();

                  ctrl.showMode = true;

                  ctrl.loadingType = 'stopped';

              }, function () {
                  ctrl.loadingType = 'stopped';
              });
          }
      }
    
  }

  angular.module('printty')
    .component('storageCurrentDetail', {
      bindings: {
        open: '=',
        updateOriginal: '<',
        storageContent: '<',
        factory: '='
      },
      controller: StorageCurrentDetailCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/current-detail.html'
    });
  
})();