(function() {

    function StoragePickingVakuumCtrl($mdSidenav, $mdToast, Storage, $timeout, $scope, Store, $mdDialog) {

        "ngInject";

        var ctrl = this,
            sidenav = {
                open: function () {
                    $mdSidenav('storage-picking-vakuum').open();
                },
                close: function () {
                    $mdSidenav('storage-picking-vakuum').close();
                }
            };

        ctrl.taskGroup = null;
        ctrl.taskGroups = null;
        ctrl.type = null;
        var subscriptions = [];
        ctrl.page = 1;
        ctrl.pages = 1;

        ctrl.allSelected = false;

        setDefault();

        function getTaskGroup(date){
            if(date) {
                ctrl.loadingType = 'full';
                date = moment(date).format('YYYY-MM-DD');

                var params = {
                    date: date,
                    page: ctrl.page
                };

                Storage.picking.getPickTaskGroup(params).then(function (data) {
                    if (data.taskGroups.length === 0) {
                        ctrl.taskGroup = null;
                        ctrl.taskGroups = null;
                    } else {
                        ctrl.taskGroups = data.taskGroups;
                        ctrl.taskGroup = data.taskGroups[ctrl.page - 1];
                        ctrl.pages = data.pages;
                    }
                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });
            }
        }

        ctrl.open = function (date) {
            setDefault();
            ctrl.date = date;
            getTaskGroup(ctrl.date);

            sidenav.open();
        };

        ctrl.close = sidenav.close;

        ctrl.$onInit = function () {
            ctrl.taskGroup = null;
            subscriptions.push(

                Store.data('Picking:Date')
                    .subscribe(function (date) {
                        ctrl.pages = 1;
                        ctrl.page = 1;
                        getTaskGroup(date);
                    })

            );
        };

        ctrl.showLargeImage = function (ev, item) {
            var confirm = $mdDialog.show({
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };

                    $scope.image_large = item.image_url;
                }],
                templateUrl: 'views/kenpin/kenpin-dialog.html',
                clickOutsideToClose:true,
            });
        };

        ctrl.$postLink = function () {

            // set default after closing (in order to destroy all bindings)

            $mdSidenav('storage-picking-vakuum').onClose(function () {
                $timeout(function () {
                    setDefault();
                }, 400)
            });

        };

        ctrl.DoCtrlPagingAct = function(page){
            ctrl.taskGroup = ctrl.taskGroups[page - 1];
        };

        /*** helpers ***/
        function setDefault() {
            ctrl.loadingType = 'full';
        }

    }

    angular.module('printty')
        .component('storagePickingVakuum', {
            require: {
                storageCtrl: '^^storageComponent'
            },
            controller: StoragePickingVakuumCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/picking-vakuum.html'
        });

})();