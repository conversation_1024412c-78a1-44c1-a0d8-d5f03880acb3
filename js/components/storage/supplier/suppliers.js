(function () {

    function StorageSupplierCtrl(Store, utils, Supplier) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.suppliers = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.supplierDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('storage:create:supplier').subscribe(function () { ctrl.viewCreate(); })
            );

            Store.data('types').update([]);
            Store.data('statuses').update([]);

            getSuppliers();

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.suppliers = [];

            getSuppliers();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (supplier) {
            ctrl.supplierDetails.type = 'details';
            ctrl.supplierDetails.isShown = true;
            ctrl.supplierDetails.active = {
                supplierId: supplier.Supplier.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.supplierDetails.type = 'add';
            ctrl.supplierDetails.isShown = true;
            ctrl.supplierDetails.active = {};
        };

        ctrl.onSupplierClose = function () {
            ctrl.supplierDetails.isShown = false;
        };

        ctrl.onSupplierCreate = function () {
            ctrl.reload();
        };

        ctrl.onSupplierUpdate = function (supplier) {
            var originalSupplier = _.find(ctrl.suppliers, function (supplierItem) {
                return supplierItem.Supplier.id == supplier.Supplier.id;
            });

            _.extend(originalSupplier.Supplier, supplier.Supplier);
        };

        ctrl.onSupplierDelete = function (supplierId) {
            _.remove(ctrl.suppliers, function(supplier) {
                return supplier.Supplier.id == supplierId;
            });
        };

        /****** private ******/
        function getSuppliers() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Supplier.fetchSuppliers({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.suppliers = data.suppliers;
                    ctrl.pagination.empty = !ctrl.suppliers.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

    }

    angular.module('printty')

        .component('storageSupplier', {
            require: {
                parentCtrl: '^^storageComponent'
            },
            controller: StorageSupplierCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/supplier/suppliers.html'
        });

})();