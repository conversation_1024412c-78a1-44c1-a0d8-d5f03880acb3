(function () {

    angular.module('printty')
        .component('supplierDetails', {
            controller: SupplierDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/supplier/supplier-detail.html',
            bindings: {
                isShown: '<',
                type:'<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                active: '='
            }
        });

    function SupplierDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, Supplier, $window) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.supplier = null;

        vm.isAdd = true;
        vm.showMode = true;
        vm.checkDeliveryTime = false;
        vm.deliveryTimeLessOne = false;

        vm.loadingType = 'full';

        var supplierForm,
            supplierClone;

        var sidenav = {
            open: function () {
                vm.checkDeliveryTime = false;
                vm.deliveryTimeLessOne = false;
                $mdSidenav('supplier-details').open();
            },
            close: function () {
                $mdSidenav('supplier-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            supplierForm = $scope.supplierForm;

            $mdSidenav('supplier-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                supplierClone = _.cloneDeep(vm.supplier);
                vm.showMode = false;
            } else {
                vm.supplier = supplierClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            supplierForm.$setSubmitted();
            if(typeof supplierForm.delivery_time.$viewValue == "undefined" || supplierForm.delivery_time.$viewValue === '' || isNaN(supplierForm.delivery_time.$viewValue) || supplierForm.delivery_time.$viewValue == null){
                vm.checkDeliveryTime = true;
            }

            if(supplierForm.delivery_time.$viewValue <= 0){
                vm.deliveryTimeLessOne = true;
            }

            if(!vm.checkDeliveryTime && !vm.deliveryTimeLessOne) {
                if ( _.isEqual(vm.supplier, supplierClone) ) {
                    vm.showMode = true;
                    setFormPristine();
                    return;
                }

                vm.loadingType = 'partial';

                Supplier.updateSupplier(vm.supplier)
                    .then(function () {
                        updateOriginal();

                        setFormPristine();
                        vm.showMode = true;

                        vm.loadingType = 'stopped';
                    }, function () {
                        vm.loadingType = 'stopped';
                    });
            }

        };

        vm.save = function () {
            supplierForm.$setSubmitted();

            if(typeof supplierForm.delivery_time.$viewValue == "undefined" || supplierForm.delivery_time.$viewValue === '' || isNaN(supplierForm.delivery_time.$viewValue) || supplierForm.delivery_time.$viewValue == null){
                vm.checkDeliveryTime = true;
            }

            if(supplierForm.delivery_time.$viewValue <= 0){
                vm.deliveryTimeLessOne = true;
            }

            if(!vm.checkDeliveryTime && !vm.deliveryTimeLessOne) {
                vm.loadingType = 'partial';

                Supplier.createSupplier(vm.supplier)
                    .then(function () {
                        vm.onCreate();
                        vm.close();
                    })
                    .catch(function () {
                        vm.loadingType = 'stopped';
                    });
            }
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('発注先を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteSupplier);
        };

        vm.close = function () {
            sidenav.close();
        };

        vm.openLink = function(link) {
            $window.open(link, '_blank');
        };

        vm.openMail = function(mail) {
            $window.open("mailto:" + mail, '_self');
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.supplierId) {
                fetchSupplier();
            }

            sidenav.open();
        }

        function fetchSupplier() {
            vm.loadingType = 'full';

            var params = {
                supplier_id : vm.active.supplierId
            };

            Supplier.fetchSupplier(params)
                .then(function (data) {
                    vm.supplier = data;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var supplierData = _.cloneDeep(vm.supplier);

            vm.onUpdate({
                supplier: supplierData
            });
        }

        function deleteSupplier() {
            vm.loadingType = 'partial';

            Supplier.deleteSupplier(vm.supplier)
                .then(function () {
                    vm.onDelete({
                        supplierId: vm.supplier.Supplier.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.supplier = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (supplierForm) {
                supplierForm.$setPristine();
                supplierForm.$setUntouched();
            }
        }

    }

})();