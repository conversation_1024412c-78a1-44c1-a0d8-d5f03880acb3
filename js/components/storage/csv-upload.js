(function() {

  function CsvUploadCtrl($mdSidenav, Storage) {

    "ngInject";
    
    var ctrl = this;

    ctrl.products = [];

    var sidenav = {
      open: function () {
        $mdSidenav('csv-upload').open();
      },
      close: function () {
        $mdSidenav('csv-upload').close();
      }
    };

    ctrl.open = function (file) {
        ctrl.loadingType = 'full';

        var params = {
            csv : file
        };

        Storage.csv.prepare(params).then(function(data){
            ctrl.products = data;
            ctrl.loadingType = 'stopped';
            sidenav.open();
        },function () {
            ctrl.loadingType = 'stopped';
        });
    };

    ctrl.close = sidenav.close;

    ctrl.$onInit = function() {
        ctrl.products = [];
    }
    
  }

  angular.module('printty')
    .component('storageCsvUpload', {
      bindings: {
        open: '='
      },
      controller: CsvUploadCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/csv-upload.html'
    });

})();