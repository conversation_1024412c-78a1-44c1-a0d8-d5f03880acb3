(function() {

    function StoragePickingCtrl($mdSidenav, $mdToast, Storage, $timeout, $scope, Store, OrderItem, $mdDialog, utils) {

        "ngInject";

        var ctrl = this,
            sidenav = {
                open: function () {
                    $mdSidenav('storage-picking').open();
                },
                close: function () {
                    $mdSidenav('storage-picking').close();
                }
            },
            sidenavVakuum = {
                open: function () {
                    $mdSidenav('storage-picking-vakuum').open();
                },
                close: function () {
                    $mdSidenav('storage-picking-vakuum').close();
                }
            };

        ctrl.date = null;
        ctrl.type = null;
        ctrl.tasks = null;
        ctrl.empty = false;
        ctrl.body_procurement_failure = null;

        ctrl.allSelected = false;

        setDefault();

        ctrl.onDateChange = function () {
            ctrl.allSelected = false;

            ctrl.loadingType = 'full';

            getTaskInfo(ctrl.date);
            if(ctrl.type === "5") {
                Store.data('Picking:Date').update(ctrl.date);
            }
        };

        function getTaskInfo(date){
            var date = moment(date).format('YYYY-MM-DD');
            var params = {
                date : date,
                type : ctrl.type,
                factory_id: ctrl.factory
            };

            Storage.picking.getPicking(params).then(function(data) {
                if(data.length === 0) {
                    ctrl.empty = true;
                    ctrl.tasks = [];
                } else {
                    ctrl.empty = false;
                    ctrl.tasks = data;
                }
                ctrl.loadingType = 'stopped';
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        }

        ctrl.open = function (type) {

            setDefault();

            ctrl.type = type;
            ctrl.date = moment().toDate();

            getTaskInfo(ctrl.date);

            sidenav.open();
            if(type === "5") {
                Store.data('Picking:Date').update(ctrl.date);
                sidenavVakuum.open();
            }
        };

        ctrl.close = function(){
            sidenav.close();
            sidenavVakuum.close();
        };

        ctrl.$onInit = function () {
            ctrl.tasks = null;
            Store.data('FactorySelect').subscribe(function (data) {
                ctrl.factory = data;
            })
        };

        ctrl.$postLink = function () {

            // set default after closing (in order to destroy all bindings)

            $mdSidenav('storage-picking').onClose(function () {
                $timeout(function () {
                    setDefault();
                }, 400)
            });

        };

        ctrl.pick = function(){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            ctrl.loadingType = 'full';

            var params = {
                tasks : selected,
                type: ctrl.type,
            }

            Storage.picking.pick(params).then(function() {
                $mdToast.show(
                    $mdToast.simple()
                        .textContent('ピッキング成功')
                        .position('top left')
                        .toastClass('printty-toast')
                        .hideDelay(2000)
                );

                sidenav.close();
                sidenavVakuum.close();

                ctrl.onPicking();

                ctrl.loadingType = 'stopped';
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        };

        ctrl.notifyOutOfStock = function () {
            var selected;

            selected = getItemIdSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.contactAboutStorage(),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelInputReason = function () {
                        $mdDialog.hide();
                    };

                    $scope.confirmInputReason = function () {
                        angular.element('.global-loading').show();
                        var reason = $('#input-reason').val();
                        if(reason.length > 0) {
                            ctrl.reason = reason;

                        } else {
                            $('#input-error').html('error');
                        }
                        var params = {
                            items_id : selected,
                            reason: ctrl.reason,
                            factory_id: ctrl.factory
                        };

                        OrderItem.updateReasonForFail(params).then(function (data){
                            angular.element('.global-loading').hide();
                            ctrl.body_procurement_failure = data.OrderItem.body_procurement_failure;
                            $mdDialog.hide();
                        })
                            .catch(function () {
                                angular.element('.global-loading').hide();
                            });
                    };
                }],
                onComplete : function(scope, element){
                    var input_reason = ctrl.body_procurement_failure;
                    $('#input-reason').val(input_reason);
                },
                clickOutsideToClose: true
            });

            $mdDialog.show(confirm);
        };

        /*** helpers ***/
        function setDefault() {
            ctrl.allSelected = false;
            ctrl.date = null;
            ctrl.type = null;
            ctrl.empty = false;

            ctrl.loadingType = 'full';
        }

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.tasks, function (task) {

                if (task.selected) {
                    selectedItems.push(task);
                }

            });

            return selectedItems;

        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.tasks, function (task) {
                task.selected = ctrl.allSelected;
            });

        };

        function getItemIdSelected() {

            var selectedItems = [];

            _.forEach(ctrl.tasks, function (task) {

                if (task.selected) {
                    selectedItems.push(task.data.Task.order_item_id);
                }

            });

            return selectedItems;

        }

    }

    angular.module('printty')
        .component('storagePicking', {
            bindings: {
                open: '=',
                onStorageUpdate: '=',
                updateOriginal: '<',
                onPicking : '&'
            },
            require: {
                storageCtrl: '^^storageComponent'
            },
            controller: StoragePickingCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/picking.html'
        });

})();