(function () {

  function MatrixCtrl($scope, $element, $timeout) {

    "ngInject";
    
    var ctrl = this;

    ctrl.isShown = false;

    var $body = angular.element('body');

    // todo: redo animation using ng-animate
    var component = {
      show: function () {
        $element.addClass('shown');
        $body.addClass('not-scrollable-x');
        ctrl.isShown = true;
      },
      hide: function () {
        $element.removeClass('shown').addClass('hiding');
        $timeout(function () {
          $element.removeClass('hiding');
          $body.removeClass('not-scrollable-x');
          ctrl.isShown = false;
        }, 300)
      }
    };

    ctrl.open = function () {
      component.show();
    };
    ctrl.close = function () {
      component.hide();
    };
    
    ctrl.matrix = [
      {
        name: 'グレー',
        sizes: [10, 21, 6, 44, 28, 0]
      },
      {
        name: 'Green',
        sizes: [15, 11, 0, 12, 1, 12]
      },
      {
        name: '<PERSON>',
        sizes: [32, 5, 0, 31, 13, 94]
      },
      {
        name: 'Yellow',
        sizes: [0, 61, 0, 66, 18, 105]
      },
      {
        name: 'Pink',
        sizes: [5, 44, 13, 28, 31, 67]
      },
      {
        name: 'Grey',
        sizes: [48, 39, 55, 1, 78, 26]
      },
      {
        name: 'グレー',
        sizes: [10, 21, 6, 44, 28, 0]
      },
      {
        name: 'Green',
        sizes: [15, 11, 0, 12, 1, 12]
      },
      {
        name: 'Blue',
        sizes: [32, 5, 0, 31, 13, 94]
      },
      {
        name: 'Yellow',
        sizes: [0, 61, 0, 66, 18, 105]
      },
      {
        name: 'Pink',
        sizes: [5, 44, 13, 28, 31, 67]
      },
      {
        name: 'Grey',
        sizes: [48, 39, 55, 1, 78, 26]
      },
      {
        name: 'グレー',
        sizes: [10, 21, 6, 44, 28, 0]
      },
      {
        name: 'Green',
        sizes: [15, 11, 0, 12, 1, 12]
      },
      {
        name: 'Blue',
        sizes: [32, 5, 0, 31, 13, 94]
      },
      {
        name: 'Yellow',
        sizes: [0, 61, 0, 66, 18, 105]
      },
      {
        name: 'Pink',
        sizes: [5, 44, 13, 28, 31, 67]
      },
      {
        name: 'Grey',
        sizes: [48, 39, 55, 1, 78, 26]
      }
    ];
    
    ctrl.selected = {
      left: 1,
      up: 1
    };

    $scope.vegetables = ['バック' ,'Poloシャッツ'];
    $scope.searchTerm;
    $scope.clearSearchTerm = function() {
      $scope.searchTerm = '';
    };
    $scope.selectedVegetables = ['バック'];
    
    // The md-select directive eats keydown events for some quick select
    // logic. Since we have a search input here, we don't need that logic.
    $element.find('.select-type input').on('keydown', function(ev) {
      ev.stopPropagation();
    });
    
  }
  
  
  angular.module('printty')
    .component('storageMatrix', {
      bindings: {
        open: '='
      },
      controller: MatrixCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/storage/matrix.html'
    });

})();