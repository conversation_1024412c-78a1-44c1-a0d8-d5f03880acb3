(function() {

    function CertificateDetailsCtrl($scope, $mdSidenav, $mdDialog, Storage, Store, utils, $filter, $timeout) {

        "ngInject";

        var ctrl = this;

        ctrl.stamp = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';
        ctrl.uploadFile = null;

        var certificateForm,
            certificateClone;
        var subscriptions = [];

        var sidenav = {
            open: function () {
                $mdSidenav('certificate-details').open();
            },
            close: function () {
                $mdSidenav('certificate-details').close();
            }
        };

        // fetch types and colors
        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('ColorSizeSide').subscribe(function (data) {
                    ctrl.details = data;
                }),
                Store.data('FactorySelect').subscribe(function (data) {
                    ctrl.factory_id = data;
                })
            );

            $('#imgInput').on('change', function (e) {
                ctrl.uploadCertificateImage(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            certificateForm = $scope.certificateFrameForm;

            $mdSidenav('certificate-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function(){
            if (ctrl.showMode) {
                certificateClone = _.cloneDeep(ctrl.stamp);
                ctrl.showMode = false;
            } else {
                ctrl.stamp = certificateClone;
                setFormPristine();
                ctrl.showMode = true;
            }
        };

        ctrl.update = function () {
            certificateForm.$setSubmitted();

            if (certificateForm.$invalid) {
                return;
            }

            // do nothing if printer didn't change
            if (_.isEqual(ctrl.stamp, certificateClone)) {
                ctrl.showMode = true;
                setFormPristine();
                // toggleShowMode();
                return;
            }

            ctrl.loadingType = 'partial';

        };

        ctrl.save = function () {
            certificateForm.$setSubmitted();

            if (certificateForm.$invalid) {
                return;
            }

            ctrl.loadingType = 'partial';

            Storage.createCertificate({
                title: ctrl.stamp.CertificateStamp.title,
                quantity: ctrl.stamp.CertificateStamp.quantity,
                comment: ctrl.stamp.CertificateStamp.comment,
                factory_id: ctrl.factory_id,
                image: ctrl.image_file,
            }).then(function () {
                ctrl.onCreate();
                ctrl.close();
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        };

        ctrl.uploadCertificateImage = function (file) {
            ctrl.image_file = null;
            if (!file) {
                $('#imgInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'png' && fileType !== 'jpg') {
                alert('Image.UnexpectedFormat');
                return;
            }

            ctrl.image_file = file;

            $('#imageName').text(file.name);

        };

        ctrl.delete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('置場を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function() {

                ctrl.loadingType = 'partial';

            });

        };

        ctrl.close = function () {
            sidenav.close();
        };

        ctrl.fetchSidesOfColor = function (color_id) {
            fetchSides(color_id);
        };

        /******** private **********/
        function initComponent() {
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.stampId) {

            }

            sidenav.open();
        }

        /*** helpers ***/
        function setDefault() {
            ctrl.isAdd = true;
            ctrl.stamp = null;

            originalPrice = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (certificateForm) {
                certificateForm.$setPristine();
                certificateForm.$setUntouched();
            }
        }

    }

    angular.module('printty')
        .component('certificateDetails', {
            controller: CertificateDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/certificate-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

})();
