(function() {

    function StorageCertificateCtrl($mdSidenav, $mdToast, Storage, $timeout, $scope, Store, $mdDialog, utils) {

        "ngInject";

        var ctrl = this,
            sidenav = {
                open: function () {
                    $mdSidenav('storage-certificate').open();
                },
                close: function () {
                    $mdSidenav('storage-certificate').close();
                }
            };

        ctrl.empty = false;
        ctrl.factory_id = null;

        ctrl.stamps = [];
        ctrl.showMode = true;

        ctrl.certificateDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };
        ctrl.certificateDetail = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.activityDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.pagination = {
            current: 0,
            perPage: 50,
            empty: false,
            disabled: true
        };

        ctrl.quantityStamp = null;

        var loader = new utils.loadCounter(
            function (type) {
                if (!type) type = 'full';
                ctrl.loadingType = type;
            },
            function () {
                ctrl.loadingType = 'stopped';
            }
        );

        setDefault();

        ctrl.open = function () {
            setDefault();

            fetchCertificate();
            sidenav.open();
        };

        ctrl.close = function(){
            sidenav.close();
        };

        ctrl.reload = function() {
            loader.start('partial');

            ctrl.pagination.current = 0;
            ctrl.pagination.disabled = false;

            ctrl.stamps = [];

            fetchCertificate();
        };

        ctrl.$onInit = function () {
            Store.data('FactorySelect').subscribe(function (data) {
                ctrl.factory_id = data;
            })
        };

        ctrl.$postLink = function () {

            $mdSidenav('storage-certificate').onClose(function () {
                $timeout(function () {
                    setDefault();
                }, 400)
            });

        };

        ctrl.loadMoreItems = function () {
            fetchCertificate();
        };

        function fetchCertificate() {
            loader.start('partial');
            ctrl.pagination.disabled = true;

            Storage.fetchCertificate({
                paging_size: ctrl.pagination.perPage,
                paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
                factory_id: ctrl.factory_id,
            })
            .then(function (data) {
                ctrl.pagination.current++;
                ctrl.stamps = ctrl.stamps.concat(data.stamps);
                ctrl.pagination.empty = !ctrl.stamps.length;
                $timeout(function () {
                    ctrl.pagination.disabled = !data.stamps.length || (ctrl.stamps.length >= data.total_count);
                });
            })
            .finally(function () {
                loader.stop();
            });
        }

        ctrl.viewCertificateDetails = function (stamp) {
            ctrl.certificateDetails.type = 'details';
            ctrl.certificateDetails.isShown = true;
            ctrl.certificateDetails.active = {
                certificateId: stamp.CertificateStamp.id
            };
        };

        ctrl.viewCertificateCreate = function () {
            ctrl.certificateDetails.type = 'add';
            ctrl.certificateDetails.isShown = true;
            ctrl.certificateDetails.active = {};
        };

        ctrl.onCertificateClose = function () {
            ctrl.certificateDetails.isShown = false;
        };

        ctrl.viewDetail = function (stamp) {
            ctrl.certificateDetail.type = 'details';
            ctrl.certificateDetail.isShown = true;
            ctrl.certificateDetail.active = {
                certificateId: stamp.CertificateStamp.id
            };
        };

        ctrl.onCertificateDetailClose = function () {
            ctrl.certificateDetail.isShown = false;
        };

        ctrl.onCertificateDetailCreate = function () {
            ctrl.reload();
        };

        ctrl.onCertificateDetailUpdate = function (stamp) {
            var originalStamp = _.find(ctrl.stamps, function (stampItem) {
                return stampItem.CertificateStamp.id == stamp.CertificateStamp.id;
            });

            _.extend(originalStamp, stamp);

        };

        ctrl.onCertificateDetailDelete = function (stampToDelete) {
            _.remove(ctrl.stamps, function(stamp) {
                return stamp.CertificateStamp.id == stampToDelete;
            });
        };


        ctrl.onCertificateCreate = function () {
            ctrl.reload();
        };

        ctrl.onCertificateUpdate = function (stamp) {
            var originalStamp = _.find(ctrl.stamps, function (stampItem) {
                return stampItem.CertificateStamp.id == stamp.CertificateStamp.id;
            });

            _.extend(originalStamp, stamp);

        };

        ctrl.onCertificateDelete = function (stampToDelete) {
            _.remove(ctrl.stamps, function(stamp) {
                return stamp.CertificateStamp.id == stampToDelete;
            });
        };
        ctrl.viewActivityDetails = function () {
            ctrl.activityDetails.type = 'details';
            ctrl.activityDetails.isShown = true;
            ctrl.activityDetails.active = {};
        };

        ctrl.onActivityClose = function () {
            ctrl.activityDetails.isShown = false;
        };

        ctrl.inputCertificate = function (stamp) {
            var confirm = $mdDialog.confirm({
                template: utils.inputCertificateStamp(),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelInput = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmInput = function () {
                        var quantityStamp = $('#input-stamp').val();
                        if(quantityStamp > 0) {
                            ctrl.quantityStamp = quantityStamp;
                            angular.element('.global-loading').show();
                            Storage.updateCertificateQuantity({
                                stampId: stamp.CertificateStamp.id,
                                quantity: ctrl.quantityStamp,
                            }).then(function (){
                                ctrl.reload();
                                angular.element('.global-loading').hide();
                                $mdDialog.cancel();
                            }).finally(function () {
                                angular.element('.global-loading').hide();
                            });
                        } else {
                            return;
                        }
                    };
                }],
                clickOutsideToClose: true
            });

            $mdDialog.show(confirm);
        };


        /*** helpers ***/
        function setDefault() {
            ctrl.stamps = [];

            ctrl.pagination.disabled = true;
            ctrl.pagination.current = 0;

            ctrl.certificateDetails.isShown = false;
            ctrl.activityDetails.isShown = false;
            ctrl.showMode = true;

            loader.stop();
        }

    }

    angular.module('printty')
        .component('storageCertificate', {
            bindings: {
                open: '=',
                onStorageUpdate: '=',
                updateOriginal: '<',
                onCertificate : '&'
            },
            require: {
                storageCtrl: '^^storageComponent'
            },
            controller: StorageCertificateCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/certificate.html'
        });

})();