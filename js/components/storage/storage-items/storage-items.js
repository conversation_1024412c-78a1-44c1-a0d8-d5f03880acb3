(function () {

    function StorageItemCtrl(StorageItemApi, Store, utils, Factory) {

        "ngInject";

        var ctrl = this;

        ctrl.storageItems = [];

        var pagination = {
            init: true,
            perPage: 40,
            current: 0,
            type_id: null,

            length: 0,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };

        ctrl.pagination = pagination;

        var subscriptions = [];

        ctrl.$onInit = function () {
            utils.appLoaded();
            getFactories();
            Store.data('types').update([]);
            ctrl.parentCtrl.onStorageItemCreate = ctrl.onStorageItemCreate;
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;
            this.current = 0;
            this.length = 0;

            ctrl.storageItems = [];
            Store.data('FactorySelected').update(ctrl.factory);
            ctrl.infiniteStorageItems.setDefault();
            ctrl.infiniteStorageItems.getItemAtIndex(1);
        };

        /** Search block **/
        pagination.liveSearch = function () {
            utils.delay(function() {
                pagination.search();
            }, 350 );
        };

        pagination.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        pagination.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.infiniteStorageItems = {
            numLoaded_: 0,
            toLoad_: 0,

            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_();
                    return null;
                }

                return ctrl.storageItems[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            setDefault: function () {
                this.numLoaded_ = 0;
            },
            fetchMoreItems_: function(index) {
                if(!ctrl.factory) return;
                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show()
                }

                this.toLoad_ += pagination.perPage;

                pagination.current++;

                StorageItemApi.fetchStorageItems(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: (pagination.current - 1) * pagination.perPage,
                        conditions_keywords: pagination.searchQuery,
                        factory_id: ctrl.factory,
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteStorageItems.numLoaded_ = ctrl.infiniteStorageItems.toLoad_ - pagination.perPage + data.storageItems.length;
                        ctrl.storageItems = ctrl.storageItems.concat(data.storageItems);

                        pagination.searchFailed = !ctrl.storageItems.length;
                        pagination.end = ctrl.storageItems.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );
            }
        };

        ctrl.onStorageItemCreate = function () {
            pagination.reload();
        };

        ctrl.onStorageItemUpdate = function () {
            pagination.reload();
        };

        ctrl.onChange = function (){
            pagination.reload();
        }

        /*** private ****/

        function getFactories() {
            Factory.fetchFactoryUser().then(function(data){
                ctrl.factories = data.factories;
                ctrl.factory = data.factorySelected;
                Store.data('FactorySelected').update(ctrl.factory);
            });
        }

    }

    angular.module('printty')

        .component('storageItem', {
            require: {
                parentCtrl: '^^storageComponent'
            },
            controller: StorageItemCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/storage-items/storage-items.html'
        });

})();