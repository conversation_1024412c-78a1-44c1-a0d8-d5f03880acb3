(function() {

    function EditStorageItemCtrl($scope, $mdSidenav, $mdDialog, Factory, Store, utils, $filter, StorageItemApi) {

        "ngInject";

        var ctrl = this;

        ctrl.storageItem = null;
        ctrl.showMode = true;
        ctrl.checkColorSize = false;
        ctrl.colors_sizes = [];
        ctrl.product_id = null;

        var subscriptions = [];
        var allSelectedColorSize = false;
        var sidenav = {
                open: function () {
                    ctrl.checkColorSize = false;
                    $mdSidenav('edit-storage-item').open();
                },
                close: function () {
                    $mdSidenav('edit-storage-item').close();
                }
            };

        ctrl.loadingType = 'full';

        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('FactorySelected').subscribe(function (data) {
                    ctrl.factory = data;
                })
            );
        };

        ctrl.close = sidenav.close;

        ctrl.open = function (storageItem) {
            setDefault();

            StorageItemApi.fetchStorageItem({
                factory_id: storageItem.StorageItemManagement.factory_id,
                product_id: storageItem.StorageItemManagement.product_id
            }).then(function (data) {
                ctrl.storageItem = data.storageItem;
                ctrl.colors_sizes = data.storageItem.colors_sizes;

                ctrl.loadingType = 'stopped';
            }, function () {
                ctrl.loadingType = 'stopped';
            });

            sidenav.open();
        };

        function getSelectedColorsSizes() {

            var selectedColorsSizes = [];
            _.forEach(ctrl.colors_sizes, function (color_size) {
                if (color_size.is_check) {
                    selectedColorsSizes.push(color_size.color_size_id);
                }
            });
            return selectedColorsSizes;

        }

        ctrl.selectAllColorSize = function () {
            ctrl.colors_sizes.forEach(function (color_size) {
                color_size.is_check = !allSelectedColorSize;
            });

            allSelectedColorSize = !allSelectedColorSize;
        };

        ctrl.selectAllColorSize = function () {
            ctrl.colors_sizes.forEach(function (color_size) {
                color_size.is_check = !allSelectedColorSize;
            });

            allSelectedColorSize = !allSelectedColorSize;
        };

        ctrl.edit = function () {

            if (ctrl.showMode) { // create clone
                storageItemClone = _.cloneDeep(ctrl.storageItem);
            } else { // revert changes
                ctrl.storageItem = storageItemClone;
            }

            toggleShowMode();

        };

        ctrl.update = function () {
            ctrl.checkColorSize = false;
            if (_.isEqual(ctrl.storageItem, storageItemClone)) {
                toggleShowMode();
                return;
            }

            var colors_sizes = getSelectedColorsSizes();
            if(colors_sizes.length == 0) {
                ctrl.checkColorSize = true;
            }

            if(!ctrl.checkProductType && !ctrl.checkProduct && !ctrl.checkColorSize) {
                ctrl.loadingType = 'partial';
                StorageItemApi.updateStorageItem(ctrl.factory, ctrl.storageItem.ProductJoin.id, colors_sizes).then(function () {
                    ctrl.loadingType = 'stopped';
                    ctrl.onUpdate();
                    sidenav.close();
                }, function () {
                    ctrl.loadingType = 'stopped';
                });
            }

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('在庫アイテム管理を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteContent);
        };

        function deleteContent() {
            ctrl.loadingType = 'partial';

            var colors_sizes = getSelectedColorsSizes();
            StorageItemApi.deleteStorageItem(ctrl.storageItem.ProductJoin.id, colors_sizes)
                .then(function () {
                    ctrl.loadingType = 'stopped';
                    ctrl.onUpdate();
                    sidenav.close();
                }, function () {
                    ctrl.loadingType = 'stopped';
                });
        }

        /*** helpers ***/
        function setDefault() {
            ctrl.storageItem= null;
            ctrl.showMode = true;
            storageItemClone = null;

            ctrl.loadingType = 'full';
        }

        function toggleShowMode() {
            ctrl.showMode = !ctrl.showMode;
        }

    }

    angular.module('printty')

        .component('editStorageItem', {
            bindings: {
                open: '=',
                onClose: '=',
                onCreate: '=',
                onUpdate: '=',
            },
            controller: EditStorageItemCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/storage-items/edit-storage-item.html'
        });

})();
