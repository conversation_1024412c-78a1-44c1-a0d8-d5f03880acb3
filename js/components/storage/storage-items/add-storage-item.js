(function() {

    function AddStorageItemCtrl($scope, $mdSidenav, Factory, Store, $filter, StorageItemApi, Product, utils) {

        "ngInject";
        var ctrl = this;

        ctrl.storageItem = null;
        ctrl.products_types = [];
        ctrl.products = [];
        ctrl.colors_sizes = [];
        ctrl.product_id = null;
        ctrl.checkProductType = false;
        ctrl.checkProduct = false;
        ctrl.checkColorSize = false;
        ctrl.product_type_id = null;
        ctrl.searchQuery = null;
        var subscriptions = [];
        var allSelectedColorSize = false;
        var lastSearch = null;

        var sidenav = {
            open: function () {
                ctrl.checkProductType = false;
                ctrl.checkProduct = false;
                ctrl.checkColorSize = false;
                ctrl.colors_sizes = [];
                ctrl.products = [];
                ctrl.product_id = null;
                $mdSidenav('add-storage-item').open();
            },
            close: function () {
                $mdSidenav('add-storage-item').close();
            }
        };
       
        ctrl.loadingType = 'stopped';

        ctrl.close = sidenav.close;

        ctrl.open = function () {
            setDefault();
            sidenav.open();
        };

        // fetch types and colors
        ctrl.$onInit = function() {
            fetchProductType();
            subscriptions.push(
                Store.data('FactorySelected').subscribe(function (data) {
                    ctrl.factory = data;
                })
            );
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        ctrl.selectProductType = function(product_type_id) {
            if(ctrl.product_type_id !== product_type_id && ctrl.product_type_id !== null) {
                ctrl.searchQuery = '';
            }
            ctrl.colors_sizes = [];
            ctrl.products = [];
            ctrl.product_id = null;
            ctrl.product_type_id = product_type_id;
            StorageItemApi.fetchProductsByType({
                type_id: ctrl.product_type_id,
                keyword: ctrl.searchQuery,
                factory_id: ctrl.factory
            }).then(function(data){
                ctrl.products = data.products;
            });
        };

        ctrl.search = function () {
            if (lastSearch === ctrl.searchQuery) return;
            if (ctrl.searchQuery == '') ctrl.reloadProducts();;
            lastSearch = ctrl.searchQuery;
            ctrl.reloadProducts();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.searchQuery = '';
            ctrl.search();
        };

        ctrl.reloadProducts = function () {
            ctrl.product_type_id = ctrl.storageItem.product_type_id;
            ctrl.storageItem.product_id = null;
            ctrl.selectProductType(ctrl.product_type_id);
        };

        ctrl.selectProduct = function(product_id) {
            ctrl.product_id = product_id;
            StorageItemApi.fetchColorSizeByProduct({
                product_id: product_id
            }).then(function(data){
                ctrl.colors_sizes = data.colors_sizes;
            });
        };

        ctrl.selectAllColorSize = function () {
            ctrl.colors_sizes.forEach(function (color_size) {
                color_size.is_check = !allSelectedColorSize;
            });

            allSelectedColorSize = !allSelectedColorSize;
        };
        
        ctrl.save = function () {
            ctrl.checkColorSize = false;
            // form validation
            $scope.addStorageItemForm.$setSubmitted();

            if(typeof $scope.addStorageItemForm.product_type_id.$viewValue == "undefined" || $scope.addStorageItemForm.product_type_id.$viewValue === '' || $scope.addStorageItemForm.product_type_id.$viewValue == null){
                ctrl.checkProductType = true;
            }

            if($scope.addStorageItemForm.product_id.$viewValue == '' || $scope.addStorageItemForm.product_id.$viewValue == null){
                ctrl.checkProduct = true;
            }

            var colors_sizes = getSelectedColorsSizes();
            if(colors_sizes.length == 0) {
                ctrl.checkColorSize = true;
            }

            if(!ctrl.checkProductType && !ctrl.checkProduct && !ctrl.checkColorSize) {
                ctrl.loadingType = 'partial';

                StorageItemApi.createStorageItem(ctrl.factory,ctrl.storageItem.product_id, colors_sizes)
                    .then(function (data) {
                        updateStorageItemList(data);
                        ctrl.close();
                    }, function () {
                        ctrl.loadingType = 'stopped';
                    });
            }

        };

        /*** helpers ***/
        function setDefault() {
            ctrl.storageItem = null;

            ctrl.loadingType = 'stopped';

            $scope.addStorageItemForm.$setPristine();
            $scope.addStorageItemForm.$setUntouched();
        }

        function updateStorageItemList() {
            ctrl.onCreate();
        }

        function fetchProductType (){
            Product.types().then(function(data){
                ctrl.products_types = data.types;
            });
        };

        function getSelectedColorsSizes() {
            var selectedColorsSizes = [];

            _.forEach(ctrl.colors_sizes, function (color_size) {
                if (color_size.is_check) {
                    var data = {
                        color_size_id: color_size.color_size_id,
                    };
                    selectedColorsSizes.push(data);


                }
            });
            return selectedColorsSizes;

        }

    }

    angular.module('printty')

        .component('addStorageItem', {
            bindings: {
                open: '=',
                onCreate: '='
            },
            controller: AddStorageItemCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/storage/storage-items/add-storage-item.html',
        });
})();
