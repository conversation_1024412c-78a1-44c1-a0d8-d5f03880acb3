(function () {

    angular.module('printty')
        .component('referenceCategoryDetails', {
            controller: CategoryDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/categories/category-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function CategoryDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, Categories) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.category = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var categoryForm,
            categoryClone;

        var sidenav = {
            open: function () {
                $mdSidenav('reference-category-details').open();
            },
            close: function () {
                $mdSidenav('reference-category-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            categoryForm = $scope.categoryFrameForm;

            $mdSidenav('reference-category-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                categoryClone = _.cloneDeep(vm.category);
                vm.showMode = false;
            } else {
                vm.category = categoryClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            categoryForm.$setSubmitted();

            if (categoryForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.category, categoryClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            Categories.updateCategory(vm.category)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.save = function () {
            categoryForm.$setSubmitted();

            if (categoryForm.$invalid) {
                return;
            }

            vm.loadingType = 'partial';

            Categories.createCategory(vm.category)
                .then(function () {
                    vm.onCreate();
                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('お客様指定を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteCategory);
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.categoryId) {
                fetchCategory();
            }

            sidenav.open();
        }

        function fetchCategory() {
            vm.loadingType = 'full';

            Categories.fetchCategory(vm.active.categoryId)
                .then(function (data) {
                    vm.category = data.ProductCategory;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var categoryData = _.cloneDeep(vm.category);

            vm.onUpdate({
                category: categoryData
            });
        }

        function deleteCategory() {
            vm.loadingType = 'partial';

            Categories.deleteCategory(vm.category.id)
                .then(function () {
                    vm.onDelete({
                        categoryId: vm.category.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.category = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (categoryForm) {
                categoryForm.$setPristine();
                categoryForm.$setUntouched();
            }
        }

    }

})();