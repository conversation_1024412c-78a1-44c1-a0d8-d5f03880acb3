(function () {

    angular.module('printty')
        .component('referenceCategories', {
            controller: CategoriesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/categories/categories.html'
        });

    function CategoriesCtrl(Store, utils, UvFrames, Categories, $timeout) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.categories = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.categoryDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:category').subscribe(function () { ctrl.viewCreate(); })
            );

            getFrames();
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.categories = [];

            getFrames();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (category) {
            ctrl.categoryDetails.type = 'details';
            ctrl.categoryDetails.isShown = true;
            ctrl.categoryDetails.active = {
                categoryId: category.ProductCategory.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.categoryDetails.type = 'add';
            ctrl.categoryDetails.isShown = true;
            ctrl.categoryDetails.active = {};
        };

        ctrl.onCategoryClose = function () {
            ctrl.categoryDetails.isShown = false;
        };

        ctrl.onCategoryCreate = function () {
            ctrl.reload();
        };

        ctrl.onCategoryUpdate = function (category) {
            var originalCategory = _.find(ctrl.categories, function (categoryItem) {
                return categoryItem.ProductCategory.id == category.id;
            });

            _.extend(originalCategory.ProductCategory, category);
        };

        ctrl.onCategoryDelete = function (categoryId) {
            _.remove(ctrl.categories, function(category) {
                return category.ProductCategory.id == categoryId;
            });
        };

        /****** private ******/
        function getFrames() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Categories.fetchCategories({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {

                    ctrl.categories = data.categories;
                    ctrl.pagination.empty = !ctrl.categories.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();
                });

        }

    }

})();