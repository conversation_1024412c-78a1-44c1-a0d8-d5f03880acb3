(function () {

  angular.module('printty')
    .component('referencePrintingBases', {
      controller: PrintingBasesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/printing-bases/printing-bases.html'
    });

  function PrintingBasesCtrl(Store, utils, PrintingBase, $timeout) {

    'ngInject';

    var vm = this;

    /**** variables ******/
    vm.bases = [];

    vm.pagination = {
      perPage: 40,

      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    vm.baseDetails = {
      isShown: false,
      type: 'add',
      active: {}
    };

    vm.infiniteBases = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {
        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return vm.bases[index];
      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchBases(index);
      }
    };

    var subscriptions = [];

    /****** methods ******/
    vm.$onInit = function () {
      utils.listLoading.show(true);

      subscriptions.push(
        Store.event('reference:create:printing-base').subscribe(function () {
          vm.viewCreate();
        })
      );

        $('#fileInput').on('change', function (e) {
            vm.CSVupload(e.target.files[0]);
        }).on('click', function () {
            this.value = null;
        });
    };

    vm.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) {
        subscription.unsubscribe();
      });
    };

    vm.reload = function() {
      vm.pagination.reload = true;
      vm.pagination.disabled = false;

      vm.bases = [];

      vm.infiniteBases.numLoaded_ = 0;
      vm.infiniteBases.toLoad_ = 0;
      vm.infiniteBases.getItemAtIndex(1);
    };

    vm.search = function () {
      if (vm.pagination.lastSearch === vm.pagination.searchQuery) {
        return;
      }
      vm.pagination.lastSearch = vm.pagination.searchQuery;
      vm.reload();
    };

    vm.liveSearch = function () {
      utils.delay(function() {
        vm.search();
      }, 1000 );
    };

    vm.clearSearch = function () {
      vm.pagination.searchQuery = '';
      vm.search();
    };

    vm.viewDetails = function (base) {
      vm.baseDetails.type = 'details';
      vm.baseDetails.isShown = true;
      vm.baseDetails.active = {
        baseId: base.PrintingBase.id
      };
    };

    vm.viewCreate = function () {
      vm.baseDetails.type = 'add';
      vm.baseDetails.isShown = true;
      vm.baseDetails.active = {};
    };

    vm.onBaseClose = function () {
      vm.baseDetails.isShown = false;
    };

    vm.onBaseCreate = function () {
      vm.reload();
    };

    vm.onBaseUpdate = function (base) {
      var originalBase = _.find(vm.bases, function (baseItem) {
        return +baseItem.PrintingBase.id === +base.id;
      });

      _.extend(originalBase.PrintingBase, base);
    };

    vm.onBaseDelete = function (baseId) {
      _.remove(vm.bases, function(base) {
        return +base.PrintingBase.id === +baseId;
      });
    };

    /****** private ******/
    function fetchBases(index) {
      if (vm.infiniteBases.toLoad_ >= index || vm.pagination.disabled) {
        return;
      }

      vm.pagination.disabled = true;

      if (vm.pagination.reload) {
        $timeout(function () {
          vm.pagination.loading = false;
          utils.listLoading.show(false);
        });
      } else {
        $timeout(function () {
          vm.pagination.loading = true;
        });
      }

      vm.infiniteBases.toLoad_ += vm.pagination.perPage;

      PrintingBase.fetchBases(
        {
          paging_size: vm.pagination.perPage,
          paging_offset: vm.bases.length,
          conditions_keywords: vm.pagination.searchQuery
        }
      ).then(
        function (data) {

          vm.infiniteBases.numLoaded_ = vm.infiniteBases.toLoad_ - vm.pagination.perPage + data.printing_bases.length;
          vm.bases = vm.bases.concat(data.printing_bases);

          vm.pagination.empty = !vm.bases.length;
          vm.pagination.reload = false;
          vm.pagination.loading = false;

          vm.pagination.disabled = data.total_count <= vm.bases.length;

          $timeout(function () {
            utils.listLoading.hide();
          }, 120);
        }
      );
    }

      vm.CSVupload = function (file) {

          if (!file) {
              $('#fileInput').click();
              return;
          }

          var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

          if ( fileType !== 'csv' ) {
              alert('File.UnexpectedFormat');
              return;
          }

          utils.globalLoading.show();

          PrintingBase.uploadCSV(file)
              .then(function () {
                  utils.globalLoading.hide();
                  vm.reload();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

      };

      vm.CSVdownload = function () {
          utils.globalLoading.show();

          var params = {
              conditions_keywords : vm.pagination.searchQuery
          };

          PrintingBase.downloadCSV(params)
              .then(function () {
                  utils.globalLoading.hide();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

      };

      vm.CSVdownloadTemplate = function () {
          utils.globalLoading.show();

          PrintingBase.CSVdownloadTemplate()
              .then(function () {
                  utils.globalLoading.hide();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

      };

  }

})();