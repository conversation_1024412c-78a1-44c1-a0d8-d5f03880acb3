(function () {

  angular.module('printty')
    .component('printingBaseDetails', {
      controller: PrintingBaseDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/printing-bases/printing-base-details.html',
      bindings: {
        isShown: '<',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        type: '<',
        active: '='
      }
    });

  function PrintingBaseDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, PrintingBase) {

    'ngInject';

    /****** variables ******/
    var vm = this;

    vm.base = null;

    vm.isAdd = true;
    vm.showMode = true;

    vm.loadingType = 'full';

    var baseForm,
        baseClone;

    var sidenav = {
      open: function () {
        $mdSidenav('printing-base-details').open();
      },
      close: function () {
        $mdSidenav('printing-base-details').close();
      }
    };

    /****** methods ******/
    vm.$postLink = function () {
      baseForm = $scope.printingBaseForm;

      $mdSidenav('printing-base-details').onClose(function () {
        $timeout(function () {
          vm.onClose();
        }, 400);
      });
    };

    vm.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (vm.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    vm.edit = function () {
      if (vm.showMode) {
        baseClone = _.cloneDeep(vm.base);
        vm.showMode = false;
      } else {
        vm.base = baseClone;
        setFormPristine();
        vm.showMode = true;
      }
    };

    vm.update = function () {
      baseForm.$setSubmitted();

      if (baseForm.$invalid) {
        return;
      }

      if ( _.isEqual(vm.base, baseClone) ) {
        vm.showMode = true;
        setFormPristine();
        return;
      }

      vm.loadingType = 'partial';

      PrintingBase.updateBase(vm.base)
        .then(function () {
          updateOriginal();

          setFormPristine();
          vm.showMode = true;

          vm.loadingType = 'stopped';
        }, function () {
          vm.loadingType = 'stopped';
        });
    };

    vm.save = function () {
      baseForm.$setSubmitted();

      if (baseForm.$invalid) {
        return;
      }

      vm.loadingType = 'partial';

      PrintingBase.createBase(vm.base)
        .then(function () {
          vm.onCreate();
          vm.close();
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    };

    vm.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('ジグを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteBase);
    };

    vm.close = function () {
      sidenav.close();
    };

    /******** private **********/
    function initComponent() {
      switch (vm.type) {
        case 'details':
          vm.loadingType = 'full';
          vm.isAdd = false;
          vm.showMode = true;
          break;
        case 'add':
        default:
          vm.loadingType = 'stopped';
          vm.isAdd = true;
          vm.showMode = false;
      }

      if (!vm.isAdd && vm.active.baseId) {
        fetchBase();
      }

      sidenav.open();
    }

    function fetchBase() {
      vm.loadingType = 'full';

      PrintingBase.fetchBase(vm.active.baseId)
        .then(function (data) {
          vm.base = data.PrintingBase;
          vm.loadingType = 'stopped';
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    }

    function updateOriginal() {
      var baseData = _.cloneDeep(vm.base);

      vm.onUpdate({
        base: baseData
      });
    }

    function deleteBase() {
      vm.loadingType = 'partial';

      PrintingBase.deleteBase(vm.base.id)
        .then(function () {
          vm.onDelete({
            baseId: vm.base.id
          });

          vm.close();
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    }

    function setDefault() {
      vm.isAdd = true;
      vm.item = null;

      setFormPristine();

      vm.loadingType = 'stopped';
    }

    function setFormPristine() {
      if (baseForm) {
        baseForm.$setPristine();
        baseForm.$setUntouched();
      }
    }

  }

})();