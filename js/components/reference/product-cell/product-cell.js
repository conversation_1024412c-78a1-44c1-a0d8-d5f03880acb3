(function () {

    angular.module('printty')
        .component('referenceProductCell', {
            controller: ProductCellCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/product-cell/product-cell.html'
        });

    function ProductCellCtrl(Auth, $state, Store, utils, ProductCellApi, $timeout) {
        'ngInject';

        var ctrl = this;

        /**** variables ******/
        ctrl.productCell = null;
        ctrl.product_cells = [];

        ctrl.pagination = {
            perPage: 40,
            current: 0,

            role_id: null,

            searchQuery: null,
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,
            disabled: false
        };

        ctrl.productCellDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];

        var loader = new utils.loadCounter(
            function (isBlocking) {
                utils.listLoading.show(isBlocking);
            },
            function () {
                utils.listLoading.hide();
            },
            4
        );

        /****** methods ******/

        ctrl.infiniteProductCells = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.product_cells[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                getFrames(index);
            }
        };
        
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:product-cell').subscribe(function () { ctrl.viewCreate(); })
            );

            fetchOptions();

        }
        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.reload = true;
            ctrl.pagination.current = 0;
            ctrl.pagination.disabled = false;

            ctrl.product_cells = [];

            ctrl.infiniteProductCells.numLoaded_ = 0;
            ctrl.infiniteProductCells.toLoad_ = 0;
            ctrl.infiniteProductCells.getItemAtIndex(1);
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (productCell) {
            ctrl.productCellDetails.type = 'details';
            ctrl.productCellDetails.isShown = true;
            ctrl.productCellDetails.active = {
                productCellId: productCell.id,
            };
        };

        ctrl.viewCreate = function () {
            ctrl.productCellDetails.type = 'add';
            ctrl.productCellDetails.isShown = true;
            ctrl.productCellDetails.active = {};
        };

        ctrl.onProductCellClose = function () {
            ctrl.productCellDetails.isShown = false;
        };

        ctrl.onProductCellCreate = function () {
            ctrl.reload();
        };

        ctrl.onProductCellUpdate = function (productCell) {
            var originalProductCell = _.find(ctrl.product_cells, function (productCellItem) {
                return productCellItem.id == productCell.id;
            });

            _.extend(originalProductCell, productCell);
        };

        ctrl.onProductCellDelete = function (productCellId) {
            _.remove(ctrl.product_cells, function(productCell) {
                return productCell.id == productCellId;
            });
        };

        /****** private ******/

        function getFrames(index) {
            if (ctrl.infiniteProductCells.toLoad_ >= index || ctrl.pagination.disabled) return;

            ctrl.pagination.disabled = true;

            if (ctrl.pagination.reload) {
                $timeout(function () {
                    ctrl.pagination.loading = false;
                    loader.start(false);
                });
            } else {
                $timeout(function () {
                    ctrl.pagination.loading = true;
                });
            }

            ctrl.infiniteProductCells.toLoad_ += ctrl.pagination.perPage;

            ProductCellApi.fetchProductCells(
                {
                    paging_size: ctrl.pagination.perPage,
                    paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
                    conditions_keywords: ctrl.pagination.searchQuery
                }
            ).then(
                function (data) {
                    ctrl.pagination.init = false;
                    ctrl.pagination.searching = false;

                    ctrl.infiniteProductCells.numLoaded_ = ctrl.infiniteProductCells.toLoad_ - ctrl.pagination.perPage + data.product_cells.length;
                    ctrl.product_cells = ctrl.product_cells.concat(data.product_cells);

                    ctrl.pagination.searchFailed = !ctrl.product_cells.length;
                    ctrl.pagination.empty = !ctrl.product_cells.length;
                    ctrl.pagination.end = ctrl.product_cells.length >= data.total_count;
                    ctrl.pagination.busy = false;

                    utils.listLoading.hide();
                    utils.moreItemsLoad.hide();

                }
            );

        }

        function fetchOptions() {

            ProductCellApi.fetchOptions().then(function (data) {
                Store.data('GeneralPurpose').update(data);
                loader.stop();
            });

        }

    }

})();