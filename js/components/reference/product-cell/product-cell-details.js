(function () {

    angular.module('printty')
        .component('referenceProductCellDetails', {
            controller: ProductCellDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/product-cell/product-cell-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ProductCellDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, Store, ProductCellApi) {
        "ngInject";

        var ctrl = this,
            subscriptions = [],
            productCellClone,
            productCellForm,
            sidenav = {
                open: function () {
                    ctrl.checkProductCell = null;
                    ctrl.checkfactoryType = null;
                    ctrl.checkCellType = null;
                    $mdSidenav('reference-product-cell-details').open();
                },
                close: function () {
                    $mdSidenav('reference-product-cell-details').close();
                }
            };

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('GeneralPurpose').subscribe(function (data) {
                    if(data){
                        ctrl.factoryTypes = data.factory_types;
                        ctrl.cellTypes = data.cell_types;
                    }
                })
            );
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            productCellForm = $scope.productCellFrameForm;

            $mdSidenav('reference-product-cell-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };


        ctrl.edit = function () {
            ctrl.checkProductCell = null;
            ctrl.checkfactoryType = null;
            ctrl.checkCellType = null;

            if (ctrl.showMode) {
                productCellClone = _.cloneDeep(ctrl.productCell);
                ctrl.showMode = false;
            } else {
                ctrl.productCell = productCellClone;
                setFormPristine();
                ctrl.showMode = true;
            }
        };

        ctrl.update = function () {
            productCellForm.$setSubmitted();

                    if(typeof productCellForm.title.$viewValue == "undefined" || productCellForm.title.$viewValue === '' || productCellForm.title.$viewValue == null) {
                        ctrl.checkProductCell = true;
                    }else {
                        ctrl.checkProductCell = false;
                    }

                    if(typeof productCellForm.factory_type.$viewValue == "undefined" || productCellForm.factory_type.$viewValue === '' || productCellForm.factory_type.$viewValue == null) {
                        ctrl.checkfactoryType = true;
                    }else {
                        ctrl.checkfactoryType = false;
                    }

                    if(typeof productCellForm.cell_type.$viewValue == "undefined" || productCellForm.cell_type.$viewValue === '' || productCellForm.cell_type.$viewValue == null) {
                        ctrl.checkCellType = true;
                    }else {
                        ctrl.checkCellType = false;
                    }

                    if (_.isEqual(ctrl.productCell, productCellClone)) {
                        ctrl.showMode = true;
                        setFormPristine();
                        return;
                    }
                    if(!ctrl.checkProductCell && !ctrl.checkfactoryType && !ctrl.checkCellType) {
                        ctrl.loadingType = 'partial';
                        ctrl.showMode = true;
                        ProductCellApi.updateProductCell(ctrl.productCell).then(function () {
                            updateProductCellList();
                            setFormPristine();
                            ctrl.loadingType = 'stopped';
                        }, function () {
                            ctrl.loadingType = 'stopped';
                        });
                    }
        };

        ctrl.save = function () {
            productCellForm.$setSubmitted();

            if(typeof productCellForm.title.$viewValue == "undefined" || productCellForm.title.$viewValue === '' || productCellForm.title.$viewValue == null) {
                ctrl.checkProductCell = true;
            }else {
                ctrl.checkProductCell = false;
            }

            if(typeof productCellForm.factory_type.$viewValue == "undefined" || productCellForm.factory_type.$viewValue === '' || productCellForm.factory_type.$viewValue == null) {
                ctrl.checkfactoryType = true;
            }else {
                ctrl.checkfactoryType = false;
            }

            if(typeof productCellForm.cell_type.$viewValue == "undefined" || productCellForm.cell_type.$viewValue === '' || productCellForm.cell_type.$viewValue == null) {
                ctrl.checkCellType = true;
            }else {
                ctrl.checkCellType = false;
            }


            if(!ctrl.checkProductCell && !ctrl.checkfactoryType && !ctrl.checkCellType) {
                ctrl.loadingType = 'partial';

                ProductCellApi.createProductCell(ctrl.productCell)
                    .then(function () {
                        ctrl.onCreate();
                        ctrl.close();
                    })
                    .catch(function () {
                        ctrl.loadingType = 'stopped';
                    });
            }
        };

        ctrl.delete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('生産セルを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function() {

                ctrl.loadingType = 'partial';

                ProductCellApi.deleteProductCell(ctrl.productCell.id).then(
                    function () {
                        ctrl.onDelete({
                            productCellId: ctrl.productCell.id
                        });
                        ctrl.close();

                    }, function () {
                        ctrl.loadingType = 'stopped';
                    }
                );

            });

        };

        ctrl.close = function () {
            sidenav.close();
        };


        /******** private **********/
        function initComponent() {
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.productCellId) {
                fetchProductCell();
            }

            sidenav.open();
        }

        function fetchProductCell() {
            ctrl.loadingType = 'full';
            ProductCellApi.fetchProductCell(ctrl.active.productCellId)
                .then(function (data) {
                    ctrl.productCell = data.product_cell;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });
        }

        function updateProductCellList() {
            var originalProductCell = _.cloneDeep(ctrl.productCell);

            originalProductCell.product_cell_type = _.find(ctrl.cellTypes, function (type) {
                return type.GeneralPurpose.key == ctrl.productCell.product_cell_type_id;
            }).GeneralPurpose.value;

            originalProductCell.factory_type = _.find(ctrl.factoryTypes, function (type) {
                return type.Factory.id == ctrl.productCell.factory_id;
            }).Factory.title;

            ctrl.onUpdate({
                productCell: originalProductCell
            });
        }

        function setDefault() {
            ctrl.productCell = null;
            ctrl.isAdd = true;
            originalProductCell = null;

            setFormPristine();
            ctrl.loadingType = 'full';
        }

        function setFormPristine() {
            if (productCellForm) {
                productCellForm.$setPristine();
                productCellForm.$setUntouched();
            }
        }

    }

})();