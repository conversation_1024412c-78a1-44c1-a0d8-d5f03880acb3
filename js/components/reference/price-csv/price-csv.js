(function () {

    angular.module('printty')
        .component('referencePriceCsv', {
            controller: PriceCsvCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/price-csv/price-csv.html'
        });

    function PriceCsvCtrl(Auth, $state, Store, utils, PriceCsvApi) {

        'ngInject';

        utils.appLoaded();

        var ctrl = this;

        /**** variables ******/

        ctrl.urls = [];

        var pagination = {
            init: true,
            perPage: 40,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {
            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.urls = [];

            ctrl.infiniteUrls.numLoaded_ = 0;
            ctrl.infiniteUrls.toLoad_ = 0;
            ctrl.infiniteUrls.getItemAtIndex(1);
        };

        ctrl.infiniteUrls = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.urls[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                PriceCsvApi.fetchPriceCsvURL(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.urls.length,
                        conditions_keywords: pagination.searchQuery,
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteUrls.numLoaded_ = ctrl.infiniteUrls.toLoad_ - pagination.perPage + data.urls.length;
                        ctrl.urls = ctrl.urls.concat(data.urls);

                        pagination.searchFailed = !ctrl.urls.length;
                        pagination.end = ctrl.urls.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );
            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            ctrl.infiniteUrls.numLoaded_ = 0;
            ctrl.infiniteUrls.toLoad_ = 0;
            ctrl.infiniteUrls.getItemAtIndex(1);
        };

    }

})();