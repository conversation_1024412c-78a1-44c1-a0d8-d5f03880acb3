(function() {

  function AddPlaceCtrl($scope, $mdSidenav, Place, Store, $filter) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        sidenav = {
          open: function () {
            ctrl.checkFactory = null;
            $mdSidenav('add-place').open();
          },
          close: function () {
            $mdSidenav('add-place').close();
          }
        };

    ctrl.loadingType = 'stopped';
    
    ctrl.close = sidenav.close;

    ctrl.open = function () {
      setDefault();
      sidenav.open();
    };

    // fetch types and colors
    ctrl.$onInit = function() {
      subscriptions.push(
        Store.data('PlaceTypes').subscribe(function (data) {
          ctrl.types = data;
        }),
        Store.data('Factories').subscribe(function (data) {
          ctrl.factories = data;
        })
      );
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };

    ctrl.save = function () {

      // form validation
      $scope.addPlaceForm.$setSubmitted();
      if(typeof $scope.addPlaceForm.factory_id.$viewValue == "undefined" || $scope.addPlaceForm.factory_id.$viewValue === '' || $scope.addPlaceForm.factory_id.$viewValue == null) {
        ctrl.checkFactory = true;
      }else {
        ctrl.checkFactory = false;
      }
      if ($scope.addPlaceForm.$invalid) {
        return;
      }
      if(!ctrl.checkFactory) {
        ctrl.loadingType = 'partial';
        Place.create(ctrl.place.Place).then(function (data) {
          updatePlacesList(data);
          ctrl.close();
        }, function () {
          ctrl.loadingType = 'stopped';
        });
      }
    };

    /*** helpers ***/
    function setDefault() {
      ctrl.place = null;

      ctrl.loadingType = 'stopped';
      
      $scope.addPlaceForm.$setPristine();
      $scope.addPlaceForm.$setUntouched();
    }

    function updatePlacesList() {
      ctrl.onCreate();
    }

  }

  angular.module('printty')

    .component('addPlace', {
      bindings: {
        open: '=',
        onCreate: '='
      },
      controller: AddPlaceCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/add-place.html'
    });

})();
