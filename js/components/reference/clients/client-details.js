(function () {

    angular.module('printty')
        .component('referenceClientDetails', {
            controller: ClientDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/clients/client-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ClientDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, Clients) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.client = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var clientForm,
            clientClone;

        var sidenav = {
            open: function () {
                $mdSidenav('reference-client-details').open();
            },
            close: function () {
                $mdSidenav('reference-client-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            clientForm = $scope.clientFrameForm;

            $mdSidenav('reference-client-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                clientClone = _.cloneDeep(vm.client);
                vm.showMode = false;
            } else {
                vm.client = clientClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            clientForm.$setSubmitted();

            if (clientForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.client, clientClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            Clients.updateClient(vm.client)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.save = function () {
            clientForm.$setSubmitted();

            if (clientForm.$invalid) {
                return;
            }

            vm.loadingType = 'partial';

            Clients.createClient(vm.client)
                .then(function () {
                    vm.onCreate();
                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('お客様指定を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteClient);
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.clientId) {
                fetchClient();
            }

            sidenav.open();
        }

        function fetchClient() {
            vm.loadingType = 'full';

            Clients.fetchClient(vm.active.clientId)
                .then(function (data) {
                    vm.client = data.Client;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var clientData = _.cloneDeep(vm.client);

            vm.onUpdate({
                client: clientData
            });
        }

        function deleteClient() {
            vm.loadingType = 'partial';

            Clients.deleteClient(vm.client.id)
                .then(function () {
                    vm.onDelete({
                        clientId: vm.client.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.client = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (clientForm) {
                clientForm.$setPristine();
                clientForm.$setUntouched();
            }
        }

    }

})();