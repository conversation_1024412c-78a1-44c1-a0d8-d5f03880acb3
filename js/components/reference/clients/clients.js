(function () {

    angular.module('printty')
        .component('referenceClients', {
            controller: ClientsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/clients/clients.html'
        });

    function ClientsCtrl(Store, utils, UvFrames, Clients, $timeout) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.clients = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.clientDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:client').subscribe(function () { ctrl.viewCreate(); })
            );

            getFrames();

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.clients = [];

            getFrames();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (client) {
            ctrl.clientDetails.type = 'details';
            ctrl.clientDetails.isShown = true;
            ctrl.clientDetails.active = {
                clientId: client.Client.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.clientDetails.type = 'add';
            ctrl.clientDetails.isShown = true;
            ctrl.clientDetails.active = {};
        };

        ctrl.onClientClose = function () {
            ctrl.clientDetails.isShown = false;
        };

        ctrl.onClientCreate = function () {
            ctrl.reload();
        };

        ctrl.onClientUpdate = function (client) {
            var originalClient = _.find(ctrl.clients, function (clientItem) {
                return clientItem.Client.id == client.id;
            });

            _.extend(originalClient.Client, client);
        };

        ctrl.onClientDelete = function (clientId) {
            _.remove(ctrl.clients, function(client) {
                return client.Client.id == clientId;
            });
        };

        /****** private ******/
        function getFrames() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Clients.fetchClients({
                conditions_keywords: ctrl.pagination.searchQuery,
                is_epson: 1,
            })
                .then(function (data) {

                    ctrl.clients = data.clients;
                    ctrl.pagination.empty = !ctrl.clients.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

    }

})();