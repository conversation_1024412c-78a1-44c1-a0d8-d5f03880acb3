(function () {

  function StoreCtrl($scope, $state, Auth, Store, utils,Factory) {

    "ngInject";

    if (!Auth.isAuthorized('reference')) {
      return $state.go( Auth.defaultRoute );
    }

    var ctrl = this;

    ctrl.state = $state;
    ctrl.statuses = [];

    var subscription;

    $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });

    ctrl.$onInit = function () {
      utils.appLoaded();
      fetchFactories();
      Store.data('statuses').update(ctrl.statuses);
      subscription = Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); });
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    ctrl.addProduct = function () {
      Store.event('reference:create:product').emit();
    };

    ctrl.addPlate = function () {
      Store.event('reference:create:plate').emit();
    };

    ctrl.addPrintingBase = function () {
      Store.event('reference:create:printing-base').emit();
    };

    ctrl.addUvFrame = function () {
        Store.event('reference:create:uv-frame').emit();
    };

    ctrl.addCpFrame = function () {
      Store.event('reference:create:cp-frame').emit();
    };

    ctrl.addReason = function () {
        Store.event('reference:create:reason').emit();
    }

    ctrl.addClient = function () {
        Store.event('reference:create:client').emit();
    }

    ctrl.addClientKornit = function () {
      Store.event('reference:create:client-kornit').emit();
    }

    ctrl.addProductCell = function () {
      Store.event('reference:create:product-cell').emit();
    }

    ctrl.addAndon = function () {
      Store.event('reference:create:andon').emit();
    }

    ctrl.addTrim = function () {
        Store.event('reference:create:trim').emit();
    }

    ctrl.addSource = function () {
        Store.event('reference:create:source').emit();
    };

    ctrl.addWarehouse = function () {
        Store.event('reference:create:warehouse').emit();
    };

    ctrl.addCategory = function () {
      Store.event('reference:create:category').emit();
    };

    ctrl.addFactory = function () {
      Store.event('reference:create:factory').emit();
    };
    ctrl.addOptionItem = function () {
      Store.event('reference:create:option-item').emit();
    };

    ctrl.showNav = function (title){
      var found = false;
      Store.data('Mode').subscribe(function (data){
        if(data && data.mode_sub){
          found = Auth.contains(data.mode_sub,title);
        }
      });
      return found;
    }

    /*** private ****/
    function onStatusesChange(statuses) {
      ctrl.statuses = statuses;
    }
    function fetchFactories(){
      Store.data('Mode').subscribe(function (data){
        ctrl.showAddProduct = !data.is_franchise;
      })
      localStorage.setItem('showAddProduct', JSON.stringify(ctrl.showAddProduct));
    }

  }

  angular.module('printty')

    .component('referenceComponent', {
      controller: StoreCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/reference.html'
    });

})();