(function () {

    angular.module('printty')
        .component('uvFrameDetails', {
            controller: UvFrameDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/uv-frames/uv-frame-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function UvFrameDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, UvFrames,Store) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.uv = null;

        vm.isAdd = true;
        vm.showMode = true;
        vm.listPaper = null;
        vm.Products = [];

        vm.loadingType = 'full';

        var uvForm,
            uvClone;

        var sidenav = {
            open: function () {
                Store.data('instruction_paper_size_list').subscribe(function (data){
                    vm.listPaper = data;
                });
                vm.check_instruction_paper_size_id = false;
                vm.Products = [];
                $mdSidenav('uv-frame-details').open();
            },
            close: function () {
                vm.check_instruction_paper_size_id = false;
                vm.Products = [];
                $mdSidenav('uv-frame-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            uvForm = $scope.uvFrameForm;

            $mdSidenav('uv-frame-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                uvClone = _.cloneDeep(vm.uv);
                vm.showMode = false;
            } else {
                vm.uv = uvClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            uvForm.$setSubmitted();

            if(typeof uvForm.instruction_paper_size_id.$viewValue == "undefined" || uvForm.instruction_paper_size_id.$viewValue == null){
                vm.check_instruction_paper_size_id = true;
            }else{
                vm.check_instruction_paper_size_id = false;
            }
            if (uvForm.$invalid || vm.check_instruction_paper_size_id) {
                return;
            }

            if ( _.isEqual(vm.uv, uvClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            UvFrames.updateUv(vm.uv)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.save = function () {
            uvForm.$setSubmitted();
            if(typeof uvForm.instruction_paper_size_id.$viewValue == "undefined" || uvForm.instruction_paper_size_id.$viewValue == null){
                vm.check_instruction_paper_size_id = true;
            }else{
                vm.check_instruction_paper_size_id = false;
            }
            if (uvForm.$invalid || vm.check_instruction_paper_size_id) {
                return;
            }

            vm.loadingType = 'partial';

            UvFrames.createUv(vm.uv)
                .then(function () {
                    vm.onCreate();
                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('UVを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteUv);
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.uvId) {
                fetchUv();
            }

            sidenav.open();
        }

        function fetchUv() {
            vm.loadingType = 'full';

            UvFrames.fetchUv(vm.active.uvId)
                .then(function (data) {
                    vm.uv = data.UVFrame;
                    vm.Products = data.Products;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var uvData = _.cloneDeep(vm.uv);

            vm.onUpdate({
                uv: uvData
            });
        }

        function deleteUv() {
            vm.loadingType = 'partial';

            UvFrames.deleteUv(vm.uv.id)
                .then(function () {
                    vm.onDelete({
                        uvId: vm.uv.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.uv = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (uvForm) {
                uvForm.$setPristine();
                uvForm.$setUntouched();
            }
        }

    }

})();