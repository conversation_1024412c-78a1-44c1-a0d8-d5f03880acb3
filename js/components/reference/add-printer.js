(function() {

  function AddPrinterCtrl($scope, $mdSidenav, Store, Printer, $filter) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        sidenav = {
          open: function () {
            ctrl.checkPrinter = false;
            $mdSidenav('add-printer').open();
          },
          close: function () {
            $mdSidenav('add-printer').close();
          }
        },
        allSelectedCode = false,
        allSelectedColor = false;
        allSelectedSize = false;


    ctrl.loadingType = 'full';

    ctrl.close = sidenav.close;

    ctrl.open = function () {
        setDefault();
        ctrl.checkCapacity = false;

        ctrl.loadingType = 'full';

        // get printer details
        Printer.getOptionList().then(function (data) {
            ctrl.optionList = data.printer.optionList;
            ctrl.remarks = data.printer.optionList.remarks;
            ctrl.showSelect = true;
            ctrl.loadingType = 'stopped';
        }, function () {

        });

      sidenav.open();
    };

    // fetch types and colors
    ctrl.$onInit = function() {
      subscriptions.push(
        Store.data('PrinterTypes').subscribe(function (data) {
          ctrl.types = data;
        }),
        Store.data('PrinterColors').subscribe(function (data) {
          ctrl.colors = data;
        }),
        Store.data('Factories').subscribe(function (data) {
            if(data){
                ctrl.factories = data;
            }
        })
      );
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };

    ctrl.save = function () {
      $scope.addPrinterForm.$setSubmitted();
      ctrl.checkCapacity = false;
      if(($scope.addPrinterForm.is_brother.$viewValue === true && $scope.addPrinterForm.kornit_id.$viewValue === true)
          || ($scope.addPrinterForm.is_epson.$viewValue === true && $scope.addPrinterForm.kornit_id.$viewValue === true)
          || ($scope.addPrinterForm.is_brother.$viewValue === true && $scope.addPrinterForm.is_epson.$viewValue === true)
          || ($scope.addPrinterForm.is_brother.$viewValue === true && $scope.addPrinterForm.is_epson.$viewValue === true && $scope.addPrinterForm.$scope.addPrinterForm.is_epson.$viewValue === true.$viewValue === true)
      ){
          ctrl.checkPrinter = true;
      }else {
          ctrl.checkPrinter = false;
      }

      if(typeof $scope.addPrinterForm.factory_id.$viewValue == "undefined" || $scope.addPrinterForm.factory_id.$viewValue === '' || $scope.addPrinterForm.factory_id.$viewValue == null) {
        ctrl.checkFactory = true;
      }else {
        ctrl.checkFactory = false;
      }
      if(!(ctrl.printer && ctrl.printer.Printer.capacity_per_hour) ||
          !(ctrl.printer.Printer.capacity_per_hour >= 1 && ctrl.printer.Printer.capacity_per_hour <= 9999)){
          ctrl.checkCapacity = true;
      }

      if ($scope.addPrinterForm.$invalid) {
        return;
      }

        var codes = getSelectedCodes();
        var colors = getSelectedColors();
        var customers = getSelectedCustomers();
        var sizes = getSelectedSizes();
        var is_auto_sort =  ctrl.printer.Printer.is_auto_sort;
        var general_purpose_key =  ctrl.printer.Printer.planning_sort_pattern_id;
        if(is_auto_sort && !general_purpose_key){
            alert('ソートパターンを選択してください。');
            return ;
        }
        if(is_auto_sort === false){
            general_purpose_key = false;
        }
        var optionList = {
            codes : codes,
            colors : colors,
            customers : customers,
            sizes : sizes,
            is_auto_sort: is_auto_sort,
            general_purpose_key: general_purpose_key,
        };
        if(!ctrl.checkFactory && !ctrl.checkPrinter && !ctrl.checkCapacity) {
          ctrl.loadingType = 'partial';


          Printer.create(ctrl.printer.Printer,optionList).then(function (data) {
              updatePrintersList(data);
              ctrl.close();
          }, function () {
              ctrl.loadingType = 'stopped';
          });
      }
    };

    /*** helpers ***/
    function setDefault() {
      ctrl.printer = null;

      $scope.addPrinterForm.$setPristine();
      $scope.addPrinterForm.$setUntouched();
    }
    
    function updatePrintersList() {
      ctrl.onCreate();
    }
      ctrl.toggleSelect = function () {
          if (ctrl.showSelect === false) {
              ctrl.showSelect = true;
              ctrl.printer.Printer.planning_sort_pattern_id = null;
          } else {
              ctrl.showSelect = false;
          }
      }

      function getSelectedCustomers() {

          var selectedCustomers = [];

          _.forEach(ctrl.optionList.customers, function (customer) {
              if (customer.selected) {
                  selectedCustomers.push(customer.Customer.title);
              }
          });
          return selectedCustomers;

      }

      function getSelectedColors() {

          var selectedColors = [];

          _.forEach(ctrl.optionList.colors, function (color) {
              if (color.selected) {
                  selectedColors.push(color.ProductColor.title);
              }
          });
          return selectedColors;

      }

      function getSelectedSizes() {

          var selectedSizes = [];

          _.forEach(ctrl.optionList.sizes, function (size) {
              if (size.selected) {
                  selectedSizes.push(size.ProductSize.title);
              }
          });
          return selectedSizes;

      }

      function getSelectedCodes() {

          var selectedCodes = [];
          _.forEach(ctrl.optionList.codes, function (code) {
              if (code.selected) {
                  var data = {
                      code: code.ProductLinkedCode.code,
                      is_same_day: code.ProductJoin.is_same_day,
                  };
                  selectedCodes.push(data);
              }
          });
          return selectedCodes;

      }

      ctrl.selectAllCode = function () {

          ctrl.optionList.codes.forEach(function (code) {
              code.selected = !allSelectedCode;
          });

          allSelectedCode = !allSelectedCode;
      };

      ctrl.selectAllColor = function () {

          ctrl.optionList.colors.forEach(function (color) {
              color.selected = !allSelectedColor;
          });

          allSelectedColor = !allSelectedColor;
      };

      ctrl.selectAllSize = function () {

          ctrl.optionList.sizes.forEach(function (size) {
              size.selected = !allSelectedSize;
          });

          allSelectedSize = !allSelectedSize;
      };

      ctrl.selectFactory = function (factoryId) {
          ctrl.optionList.andons = [];
          Printer.getOptionAndon(factoryId).then(function(data){
              ctrl.optionList.andons = data;
          });
      };
    
  }

  angular.module('printty')

    .component('addPrinter', {
      bindings: {
        open: '=',
        onCreate: '='
      },
      controller: AddPrinterCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/add-printer.html'
    });

})();
