(function () {

    angular.module('printty')
        .component('referenceSources', {
            controller: SourcesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/sources/sources.html'
        });

    function SourcesCtrl(Auth, $state, Store, utils, Source) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.sources = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.sourceDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:source').subscribe(function () { ctrl.viewCreate(); })
            );

            getSources();

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.sources = [];

            getSources();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (source) {
            ctrl.sourceDetails.type = 'details';
            ctrl.sourceDetails.isShown = true;
            ctrl.sourceDetails.active = {
                sourceId: source.ProductLinkedSource.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.sourceDetails.type = 'add';
            ctrl.sourceDetails.isShown = true;
            ctrl.sourceDetails.active = {};
        };

        ctrl.onSourceClose = function () {
            ctrl.sourceDetails.isShown = false;
        };

        ctrl.onSourceCreate = function () {
            ctrl.reload();
        };

        ctrl.onSourceUpdate = function (source) {
            var originalSource = _.find(ctrl.sources, function (sourceItem) {
                return sourceItem.ProductLinkedSource.id == source.id;
            });

            _.extend(originalSource.ProductLinkedSource, source);
        };

        ctrl.onSourceDelete = function (sourceId) {
            _.remove(ctrl.sources, function(source) {
                return source.ProductLinkedSource.id == sourceId;
            });
        };

        /****** private ******/
        function getSources() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Source.fetchSources({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {

                    ctrl.sources = data.sources;
                    ctrl.pagination.empty = !ctrl.sources.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

    }

})();