(function() {

  function EditWarehouseCtrl($scope, $mdSidenav, $mdDialog, Quality, Store, utils, $filter, StorageActivityContent) {

    "ngInject";
    
    var ctrl = this,
        sidenav = {
          open: function () {
            $mdSidenav('edit-warehouse').open();
          },
          close: function () {
            $mdSidenav('edit-warehouse').close();
          }
        };

    ctrl.loadingType = 'full';
    
    // fetch types and colors
    ctrl.$onInit = function() {

    };
    
    ctrl.close = sidenav.close;

    ctrl.open = function (warehouse) {
      setDefault();

      var params = {id: warehouse.id};

      StorageActivityContent.detail(
          params
      ).then(function (data) {
        ctrl.warehouse = data;

        ctrl.loadingType = 'stopped';
      }, function () {
        ctrl.loadingType = 'stopped';
      });

      sidenav.open();
    };

    ctrl.edit = function () {

      if (ctrl.showMode) { // create clone
        warehouseClone = _.cloneDeep(ctrl.warehouse);
      } else { // revert changes
        ctrl.warehouse = warehouseClone;
      }

      toggleShowMode();

    };

    ctrl.update = function () {
      // do nothing if printer didn't change
      if (_.isEqual(ctrl.warehouse, warehouseClone)) {
        toggleShowMode();
        return;
      }

      ctrl.loadingType = 'partial';

      var params = {
          Warehouse : ctrl.warehouse
      };

      StorageActivityContent.update(params).then(function () {
        ctrl.loadingType = 'stopped';
        ctrl.onUpdate();
        sidenav.close();
      }, function () {
        ctrl.loadingType = 'stopped';
      });

    };

      ctrl.confirmDelete = function (ev) {

          var confirm = $mdDialog.confirm({
              template: utils.deleteTemplate('you want deleted this？'),
              controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                  $scope.cancelDelete = function () {
                      $mdDialog.cancel();
                  };
                  $scope.confirmDelete = function () {
                      $mdDialog.hide();
                  };
              }]
          }).targetEvent(ev);

          $mdDialog.show(confirm).then(deleteContent);
      };

      function deleteContent() {
          ctrl.loadingType = 'partial';

          StorageActivityContent.remove(ctrl.warehouse.id)
              .then(function () {
              ctrl.loadingType = 'stopped';
              ctrl.onUpdate();
              sidenav.close();
          }, function () {
              ctrl.loadingType = 'stopped';
          });
      }

    /*** helpers ***/
    function setDefault() {
      ctrl.warehouse = null;
      ctrl.showMode = true;
      warehouseClone = null;

      ctrl.loadingType = 'full';
    }

    function toggleShowMode() {
      ctrl.showMode = !ctrl.showMode;
    }
    
  }

  angular.module('printty')

    .component('editWarehouse', {
      bindings: {
        open: '=',
        onClose: '=',
        onCreate: '=',
        onUpdate: '=',
      },
      controller: EditWarehouseCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/warehouses/edit-warehouse.html'
    });

})();
