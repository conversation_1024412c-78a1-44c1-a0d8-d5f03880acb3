(function() {

  function AddWarehouseCtrl($scope, $mdSidenav, Quality, Store, $filter, StorageActivityContent) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        sidenav = {
          open: function () {
            $mdSidenav('add-warehouse').open();
          },
          close: function () {
            $mdSidenav('add-warehouse').close();
          }
        };

    ctrl.loadingType = 'stopped';
    
    ctrl.close = sidenav.close;

    ctrl.open = function () {
      setDefault();
      sidenav.open();
    };

    // fetch types and colors
    ctrl.$onInit = function() {
      ctrl.types = [
          {id:0, title:'IN'},
          {id:1, title:'OUT'}
      ]
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };

    ctrl.save = function () {

      // form validation
      $scope.addWarehouseForm.$setSubmitted();

      if ($scope.addWarehouseForm.$invalid) {
        return;
      }

      ctrl.loadingType = 'partial';

        StorageActivityContent.create(ctrl.warehouse)
          .then(function (data) {
            updateWarehousesList(data);
            ctrl.close();
          }, function () {
            ctrl.loadingType = 'stopped';
          });

    };

    /*** helpers ***/
    function setDefault() {
      ctrl.warehouse = null;

      ctrl.loadingType = 'stopped';
      
      $scope.addWarehouseForm.$setPristine();
      $scope.addWarehouseForm.$setUntouched();
    }

    function updateWarehousesList() {
      ctrl.onCreate();
    }

  }

  angular.module('printty')

    .component('addWarehouse', {
      bindings: {
        open: '=',
        onCreate: '='
      },
      controller: AddWarehouseCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/warehouses/add-warehouse.html'
    });

})();
