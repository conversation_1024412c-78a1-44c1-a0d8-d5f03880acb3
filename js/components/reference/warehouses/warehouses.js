(function () {

  function WarehousesCtrl(utils, Store, Auth, Quality, StorageActivityContent) {

    "ngInject";

    utils.appLoaded();

    var ctrl = this;

    ctrl.warehouses = [];

    var pagination = {
      perPage: 40,
      type_id: 0,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false
    };
    ctrl.pagination = pagination;

    var subscriptions = [];

    pagination.reload = function () {
      ctrl.warehouses = [];

        fetchWarehouse();
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 1000 );
    };

    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };

    ctrl.$onInit = function () {

      getTypes();
      fetchWarehouse();

        subscriptions.push(
            Store.event('sidebarActiveStatus')
                .subscribe(function (status) {
                    onStatusChange(status)
                })
        );

        ctrl.parentCtrl.onWarehouseCreate = ctrl.onWarehouseCreate;
    };

    ctrl.$onDestroy = function () {
        _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };


      function fetchWarehouse() {
          utils.listLoading.show()
          StorageActivityContent.fetch(
            {
              conditions_keywords: pagination.searchQuery,
              type_id: pagination.type_id
            }
          ).then(
            function (data) {
              pagination.init = false;
              pagination.searching = false;

              ctrl.warehouses = data;

              pagination.searchFailed = !ctrl.warehouses.length;

              utils.listLoading.hide();
            }
          );
      }

    ctrl.onWarehouseCreate = function () {
      pagination.reload();
    };

      ctrl.onWarehouseUpdate = function () {
          pagination.reload();
      };

    /**** helpers *****/
    function getTypes() {
        var statuses = [
            {id: 0, title: '入庫'},
            {id: 1, title: '出庫'}
        ];

        Store.data('statuses').update(statuses);
    }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }

    ctrl.sortableOptions = {
        update: function(e, ui) {
        },
        stop: function(e, ui) {
            utils.listLoading.show();
            Quality.warehouse.sort(
            {
              warehouses: ctrl.warehouses,
              type_id: pagination.type_id
            }
          ).then(
            function () {
              utils.listLoading.hide();
            }, function (){
                utils.listLoading.hide();}
          );
        }
      };

  }

  angular.module('printty')

    .component('referenceWarehouses', {
      require: {
        parentCtrl: '^^referenceComponent'
      },
      controller: WarehousesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/warehouses/warehouses.html'
    });

})();