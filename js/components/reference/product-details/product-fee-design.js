(function () {

    angular.module('printty')
        .component('productFeeDesign', {
            controller: ProductFeeDesignCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/product-details/product-fee-design.html',
            bindings: {
                loadingType: '=',
                onClose: '&',
                isShown: '<ngShow',
                type: '<',
                active: '=',
                showButton: '='
            }
        });

    function ProductFeeDesignCtrl(Product) {
        "ngInject";

        /****** variables ******/
        var ctrl = this;


        ctrl.isAdd = true;
        ctrl.showMode = true;
        ctrl.errors = [];


        /****** methods ******/
        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {
                initComponent();
            }
        };

        ctrl.close = function () {
            ctrl.onClose(ctrl.product_design_fees);
        };

        ctrl.addDesignFee = function() {
            const newFee = {
                min_quantity: null,
                max_quantity: null,
                design_fee_light_color: null,
                design_fee_dark_color: null,
            };
            ctrl.product_design_fees.push(newFee);
        };

        ctrl.feeTitle = function (){
            return {
                min_quantity : '数量開始',
                max_quantity : '数量終了',
                special_design_fee_light_color : '工賃（淡色）',
                special_design_fee_dark_color : '工賃（濃色）',
            }
        }

        ctrl.deleteDesignFee = function(item) {
            const index = ctrl.product_design_fees.indexOf(item);
            if (index > -1) {
                if (ctrl.errors[index]) {
                    ctrl.errors.splice(index, 1);
                }
                ctrl.product_design_fees.splice(index, 1);
            }
        };

        ctrl.save = function (){
            if(validate()){
                return true;
            }
            saveFeeDesign()
        }


        /******** private **********/

        function initComponent() {
            if(ctrl.isShown){
                ctrl.isAdd = true;
                ctrl.showMode = false;
                if(ctrl.active && ctrl.active.productId){
                    fetchProductFee();
                }
            }
        }

        function formatData(){
            ctrl.product_design_fees.forEach(function (item) {
                item.min_quantity = Number(item.min_quantity);
                item.max_quantity = item.max_quantity == null ? null : Number(item.max_quantity);
                item.special_design_fee_light_color = item.special_design_fee_light_color == null ? null : Number(item.special_design_fee_light_color);
                item.special_design_fee_dark_color = item.special_design_fee_dark_color == null ? null : Number(item.special_design_fee_dark_color);
            });
        }

        function fetchProductFee() {
            ctrl.product = null
            ctrl.product_type = null;
            ctrl.product_design_fees = [];
            ctrl.customers = [];
            ctrl.loadingType = 'partial';
            ctrl.errors = {};

            Product.fetchProductFee(ctrl.active.productId)
                .then(function (data) {
                    ctrl.product = data.product;
                    ctrl.product_type = data.product_type;
                    ctrl.product_design_fees = data.product_design_fees;
                    ctrl.customers = data.customers;
                    formatData()
                    stopLoading();
                })
                .catch(function () {
                    stopLoading();
                });
        }

        function saveFeeDesign(){
            ctrl.loadingType = 'partial';
            Product.saveFeeDesign(ctrl.active.productId,ctrl.product_design_fees,getSelectedCustomers())
                .then(function () {
                    stopLoading();
                })
                .catch(function () {
                    stopLoading();
                });
        }

        function getSelectedCustomers() {
            var selectedCustomers = [];
            _.forEach(ctrl.customers, function (customer) {
                if (customer.Customer.selected) {
                    selectedCustomers.push({
                        id: customer.Customer.id,
                    });
                }
            });
            return selectedCustomers;
        }

        function stopLoading() {
            ctrl.loadingType = 'stopped';
        }

        function validate(){
            ctrl.errors = [];
            var isError = false;
            var titles = ctrl.feeTitle();
            ctrl.product_design_fees.forEach(function (item, index) {
                ctrl.errors[index] = {};
                if (item.min_quantity == null) {
                    ctrl.errors[index].min_quantity = "【" + titles.min_quantity + "】必須入力です。";
                    isError = true;
                }
                if ((index < ctrl.product_design_fees.length - 1) && (item.max_quantity == null )) {
                    ctrl.errors[index].max_quantity = "【"+ titles.max_quantity +"】は必須入力です。";
                    isError = true;
                }

                if(item.min_quantity != null && (Number(item.min_quantity) <= 0 || Number(item.min_quantity) > 99999)){
                    ctrl.errors[index].min_quantity = "【" + titles.min_quantity + "】を1～99999を入力してください。";
                    isError = true;
                }
                if(item.max_quantity != null && (Number(item.max_quantity) <= 0 || Number(item.max_quantity) > 99999)){
                    ctrl.errors[index].max_quantity = "【"+ titles.max_quantity +"】を1～99999を入力してください。";
                    isError = true;
                }
                if (item.special_design_fee_light_color == null && item.special_design_fee_dark_color == null) {
                    ctrl.errors[index].design_fee_dark_light_color = "【工賃（淡色）】【工賃（濃色）】いずれか入力して下さい。";
                    isError = true;
                }
                if (item.max_quantity && item.min_quantity && ( Number(item.max_quantity) < Number(item.min_quantity) )) {
                    ctrl.errors[index].max_quantity = '数量開始<=数量終了 となるように入力して下さい。';
                    isError = true;
                }
                if (item.special_design_fee_light_color && (Number(item.special_design_fee_light_color) < 0 || Number(item.special_design_fee_light_color) > 999999)) {
                    ctrl.errors[index].special_design_fee_light_color = "【"+ titles.special_design_fee_light_color +"】を0～999999を入力してください。";
                    isError = true;
                }
                if (item.special_design_fee_dark_color && (Number(item.special_design_fee_dark_color) < 0 || Number(item.special_design_fee_dark_color) > 999999)) {
                    ctrl.errors[index].special_design_fee_dark_color = "【"+ titles.special_design_fee_dark_color +"】を0～999999を入力してください。";
                    isError = true;
                }
                if (index > 0 && item.min_quantity && Number(item.min_quantity) > 0 &&
                    Number(item.min_quantity) !== Number(ctrl.product_design_fees[index - 1].max_quantity) + 1) {
                    ctrl.errors[index].min_quantity = "【数量開始】前行の数量終了と連続する値となるように入力して下さい。";
                    isError = true;
                }


            });
            return isError
        }

    }

})();