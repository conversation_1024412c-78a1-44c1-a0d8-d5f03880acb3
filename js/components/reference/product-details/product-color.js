(function () {

  angular.module('printty')
    .component('productColorDetails', {
      controller: ProductColorDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product-color.html',
      bindings: {
        loadingType: '=',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        isShown: '<ngShow',
        type: '<',
        active: '=',
        showButton: '='
      }
    });

  function ProductColorDetailsCtrl($scope, utils, $mdDialog, ProductColor) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.color = null;
    ctrl.sides = [];
    ctrl.codes = [];
    
    ctrl.isAdd = true;
    ctrl.showMode = true;
    
    var colorForm,
        colorClone,
        loadCount = 3;

    /****** methods ******/
    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      colorForm = $scope.productColorForm;
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        colorClone = _.cloneDeep(ctrl.color.ProductColor);
        ctrl.showMode = false;
      } else {
        ctrl.color.ProductColor = colorClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      colorForm.$setSubmitted();

      if (colorForm.$invalid) return;

      if  ( _.isEqual(ctrl.color.ProductColor, colorClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      ctrl.loadingType = 'partial';

      ProductColor.update({
        ProductColor: ctrl.color.ProductColor
      })
        .then(function () {
          setFormPristine();
          updateOriginal();
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        }, function () {
          ctrl.loadingType = 'stopped';
        });

    };

    ctrl.save = function () {

      colorForm.$setSubmitted();

      if (colorForm.$invalid) return;

      ctrl.loadingType = 'partial';

      ProductColor.create({
        Product: {
          id: ctrl.active.productId
        },
        ProductColor: ctrl.color.ProductColor
      })
        .then(function (data) {

          ctrl.onCreate();

          _.extend(ctrl.color.ProductColor, {
            id: data.ProductColor.id,
            is_main: false
          });

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
          ctrl.close();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('カラーを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteColor);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };

    ////////// Link Codes //////////
    ctrl.linkCodeBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.linkCodeBlock.show = function (code) {
      this.type = 'details';

      this.activeItem = {
        colorId: ctrl.color.ProductColor.id,
        codeId: code.ProductColorLinkedCode.id
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        colorId: ctrl.color.ProductColor.id,
        codeId: null
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.linkCodeBlock.onCreate = function () {
      fetchLinkedCodes(ctrl.color.ProductColor.id);
    };

    ctrl.linkCodeBlock.onUpdate = function (codeData) {

      var originalCode = _.find(ctrl.codes, function (code) {
        return code.ProductColorLinkedCode.id == codeData.id;
      });

      originalCode.ProductColorLinkedCode = codeData;

    };

    ctrl.linkCodeBlock.onDelete = function (codeId) {
      _.remove(ctrl.codes, function (code) {
        return code.ProductColorLinkedCode.id == codeId;
      });
    };
    
    /**** sides block *****/

    function fetchSides(colorId, afterCallback) {

      ProductColor.side.list(colorId)
        .then(function (data) {
          ctrl.sides = data.sides;

          (afterCallback || _.noop)();
          
          stopLoading();
        })
        .catch(function () {
          stopLoading();
        });

    }
    
    ctrl.sideBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.sideBlock.show = function (side) {
      this.type = 'details';

      this.activeItem = {
        colorId: ctrl.color.ProductColor.id,
        sideId: side.ProductColorSide.id
      };

      this.isShown = true;
    };

    ctrl.sideBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        colorId: ctrl.color.ProductColor.id,
        sideId: null
      };

      this.isShown = true;
    };

    ctrl.sideBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.sideBlock.onCreate = function () {
      fetchSides(ctrl.color.ProductColor.id, function () {
        
        // update colors list to update images
        ctrl.onUpdate({
          color: ctrl.color.ProductColor,
          updateList: true
        });
        
      });
    };

    ctrl.sideBlock.onUpdate = function (sideData) {

      var originalSide = _.find(ctrl.sides, function (side) {
        return side.ProductColorSide.id == sideData.id;
      });

      originalSide.ProductColorSide = sideData;

      // update colors list to update images
      ctrl.onUpdate({
        color: ctrl.color.ProductColor,
        updateList: true
      });
      
    };

    ctrl.sideBlock.onDelete = function (sideId) {
      _.remove(ctrl.sides, function (side) {
        return side.ProductColorSide.id == sideId;
      });

      // update colors list to update images
      ctrl.onUpdate({
        color: ctrl.color.ProductColor,
        updateList: true
      });
    };

    ctrl.sideBlock.setAsMain = function (side) {

      ctrl.loadingType = 'partial';

      ProductColor.side.setAsMain(side.ProductColorSide.id)
        .then(
          function () {
            
            // update is_main param
            _.forEach(ctrl.sides, function (sideData) {
              sideData.ProductColorSide.is_main = (sideData === side);
            });
  
            ctrl.loadingType = 'stopped';
          }
        )
        .catch(function () {
          ctrl.loadingType = 'stopped';
        });

    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active.colorId) {
        fetchColor();
        fetchSides(ctrl.active.colorId);
        fetchLinkedCodes(ctrl.active.colorId);
      }

    }

    function fetchLinkedCodes(colorId) {

      ProductColor.linkCode.fetchCodes(colorId)
        .then(function (data) {
          ctrl.codes = data.codes;
          stopLoading();
        }, function () {
          stopLoading();
        });

    }

    function stopLoading() {
      if (loadCount) {
        loadCount--;
      }
      if (!loadCount) {
        ctrl.loadingType = 'stopped';
      }
    }

    function fetchColor() {

      ctrl.loadingType = 'partial';

      ProductColor.details(ctrl.active.colorId)
        .then(function (data) {
          ctrl.color = data;
          stopLoading();
        })
        .catch(function () {
          stopLoading();
        });
      
    }

    function updateOriginal() {
      var colorData = _.cloneDeep(ctrl.color.ProductColor);

      ctrl.onUpdate({
        color: colorData
      });
    }

    function deleteColor() {

      ctrl.loadingType = 'partial';

      ProductColor.delete(ctrl.color.ProductColor.id)
        .then(function () {

          ctrl.onDelete({
            color: ctrl.color.ProductColor
          });

          ctrl.close();

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.close();
          ctrl.loadingType = 'stopped';
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.color = null;
      ctrl.sides = [];
      ctrl.codes = [];
      
      loadCount = 3;

      ctrl.sideBlock.isShown = false;
      ctrl.sideBlock.type = 'add';

      ctrl.linkCodeBlock.isShown = false;
      ctrl.linkCodeBlock.type = 'add';

      setFormPristine();
    }

    function setFormPristine() {
      if (colorForm) {
        colorForm.$setPristine();
        colorForm.$setUntouched();
      }
    }

  }

})();