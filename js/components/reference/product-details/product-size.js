(function () {

  angular.module('printty')
    .component('productSizeDetails', {
      controller: ProductSizeDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product-size.html',
      bindings: {
        loadingType: '=',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        isShown: '<ngShow',
        type: '<',
        active: '=',
        showButton: '='
      }
    });

  function ProductSizeDetailsCtrl($scope, ProductSize, utils, $mdDialog) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.size = null;
    ctrl.codes = [];
    
    ctrl.isAdd = true;
    ctrl.showMode = true;

    var sizeForm,
        sizeClone,
        loadCount = 2;

    /****** methods ******/
    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      sizeForm = $scope.productSizeForm;
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        sizeClone = _.cloneDeep(ctrl.size);
        ctrl.showMode = false;
      } else {
        ctrl.size = sizeClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };
    function validateInput(coller_open_back, coller_open_front) {
      function isInt(value) {
        var x = parseFloat(value);
        return !isNaN(value) && (x | 0) === x;
      }

      function isValid(value) {
        return value !== '' &&
            value !== null &&
            value !== undefined &&
            value !== 'undefined';
      }

      const isValidBack = isValid(coller_open_back) && isInt(coller_open_back) && coller_open_back >= 0 && coller_open_back <= 100;
      const isValidFront = isValid(coller_open_front) && isInt(coller_open_front) && coller_open_front >= 0 && coller_open_front <= 100;

      return {
        check_back: !isValidBack,
        check_front: !isValidFront
      };
    }

    ctrl.update = function () {

      sizeForm.$setSubmitted();
      var check = validateInput(sizeForm.coller_open_back.$viewValue,sizeForm.coller_open_front.$viewValue)
      ctrl.check_back = check.check_back;
      ctrl.check_front = check.check_front;

      if (sizeForm.$invalid || ctrl.check_back || ctrl.check_front) return;

      if  ( _.isEqual(ctrl.size, sizeClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      ctrl.loadingType = 'partial';

      ProductSize.update({
        ProductSize: ctrl.size
      })
        .then(function () {
          setFormPristine();
          updateOriginal();
          ctrl.showMode = true;
          
          ctrl.loadingType = 'stopped';
        }, function () {
          ctrl.loadingType = 'stopped';
        });

    };

    ctrl.save = function () {

      sizeForm.$setSubmitted();
      var check = validateInput(sizeForm.coller_open_back.$viewValue,sizeForm.coller_open_front.$viewValue)
      ctrl.check_back = check.check_back;
      ctrl.check_front = check.check_front;

      if (sizeForm.$invalid || ctrl.check_back || ctrl.check_front) return;
      ctrl.loadingType = 'partial';

      ProductSize.create({
        Product: {
          id: ctrl.active.productId
        },
        ProductSize: ctrl.size
      })
        .then(function (data) {

          ctrl.onCreate();
          
          _.extend(ctrl.size, {
            id: data.ProductSize.id,
            is_main: false
          });

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
          ctrl.close();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('サイズを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteSize);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };

    ////////// Link Codes //////////
    ctrl.linkCodeBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.linkCodeBlock.show = function (code) {
      this.type = 'details';

      this.activeItem = {
        sizeId: ctrl.size.id,
        codeId: code.ProductSizeLinkedCode.id
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        sizeId: ctrl.size.id,
        codeId: null
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.linkCodeBlock.onCreate = function () {
      getLinkedCodes(ctrl.size.id);
    };

    ctrl.linkCodeBlock.onUpdate = function (codeData) {

      var originalCode = _.find(ctrl.codes, function (code) {
        return code.ProductSizeLinkedCode.id == codeData.id;
      });

      originalCode.ProductSizeLinkedCode = codeData;

    };

    ctrl.linkCodeBlock.onDelete = function (codeId) {
      _.remove(ctrl.codes, function (code) {
        return code.ProductSizeLinkedCode.id == codeId;
      });
    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          ctrl.check_back = false;
          ctrl.check_front = false;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
          ctrl.check_back = false;
          ctrl.check_front = false;
      }

      if (!ctrl.isAdd && ctrl.active.sizeId) {
        fetchSize();
        getLinkedCodes(ctrl.active.sizeId);
      }

    }

    function fetchSize() {
      
      ctrl.loadingType = 'partial';

      ProductSize.details(ctrl.active.sizeId)
        .then(function (data) {
          ctrl.size = data.ProductSize;
          stopLoading();
        })
        .catch(function () {
          stopLoading();
        });
    }

    function getLinkedCodes(sizeId) {

      ProductSize.linkCode.fetchCodes(sizeId)
        .then(function (data) {
          ctrl.codes = data.codes;
          stopLoading();
        }, function () {
          stopLoading();
        });

    }

    function updateOriginal() {
      var sizeData = _.cloneDeep(ctrl.size);

      ctrl.onUpdate({
        size: sizeData
      });
    }

    function deleteSize() {

      ctrl.loadingType = 'partial';

      ProductSize.delete(ctrl.size.id)
        .then(function () {

          ctrl.onDelete({
            sizeId: ctrl.size.id
          });

          ctrl.close();

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.close();
          ctrl.loadingType = 'stopped';
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.size = null;
      ctrl.codes = [];

      loadCount = 2;

      ctrl.linkCodeBlock.isShown = false;
      ctrl.linkCodeBlock.type = 'add';

      setFormPristine();
    }

    function setFormPristine() {
      if (sizeForm) {
        sizeForm.$setPristine();
        sizeForm.$setUntouched();
      }
    }
    
    function stopLoading() {
      if (loadCount) {
        loadCount--;
      }
      if (!loadCount) {
        ctrl.loadingType = 'stopped';
      }
    }
  }

})();