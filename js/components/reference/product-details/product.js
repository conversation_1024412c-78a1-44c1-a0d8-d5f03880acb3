(function() {

  angular.module('printty')

    .component('referenceProductDetails', {
      bindings: {
        isShown: '<',
        active: '<',
        type: '<',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&'
      },
      controller: EditProductCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product.html'
    });

  function EditProductCtrl($scope, $timeout, $mdSidenav, $mdDialog, Product, ProductLinkedCode, Store, utils, ProductSize, ProductColor) {

    'ngInject';

    /****** variables ******/
    var ctrl = this;

    ctrl.product = null;
    ctrl.deliveries = [];
    ctrl.sides = [];
    ctrl.sizes = [];
    ctrl.colors = [];
    ctrl.codes = [];
    ctrl.printingBases = [];
    ctrl.uvFrames = [];

    ctrl.loadingType = 'full';
    ctrl.showMode = true;
    ctrl.isAdd = true;
    ctrl.uploadFile = '';

    ctrl.sizeBlock = {};
    ctrl.colorBlock = {};
    ctrl.linkCodeBlock = {};
    ctrl.feeBlock = {};

    ctrl.vakuumTypeId;
    ctrl.uvTypeId;

    function isInt(value) {
      var x = parseFloat(value);
      return !isNaN(value) && (x | 0) === x;
    }


    var subscriptions = [],
        productForm,
        productClone,
        sidenav = {
          open: function () {
            $mdSidenav('edit-product').open();
            resetError()
            ctrl.showButton = JSON.parse(localStorage.getItem('showAddProduct'));
          },
          close: function () {
            $mdSidenav('edit-product').close();
          }
        },
        loadCount = 4;

    /****** methods ******/
    ctrl.$onInit = function() {

      subscriptions.push(
        Store.data('ProductTypes').subscribe(function (data) {
          ctrl.types = data;
          _.forEach(ctrl.types, function(type) {
            if (type.ProductType.code === 'case_vakuum') {
              ctrl.vakuumTypeId = type.ProductType.id;
            }
            if (type.ProductType.code === 'case_uv' || type.ProductType.code === 'laser') {
                ctrl.uvTypeId = type.ProductType.id;
            }
          })
        }),
        Store.data('ProductCategories').subscribe(function (data) {
          ctrl.categories = data;
        }),
        Store.data('ProductPrintingBases').subscribe(function (data) {
          ctrl.printingBases = data;
        }),
          Store.data('ProductUvFrames').subscribe(function (data) {
              ctrl.uvFrames = data;
          })
      );

        $('#imgInput').on('click',function(e){
            this.value = null;
        });

        $('#imgInput').on('change', function (e) {
            ctrl.editImage(e.target.files[0]);
        });

    };

    ctrl.$postLink = function () {

      productForm = $scope.editProductForm;

      $mdSidenav('edit-product').onClose(function () {
        $timeout(function () { ctrl.onClose(); }, 400);
      });

    };

    ctrl.$onChanges = function (changes) {

      if ( !('isShown' in changes) ) {
        return;
      }

      if (ctrl.isShown) {
        openComponent();
      } else {
        setDefault();
      }

    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      });

      Store.data('ProductItemSizes').update([]);
    };

    ctrl.close = function () {
      sidenav.close();
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        productClone = _.cloneDeep(ctrl.product);
        ctrl.showMode = false;
      } else {
        ctrl.product = productClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      // form validation
      productForm.$setSubmitted();

      var error = validate();
      if (productForm.$invalid || error) {
        return
      }

      if ( _.isEqual(ctrl.product, productClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      ctrl.loadingType = 'partial';

      Product.update(ctrl.product)
        .then(function (data) {
          updateOriginal();
          var productData = _.cloneDeep(ctrl.product);
          productData.Product.updated_at = data;
          ctrl.onUpdate({
            product: productData
          });
          setFormPristine();
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
        });

    };

    ctrl.save = function () {

      productForm.$setSubmitted();

      var error = validate();
      if (productForm.$invalid || error) {
        return
      }

      ctrl.loadingType = 'partial';

      Product.create(ctrl.product)
        .then(function (data) {

          ctrl.onCreate();

          ctrl.product.Product.id = data.Product.id;

          if (!ctrl.product.ProductPrice || !ctrl.product.ProductPrice.price) {
            ctrl.product.ProductPrice = {
              price: 0
            }
          }

          ctrl.product.sides = [];

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';

        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
          ctrl.close();
        });

    };

    ctrl.confirmDelete = function (ev) {
      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('連携設定を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteProduct);
    };

    ctrl.makeColorArr = function (hexArr) {
      if (hexArr && angular.isString(hexArr)) {
        return hexArr.replace(/ /g,'').split(',');
      }
    };

    ctrl.onTypeChange = function() {
      if (ctrl.product.Product.type_id !== ctrl.vakuumTypeId) {
        ctrl.product.Product.printing_base_id = null;
      }
      if (ctrl.product.Product.type_id !== ctrl.uvTypeId) {
          ctrl.product.Product.uv_frame = null;
      }
    }

    ////////// Product Sizes //////////

    ctrl.sizeBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.sizeBlock.show = function (size) {
      this.type = 'details';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        sizeId: size.ProductSize.id
      };

      this.isShown = true;
    };

    ctrl.sizeBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        sizeId: null
      };

      this.isShown = true;
    };

    ctrl.sizeBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.sizeBlock.onCreate = function () {
      getSizes(ctrl.product.Product.id, updateOriginal);
    };

    ctrl.sizeBlock.onUpdate = function (sizeData) {

      var originalSize = _.find(ctrl.sizes, function (size) {
        return size.ProductSize.id == sizeData.id;
      });

      originalSize.ProductSize = sizeData;

      Store.data('ProductItemSizes').update(ctrl.sizes);

    };

    ctrl.feeBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.feeBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        productId: ctrl.product.Product.id,
      };

      this.isShown = true;
    };

    ctrl.feeBlock.onClose = function () {
      this.isShown = false;
    };

    function validate(){
      var error = false;
      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.unit_price_light_color ||
          Number(ctrl.product.Product.unit_price_light_color) < 0  || Number(ctrl.product.Product.unit_price_light_color) > 9999999){
        ctrl.validationErrors.unit_price_light_color = true;
        error = true
      }else{
        ctrl.validationErrors.unit_price_light_color = false;
      }
      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.unit_price_dark_color ||
          Number(ctrl.product.Product.unit_price_dark_color) < 0  || Number(ctrl.product.Product.unit_price_dark_color) > 9999999){
        ctrl.validationErrors.unit_price_dark_color = true;
        error = true
      }else{
        ctrl.validationErrors.unit_price_dark_color = false;
      }
      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.design_fee_light_color ||
          Number(ctrl.product.Product.design_fee_light_color) < 0  || Number(ctrl.product.Product.design_fee_light_color) > 9999999){
        ctrl.validationErrors.design_fee_light_color = true;
        error = true
      }else{
        ctrl.validationErrors.design_fee_light_color = false;
      }

      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.design_fee_dark_color ||
          Number(ctrl.product.Product.design_fee_dark_color) < 0  || Number(ctrl.product.Product.design_fee_dark_color) > 9999999){
        ctrl.validationErrors.design_fee_dark_color = true;
        error = true
      }else{
        ctrl.validationErrors.design_fee_dark_color = false;
      }
      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.sublimation_image_borderline_size ||
          Number(ctrl.product.Product.sublimation_image_borderline_size) < 0  || Number(ctrl.product.Product.sublimation_image_borderline_size) >= 1000){
        ctrl.checkBorderLine = true;
        error = true
      }else{
        ctrl.checkBorderLine = false;
      }
      if(!ctrl.product ||!ctrl.product.Product || !ctrl.product.Product.set_sales_quantity ||
          Number(ctrl.product.Product.set_sales_quantity) <= 0  || Number(ctrl.product.Product.set_sales_quantity) >= 101){
        ctrl.check_set_sales_quantity = true;
        error = true
      }else{
        ctrl.check_set_sales_quantity = false;
      }
      return error;
    }

    function resetError(){
      ctrl.validationErrors = {
        unit_price_light_color: false,
        unit_price_dark_color: false,
        design_fee_light_color: false,
        design_fee_dark_color: false,
      };
      ctrl.check_set_sales_quantity = false;
      ctrl.checkBorderLine = false;
    }

    ctrl.sizeBlock.onDelete = function (sizeId) {
      _.remove(ctrl.sizes, function (size) {
        return size.ProductSize.id == sizeId;
      });

      Store.data('ProductItemSizes').update(ctrl.sizes);

      updateOriginal();
    };

    ctrl.sizeBlock.setAsMain = function (size) {

      ctrl.loadingType = 'partial';

      ProductSize.setAsMain(size.ProductSize.id).then(
        function () {
          // update is_main param
          ctrl.sizes.forEach(function (sizeData) {
            sizeData.ProductSize.is_main = (sizeData === size);
          });

          ctrl.loadingType = 'stopped';
        }, function () {
          ctrl.loadingType = 'stopped';
        }
      );

    };

    ////////// Product Colors //////////

    ctrl.colorBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.colorBlock.show = function (color) {
      this.type = 'details';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        colorId: color.ProductColor.id
      };

      this.isShown = true;
    };

    ctrl.colorBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        colorId: null
      };

      this.isShown = true;
    };

    ctrl.colorBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.colorBlock.onCreate = function () {
      getColors(ctrl.product.Product.id, updateOriginal);
    };

    ctrl.colorBlock.onUpdate = function (colorData, updateList) {

      var originalColor = _.find(ctrl.colors, function (color) {
        return color.ProductColor.id == colorData.id;
      });

      if (updateList) {

        // update top sides
        if (originalColor.ProductColor.is_main) {
          getProductDetails(ctrl.product, true);
        }

        getColors(ctrl.product.Product.id);

      } else {
        originalColor.ProductColor = colorData;
      }

    };

    ctrl.colorBlock.onDelete = function (colorData) {

      var originalColor = _.find(ctrl.colors, function (color) {
        return color.ProductColor.id == colorData.id;
      });

      // if main => update products details to get top sides
      if (originalColor.is_main) {
        getProductDetails(ctrl.product, true);
      }

      _.remove(ctrl.colors, originalColor);

      updateOriginal();
    };

    ctrl.colorBlock.setAsMain = function (color) {

      ctrl.loadingType = 'partial';

      ProductColor.setAsMain(color.ProductColor.id).then(
        function () {

          // update top images
          getProductDetails(ctrl.product, true);

          // update data about main color
          ctrl.colors.forEach(function (colorData) {
            colorData.ProductColor.is_main = (colorData === color);
          });

          ctrl.loadingType = 'stopped';

        }, function () {
          ctrl.loadingType = 'stopped';
        }
      );
    };

    ////////// Product link codes //////////
    ctrl.linkCodeBlock = {
      isShown: false,
      type: 'add',
      activeItem: null
    };

    ctrl.linkCodeBlock.show = function (code) {
      this.type = 'details';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        codeId: code.ProductLinkedCode.id
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        productId: ctrl.product.Product.id,
        codeId: null
      };

      this.isShown = true;
    };

    ctrl.linkCodeBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.linkCodeBlock.onCreate = function () {
      getLinkedCodes(ctrl.product.Product.id);
    };

    ctrl.linkCodeBlock.onUpdate = function (codeData) {

      var originalCode = _.find(ctrl.codes, function (code) {
        return code.ProductLinkedCode.id == codeData.id;
      });

      originalCode.ProductLinkedCode = codeData;

    };

    ctrl.linkCodeBlock.onDelete = function (codeId) {
      _.remove(ctrl.codes, function (code) {
        return code.ProductLinkedCode.id == codeId;
      });
    };

    /******** private **********/

    function openComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.loadingType = 'full';
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active) {
        getProductDetails(ctrl.active);
        getSizes(ctrl.active.Product.id);
        getColors(ctrl.active.Product.id);
        getLinkedCodes(ctrl.active.Product.id);
      }
      if(ctrl.isAdd){
        ctrl.loadingType = 'partial';
          Product.delivery()
              .then(function (data) {
                ctrl.deliveries = _.flatMap(data, function (delivery) {
                  return delivery.DeliverySlipItems;
                });
                ctrl.loadingType = 'stopped';
              })
              .finally(function () {
                ctrl.loadingType = 'stopped';
              });
      }

      sidenav.open();

    }

    function getProductDetails(product, toUpdateOriginal) {

      Product.details(product.Product.id)
        .then(function (data) {

          ctrl.product = data;
          ctrl.sides = _.flatMap(ctrl.product.sides, function (side) {
            return side.ProductColorSide;
          });
          ctrl.deliveries = _.flatMap(ctrl.product.delivery, function (delivery) {
            return delivery.DeliverySlipItems;
          });

          if (toUpdateOriginal) updateOriginal();

          stopLoading();
        })
        .catch(function () {
          stopLoading();
        })
    }

    function getSizes(productId, afterCallback) {

      ProductSize.list(productId).then(function (data) {
        ctrl.sizes = data.sizes;

        Store.data('ProductItemSizes').update(ctrl.sizes);

        (afterCallback || _.noop)();

        stopLoading();
      }, function () {
        stopLoading();
      });

    }

    function getColors(productId, afterCallback) {

      ProductColor.list(productId).then(function (data) {
        ctrl.colors = data.colors;

        (afterCallback || _.noop)();

        stopLoading();
      }, function () {
        stopLoading();
      });

    }

    function getLinkedCodes(productId) {

      ProductLinkedCode.fetchCodes(productId)
        .then(function (data) {
          ctrl.codes = data.codes;
          stopLoading();
        }, function () {
          stopLoading();
        });

    }

    function deleteProduct() {

      ctrl.loadingType = 'partial';

      Product.delete(ctrl.product.Product.id)
        .then(function () {

          ctrl.onDelete({
            productId: ctrl.product.Product.id
          });

          ctrl.close();
          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.close();
          ctrl.loadingType = 'stopped';
        });

    }

    function setDefault() {
      ctrl.showMode = true;

      ctrl.product = null;
      ctrl.deliveries = [];
      ctrl.sides = [];

      ctrl.sizes = [];
      ctrl.colors = [];
      ctrl.codes = [];

      ctrl.sizeBlock.isShown = ctrl.colorBlock.isShown = ctrl.linkCodeBlock.isShown = false;
      ctrl.sizeBlock.type = ctrl.colorBlock.type = ctrl.linkCodeBlock.type = 'add';
      ctrl.feeBlock.isShown = ctrl.colorBlock.isShown = ctrl.linkCodeBlock.isShown = false;
      ctrl.feeBlock.type = ctrl.colorBlock.type = ctrl.linkCodeBlock.type = 'add';

      ctrl.loadingType = 'stopped';

      loadCount = 4;

      ctrl.printingBase = null;

      Store.data('ProductItemSizes').update([]);
      setFormPristine();
    }

    function setFormPristine() {
      if (productForm) {
        productForm.$setPristine();
        productForm.$setUntouched();
      }
    }

    function stopLoading() {
      if (loadCount) {
        loadCount--;
      }
      if (!loadCount) {
        ctrl.loadingType = 'stopped';
      }
    }

    function updateOriginal() {

      var productData = _.cloneDeep(ctrl.product);

      var category = _.find(ctrl.categories, function (category) {
        return category.ProductCategory.id == ctrl.product.Product.category_id;
      });

      var type = _.find(ctrl.types, function (type) {
        return type.ProductType.id == ctrl.product.Product.type_id;
      });

      productData.Product.colors_count = ctrl.colors.length;
      productData.Product.sizes_count = ctrl.sizes.length;
      productData.Product.category = category ? category.ProductCategory.title : null;
      productData.Product.type = type ? type.ProductType.title : null;
      ctrl.onUpdate({
        product: productData
      });

    }

      ctrl.editImage = function (file) {

          if (!file) {
              $('#imgInput').click();
              return;
          }

          var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

          if ( fileType !== 'pdf') {
              alert('File.UnexpectedFormat');
              return;
          }

          ctrl.loadingType = 'partial';

          Product.uploadPdfFile(file)
              .then(function (data) {
                  ctrl.product.Product.guide_note_url = data.url;
                  ctrl.product.Product.guide_note_filename = data.filename;
                  ctrl.loadingType = 'stopped';
                  updateOriginal();

                  setFormPristine();
              })
              .catch(function () {
                  ctrl.loadingType = 'stopped';
              });

      };

  }

})();
