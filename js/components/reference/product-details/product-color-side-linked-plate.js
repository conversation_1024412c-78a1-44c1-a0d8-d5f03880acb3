(function () {

  angular.module('printty')
    .component('productColorSideLinkedPlateDetails', {
      controller: LinkedPlateDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product-color-side-linked-plate.html',
      bindings: {
        isShown: '<ngShow',
        type: '<',
        active: '<',
        
        loadingType: '=',
       
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        showButton: '='
      }
    });

  function LinkedPlateDetailsCtrl($scope, ProductColor, Store, utils, $mdDialog) {
    
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.plate = null;
    ctrl.sizes = [];
    ctrl.plates = [];

    ctrl.isAdd = true;
    ctrl.showMode = true;

    var subscriptions = [],
        plateForm,
        plateClone;

    var loader = new utils.loadCounter(
      function (type) {

        if (!type) {
          type = 'full';
        }

        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    /****** methods ******/
    ctrl.$onInit = function () {

      subscriptions.push(

        Store.data('ProductLinkedPlates').subscribe(function (plates) {
          ctrl.plates = plates;
        }),

        Store.data('ProductItemSizes').subscribe(function (sizes) {
          ctrl.sizes = sizes;
        })
        
      )
      
    };

    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      plateForm = $scope.plateForm;
    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        plateClone = _.cloneDeep(ctrl.plate);
        ctrl.showMode = false;
      } else {
        ctrl.plate = plateClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      plateForm.$setSubmitted();

      if (plateForm.$invalid) return;

      if  ( _.isEqual(ctrl.plate, plateClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      loader.start('partial');

      ProductColor.side.linkedPlates.updatePlate(ctrl.plate)
        .then(function () {

          updateOriginal();

          setFormPristine();
          ctrl.showMode = true;

          loader.stop();
        }, function () {
          loader.stop();
        });

    };

    ctrl.save = function () {

      plateForm.$setSubmitted();

      if (plateForm.$invalid) return;

      loader.start('partial');

      ProductColor.side.linkedPlates.createPlate({
        sideId: ctrl.active.sideId,
        sizeId: ctrl.plate.size_id,
        plateId: ctrl.plate.plate_id
      })
        .then(function (data) {

          ctrl.onCreate();

          ctrl.plate.id = data.ProductColorSideSizeLinkedPlate.id;

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          loader.stop();

        })
        .catch(function () {
          loader.stop();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('連携設定を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deletePlate);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active.linkedPlateId) {
        fetchPlate();
      }

    }

    function fetchPlate() {
      
      loader.start('partial');

      ProductColor.side.linkedPlates.fetchPlate(ctrl.active.linkedPlateId)
        .then(function (data) {
          ctrl.plate = data.ProductColorSideSizeLinkedPlate;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });

    }

    function updateOriginal() {
      
      var plate = _.find(ctrl.plates, function (plate) {
        return plate.Plate.id == ctrl.plate.plate_id;
      });
      
      var size = _.find(ctrl.sizes, function (size) {
        return size.ProductSize.id == ctrl.plate.size_id;
      });

      var plateData = {
        ProductColorSideSizeLinkedPlate: {
          id: ctrl.plate.id
        },
        Plate: {
          id: ctrl.plate.plate_id,
          title: plate.Plate.title
        },
        ProductSize: {
          id: ctrl.plate.size_id,
          title: size.ProductSize.title
        }
      };
      
      ctrl.onUpdate({
        plate: plateData
      });

    }

    function deletePlate() {

      loader.start('partial');

      ProductColor.side.linkedPlates.deletePlate(ctrl.plate.id)
        .then(function () {

          ctrl.onDelete({
            plateId: ctrl.plate.id
          });
          
          ctrl.close();
          
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.plate = null;

      setFormPristine();
    }

    function setFormPristine() {
      if (plateForm) {
        plateForm.$setPristine();
        plateForm.$setUntouched();
      }
    }

  }

})();