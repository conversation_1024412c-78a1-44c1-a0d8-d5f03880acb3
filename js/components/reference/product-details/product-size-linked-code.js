(function () {

  angular.module('printty')
    .component('productSizeLinkedCodeDetails', {
      controller: ProductSizeLinkedCodeDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product-linked-code.html',
      bindings: {
        loadingType: '=',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        isShown: '<ngShow',
        type: '<',
        active: '=',
        showButton: '='
      }
    });

  function ProductSizeLinkedCodeDetailsCtrl($scope, Store, utils, $mdDialog, ProductSize) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.code = null;
    ctrl.sources = [];

    ctrl.isAdd = true;
    ctrl.showMode = true;

    var subscription,
        codeForm,
        codeClone;

    /****** methods ******/
    ctrl.$onInit = function () {
      subscription = Store.data('ProductLinkedSources')
        .subscribe(function (sources) {
          ctrl.sources = sources;
        });
    };

    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      codeForm = $scope.linkedCodeForm;
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        codeClone = _.cloneDeep(ctrl.code);
        ctrl.showMode = false;
      } else {
        ctrl.code = codeClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      codeForm.$setSubmitted();

      if (codeForm.$invalid) return;

      if  ( _.isEqual(ctrl.code, codeClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      ctrl.loadingType = 'partial';

      ProductSize.linkCode.updateCode(ctrl.code)
        .then(function () {

          updateOriginal();

          setFormPristine();
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        }, function () {
          ctrl.loadingType = 'stopped';
        });

    };

    ctrl.save = function () {

      codeForm.$setSubmitted();

      if (codeForm.$invalid) return;

      ctrl.loadingType = 'partial';

      ProductSize.linkCode.createCode(ctrl.active.sizeId, ctrl.code)
        .then(function (data) {

          ctrl.onCreate();

          ctrl.code.id = data.ProductSizeLinkedCode.id;

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';

        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
          ctrl.close();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('連携設定を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteCode);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };
    
    

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active.codeId) {
        fetchCode();
      }

    }

    function fetchCode() {

      // load here
      ctrl.loadingType = 'partial';

      ProductSize.linkCode.fetchCode(ctrl.active.codeId)
        .then(function (data) {
          ctrl.code = data.ProductSizeLinkedCode;
          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
        });

    }

    function updateOriginal() {

      var codeData = _.cloneDeep(ctrl.code);

      var source = _.find(ctrl.sources, function (source) {
        return source.ProductLinkedSource.id == codeData.source_id;
      });

      codeData.source = source.ProductLinkedSource.title;

      ctrl.onUpdate({
        code: codeData
      });

    }

    function deleteCode() {

      ctrl.loadingType = 'partial';

      ProductSize.linkCode.deleteCode(ctrl.code.id)
        .then(function () {

          ctrl.onDelete({
            codeId: ctrl.code.id
          });

          ctrl.close();
          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.close();
          ctrl.loadingType = 'stopped';
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.code = null;

      setFormPristine();
    }

    function setFormPristine() {
      if (codeForm) {
        codeForm.$setPristine();
        codeForm.$setUntouched();
      }
    }

  }

})();