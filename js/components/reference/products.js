(function () {

  function ProductsCtrl(Product, utils, Store, Auth, ProductLinkedCode, Plate) {

    'ngInject';

    utils.appLoaded();

    var ctrl = this;

    var subscriptions = [];

    ctrl.products = [];
    ctrl.isDetailsShown = false;
    ctrl.detailsType = 'add';
    ctrl.activeProduct = null;

    var pagination = {
      init: true,
      perPage: 40,
      current: 0,
      type_id: null,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;

    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      7
    );

    pagination.reload = function () {

      this.init = true;
      this.end = false;
      this.busy = false;
      this.current = 0;

      ctrl.products = [];

      ctrl.infiniteProducts.numLoaded_ = 0;
      ctrl.infiniteProducts.toLoad_ = 0;
      ctrl.infiniteProducts.getItemAtIndex(1);
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 1000 );
    };

    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) {
        return;
      }

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };

    ctrl.$onInit = function () {

      getStatuses();
      getProductItems();

      subscriptions.push(
        Store.event('reference:create:product')
          .subscribe(function () {
            ctrl.viewCreate();
          }),

        Store.event('sidebarActiveStatus')
          .subscribe(function (status) {
            onStatusChange(status)
          })
      );

    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });

      Store.data('ProductTypes').update([]);
      Store.data('ProductCategories').update([]);
      Store.data('ProductLinkedSources').update([]);
      Store.data('ProductLinkedPlates').update([]);
      Store.data('ProductPrintingBases').update([]);
      Store.data('ProductUvFrames').update([]);
    };

    ctrl.infiniteProducts = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.products[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {

        if (this.toLoad_ >= index || pagination.end || pagination.busy) {
          return;
        }

        pagination.busy = true;

        if (pagination.init) {
          loader.start(!pagination.searching)
        } else {
          utils.moreItemsLoad.show()
        }

        this.toLoad_ += pagination.perPage;

        pagination.current++;

        Product.list(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            conditions_keywords: pagination.searchQuery,
            type_id: pagination.type_id
          }
        ).then(
          function (data) {
            pagination.init = false;
            pagination.searching = false;

            ctrl.infiniteProducts.numLoaded_ = ctrl.infiniteProducts.toLoad_ - pagination.perPage + data.products.length;
            ctrl.products = ctrl.products.concat(data.products);

            pagination.searchFailed = !ctrl.products.length;
            pagination.end = ctrl.products.length >= data.total_count;
            pagination.busy = false;

            loader.stop();
            utils.moreItemsLoad.hide();
          }
        );

      }
    };

    ctrl.viewDetails = function (product) {
      ctrl.detailsType = 'details';
      ctrl.activeProduct = product;
      ctrl.isDetailsShown = true;
    };

    ctrl.viewCreate = function () {
      ctrl.detailsType = 'add';
      ctrl.activeProduct = null;
      ctrl.isDetailsShown = true;
    };

    ctrl.onDetailsClose = function () {
      ctrl.isDetailsShown = false;
    };

    ctrl.onProductCreate = function () {
      pagination.reload();
    };

    ctrl.onProductUpdate = function (product) {

      var originalProduct = _.find(ctrl.products, function (productItem) {
        return productItem.Product.id == product.Product.id;
      });

      if (originalProduct) {
        _.extend(originalProduct.Product, product.Product);
        originalProduct.sides = product.sides;
      }

    };

    ctrl.onProductDelete = function (productId) {
      _.remove(ctrl.products, function(product) {
        return product.Product.id == productId;
      });
    };

    /**** helpers *****/
    function getStatuses() {
      Product.typesAll().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.ProductType;
        });

        Store.data('statuses').update(statuses);
        loader.stop();
      });
    }

    function getProductItems() {

      Product.types().then(function (data) {
        Store.data('ProductTypes').update(data.types);
        loader.stop();
      });
      Product.categories().then(function (data) {
        Store.data('ProductCategories').update(data.categories);
        loader.stop();
      });
      Product.printingBases().then(function (data) {
        Store.data('ProductPrintingBases').update(data.printingBases);
        loader.stop();
      });
      Product.uvFrames().then(function (data) {
          Store.data('ProductUvFrames').update(data.uvFrames);
          loader.stop();
      });
      ProductLinkedCode.fetchSources().then(function (data) {
        Store.data('ProductLinkedSources').update(data.sources);
        loader.stop();
      });

      Plate.fetchPlates()
        .then(function (data) {
          Store.data('ProductLinkedPlates').update(data.plates);
          loader.stop();
        });

    }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }

  }

  angular.module('printty')

    .component('referenceProducts', {
      require: {
        parentCtrl: '^^referenceComponent'
      },
      controller: ProductsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/products.html'
    });

})();