(function () {

    angular.module('printty')
        .component('referenceOrderProductCode', {
            controller: OrderProductCodeCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/order-product-code/order-product-code.html'
        });

    function OrderProductCodeCtrl(Auth, $state, Store, utils, OrderProductCodeApi, $timeout) {

        'ngInject';

        utils.appLoaded();

        var ctrl = this;

        /**** variables ******/

        ctrl.products_code = [];

        var pagination = {
            init: true,
            perPage: 40,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {
            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.products_code = [];

            ctrl.infiniteProductCode.numLoaded_ = 0;
            ctrl.infiniteProductCode.toLoad_ = 0;
            ctrl.infiniteProductCode.getItemAtIndex(1);
        };

        /* Search */
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;
            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.productCodeDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteProductCode = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.products_code[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                OrderProductCodeApi.fetchProductsCode(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.products_code.length,
                        conditions_keywords: pagination.searchQuery,
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteProductCode.numLoaded_ = ctrl.infiniteProductCode.toLoad_ - pagination.perPage + data.products_code.length;
                        ctrl.products_code = ctrl.products_code.concat(data.products_code);

                        pagination.searchFailed = !ctrl.products_code.length;
                        pagination.end = ctrl.products_code.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteProductCode.numLoaded_ = 0;
            ctrl.infiniteProductCode.toLoad_ = 0;
            ctrl.infiniteProductCode.getItemAtIndex(1);
        };

        ctrl.viewDetails = function (product_code) {
            ctrl.productCodeDetails.type = 'details';
            ctrl.productCodeDetails.isShown = true;
            ctrl.productCodeDetails.active = {
                productId: product_code.Product.id,
                colorId: product_code.ProductColorJoin.id,
                sizeId: product_code.ProductSizeJoin.id,
            };
        };

        ctrl.onProductCodeClose = function () {
            ctrl.productCodeDetails.isShown = false;
        };

        ctrl.onProductCodeCreate = function () {
            ctrl.reload();
        };

        ctrl.onProductCodeUpdate = function (product_code) {
            var originalProduct = _.find(ctrl.products_code, function (productItem) {
                return productItem.Product.id == product_code.Product.id && productItem.ProductColorJoin.id == product_code.ProductColorJoin.id && productItem.ProductSizeJoin.id == product_code.ProductSizeJoin.id;
            });

            _.extend(originalProduct, product_code);
        };

    }

})();