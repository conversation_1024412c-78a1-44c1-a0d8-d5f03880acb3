(function () {

    angular.module('printty')
        .component('orderProductCodeDetails', {
            controller: OrderProductCodeDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/order-product-code/order-product-code-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function OrderProductCodeDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, OrderProductCodeApi) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.product_code = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var productCodeForm,
            productCodeClone;

        var sidenav = {
            open: function () {
                $mdSidenav('order-product-code-details').open();
            },
            close: function () {
                $mdSidenav('order-product-code-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            productCodeForm = $scope.productCodeFrameForm;

            $mdSidenav('order-product-code-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                productCodeClone = _.cloneDeep(vm.product_code);
                vm.showMode = false;
            } else {
                vm.product_code = productCodeClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            productCodeForm.$setSubmitted();

            if (productCodeForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.product_code, productCodeClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            OrderProductCodeApi.updateSku(vm.product_code)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.productId) {
                fetchProductCode();
            }

            sidenav.open();
        }

        function fetchProductCode() {
            vm.loadingType = 'full';

            OrderProductCodeApi.fetchProductCode({
                product_id: vm.active.productId,
                color_id: vm.active.colorId,
                size_id: vm.active.sizeId,
            })
                .then(function (data) {
                    vm.product_code = data;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var productCodeData = _.cloneDeep(vm.product_code);

            vm.onUpdate({
                product_code: productCodeData
            });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.category = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (productCodeForm) {
                productCodeForm.$setPristine();
                productCodeForm.$setUntouched();
            }
        }

    }

})();