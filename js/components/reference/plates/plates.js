(function () {

  angular.module('printty')
    .component('referencePlates', {
      controller: PlatesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/plates/plates.html'
    });

  function PlatesCtrl(Auth, $state, Store, utils, Plate) {

    'ngInject';

    var ctrl = this;

    /**** variables ******/

    ctrl.plates = [];

    ctrl.pagination = {
      searchQuery: null,
      lastSearch: null,

      loading: false,
      reload: false,
      empty: false
    };

    ctrl.plateDetails = {
      isShown: false,
      type: 'add',
      active: {}
    };

    var subscriptions = [];


    /****** methods ******/
    ctrl.$onInit = function () {

      utils.listLoading.show(true);

      subscriptions.push(
        Store.event('reference:create:plate').subscribe(function () { ctrl.viewCreate(); })
      );

      getPlates();

    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function () {
      ctrl.pagination.reload = true;
      ctrl.plates = [];

      getPlates();
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };

    ctrl.viewDetails = function (plate) {
      ctrl.plateDetails.type = 'details';
      ctrl.plateDetails.isShown = true;
      ctrl.plateDetails.active = {
        plateId: plate.Plate.id
      };
    };

    ctrl.viewCreate = function () {
      ctrl.plateDetails.type = 'add';
      ctrl.plateDetails.isShown = true;
      ctrl.plateDetails.active = {};
    };

    ctrl.onPlateClose = function () {
      ctrl.plateDetails.isShown = false;
    };

    ctrl.onPlateCreate = function () {
      ctrl.reload();
    };

    ctrl.onPlateUpdate = function (plate) {
      var originalPlate = _.find(ctrl.plates, function (plateItem) {
        return plateItem.Plate.id == plate.id;
      });

      _.extend(originalPlate.Plate, plate);
    };

    ctrl.onPlateDelete = function (plateId) {
      _.remove(ctrl.plates, function(plate) {
        return plate.Plate.id == plateId;
      });
    };

    /****** private ******/
    function getPlates() {

      if (ctrl.pagination.reload) {
        utils.listLoading.show(false)
      }

      Plate.fetchPlates({
        conditions_keywords: ctrl.pagination.searchQuery
      })
        .then(function (data) {

          ctrl.plates = data.plates;
          ctrl.pagination.empty = !ctrl.plates.length;
          ctrl.pagination.reload = false;

          utils.listLoading.hide();

        });

    }

  }

})();