(function() {

  function EditPlaceCtrl($scope, $mdSidenav, $mdDialog, Place, Store, utils, $filter) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        placeClone,
        originalPlace,
        sidenav = {
          open: function () {
            $mdSidenav('edit-place').open();
          },
          close: function () {
            $mdSidenav('edit-place').close();
          }
        };

    ctrl.loadingType = 'full';
    
    // fetch types and colors
    ctrl.$onInit = function() {
      subscriptions.push(
        Store.data('PlaceTypes').subscribe(function (data) {
          ctrl.types = data;
        }),
        Store.data('Factories').subscribe(function (data) {
          ctrl.factories = data;
        })
      );
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };
    
    ctrl.close = sidenav.close;

    ctrl.open = function (place) {
      setDefault();
      
      originalPlace = place;

      // get printer details
      Place.details(place.Place.id).then(function (data) {
        ctrl.place = data;

        ctrl.loadingType = 'stopped';
      }, function () {
        ctrl.loadingType = 'stopped';
      });

      sidenav.open();
    };

    ctrl.edit = function () {
      ctrl.checkFactory = null;
      if (ctrl.showMode) { // create clone
        placeClone = _.cloneDeep(ctrl.place);
      } else { // revert changes
        ctrl.place = placeClone;
      }

      toggleShowMode();

    };

    ctrl.update = function () {

      // form validation
      $scope.editPlaceForm.$setSubmitted();
      if(typeof $scope.editPlaceForm.factory_id.$viewValue == "undefined" || $scope.editPlaceForm.factory_id.$viewValue === '' || $scope.editPlaceForm.factory_id.$viewValue == null) {
        ctrl.checkFactory = true;
      }else {
        ctrl.checkFactory = false;
      }

      if ($scope.editPlaceForm.$invalid) {
        return;
      }

      // do nothing if printer didn't change
      if (_.isEqual(ctrl.place, placeClone)) {
        toggleShowMode();
        return;
      }
      if(!ctrl.checkFactory) {
        ctrl.loadingType = 'partial';
        Place.update(ctrl.place.Place).then(function () {
          updatePlacesList();
          toggleShowMode();
          ctrl.loadingType = 'stopped';
        }, function () {
          ctrl.loadingType = 'stopped';
        });
      }
    };

    ctrl.delete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('置き場を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function() {

        ctrl.loadingType = 'partial';
        
        Place.delete(ctrl.place.Place.id).then(
          function () {
            // call onDelete event
            ctrl.onDelete(ctrl.place);
            // close sidenav
            ctrl.close();
            
          }, function () {
            ctrl.loadingType = 'stopped';
          }
        );
        
      });

    };

    /*** helpers ***/
    function setDefault() {
      ctrl.place = null;
      ctrl.showMode = true;
      originalPlace = null;
      placeClone = null;

      ctrl.loadingType = 'full';

      $scope.editPlaceForm.$setPristine();
      $scope.editPlaceForm.$setUntouched();
    }

    function toggleShowMode() {
      ctrl.showMode = !ctrl.showMode;
    }

    function updatePlacesList() {

      originalPlace.Place.title = ctrl.place.Place.title;
      originalPlace.Place.updated_at = $filter('date')(new Date, 'yyyy-MM-dd HH:mm:ss', '+0000');

      originalPlace.Place.type = _.find(ctrl.types, function (type) {
        return type.PlaceType.id == ctrl.place.Place.type_id;
      }).PlaceType.title;

      originalPlace.Place.factory = _.find(ctrl.factories, function (factory) {
        return factory.id == ctrl.place.Place.factory_id;
      }).title;
    }
    
  }

  angular.module('printty')

    .component('editPlace', {
      bindings: {
        open: '=',
        onDelete: '='
      },
      controller: EditPlaceCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/edit-place.html'
    });

})();
