(function () {

    angular.module('printty')
        .component('referenceFactoryDetails', {
            controller: FactoryDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/factories/factory-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function FactoryDetailsCtrl($scope, utils, Factory, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.factory = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var factoryForm,
            factoryClone;

        var sidenav = {
            open: function () {
                ctrl.checkFactory = null;
                $mdSidenav('factory-details').open();
            },
            close: function () {
                $mdSidenav('factory-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            factoryForm = $scope.factoryForm;

            $mdSidenav('factory-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {
            ctrl.checkFactory = null;
            if (ctrl.showMode) {
                factoryClone = _.cloneDeep(ctrl.factory);
                ctrl.showMode = false;
            } else {
                ctrl.factory = factoryClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            factoryForm.$setSubmitted();

            if(typeof factoryForm.title.$viewValue == "undefined" || factoryForm.title.$viewValue === '' || factoryForm.title.$viewValue == null) {
                ctrl.checkFactory = true;
            }else {
                ctrl.checkFactory = false;
            }

            if  ( _.isEqual(ctrl.factory, factoryClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            if(!ctrl.checkFactory) {
                ctrl.loadingType = 'partial';
                ctrl.showMode = true;
                Factory.updateFactory(ctrl.factory)
                    .then(function () {
                        updateOriginal();

                        setFormPristine();
                        ctrl.showMode = true;

                        ctrl.loadingType = 'stopped';
                    }, function () {
                        ctrl.loadingType = 'stopped';
                    });

            }
        };

        ctrl.save = function () {

            factoryForm.$setSubmitted();

            if(typeof factoryForm.title.$viewValue == "undefined" || factoryForm.title.$viewValue === '' || factoryForm.title.$viewValue == null) {
                ctrl.checkFactory = true;
            }else {
                ctrl.checkFactory = false;
            }

            if(!ctrl.checkFactory) {
                ctrl.loadingType = 'partial';
                Factory.createFactory(ctrl.factory)
                    .then(function () {
                        ctrl.onCreate();
                        ctrl.close();
                    })
                    .catch(function () {
                        ctrl.loadingType = 'stopped';
                    });
            }
        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('工場を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteFactory);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.factoryId) {
                fetchFactory();
            }

            sidenav.open();

        }

        function fetchFactory() {

            ctrl.loadingType = 'full';

            Factory.fetchFactory(ctrl.active.factoryId)
                .then(function (data) {
                    ctrl.factory = data.Factory;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var factoryData = _.cloneDeep(ctrl.factory);

            ctrl.onUpdate({
                factory: factoryData
            });

        }

        function deleteFactory() {

            ctrl.loadingType = 'partial';

            Factory.deleteFactory(ctrl.factory.id)
                .then(function () {

                    ctrl.onDelete({
                        factoryId: ctrl.factory.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.factory = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (factoryForm) {
                factoryForm.$setPristine();
                factoryForm.$setUntouched();
            }
        }

    }

})();