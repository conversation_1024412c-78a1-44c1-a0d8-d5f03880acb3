(function () {

    angular.module('printty')
        .component('referenceFactories', {
            controller: FactoriesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/factories/factories.html'
        });

    function FactoriesCtrl(Auth, $state, Store, utils, Factory) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.factories = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.factoryDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:factory').subscribe(function () { ctrl.viewCreate(); })
            );

            getFactories();

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.factories = [];

            getFactories();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (factory) {
            ctrl.factoryDetails.type = 'details';
            ctrl.factoryDetails.isShown = true;
            ctrl.factoryDetails.active = {
                factoryId: factory.Factory.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.factoryDetails.type = 'add';
            ctrl.factoryDetails.isShown = true;
            ctrl.factoryDetails.active = {};
        };

        ctrl.onFactoryClose = function () {
            ctrl.factoryDetails.isShown = false;
        };

        ctrl.onFactoryCreate = function () {
            ctrl.reload();
        };

        ctrl.onFactoryUpdate = function (factory) {
            var originalFactory = _.find(ctrl.factories, function (factoryItem) {
                return factoryItem.Factory.id == factory.id;
            });

            _.extend(originalFactory.Factory, factory);
        };

        ctrl.onFactoryDelete = function (factoryId) {
            _.remove(ctrl.factories, function(factory) {
                return factory.Factory.id == factoryId;
            });
        };

        /****** private ******/
        function getFactories() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Factory.fetchFactories({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.factories = data.factories;
                    ctrl.pagination.empty = !ctrl.factories.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

    }

})();