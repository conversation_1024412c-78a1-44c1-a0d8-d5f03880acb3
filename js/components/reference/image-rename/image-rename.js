(function () {

    angular.module('printty')
        .component('referenceImageRename', {
            controller: ImageRenameCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/image-rename/image-rename.html'
        });

    function ImageRenameCtrl(Auth, $state, Store, utils, ImageName, $timeout) {

        'ngInject';

        utils.appLoaded();

        var ctrl = this;

        /**** variables ******/

        ctrl.images_name = [];

        var pagination = {
            init: true,
            perPage: 40,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {
            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.images_name = [];

            ctrl.infiniteImageName.numLoaded_ = 0;
            ctrl.infiniteImageName.toLoad_ = 0;
            ctrl.infiniteImageName.getItemAtIndex(1);
        };

        /* Search */
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;
            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.imageNameDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteImageName = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.images_name[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                ImageName.fetchImagesSizeName(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.images_name.length,
                        conditions_keywords: pagination.searchQuery,
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteImageName.numLoaded_ = ctrl.infiniteImageName.toLoad_ - pagination.perPage + data.images_name.length;
                        ctrl.images_name = ctrl.images_name.concat(data.images_name);

                        pagination.searchFailed = !ctrl.images_name.length;
                        pagination.end = ctrl.images_name.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteImageName.numLoaded_ = 0;
            ctrl.infiniteImageName.toLoad_ = 0;
            ctrl.infiniteImageName.getItemAtIndex(1);
        };

        ctrl.viewDetails = function (image_name) {
            ctrl.imageNameDetails.type = 'details';
            ctrl.imageNameDetails.isShown = true;
            ctrl.imageNameDetails.active = {
                imageNameId: image_name.id
            };
        };

        ctrl.onImageNameClose = function () {
            ctrl.imageNameDetails.isShown = false;
        };

        ctrl.onImageNameCreate = function () {
            ctrl.reload();
        };

        ctrl.onImageNameUpdate = function (image_name) {
            var originalImageName = _.find(ctrl.images_name, function (imageNameItem) {
                return imageNameItem.id == image_name.id;
            });

            _.extend(originalImageName, image_name);
        };

        ctrl.onImageNameDelete = function (imageNameId) {
            _.remove(ctrl.images_name, function(image_name) {
                return image_name.id == imageNameId;
            });
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();