(function () {

    angular.module('printty')
        .component('referenceImageRenameDetails', {
            controller: ImageRenameDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/image-rename/image-rename-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ImageRenameDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, ImageName) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.image_name = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var imageNameForm,
            imageNameClone;

        var sidenav = {
            open: function () {
                $mdSidenav('reference-image-rename-details').open();
            },
            close: function () {
                $mdSidenav('reference-image-rename-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            imageNameForm = $scope.imageRenameFrameForm;

            $mdSidenav('reference-image-rename-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                imageNameClone = _.cloneDeep(vm.image_name);
                vm.showMode = false;
            } else {
                vm.image_name = imageNameClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            imageNameForm.$setSubmitted();

            if (imageNameForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.image_name, imageNameClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            ImageName.updateImageSizeName(vm.image_name)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.imageNameId) {
                fetchImageSizeName();
            }

            sidenav.open();
        }

        function fetchImageSizeName() {
            vm.loadingType = 'full';

            ImageName.fetchImageSizeName(vm.active.imageNameId)
                .then(function (data) {
                    vm.image_name = data.ProductSize;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var imageNameData = _.cloneDeep(vm.image_name);

            vm.onUpdate({
                image_name: imageNameData
            });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.category = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (imageNameForm) {
                imageNameForm.$setPristine();
                imageNameForm.$setUntouched();
            }
        }

    }

})();