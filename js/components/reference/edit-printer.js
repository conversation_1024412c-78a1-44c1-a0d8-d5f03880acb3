(function() {

  function EditPrinterCtrl($scope, $mdSidenav, $mdDialog, Printer, utils, Store, $filter) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        printerClone,
        originalPrinter,
        sidenav = {
           open: function () {
             ctrl.checkPrinter = false;
             $mdSidenav('edit-printer').open();
           },
           close: function () {
             $mdSidenav('edit-printer').close();
           }
         },
        allSelectedCode = false,
        allSelectedColor = false;
        allSelectedSize = false;

    ctrl.loadingType = 'full';

    ctrl.close = sidenav.close;

    ctrl.open = function (printer) {

      setDefault();
      ctrl.checkCapacity = false;

      originalPrinter = printer;
      
      // get printer details
      Printer.details(printer.Printer.id).then(function (data) {
        ctrl.printer = data.printer;
        ctrl.optionList = data.printer.optionList;
        ctrl.remarks = data.printer.GeneralPurpose;
        ctrl.showSelect = true;
        ctrl.loadingType = 'stopped';
      }, function () {
        ctrl.loadingType = 'stopped';
      });
      
      sidenav.open();
    };
    
    // fetch types and colors
    ctrl.$onInit = function() {
      subscriptions.push(
        Store.data('PrinterTypes').subscribe(function (data) {
          ctrl.types = data;
        }),
        Store.data('PrinterColors').subscribe(function (data) {
          ctrl.colors = data;
        }),
       Store.data('Factories').subscribe(function (data) {
           if(data){
               ctrl.factories = data;
           }
      })
      );
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };
    
    ctrl.edit = function () {
        ctrl.checkFactory = null;
      if (ctrl.showMode) { // create clone
        printerClone = _.cloneDeep(ctrl.printer);
        Printer.getOptionAndon(ctrl.printer.Printer.factory_id).then(function(data){
          ctrl.optionList.andons = data;
          printerClone.optionList.andons = data;
        });
      } else { // revert changes
        ctrl.printer = printerClone;
      }

      toggleShowMode();
      
    };
    ctrl.toggleSelect = function (){
        if(!ctrl.printer.Printer.is_auto_sort){
            ctrl.showSelect = false;
        }else{
            ctrl.showSelect = true;
            ctrl.printer.Printer.planning_sort_pattern_id = null;
        }
    }
    
    ctrl.update = function () {
        $scope.editPrinterForm.$setSubmitted();
        ctrl.checkCapacity = false;
        if(($scope.editPrinterForm.is_brother.$viewValue === true && $scope.editPrinterForm.kornit_id.$viewValue === true)
            || ($scope.editPrinterForm.is_epson.$viewValue === true && $scope.editPrinterForm.kornit_id.$viewValue === true)
            || ($scope.editPrinterForm.is_brother.$viewValue === true && $scope.editPrinterForm.is_epson.$viewValue === true)
            || ($scope.editPrinterForm.is_brother.$viewValue === true && $scope.editPrinterForm.is_epson.$viewValue === true && $scope.editPrinterForm.$scope.editPrinterForm.is_epson.$viewValue === true)
        ){
            ctrl.checkPrinter = true;
        }else {
            ctrl.checkPrinter = false;
        }

        if(typeof $scope.editPrinterForm.factory_id.$viewValue == "undefined" || $scope.editPrinterForm.factory_id.$viewValue === '' || $scope.editPrinterForm.factory_id.$viewValue == null) {
            ctrl.checkFactory = true;
        }else {
            ctrl.checkFactory = false;
        }
        if(!(ctrl.printer && ctrl.printer.Printer.capacity_per_hour) ||
            !(ctrl.printer.Printer.capacity_per_hour >= 1 && ctrl.printer.Printer.capacity_per_hour <= 9999)){
            ctrl.checkCapacity = true;
        }
        console.log(ctrl.printer.Printer.capacity_per_hour >= 1 && ctrl.printer.Printer.capacity_per_hour <= 9999);

      if ($scope.editPrinterForm.$invalid) {
        return;
      }

      // do nothing if printer didn't change
      if (_.isEqual(ctrl.printer, printerClone)) {
        toggleShowMode();
        return;
      }

      var codes = getSelectedCodes();
      var colors = getSelectedColors();
      var sizes = getSelectedSizes();
      var customers = getSelectedCustomers();
      var is_auto_sort =  ctrl.printer.Printer.is_auto_sort;
      var general_purpose_key =  ctrl.printer.Printer.planning_sort_pattern_id;
      if(is_auto_sort && !general_purpose_key){
          alert('ソートパターンを選択してください。');
          return ;
      }
      if(is_auto_sort === false){
          general_purpose_key = false;
      }

      var optionList = {
          codes : codes,
          colors : colors,
          customers : customers,
          sizes : sizes,
          is_auto_sort: is_auto_sort,
          general_purpose_key: general_purpose_key,
      };

      if(!ctrl.checkFactory && !ctrl.checkPrinter && !ctrl.checkCapacity) {
          ctrl.loadingType = 'partial';
          ctrl.showMode = true;
          Printer.update(ctrl.printer.Printer,optionList).then(function (data) {
              updatePrintersList(data);
              ctrl.showMode = false;
              toggleShowMode();
              if(is_auto_sort === false){
                  ctrl.printer.Printer.planning_sort_pattern_id = null;
              }
              ctrl.loadingType = 'stopped';
          }, function () {
              ctrl.loadingType = 'stopped';
          });
      }
    };

      function getSelectedCustomers() {

          var selectedCustomers = [];

          _.forEach(ctrl.optionList.customers, function (customer) {
              if (customer.selected) {
                  selectedCustomers.push(customer.Customer.title);
              }
          });
          return selectedCustomers;

      }

      function getSelectedColors() {

          var selectedColors = [];

          _.forEach(ctrl.optionList.colors, function (color) {
              if (color.selected) {
                  selectedColors.push(color.ProductColor.title);
              }
          });
          return selectedColors;

      }

      function getSelectedSizes() {

          var selectedSizes = [];

          _.forEach(ctrl.optionList.sizes, function (size) {
              if (size.selected) {
                  selectedSizes.push(size.ProductSize.title);
              }
          });
          return selectedSizes;

      }

      function getSelectedCodes() {

          var selectedCodes = [];

          _.forEach(ctrl.optionList.codes, function (code) {
              if (code.selected) {
                  var data = {
                      code: code.ProductLinkedCode.code,
                      is_same_day: code.ProductJoin.is_same_day,
                  };
                  selectedCodes.push(data);
              }
          });
          return selectedCodes;

      }

      ctrl.selectAllCode = function () {

          ctrl.optionList.codes.forEach(function (code) {
              code.selected = !allSelectedCode;
          });

          allSelectedCode = !allSelectedCode;
      };

      ctrl.selectAllColor = function () {

          ctrl.optionList.colors.forEach(function (color) {
              color.selected = !allSelectedColor;
          });

          allSelectedColor = !allSelectedColor;
      };

      ctrl.selectAllSize = function () {

          ctrl.optionList.sizes.forEach(function (size) {
              size.selected = !allSelectedSize;
          });

          allSelectedSize = !allSelectedSize;
      };

      ctrl.selectFactory = function (factoryId) {
          ctrl.optionList.andons = [];
          Printer.getOptionAndon(factoryId).then(function(data){
              ctrl.optionList.andons = data;
          });
      };

    ctrl.delete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('プリンターを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function() {

        ctrl.loadingType = 'partial';
        
        Printer.delete(ctrl.printer.Printer.id).then(
          function () {
            // call onDelete event
            ctrl.onDelete(ctrl.printer);
            // close sidenav
            ctrl.close();
          }, function () {
            ctrl.loadingType = 'stopped';
          }
        );
        
      });
      
    };
    
    /*** helpers ***/
    function setDefault() {
      ctrl.printer = null;
      ctrl.showMode = true;
      originalPrinter = null;
      printerClone = null;

      ctrl.loadingType = 'full';

      $scope.editPrinterForm.$setPristine();
      $scope.editPrinterForm.$setUntouched();
    }

    function toggleShowMode() {
      ctrl.showMode = !ctrl.showMode;
      if(ctrl.printer.Printer.is_auto_sort && !ctrl.showMode){
          ctrl.showSelect = false;
      }else{
          ctrl.showSelect = true;
      }
    }
    
    function updatePrintersList(data) {

      originalPrinter.Printer.title = ctrl.printer.Printer.title;
      originalPrinter.Printer.capacity = ctrl.printer.Printer.capacity;
      originalPrinter.Printer.updated_at = data;

      originalPrinter.Printer.type = _.find(ctrl.types, function (type) {
        return type.PrinterType.id == ctrl.printer.Printer.type_id;
      }).PrinterType.title;
      
      originalPrinter.Printer.color = _.find(ctrl.colors, function (color) {
        return color.PrinterColor.id == ctrl.printer.Printer.color_id;
      }).PrinterColor.title;

      originalPrinter.Printer.factory = _.find(ctrl.factories, function (factory) {
        return factory.id == ctrl.printer.Printer.factory_id;
      }).title;
      
      Printer.extractColorsFromPrinter(originalPrinter);
      
    }
    
  }

  angular.module('printty')

    .component('editPrinter', {
      bindings: {
        open: '=',
        onDelete: '='
      },
      controller: EditPrinterCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/edit-printer.html'
    });

})();
