(function () {

    angular.module('printty')
        .component('referenceOptionItemsDetails', {
            controller: OptionItemsDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/option-items/option-items-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function OptionItemsDetailsCtrl($scope, utils, OptionItems, $mdDialog, $mdSidenav, $timeout,Side) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.option_item = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';
        ctrl.error = {
            title: '',
            code: '',
            size_title: '',
            color_title: '',
            material: '',
            description: '',
            unit_price: '',
            image_url: ''
        }
        ctrl.uploadImg = '';

        var optionItemForm,
            optionItemClone;

        var sidenav = {
            open: function () {
                $mdSidenav('option-item-detail').open();
                ctrl.hasError = false;
                ctrl.error = {
                    title: '',
                    code: '',
                    size_title: '',
                    color_title: '',
                    material: '',
                    description: '',
                    unit_price: '',
                    image_url: ''
                }
            },
            close: function () {
                $mdSidenav('option-item-detail').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            optionItemForm = $scope.optionItemForm;

            $mdSidenav('option-item-detail').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {
            if (ctrl.showMode) {
                optionItemClone = _.cloneDeep(ctrl.option_item);
                ctrl.showMode = false;
            } else {
                ctrl.option_item = optionItemClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {
            optionItemForm.$setSubmitted();
            if  ( _.isEqual(ctrl.option_item, optionItemClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }
            ctrl.hasError =  validateOptionItem();

            if(!ctrl.hasError) {
                ctrl.loadingType = 'partial';
                ctrl.showMode = true;
                OptionItems.updateOptionItem(ctrl.option_item)
                    .then(function (data) {
                        ctrl.option_item = data.option_item;
                        updateOriginal();

                        setFormPristine();
                        ctrl.showMode = true;

                        ctrl.loadingType = 'stopped';
                    }, function () {
                        ctrl.loadingType = 'stopped';
                    });

            }
        };

        ctrl.save = function () {

            optionItemForm.$setSubmitted();
            ctrl.hasError =  validateOptionItem();
            if(!ctrl.hasError) {
                ctrl.loadingType = 'partial';
                OptionItems.createOptionItem(ctrl.option_item)
                    .then(function () {
                        ctrl.onCreate();
                        ctrl.close();
                    })
                    .catch(function () {
                        ctrl.loadingType = 'stopped';
                    });
            }
        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('オプションアイテムを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteOptionItem);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.optionItemId) {
                fetchOptionItem();
            }
            $('#imgInput').on('click',function(e){
                this.value = null;
            });

            $('#imgInput').off('change').on('change', function (e) {
                ctrl.editImage(e.target.files[0]);
            });

            sidenav.open();
        }

        function fetchOptionItem() {

            ctrl.loadingType = 'full';

            OptionItems.fetchOptionItem(ctrl.active.optionItemId)
                .then(function (data) {
                    ctrl.option_item = data.option_item;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {
            var optionItemData = _.cloneDeep(ctrl.option_item);
            ctrl.onUpdate({
                option_item: optionItemData
            });
        }

        function deleteOptionItem() {

            ctrl.loadingType = 'partial';

            OptionItems.deleteOptionItem(ctrl.option_item.id)
                .then(function () {

                    ctrl.onDelete({
                        optionItemId: ctrl.option_item.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.option_item = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (optionItemForm) {
                optionItemForm.$setPristine();
                optionItemForm.$setUntouched();
            }
        }

        function validateOptionItem() {
            var tempError = false;
            clearError();
            if(!ctrl.option_item || !ctrl.option_item.title) {
                ctrl.error.title = '【タイトル】入力して下さい。';
                tempError = true;
            }

            if(!ctrl.option_item || !ctrl.option_item.code) {
                ctrl.error.code = '【コード】入力して下さい。';
                tempError = true;
            }

            if(!ctrl.option_item ||!ctrl.option_item.size_title) {
                ctrl.error.size_title = '【サイズ】入力して下さい。';
                tempError = true;
            }

            if(!ctrl.option_item || !ctrl.option_item.color_title) {
                ctrl.error.color_title = '【カラー】入力して下さい。';
                tempError = true;
            }

            if (!ctrl.option_item || ctrl.option_item.unit_price == null || ctrl.option_item.unit_price == undefined) {
                ctrl.error.unit_price = '【単価】入力して下さい。';
                tempError = true;
            }


            if(ctrl.option_item && ctrl.option_item.unit_price && (Number(ctrl.option_item.unit_price) < 0 || Number(ctrl.option_item.unit_price) > 9999999)) {
                ctrl.error.unit_price = '【単価】0～9999999を入力してください。';
                tempError = true;
            }

            return tempError;
        }

        function clearError() {
            ctrl.error = {
                title: '',
                code: '',
                size_title: '',
                color_title: '',
                material: '',
                description: '',
                unit_price: '',
                image_url: ''
            }
        }

        ctrl.editImage = function (file) {
            if (!file) {
                $('#imgInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'png' && fileType !== 'jpg' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            ctrl.loadingType = 'partial';

            var datUpload = {
                img: file,
                id : ctrl.option_item.id
            };
            Side.uploadImgCustom(datUpload,'/option-item/image')
                .then(function (data) {
                    ctrl.option_item.image_url = data.data_update.image_url;
                    ctrl.option_item.updated_at = data.data_update.updated_at;
                    ctrl.loadingType = 'stopped';
                    updateOriginal();

                    setFormPristine();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

    }

})();