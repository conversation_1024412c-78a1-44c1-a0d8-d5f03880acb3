(function () {

    angular.module('printty')
        .component('referenceOptionItems', {
            controller: OptionItemsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/option-items/option-items.html'
        });

    function OptionItemsCtrl(Auth, $state, Store, utils, OptionItems) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.option_items = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.optionItemsDetail = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:option-item').subscribe(function () { ctrl.viewCreate(); })
            );

            getOptionItems();

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.option_items = [];

            getOptionItems();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (option_item) {
            ctrl.optionItemsDetail.type = 'details';
            ctrl.optionItemsDetail.isShown = true;
            ctrl.optionItemsDetail.active = {
                optionItemId: option_item.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.optionItemsDetail.type = 'add';
            ctrl.optionItemsDetail.isShown = true;
            ctrl.optionItemsDetail.active = {};
        };

        ctrl.onOptionItemClose = function () {
            ctrl.optionItemsDetail.isShown = false;
        };

        ctrl.onOptionItemCreate = function () {
            ctrl.reload();
        };

        ctrl.onOptionItemUpdate = function (option_item) {
            var originalOptionItem = _.find(ctrl.option_items, function (optionItem) {
                return option_item.id == optionItem.id;
            });

            _.extend(originalOptionItem, option_item);
        };

        ctrl.onOptionItemDelete = function (optionItemId) {
            _.remove(ctrl.option_items, function(option_item) {
                return option_item.id == optionItemId;
            });
        };

        /****** private ******/
        function getOptionItems() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            OptionItems.fetchOptionItems({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.option_items = data.option_items;
                    ctrl.pagination.empty = !ctrl.option_items.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

    }

})();