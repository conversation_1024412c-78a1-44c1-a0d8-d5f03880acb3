(function() {

  function AddReasonCtrl($scope, $mdSidenav, Quality, Store, $filter) {

    "ngInject";
    
    var ctrl = this,
        subscriptions = [],
        sidenav = {
          open: function () {
            $mdSidenav('add-reason').open();
          },
          close: function () {
            $mdSidenav('add-reason').close();
          }
        };

    ctrl.loadingType = 'stopped';
    
    ctrl.close = sidenav.close;

    ctrl.open = function () {
      setDefault();
      sidenav.open();
    };

    // fetch types and colors
    ctrl.$onInit = function() {
      ctrl.types = [
          {id:1, title:'シングルタスク'},
          {id:4, title:'グループタスク'}
      ]
    };

    ctrl.$onDestroy = function () {
      subscriptions.forEach(function (subscription) {
        subscription.unsubscribe();
      })
    };

    ctrl.save = function () {

      // form validation
      $scope.addReasonForm.$setSubmitted();

      if ($scope.addReasonForm.$invalid) {
        return;
      }

      ctrl.loadingType = 'partial';

      var params = {
          KenpinStatus : ctrl.reason.KenpinStatus
      };

      Quality.reason.create(params).then(function (data) {
        updateReasonsList(data);
        ctrl.close();
      }, function () {
        ctrl.loadingType = 'stopped';
      });

    };

    /*** helpers ***/
    function setDefault() {
      ctrl.reason = null;

      ctrl.loadingType = 'stopped';
      
      $scope.addReasonForm.$setPristine();
      $scope.addReasonForm.$setUntouched();
    }

    function updateReasonsList() {
      ctrl.onCreate();
    }

  }

  angular.module('printty')

    .component('addReason', {
      bindings: {
        open: '=',
        onCreate: '='
      },
      controller: AddReasonCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/reasons/add-reason.html'
    });

})();
