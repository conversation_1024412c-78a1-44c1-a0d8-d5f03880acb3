(function() {

  function EditReasonCtrl($scope, $mdSidenav, $mdDialog, Quality, Store, utils, $filter) {

    "ngInject";
    
    var ctrl = this,
        sidenav = {
          open: function () {
            $mdSidenav('edit-reason').open();
          },
          close: function () {
            $mdSidenav('edit-reason').close();
          }
        };

    ctrl.loadingType = 'full';
    
    // fetch types and colors
    ctrl.$onInit = function() {

    };
    
    ctrl.close = sidenav.close;

    ctrl.open = function (reason) {
      setDefault();

      var params = {id:reason.KenpinStatus.id};

      // get printer details
      Quality.reason.detail(params).then(function (data) {
        ctrl.reason = data;

        ctrl.loadingType = 'stopped';
      }, function () {
        ctrl.loadingType = 'stopped';
      });

      sidenav.open();
    };

    ctrl.edit = function () {

      if (ctrl.showMode) { // create clone
        reasonClone = _.cloneDeep(ctrl.reason);
      } else { // revert changes
        ctrl.reason = reasonClone;
      }

      toggleShowMode();

    };

    ctrl.update = function () {
      // do nothing if printer didn't change
      if (_.isEqual(ctrl.reason, reasonClone)) {
        toggleShowMode();
        return;
      }

      ctrl.loadingType = 'partial';

      var params = {
          KenpinStatus : ctrl.reason.KenpinStatus
      };
      
      Quality.reason.update(params).then(function () {
        ctrl.loadingType = 'stopped';
        ctrl.onUpdate();
        sidenav.close();
      }, function () {
        ctrl.loadingType = 'stopped';
      });

    };

    /*** helpers ***/
    function setDefault() {
      ctrl.reason = null;
      ctrl.showMode = true;
      reasonClone = null;

      ctrl.loadingType = 'full';
    }

    function toggleShowMode() {
      ctrl.showMode = !ctrl.showMode;
    }
    
  }

  angular.module('printty')

    .component('editReason', {
      bindings: {
        open: '=',
        onUpdate: '='
      },
      controller: EditReasonCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/reasons/edit-reason.html'
    });

})();
