(function () {

  function ReasonsCtrl(utils, Store, Auth, Quality) {

    "ngInject";

    utils.appLoaded();

    var ctrl = this;

    ctrl.reasons = [];

    var pagination = {
      perPage: 40,
      type_id: 0,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false
    };
    ctrl.pagination = pagination;

    var subscriptions = [];

    pagination.reload = function () {
      ctrl.reasons = [];

        fetchReason();
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 1000 );
    };

    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };

    ctrl.$onInit = function () {

      getTypes();
      fetchReason();
        subscriptions.push(Store.event('sidebarActiveStatus')
        .subscribe(function (status) {
          onStatusChange(status)
        }));

        ctrl.parentCtrl.onReasonCreate = ctrl.onReasonCreate;
    };

    ctrl.$onDestroy = function () {
        _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

      function fetchReason() {
          utils.listLoading.show()
        Quality.reason.fetch(
        {
          conditions_keywords: pagination.searchQuery,
          type_id: pagination.type_id
        }
      ).then(
        function (data) {
          pagination.init = false;
          pagination.searching = false;

          ctrl.reasons = data;

          pagination.searchFailed = !ctrl.reasons.length;

          utils.listLoading.hide();
        }
      );
      }

    ctrl.onReasonCreate = function () {
      pagination.reload();
    };

      ctrl.onReasonUpdate = function () {
          pagination.reload();
      };

    /**** helpers *****/
    function getTypes() {
        var statuses = [
            {id: 0, title: 'シングルタスク'},
            {id: 1, title: 'グループタスク'}
        ];

        Store.data('statuses').update(statuses);
    }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }

    ctrl.sortableOptions = {
        update: function(e, ui) {

        },
        stop: function(e, ui) {
            utils.listLoading.show();
            Quality.reason.sort(
            {
              reasons: ctrl.reasons,
              type_id: pagination.type_id
            }
          ).then(
            function () {
              utils.listLoading.hide();
            }, function (){
                utils.listLoading.hide();}
          );
        }
      };

  }

  angular.module('printty')

    .component('referenceReasons', {
      require: {
        parentCtrl: '^^referenceComponent'
      },
      controller: ReasonsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/reasons/reasons.html'
    });

})();