(function () {

    angular.module('printty')
        .component('referenceAndon', {
            controller: AndonCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/andon/andon.html'
        });

    function AndonCtrl(Auth, $state, Store, utils, AndonApi, $timeout) {
        'ngInject';

        var ctrl = this;

        /**** variables ******/
        ctrl.andon = null;
        ctrl.andons = [];

        ctrl.pagination = {
            perPage: 40,
            current: 0,

            role_id: null,

            searchQuery: null,
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,
            disabled: false
        };

        ctrl.andonDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];

        var loader = new utils.loadCounter(
            function (isBlocking) {
                utils.listLoading.show(isBlocking);
            },
            function () {
                utils.listLoading.hide();
            },
            4
        );

        /****** methods ******/

        ctrl.infiniteAndons = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.andons[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                getFrames(index);
            }
        };

        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:andon').subscribe(function () { ctrl.viewCreate(); })
            );

            fetchOptions();

        }
        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.reload = true;
            ctrl.pagination.current = 0;
            ctrl.pagination.disabled = false;

            ctrl.andons = [];

            ctrl.infiniteAndons.numLoaded_ = 0;
            ctrl.infiniteAndons.toLoad_ = 0;
            ctrl.infiniteAndons.getItemAtIndex(1);
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (andon) {
            ctrl.andonDetails.type = 'details';
            ctrl.andonDetails.isShown = true;
            ctrl.andonDetails.active = {
                andonId: andon.id,
            };
        };

        ctrl.viewCreate = function () {
            ctrl.andonDetails.type = 'add';
            ctrl.andonDetails.isShown = true;
            ctrl.andonDetails.active = {};
        };

        ctrl.onAndonClose = function () {
            ctrl.andonDetails.isShown = false;
        };

        ctrl.onAndonCreate = function () {
            ctrl.reload();
        };

        ctrl.onAndonUpdate = function (andon) {
            var originalAndon = _.find(ctrl.andons, function (andonItem) {
                return andonItem.id == andon.id;
            });

            _.extend(originalAndon, andon);
        };

        ctrl.onAndonDelete = function (andonId) {
            _.remove(ctrl.andons, function(andon) {
                return andon.id == andonId;
            });
        };

        /****** private ******/

        function getFrames(index) {
            if (ctrl.infiniteAndons.toLoad_ >= index || ctrl.pagination.disabled) return;

            ctrl.pagination.disabled = true;

            if (ctrl.pagination.reload) {
                $timeout(function () {
                    ctrl.pagination.loading = false;
                    loader.start(false);
                });
            } else {
                $timeout(function () {
                    ctrl.pagination.loading = true;
                });
            }

            ctrl.infiniteAndons.toLoad_ += ctrl.pagination.perPage;

            AndonApi.fetchAndons(
                {
                    paging_size: ctrl.pagination.perPage,
                    paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
                    conditions_keywords: ctrl.pagination.searchQuery
                }
            ).then(
                function (data) {
                    ctrl.pagination.init = false;
                    ctrl.pagination.searching = false;

                    ctrl.infiniteAndons.numLoaded_ = ctrl.infiniteAndons.toLoad_ - ctrl.pagination.perPage + data.andons.length;
                    ctrl.andons = ctrl.andons.concat(data.andons);

                    ctrl.pagination.searchFailed = !ctrl.andons.length;
                    ctrl.pagination.empty = !ctrl.andons.length;
                    ctrl.pagination.end = ctrl.andons.length >= data.total_count;
                    ctrl.pagination.busy = false;

                    utils.listLoading.hide();
                    utils.moreItemsLoad.hide();

                }
            );

        }

        function fetchOptions() {

            AndonApi.fetchOptions().then(function (data) {
                Store.data('GeneralPurpose').update(data);
                loader.stop();
            });

        }

    }

})();