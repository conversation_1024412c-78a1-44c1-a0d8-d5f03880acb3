(function () {

    angular.module('printty')
        .component('adminImageError', {
            controller: ImageErrorCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/admin/image-error/image-error.html'
        });

    function ImageErrorCtrl(Store, utils, ImageError, $timeout) {

        'ngInject';

        var vm = this;

        /**** variables ******/
        vm.imageError = [];

        vm.pagination = {
            perPage: 40,

            searchQuery: null,
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,
            disabled: false
        };

        vm.iamgeErrorDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        vm.infiniteImageErrors = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {
                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return vm.imageError[index];
            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                fetchImageError(index);
            }
        };

        vm.firstDate = 'date';

        var subscriptions = [];

        /****** methods ******/
        vm.$onInit = function () {
            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('admin:create:error-log').subscribe(function () {
                    vm.viewCreate();
                })
            );
        };

        vm.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) {
                subscription.unsubscribe();
            });
        };

        vm.reload = function() {
            vm.pagination.reload = true;
            vm.pagination.disabled = false;

            vm.imageError = [];

            vm.infiniteImageErrors.numLoaded_ = 0;
            vm.infiniteImageErrors.toLoad_ = 0;
            vm.infiniteImageErrors.getItemAtIndex(1);
        };

        vm.search = function () {
            if (vm.pagination.lastSearch === vm.pagination.searchQuery) {
                return;
            }
            vm.pagination.lastSearch = vm.pagination.searchQuery;
            vm.reload();
        };

        vm.liveSearch = function () {
            utils.delay(function() {
                vm.search();
            }, 350 );
        };

        vm.clearSearch = function () {
            vm.pagination.searchQuery = '';
            vm.search();
        };

        vm.onLogClose = function () {
            vm.iamgeErrorDetails.isShown = false;
        };

        vm.onLogCreate = function () {
            vm.reload();
        };

        /****** private ******/
        function fetchImageError(index) {

            if (vm.infiniteImageErrors.toLoad_ >= index || vm.pagination.disabled) {
                return;
            }

            vm.pagination.disabled = true;

            if (vm.pagination.reload) {
                $timeout(function () {
                    vm.pagination.loading = false;
                    utils.listLoading.show(false);
                });
            } else {
                $timeout(function () {
                    vm.pagination.loading = true;
                });
            }

            vm.infiniteImageErrors.toLoad_ += vm.pagination.perPage;

            ImageError.fetchImageError(
                {
                    paging_size: vm.pagination.perPage,
                    paging_offset: vm.imageError.length,
                    conditions_keywords: vm.pagination.searchQuery,
                    first_date: vm.firstDate,
                }
            ).then(
                function (data) {

                    vm.infiniteImageErrors.numLoaded_ = vm.infiniteImageErrors.toLoad_ - vm.pagination.perPage + data.image_error.length;
                    vm.firstDate = data.first_date;
                    vm.imageError = vm.imageError.concat(data.image_error);

                    vm.pagination.empty = !vm.imageError.length;
                    vm.pagination.reload = false;
                    vm.pagination.loading = false;

                    vm.pagination.disabled = data.total_count <= vm.imageError.length;

                    $timeout(function () {
                        utils.listLoading.hide();
                    }, 120);
                }
            );
        }

    }

})();