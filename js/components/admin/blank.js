(function () {

    angular.module('printty')
        .component('adminBlank', {
            controller: BlankController,
            controllerAs: 'vm',
            templateUrl: 'views/admin/blank.html'
        });

    function BlankController($state, Auth) {

        "ngInject";

        if (!Auth.isAuthorized('customers')) {
            return $state.go( Auth.defaultRoute );
        }

    }

})();