(function() {

  angular.module('printty')
    .component('adminProductionTasks', {
      controller: ProductionTasksCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/tasks/tasks.html'
    });

  function ProductionTasksCtrl(ProductionTasks, ProductionPrinters, utils, Store,$state,$q, electron, $mdDialog, Customers,Factory) {

    "ngInject";

    var ctrl = this;

    /****** variables ********/
    ctrl.tasks = [];
    ctrl.isElectron = !!electron;
    ctrl.activeFilter = null;
    ctrl.selectedCustomer = [];
    ctrl.allCustomerSelected = false;

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      date: null,
      type_id: null,

      searchQuery: $state.params.q,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    ctrl.taskDetails = {
      isShown: false,
      active: {}
    };

    ctrl.statusFilter = [
        {'key': '', 'value' : 'すべて'},
        {'key': 'qr_waiting', 'value' : 'QR印刷待ち' },
        {'key': 'print_waiting', 'value' : '印刷待ち' },
        {'key': 'inspection_waiting', 'value' : '検品待ち' },
        {'key': 'delivery_request_waiting', 'value' : '発送準備中' },
        {'key': 'delivery_preparing_complite', 'value' : '配送準備済' },
        {'key': 'delivery_complite', 'value' : '発送完了' },
    ];

    ctrl.statusChange = [
        {'key': 'qr_waiting', 'value' : 'QR印刷待ち' },
        {'key': 'print_waiting', 'value' : '印刷待ち' },
        {'key': 'inspection_waiting', 'value' : '検品待ち' },
        {'key': 'delivery_request_waiting', 'value' : '発送準備中' },
        {'key': 'delivery_preparing_complite', 'value' : '配送準備済' },
        {'key': 'delivery_complite', 'value' : '発送完了' },
    ];

    ctrl.customerFilter = [
        {'Customer': {'id' : 0, 'title' : '会社名' }}
    ];

      ctrl.setActiveFilter = function (filter) {
          ctrl.activeFilter = filter;
          ctrl.reload();
      };

      ctrl.filterCustomer = function (id){
          if(id === 0) {
              selecteAllCustomter();
          }
          ctrl.selectedCustomer = getSelectedCustomter();
          ctrl.reload();
      };

      function getSelectedCustomter() {

          var selectedItems = [];

          _.forEach(ctrl.customerFilter, function (customer) {

              if (customer.selected && customer.Customer.id !== 0) {
                  selectedItems.push(customer.Customer.id);
              }

          });

          return selectedItems;

      }

      function selecteAllCustomter() {

          ctrl.allCustomerSelected = !ctrl.allCustomerSelected;

          _.forEach(ctrl.customerFilter, function (customer) {
              customer.selected = ctrl.allCustomerSelected;
          });

      }

      ctrl.activeFilter = ctrl.statusFilter[0];
      ctrl.activeCustomer = ctrl.customerFilter[0];

    var subscriptions = [];

    var allSelected = false;

    var loader = new utils.loadCounter(
      function(isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function() {
        utils.listLoading.hide();
      },
      2
    );

    ctrl.infiniteTasks = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.tasks[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchTasks(index);
      }
    };

    /****** methods ******/
    ctrl.$onInit = function() {

      loader.start(true);

      setType();
      getTypes();

      getCustomers();
      getFactories();

      subscriptions.push(
        Store.data('AdminProduction:HeaderDate')
          .subscribe(function(date) {
            ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
            ctrl.reload();
          }),

        Store.event('sidebarActiveStatus')
          .subscribe(function(status) {
            ctrl.pagination.type_id = status;
            ctrl.reload();
          }),

        Store.event('CreatePlan:Update')
          .subscribe(function() {
            ctrl.reload();
          })
      );

    };

      function setType() {
          var still_type = ['0', '1', '2', '3', '4', '7'];
          var type_id = 0;
          if (still_type.includes(localStorage.getItem('type_id')))
          {
              type_id = localStorage.getItem('type_id');
          }
          ctrl.pagination.type_id = type_id;
          Store.event('activeTypeId').emit(type_id);
      }

    ctrl.$onDestroy = function() {
      _.forEach(subscriptions, function(subscription) {
        subscription.unsubscribe();
      });
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.tasks = [];

      ctrl.infiniteTasks.numLoaded_ = 0;
      ctrl.infiniteTasks.toLoad_ = 0;
      ctrl.infiniteTasks.getItemAtIndex(1);
    };

    ctrl.search = function() {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function() {
      utils.delay(function() {
        ctrl.search();
      }, 350);
    };

    ctrl.clearSearch = function() {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };

    ctrl.viewDetails = function(task) {
      ctrl.taskDetails.isShown = true;
      ctrl.taskDetails.active = {
        taskId: task.Task.id
      };
    };

    ctrl.onTaskClose = function() {
      ctrl.taskDetails.isShown = false;
    };

    ctrl.onTaskDelete = function(taskId) {
      _.remove(ctrl.tasks, function(task) {
        return task.Task.id == taskId;
      });
    };

    ctrl.onChange = function (){
        ctrl.reload();
    }

    /****** private ******/
    var canceler;
    function fetchTasks(index) {

      if (ctrl.infiniteTasks.toLoad_ >= index || ctrl.pagination.disabled) return;
      if(!ctrl.factory) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        ctrl.pagination.loading = false;
        loader.start(false);
      } else {
        ctrl.pagination.loading = true;
      }

      ctrl.infiniteTasks.toLoad_ += ctrl.pagination.perPage;
        if (canceler) {
            canceler.resolve();
        }

        var cancelerDefer = $q.defer();
        canceler = cancelerDefer;
      $q.race([
      ProductionTasks.fetchTasks(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
          date: ctrl.pagination.date,
          type_id: ctrl.pagination.type_id,
          keywords: ctrl.pagination.searchQuery,
          status: ctrl.activeFilter ? ctrl.activeFilter.key : '',
          customer: ctrl.selectedCustomer.length > 0 ? ctrl.selectedCustomer.join(',') : null,
          factory_id: ctrl.factory,
        }),cancelerDefer.promise
      ]).then(
        function(data) {
        if(data){
          ctrl.pagination.current++;

          ctrl.infiniteTasks.numLoaded_ = ctrl.infiniteTasks.toLoad_ - ctrl.pagination.perPage + data.tasks.length;
          ctrl.tasks = data.tasks;

          if (allSelected) {
            _.forEach(data.tasks, function(task) {
              task.selected = allSelected;
            });
          }
          ctrl.pagination.empty = !ctrl.tasks.length;
          ctrl.pagination.loading = false;
          ctrl.pagination.reload = false;

          ctrl.pagination.disabled = data.total_count <= ctrl.tasks.length;

          // ctrl.statusFilter = data.status_list;

          loader.stop();
          }
        }
      );

    }

    function getTypes() {

      ProductionPrinters.fetchTypes().then(function(data) {
        var statuses = _.flatMap(data.types, function(type) {
          return type.PrinterType;
        });

        Store.data('statuses').update(statuses);

        loader.stop();
      });

    }

      function getFactories() {
          Factory.fetchFactoryUser().then(function(data){
              ctrl.factories = data.factories;
              ctrl.factory = data.factorySelected;
          });
      }

      function getCustomers() {

          Customers.fetchCustomers().then(function(data) {
              ctrl.customerFilter = ctrl.customerFilter.concat(data.customers);
          });

      }

    ctrl.selectAll = function() {

      _.forEach(ctrl.tasks, function(task) {
        task.selected = !allSelected;
      });

      allSelected = !allSelected;

    };

    ctrl.changeStatusSelected = function(statusKey) {
      var selectedTasks = _.chain(ctrl.tasks)
        .filter({selected: true})
        .map('Task.id')
        .value();
      if (selectedTasks.length === 0)
        return;
      return changeStatus(selectedTasks, statusKey);

    };

    ctrl.changeStatusTask = function(task, statusKey) {
      return changeStatus([task.Task.id], statusKey);
    };

    function changeStatus(tasks, statusKey) {
        var confirm = $mdDialog.confirm({
            template: utils.changeStatusTemplate('本当に変更してもよいですか?'),
            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                $scope.cancelDelete = function () {
                    $mdDialog.cancel();
                };
                $scope.confirmDelete = function () {
                    $mdDialog.hide();
                };
            }],
        });

        $mdDialog.show(confirm).then(function () {
            loader.start(false);
            return ProductionTasks.changeStatus(tasks, statusKey)
                .then(function() {
                    allSelected = false;
                    ctrl.reload();
                })
                .finally(loader.stop);
        });
    }

      ctrl.printQr = function (ev, taskId) {

          // if (!ctrl.isElectron) {
          //     alert('アプリ外で印刷不可です。');
          //     return;
          // }
          var size;
          var confirm = $mdDialog.confirm({
              template: utils.choosePaperSizeTemplateQRTasks('印刷用紙サイズを選択してください。'),
              controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                  $scope.cancelChoose = function () {
                      $mdDialog.cancel();
                  };
                  $scope.confirmChoose = function () {
                      size = $('.paper-size-radio:checked').val();
                      if(size) {
                          $mdDialog.hide();
                      }
                      return false;
                  };
              }]
          }).targetEvent(ev);

          $mdDialog.show(confirm).then(function () {
              ProductionTasks.fetchQRCode({
                  date : ctrl.pagination.date,
                  'tasks_ids[]': taskId,
                  size : size,
              })
                  .then(function (data) {

                      if (data) {
                          electron.ipcRenderer.send('print-images', {
                              urls: data,
                              size: size,
                          });

                          loader.stop();
                      }
                  })
                  .catch(function () {
                      loader.stop();
                  });
          });

      };

      ctrl.printQrSelected = function($event) {
          var selectedTasks = _.chain(ctrl.tasks)
              .filter({selected: true})
              .map('Task.id')
              .value();
          if (selectedTasks.length === 0)
              return;
          return ctrl.printQr($event,selectedTasks);

      };

      ctrl.deleteTaskSelected = function() {
          var selectedTasks = _.chain(ctrl.tasks)
              .filter({selected: true})
              .map('Task.id')
              .value();
          if (selectedTasks.length === 0)
              return;
          var confirm = $mdDialog.confirm({
              template: utils.confirmDeleteTask(),
              controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                  $scope.cancelDownload = function () {
                      $mdDialog.cancel();
                  };
                  $scope.confirmDownload = function () {
                      $mdDialog.hide();
                  };
              }]
          });

          $mdDialog.show(confirm).then(function () {

              return deleteTasks(selectedTasks);

          }, function (error) {

          });

      };

      function deleteTasks(tasks) {
          loader.start(false);
          return ProductionTasks.deleteTasks(tasks)
              .then(function() {
                  allSelected = false;
                  ctrl.reload();
              })
              .finally(loader.stop);
      }

      ctrl.downloadBusinessCSV = function () {

          loader.start(false);

          var selectedTasks = _.chain(ctrl.tasks)
              .filter({selected: true})
              .map('Task.id')
              .value();
          if (selectedTasks.length === 0)
              selectedTasks = null;

          ProductionTasks.downloadBusinessCSV({
              date: ctrl.pagination.date,
              'tasks_ids[]': selectedTasks,
              type_id: ctrl.pagination.type_id,
              status: ctrl.activeFilter ? ctrl.activeFilter.key : '',
          })
              .then(function (data) {
                  loader.stop();
              })
              .catch(function () {
                  loader.stop();
              });

      };

  }

})();