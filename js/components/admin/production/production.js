(function () {

  function PlanCtrl($state, Auth, Store,ProductionPrinters,$scope) {

    "ngInject";

    if (!Auth.isAuthorized('production')) {
      return $state.go( Auth.defaultRoute );
    }
    
    var ctrl = this;

    ctrl.state = $state;
    ctrl.initialState = ctrl.state.current.name;
    ctrl.isCreatePlanShown = false;
    $scope.$on('$stateChangeSuccess', function (event, toState) {
        ctrl.initialState = toState.name;
    });

    ctrl.date = moment().toDate();
    ctrl.subcontractor = false;
    
    var subscription;
    
    ctrl.$onInit = function () {
      localStorage.setItem("type_id", 0)

      subscription = Store.event('admin:create:plan').subscribe(function () {
        ctrl.isCreatePlanShown = true;
      });

      Store.data('AdminProduction:HeaderDate').update(ctrl.date);
      getGarmentInfo(ctrl.date);
    };
    
    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };
    
    ctrl.onCreatePlanClose = function () {
      ctrl.isCreatePlanShown = false;
    };
    
    ctrl.onDateChange = function () {
      Store.data('AdminProduction:HeaderDate').update(ctrl.date);
        getGarmentInfo(ctrl.date);
    };

    ctrl.showSubModeTab = function (title){
        var found = false;
        Store.data('Mode').subscribe(function (data){
            if(data && data.mode_sub_tab){
                found = Auth.contains(data.mode_sub_tab,title);
            }
        });
        return found;
    }
    function getGarmentInfo(date){
        var date = moment(date).format('YYYY-MM-DD');
        var params = {
            date : date
        };
        ProductionPrinters.totalGarment(params).then(function(data) {
            if(typeof data.subcontractor !== 'undefined' && data.subcontractor){
                ctrl.subcontractor = true;
                return;
            }
            ctrl.total_garment = data;
        });
    }

  }

  angular.module('printty')

    .component('adminProduction', {
      controller: PlanCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/production.html'
    });

})();