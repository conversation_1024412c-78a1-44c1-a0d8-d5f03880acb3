(function() {

  angular.module('printty')
    .component('printerQrGroups', {
      controller: GroupsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printer-qr-groups.html'
    });

  function GroupsCtrl(ProductionPrinters, Store, utils, electron, $timeout, $element) {

    "ngInject";

    var ctrl = this;

    /****** variables ********/
    ctrl.isElectron = !!electron;
    ctrl.groups = [];

    var subscriptions = [];

    /****** methods ******/
    ctrl.$onInit = function() {

      subscriptions.push(
        Store.event('print:PrinterQrcodesGroups')
          .subscribe(function(params) {
            fetchGroups(params);
          })
      );
    };

    ctrl.$onDestroy = function() {
      _.forEach(subscriptions, function(subscription) {
        subscription.unsubscribe();
      });
    };
    if (electron)
      electron.ipcRenderer.on('simple:print-document:printed', function() {
        $timeout(function() {
          ctrl.groups = [];
          utils.globalLoading.hide();
        });
      });

    /****** private ******/

    /****** private ******/
    function fetchGroups(params) {

      utils.globalLoading.show();

      ProductionPrinters.fetchQrGroups(params)
        .then(function(data) {

          ctrl.groups = data.groups;
          $timeout(function() {

            var minCellHeight = 80;
            var minQR = 100;
            var QRsize = 210;
            angular.forEach(ctrl.groups, function(group, index) {
              var options = group.qrcodes[0].options;
              var group_cells = $element.find('.page[data-index="' + index + '"] .cell');
              var cellWidth = (100 - options.columns) / options.columns;
              var cellHeight = (487 - options.rows * 5) / options.rows;
              group.qrSize = QRsize;
              if (cellHeight < minCellHeight) {
                var shift = minCellHeight - cellHeight;
                var totalReduce = shift * options.rows;
                if (totalReduce < (QRsize - minQR)) {
                  group.qrSize = Math.round(QRsize - totalReduce);
                  cellHeight = (487 + totalReduce - options.rows * 5) / options.rows;
                } else {
                  group.qrSize = minQR;
                  cellHeight = (487 + (QRsize - minQR) - options.rows * 5) / options.rows;
                }
              }
              group_cells.css("width", cellWidth + "%");
              group_cells.css("height", cellHeight + "px");
              group_cells.each(function(indx, cell) {
                // Need width, because our page is transform by 90'
                var cellWidthPix = $(cell).height();
                var title = $(cell.querySelector('.title'));
                var detail = $(cell.querySelector('.detail'));
                title.css("font-size", Math.min(Math.max(Math.round(cellWidthPix * 0.15), 7), 18));
                var titleHeight = title.width();
                var detailHeight = detail.width();
                detail.css("font-size", Math.min(Math.max(Math.round(cellWidthPix * 0.05), 7), 15));
                var image = cell.querySelector('img');
                image.style.height = Math.min(Math.max(cellHeight - titleHeight - detailHeight, 1),cellWidthPix) + 'px';
              })
            });

            $element.find('.qr-code').each(function() {
              var $this = $(this);
              $this.find('img').get(0).src = $this.find('qrcode .qrcode-link').get(0).href;
            });

            setTimeout(function() {
              if (electron)
                electron.ipcRenderer.send('simple:print-document', {
                  title: 'qr groups',
                  contents: $element.html(),
                  styles: '@page{size:A4;margin:6.35mm}body{font-family:"Noto Sans CJK JP","Noto Sans",Roboto,"Helvetica Neue",Meiryo,"メイリオ",sans-serif;font-size:14px;line-height:1;padding:0;margin:0}.page{transform-origin:0 0;transform:rotate(90deg) translate(0,-100%);height:197.3mm;width:284.3mm;page-break-after:always}.qr-codes{display:flex;flex-direction:row;justify-content:space-between}.qr-codes .qr-code .title{padding:0;margin:5px 0 0}.qr-code-dummy{width:210px;height:210px;background:#000}.table{display:flex;flex-direction:row;flex-wrap:wrap}.cell{margin-right:1%;box-sizing:border-box;margin-bottom:5px}.cell p{margin:0;padding:0}.cell .detail{overflow:hidden;text-overflow:ellipsis;font-size:12px;height:17px;white-space:nowrap}.cell img{display:block;max-width:100%}'
                });
            },50);

          }, 10);

        })
        .catch(function() {
          utils.globalLoading.hide();
        });

    }

  }

})();