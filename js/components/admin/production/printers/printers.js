(function () {

  angular.module('printty')
    .component('adminProductionPrinters', {
      controller: ProductionPrintersCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printers.html'
    });
  
  function ProductionPrintersCtrl(ProductionPrinters, Store, utils, electron, $timeout, $mdDialog, ProductionTasks, ApiService,$state,Factory,$scope) {

    "ngInject";
    
    var ctrl = this;
    
    /****** variables ********/
    ctrl.isElectron = !!electron;
    ctrl.printers = [];
    ctrl.showChart = false;
    ctrl.is_seiren = null;
    ctrl.second_factory = null;

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      date: null,
      type_id: null,

      searchQuery: $state.params.q,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: true
    };
    
    ctrl.printerDetails = {
      isShown: false,
      active: {}
    };

    ctrl.csvDate = null;
    ctrl.csvDateTotalSide = null;
    var allSelected = false;
    var subscriptions = [];
    
    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      2
    );
    $scope.$on('$stateChangeStart', function (event, toState) {
      if (toState.name === 'admin.production.printers') {
        getTypes();
      }
    });

    /****** methods ******/
    ctrl.$onInit = function () {

      loader.start(true);

      setType();
      getTypes();
      
      subscriptions.push(

        Store.data('AdminProduction:HeaderDate')
          .subscribe(function (date) {
            ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
            ctrl.reload();
          }),

        Store.event('sidebarActiveStatus')
          .subscribe(function (status) {
            ctrl.pagination.type_id = status;
            ctrl.reload();
          }),

        Store.event('CreatePlan:Update')
          .subscribe(function () {
            ctrl.reload();
          })
        
      );
      
    };

    function setType() {
      var still_type = ['0', '1', '2', '3', '4', '7'];
      var type_id = 0;
      var now = localStorage.getItem('type_id');
      if (still_type.includes(now))
      {
        type_id = localStorage.getItem('type_id');
      }
      ctrl.pagination.type_id = type_id;
      Store.event('activeTypeId').emit(type_id);
    }
    
    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };
    
    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;

      ctrl.printers = [];

      if(ctrl.factory){
        fetchPrinters();
      }else{
        getFactories();
      }
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };

    ctrl.loadMore = function () {
      fetchPrinters();
    };
    
    ctrl.viewPrinter = function (printer) {
      ctrl.printerDetails.active = {
        printerId: printer.Printer.id,
        date: ctrl.pagination.date
      };
      ctrl.printerDetails.isShown = true;
    };
    
    ctrl.onPrinterClose = function () {
      ctrl.printerDetails.isShown = false;
    };
    
    ctrl.downloadCSV = function (event) {
      
      $timeout(function() {
        $(event.currentTarget).next().find('.md-datepicker-button').click();
      });

    };

    ctrl.onCsvDateChange = function() {
      
      var params = {
        start_date: moment(ctrl.csvDate).format('YYYY-MM-DD'),
        end_date: moment(ctrl.csvDate).endOf('month').format('YYYY-MM-DD')
      };
      
      utils.globalLoading.show();

      ProductionTasks.fetchTasksCSV(params)
        .then(function(data) {
          
          ApiService.download(data.url, {
            type: 'csv',
            name: ApiService.getFileNameFromUrl(data.url)
          })
            .finally(function() {
              ctrl.csvDate = null;
              utils.globalLoading.hide();
            });
          
        })
        .catch(function() {
          ctrl.csvDate = null;
          utils.globalLoading.hide();
        });

    };
    
    ctrl.printQRs = function (size) {
      if (!ctrl.isElectron) {
        alert('プリンターは設定されてません。');
        return;
      }

      utils.globalLoading.show();

      ProductionPrinters.fetchQrCodes({
        date: ctrl.pagination.date,
        'printers_ids[]': getSelected(),
        type_id: ctrl.pagination.type_id,
        factory_id: ctrl.factory,
        'size' : size,
      })
        .then(function (data) {
          
          if (data.length) {
            electron.ipcRenderer.send('print-images', {
              urls: data,
              size: size
            });
          } else {
            utils.globalLoading.hide();
          }
          
        })
        .catch(function () {
          utils.globalLoading.hide();
        });
      
    };

    ctrl.printQRsRemaining = function (size) {
      if (!ctrl.isElectron) {
        alert('プリンターは設定されてません。');
        return;
      }

      utils.globalLoading.show();

      ProductionPrinters.fetchQrCodesRemaining({
        date: ctrl.pagination.date,
        'printers_ids[]': getSelected(),
        type_id: ctrl.pagination.type_id,
        factory_id: ctrl.factory,
        'size' : size,
      })
          .then(function (data) {

            if (data.length) {
              electron.ipcRenderer.send('print-images', {
                urls: data,
                size: size
              });
            } else {
              utils.globalLoading.hide();
            }

          })
          .catch(function () {
            utils.globalLoading.hide();
          });

    };

    ctrl.printQRGroups = function () {

      Store.event('print:PrinterQrcodesGroups').emit({
        date: ctrl.pagination.date,
        'printers_ids[]': getSelected(),
        factory_id: ctrl.factory,
        type_id: ctrl.pagination.type_id
      });
      
    };
    
    if (ctrl.isElectron) {
      electron.ipcRenderer.on('print-images:printed', function () {
        $timeout(function () { utils.globalLoading.hide(); });
      });
    }
    
    ctrl.getPercent = function (printer) {
      var num = printer.workload / printer.capacity;
      return num > 1 ? 100 : num * 100;
    };
    
    ctrl.selectAll = function () {
      
      _.forEach(ctrl.printers, function (printer) {
        printer.selected = !allSelected;
      });

      allSelected = !allSelected;

    };
    
    ctrl.printSheet = function (is_remain, printer) {

      if (!ctrl.isElectron) {
        alert('アプリ外で印刷不可です。');
        return;
      }
      
      var selected;
      
      if (printer) {
        selected = printer.Printer.id;
      } else {
        selected = getSelected();
      }

      var params = {
        date: ctrl.pagination.date,
        'printers_ids[]': selected,
        factory_id: ctrl.factory,
        type_id: ctrl.pagination.type_id,
        remain: is_remain,
      }

      Store.event('print:PrinterProductSheet').emit(params);

    };

    ctrl.deleteTasks = function (ev, printer) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('タスクを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function () {
        deleteTasks(printer);
      });
      
      function deleteTasks(printer) {
        var selected;

        if (printer) {
          selected = [printer.Printer.id];
        } else {
          selected = getSelected();

          if (_.isArray(selected) && !selected.length) {
            selected = null;
          }

        }

        loader.start(false);

        ProductionPrinters.deleteTasks({
          date: ctrl.pagination.date,
          printers: selected
        })
          .finally(function () {
            ctrl.reload();
          });
      }
    
    };

      ctrl.prePrintQR = function (ev) {
          if (!ctrl.isElectron) {
              alert('アプリ外で印刷不可です。');
              return;
          }
          var size;
          var confirm = $mdDialog.confirm({
              template: utils.choosePaperSizeTemplateNew('印刷用紙サイズを選択してください。'),
              controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                  $scope.cancelChoose = function () {
                      $mdDialog.cancel();
                  };
                  $scope.confirmChoose = function () {
                      size = $('.paper-size-radio:checked').val();
                      if(size) {
                          $mdDialog.hide();
                      }
                      return false;
                  };
              }]
          }).targetEvent(ev);

          $mdDialog.show(confirm).then(function () {
              ctrl.printQRs(size)
          });

      };

      ctrl.printRemainingQR = function (ev) {
      if (!ctrl.isElectron) {
        alert('アプリ外で印刷不可です。');
        return;
      }

      var size;
      var confirm = $mdDialog.confirm({
        template: utils.choosePaperSizeTemplateNew('印刷用紙サイズを選択してください。'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelChoose = function () {
            $mdDialog.cancel();
          };
          $scope.confirmChoose = function () {
            size = $('.paper-size-radio:checked').val();
            if(size) {
              $mdDialog.hide();
            }
            return false;
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function () {
        ctrl.printQRsRemaining(size)
      });

    };
    
    /****** private ******/
    function fetchPrinters() {

      ctrl.pagination.disabled = true;

      // show loading screen if reloading
      if (ctrl.pagination.reload) {
        ctrl.pagination.loading = false;
        loader.start();
      } else {
        ctrl.pagination.loading = true;
      }
      
      ProductionPrinters.fetchPrinters({
        paging_size: ctrl.pagination.perPage,
        paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
        date: ctrl.pagination.date,
        type_id: ctrl.pagination.type_id,
        keywords: ctrl.pagination.searchQuery,
        factory_id: ctrl.factory,
      })
        .then(function (data) {
          
          allSelected = false;
          
          ctrl.pagination.current++;
          
          ctrl.printers = ctrl.printers.concat(data.printers);
          
          ctrl.pagination.empty = !ctrl.printers.length;
          ctrl.pagination.loading = false;
          ctrl.pagination.reload = false;

          ctrl.pagination.disabled = data.total_count <= ctrl.printers.length;
          
          loader.stop();

        });
      
    }
    
    function getTypes() {

      ProductionPrinters.fetchTypes().then(function (data) {
        if(data === false){
          loader.stop();
          return;
        }

        var statuses = _.flatMap(data.types, function (type) {
          return type.PrinterType;
        });

        var custom_status = [];

          _.forEach(statuses, function (status) {
              custom_status.push(status);
              if(status.id === "1") {
                  custom_status.push({id : "101", title: "ガーメント（Up-T・Budgets）"});
                  custom_status.push({id : "102", title: "ガーメント（TMIX）"});
                  custom_status.push({id : "103", title: "ガーメント（その他）"});
              }
          });

        Store.data('statuses').update(custom_status);

        loader.stop();
      });
      
    }

    function getSelected() {
      
      var selectedItems = [];

      _.forEach(ctrl.printers, function (printer) {

        if (printer.selected) {
          selectedItems.push(printer.Printer.id);
        }

      });

      return selectedItems;

    }

    function getFactories() {
      Factory.fetchFactoryUser().then(function(data){
        ctrl.factories = data.factories;
        ctrl.factory = data.factorySelected;
        fetchPrinters();
      });
    }

    ctrl.onChange = function (){
      ctrl.reload();
    }

    ctrl.backToTop = function () {
      var firstPrinterItem = $('.printer-item').first();
      if (firstPrinterItem.length) {
        firstPrinterItem[0].scrollIntoView({ behavior: 'smooth' });
      }
    };

    ctrl.backToBottom = function () {
      var lastPrinterItem = $('.printer-item').last();
      if (lastPrinterItem.length) {
        lastPrinterItem[0].scrollIntoView({ behavior: 'smooth' });
      }
    };
  }
  
})();