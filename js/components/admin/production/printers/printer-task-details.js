(function () {

  angular.module('printty')
    .component('adminProductionPrinterTaskDetails', {
      controller: TaskDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printer-task-details.html',
      bindings: {
        isShown: '<ngShow',
        active: '<',
        loadingType: '=',
        
        onClose: '&',
        onDelete: '&',
        onReplan: '&'
      }
    });

  function TaskDetailsCtrl($scope, utils, ProductionTasks, $mdDialog, $timeout, electron, ProductionPrinters) {

    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.isElectron = !!electron;
    
    ctrl.task = null;
    ctrl.sides = [];

    ctrl.activeStep = null;

    ctrl.qrShown = false;
    ctrl.clipSuccessMsg = false;

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    /****** methods ******/
    ctrl.$onChanges = function (changes) {

      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('タスクを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteTask);

    };

    ctrl.toggleQr = function () {
      ctrl.qrShown = !ctrl.qrShown;
    };

    ctrl.back = function () {
      ctrl.onClose();
    };

    ctrl.onSelect = function (activeStep) {
      ctrl.activeStep = activeStep;
    };

    ctrl.onSuccessCopy = function(e) {
      e.clearSelection();

      ctrl.clipSuccessMsg = true;
      $timeout(function () {
        ctrl.clipSuccessMsg = false;
      }, 500);
    };
    
    ctrl.printQr = function (ev) {

      if (!ctrl.isElectron) {
        alert('プリンターは設定されてません。');
        return;
      }
      var size;
      var confirm = $mdDialog.confirm({
        template: utils.choosePaperSizeTemplateQRTasks('印刷用紙サイズを選択してください。'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelChoose = function () {
            $mdDialog.cancel();
          };
          $scope.confirmChoose = function () {
            size = $('.paper-size-radio:checked').val();
            if(size) {
              $mdDialog.hide();
            }
            return false;
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function () {
        loader.start('partial');

        ProductionPrinters.fetchQrCodes({
          date: ctrl.active.date,
          'printers_ids[]': ctrl.active.printerId,
          'tasks_ids[]': ctrl.active.taskId,
          size: size,
        })
            .then(function (data) {
              if (data.length) {
                electron.ipcRenderer.send('print-images', {
                  urls: data,
                  size: size
                });
              } else {
                loader.stop();
              }
            })
            .catch(function () {
              loader.stop();
            });
      });
    };
    
    ctrl.replan = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('再プランニングしますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(function () {
        replan();
      });
      
    };

    if (ctrl.isElectron) {
      electron.ipcRenderer.on('print-images:printed', function () {
        $timeout(function () { loader.stop(); });
      });
    }

    /******** private **********/

    function initComponent() {

      loader.start();

      if (!ctrl.isAdd && ctrl.active.taskId) {
        fetchTask();
      }
      
    }

    function fetchTask() {

      ProductionTasks.fetchTask(ctrl.active)
        .then(function (data) {
          ctrl.task = data;

          ctrl.sides = _.map(ctrl.task.steps, function (step) {

            return {
              title: step.TaskStep.title,
              image_url: step.TaskStep.image_url,
              code: step.TaskStep.code
            };

          });

          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
    }

    function deleteTask() {
      
      loader.start('partial');

      ProductionTasks.deleteTask(ctrl.task.Task.id)
        .then(function () {

          ctrl.onDelete({
            taskId: ctrl.task.Task.id
          });

          ctrl.back();
        })
        .catch(function () {
          loader.stop();
        });

    }
    
    function replan() {

      loader.start('partial');
      
      ProductionTasks.replanTask(ctrl.task.Task.id)
        .then(function () {
          fetchTask();
        })
        .catch(function () {
          loader.stop();
        });
      
    }

    function setDefault() {
      ctrl.task = null;
      ctrl.sides = [];
      ctrl.qrShown = false;
      ctrl.activeStep = null;
      loader.stop();
    }

  }

})();