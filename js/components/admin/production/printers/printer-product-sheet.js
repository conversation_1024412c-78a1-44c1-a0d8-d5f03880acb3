(function () {

  angular.module('printty')
    .component('printerProductSheet', {
      controller: ProductSheetCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printer-product-sheet.html'
    });

  function ProductSheetCtrl(ProductionPrinters, Store, utils, electron, $timeout, $element) {

    "ngInject";

    var ctrl = this;

    /****** variables ********/
    ctrl.isElectron = !!electron;
    // electron
    ctrl.sheets = [];

    var subscriptions = [];

    /****** methods ******/
    ctrl.$onInit = function () {

      subscriptions.push(

        Store.event('print:PrinterProductSheet')
          .subscribe(function (params) {
            fetchSheets(params);
          })

      );

    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    electron.ipcRenderer.on('print-sheets:printed', function () {
      $timeout(function () {
        ctrl.sheets = [];
        utils.globalLoading.hide();
      });
    });

    /****** private ******/
    function fetchSheets(params) {
      
      utils.globalLoading.show();
      
      ProductionPrinters.fetchSheet(params)
        .then(function (data) {

          ctrl.sheets = data.products;

          $timeout(function () {
            electron.ipcRenderer.send('print-sheets', {
              contents: $element.html(),
              styles: '@page{size:A4}body{font-family:"Noto Sans","Noto Sans CJK JP",Roboto,"Helvetica Neue",Meiryo,"メイリオ",sans-serif;line-height:1.4;padding:0;margin:0}table{border-collapse:collapse;width:100%}table th{text-align:left}td,th{border:1px solid #000;padding:4px}'
            });
          }, 10);

        })
        .catch(function () {
          utils.globalLoading.hide();
        });

    }

  }

})();