(function () {

  angular.module('printty')
    .component('printerInfo', {
      controller: PrinterInfoCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printer-info.html',
      bindings: {
        isShown: '<',
        active: '<',
        
        onClose: '&'
      }
    });
  
  function PrinterInfoCtrl(ProductionPrinters, ProductionTasks, $mdSidenav, utils, $timeout) {

    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.printer = null;
    ctrl.tasks = [];
    ctrl.loadingType = 'full';
    
    ctrl.pagination = {
      current: 0,
      perPage: 50,

      disabled: true
    };

    ctrl.taskDetails = {
      isShown: false,
      active: {}
    };
    
    var sidenav = {
      open: function () {
        $mdSidenav('printer-info').open();
      },
      close: function () {
        $mdSidenav('printer-info').close();
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      },
      2
    );

    /****** methods ******/
    ctrl.$postLink = function () {
      $mdSidenav('printer-info').onClose(function () {
        $timeout(function () { ctrl.onClose(); }, 400);
      });
    };

    ctrl.$onChanges = function (changes) {

      if ('isShown' in changes) {
        if (ctrl.isShown) {
          openComponent();
        } else {
          setDefault();
        }
      }
      
    };

    ctrl.close = function () {
      sidenav.close();
    };
    
    ctrl.loadMoreTasks = function () {
      fetchTasks();
    };
    
    ctrl.viewTaskDetails = function (task) {
      ctrl.taskDetails.isShown = true;
      ctrl.taskDetails.active = {
        taskId: task.Task.id,
        date: ctrl.active.date,
        printerId: ctrl.active.printerId
      }
    };
    
    ctrl.onTaskClose = function () {
      ctrl.taskDetails.isShown = false;
    };

    ctrl.onTaskDelete = function (taskId) {
      _.remove(ctrl.tasks, function(task) {
        return task.Task.id == taskId;
      });
    };

    /******** private **********/
    function openComponent() {
      loader.start();

      if (!ctrl.isAdd && ctrl.active.printerId) {
        fetchPrinter();
        fetchTasks();
      }

      sidenav.open();
    }

    function setDefault() {
      ctrl.printer = null;
      ctrl.tasks = [];

      ctrl.pagination.disabled = true;
      ctrl.pagination.current = 0;

      ctrl.taskDetails.isShown = false;
      
      loader.stop();
      loader.default();
    }
    
    function fetchPrinter() {
      
      ProductionPrinters.fetchPrinter({
        printerId: ctrl.active.printerId,
        date: ctrl.active.date
      })
        .then(function (data) {
          ctrl.printer = data.Printer;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    }
    
    function fetchTasks() {

      ctrl.pagination.disabled = true;
      
      ProductionTasks.fetchTasks({
        paging_size: ctrl.pagination.perPage,
        paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
        printer_id : ctrl.active.printerId,
        date: ctrl.active.date
      })
        .then(function (data) {
          ctrl.pagination.current++;
          ctrl.tasks = ctrl.tasks.concat(data.tasks);
          ctrl.pagination.disabled = !data.tasks.length || (ctrl.tasks.length >= data.total_count);
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    }
    
  }

})();