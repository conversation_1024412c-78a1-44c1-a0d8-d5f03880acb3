(function () {

  angular.module('printty')

    .component('adminProductionWaiting', {
      controller: PlanWaitingCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/waiting.html'
    });
  
  function PlanWaitingCtrl(ProductionUnplanned, Store, utils,$state) {

    "ngInject";
    
    var ctrl = this;

    /****** variables ********/
    ctrl.unplanned = [];

    ctrl.date = null;

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      type_code: null,

      searchQuery: $state.params.q,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    var subscriptions = [];

    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      2
    );
    
    var codes = [];

    ctrl.infiniteUnplanned = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.unplanned[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchUnplanned(index);
      }
    };

    /****** methods ******/
    ctrl.$onInit = function () {

      loader.start(true);

      setType();
      fetchTypes();

      subscriptions.push(

          Store.data('AdminProduction:HeaderDate')
              .subscribe(function (date) {
                  ctrl.date = moment(date).format('YYYY-MM-DD');
              }),
        
        Store.event('sidebarActiveStatus')
          .subscribe(function (code) {
            ctrl.pagination.type_code = codes[code];
            ctrl.reload();
          }),

        Store.event('CreatePlan:Update')
          .subscribe(function () {
            ctrl.reload();
          })

      );

    };

    function setType() {
      var still_type = ['0', '1', '2', '3', '4', '7'];
      var type_id = 0;
      var fetch_code = ['all', 'clothes', 'case_uv', 'case_vakuum', 'sublimation', 'embroidery'];
      if (still_type.includes(localStorage.getItem('type_id')))
      {
        type_id = localStorage.getItem('type_id');
      }
      ctrl.pagination.type_code = fetch_code[type_id];
      Store.event('activeTypeId').emit(type_id);
    }

    ctrl.CSVdownload = function () {

        // if (!ctrl.isElectron) {
        //     alert('ファイルは設定されてません。');
        //     return;
        // }

        utils.globalLoading.show();

        ProductionUnplanned.downloadCSV(ctrl.date)
            .then(function (data) {
                alert(data.success);
                utils.globalLoading.hide();
            })
            .catch(function () {
                utils.globalLoading.hide();
            });

    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.unplanned = [];

      ctrl.infiniteUnplanned.numLoaded_ = 0;
      ctrl.infiniteUnplanned.toLoad_ = 0;
      ctrl.infiniteUnplanned.getItemAtIndex(1);
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };
    
    /****** private ******/
    function fetchUnplanned(index) {

      if (ctrl.infiniteUnplanned.toLoad_ >= index || ctrl.pagination.disabled) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        ctrl.pagination.loading = false;
        loader.start(false);
      } else {
        ctrl.pagination.loading = true;
      }

      ctrl.infiniteUnplanned.toLoad_ += ctrl.pagination.perPage;

      ProductionUnplanned.fetchUnplanned(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
          type_code: ctrl.pagination.type_code,
          keywords: ctrl.pagination.searchQuery
        }
      ).then(
        function (data) {

          ctrl.pagination.current++;

          ctrl.infiniteUnplanned.numLoaded_ = ctrl.infiniteUnplanned.toLoad_ - ctrl.pagination.perPage + data.unplanned.length;
          ctrl.unplanned = ctrl.unplanned.concat(data.unplanned);

          ctrl.pagination.empty = !ctrl.unplanned.length;
          ctrl.pagination.loading = false;
          ctrl.pagination.reload = false;

          ctrl.pagination.disabled = data.total_count <= ctrl.unplanned.length;

          loader.stop();
        }
      );
      
    }
    
    function fetchTypes() {

      ProductionUnplanned.fetchTypes().then(function (data) {
        
        var types = _.map(data.types, function (type) {
          return {
            title: type.ProductionType.title,
            id: codes.push(type.ProductionType.code) - 1
          }
        });
        
        Store.data('statuses').update(types);

        loader.stop();
      });
      
    }

  }
  

})();