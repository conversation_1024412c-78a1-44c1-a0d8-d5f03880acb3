(function () {

    angular.module('printty')
        .component('manualShippingCompleteDetails', {
            controller: CompleteShippingDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/admin/production/manual-shipping-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function CompleteShippingDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, ProductionPlaces, Store) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.order = null;
        vm.deliveryServices = null;

        vm.loadingType = 'full';

        var completeShippingForm,
            completeShippingClone;

        var sidenav = {
            open: function () {
                $mdSidenav('manual-shipping-complete-details').open();
            },
            close: function () {
                $mdSidenav('manual-shipping-complete-details').close();
            }
        };
        var subscriptions = [];

        vm.$onInit = function() {
            subscriptions.push(
                Store.data('DeliveryServices')
                    .subscribe(function (data) {
                        vm.deliveryServices = data;
                    })
            );
        };

        vm.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        vm.$postLink = function () {
            completeShippingForm = $scope.completeShippingFrameForm;

            $mdSidenav('manual-shipping-complete-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.updateTrackingNumber = function () {
            completeShippingForm.$setSubmitted();

            if (completeShippingForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.order, completeShippingClone) ) {
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            ProductionPlaces.updateTrackingNumberOrder(vm.order)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.loadingType = 'stopped';
                    vm.close();
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
            }

            if (vm.active.orderId) {
                fetchCompleteShippingTemp();
            }

            sidenav.open();
        }

        function fetchCompleteShippingTemp() {
            vm.loadingType = 'full';

            ProductionPlaces.fetchInfoCompleteShipping({
                order_id: vm.active.orderId,
                place_id: vm.active.placeId,
            })
                .then(function (data) {
                    vm.order = data;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var orderData = _.cloneDeep(vm.order);

            vm.onUpdate({
                order: orderData
            });
        }

        function setDefault() {
            vm.category = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (completeShippingForm) {
                completeShippingForm.$setPristine();
                completeShippingForm.$setUntouched();
            }
        }

    }

})();