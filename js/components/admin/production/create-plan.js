(function () {

  angular.module('printty')
    .component('createPlan', {
      controller: CreatePlanCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/create-plan.html',
      bindings: {
        isShown: '<',
        onClose: '&'
      }
    });
  
  function CreatePlanCtrl($element, $timeout, ProductionPlanning, utils, Store, $mdDialog) {

    "ngInject";
    
    var ctrl = this;

    ctrl.isShown = false;
    ctrl.unplanned = [];
    ctrl.filter_emb = null;
    ctrl.unplannedTypes = [];
    ctrl.unplannedFilter = [];
    ctrl.loadingType = 'full';
    ctrl.activeType = null;
    ctrl.activeSort = null;
    ctrl.emailClient = null;
    ctrl.emailClientKornit = null;
    ctrl.isBattle = null;
    ctrl.activeCustomer = null;
    ctrl.activeColor = null;
    ctrl.activeSize = null;
    ctrl.activeProductCode = null;
    ctrl.activeDate = null;
    ctrl.activeFilterPrinter = null;
    ctrl.activeSku = null;
    ctrl.activeMaker = null;
    ctrl.activeSameDay = null;
    ctrl.activeDeliveryType = null;
    ctrl.activeTotalQuantity = null;
    ctrl.activeNumberItemTasks = null;
    ctrl.chooseCustomer = [];
    ctrl.chooseNumberItemTask = [];
    ctrl.chooseMaker = [];
    ctrl.chooseColor = [];
    ctrl.chooseSize = [];
    ctrl.chooseProductCode = [];
    ctrl.chooseDate = [];
    ctrl.chooseUploadDate = [];
    ctrl.chooseSku = [];
    ctrl.factoryFilter = [];
    ctrl.specificFactories = [];
    ctrl.allSelected = false;
    ctrl.plannedSorts = [
        {'key': 'OrderOptionalKeyCustomerOrderNumberJoin.value', 'value' : '品番'},
        {'key': 'ProductJoin.title', 'value' : '商品名'},
        {'key': 'ProductSizeJoin.title', 'value' : 'サイズ'},
        {'key': 'ProductColorJoin.title', 'value' : 'カラー'},
        {'key': 'Order.production_date_preferred', 'value' : '納期日'},
        {'key': 'CustomerJoin.title', 'value' : '会社名'},
        {'key': 'ProductColorSizeSkuJoin.sku', 'value' : '指定'},
        {'key': 'ProductLinkedSourceJoin.title', 'value' : 'メーカー'},
    ];

    ctrl.autodone = true;
    
    ctrl.printers = [];
    ctrl.recommendations = [];
    
    ctrl.selectedItems = [];
    ctrl.selectedProducts = [];
    ctrl.selectedItemObjs = [];
    
    ctrl.searchQuery = null;
    
    ctrl.paginationItems = {
      current: 0,
      perPage: 50,
      
      empty: false,
      disabled: true,
      loadingItem: false,
      reloadItem: false,
    };

    ctrl.paginationPrinters = {
      current: 0,
      perPage: 50,

      empty: false,
      disabled: true
    };
    
    ctrl.date = moment().toDate();

    ctrl.factory_id = null;
    ctrl.factorySelected = null;
    ctrl.is_emb = null;
    
    var $body = angular.element('body');
    var lastSearch = null;
    
    // todo: redo animation using ng-animate
    var component = {
      show: function () {
        $element.addClass('shown');
        $body.addClass('not-scrollable-x');
        ctrl.isShown = true;
      },
      hide: function () {
        $element.removeClass('shown').addClass('hiding');
        $timeout(function () {
          $element.removeClass('hiding');
          $body.removeClass('not-scrollable-x');
          ctrl.isShown = false;
        }, 300)
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      },
      3
    );

    /****** methods ******/
    ctrl.$onChanges = function (changes) {
      
      if ( !('isShown' in changes) ) return;
      
      if (ctrl.isShown) {
        openComponent();
      } else {
        setDefault();
      }
      
    };
    
    ctrl.setActiveSort = function (sort) {
      ctrl.activeSort = sort;
      ctrl.reloadAll();
    };
    ctrl.setEmailClient = function () {
      ctrl.emailClient = ctrl.emailClient ? null : true;
      ctrl.emailClientKornit = null;
      ctrl.reloadAll();
    };
    ctrl.setEmailClientKornit = function(){
        ctrl.emailClientKornit = ctrl.emailClientKornit ? null : true;
        ctrl.emailClient = null;
        ctrl.reloadAll();
    };
      ctrl.filterBrother = function(){
          ctrl.isBattle = ctrl.isBattle ? null : true;
          ctrl.reloadAll();
      };
      ctrl.setActiveSize = function (size) {
          ctrl.activeSize = size;
          ctrl.reloadAll();
      };
      ctrl.setActiveCustomer = function (customer) {
          ctrl.activeCustomer = customer;
          ctrl.reloadAll();
      };
      ctrl.setActiveDate = function (date) {
          ctrl.activeDate = date;
          ctrl.reloadAll();
      };
      ctrl.setActiveUploadDate = function (date) {
          ctrl.activeUploadDate = date;
          ctrl.reloadAll();
      };
      ctrl.setActivePCode = function (code) {
          ctrl.activePCode = code;
          ctrl.reloadAll();
      };
      ctrl.setActiveColor = function (color) {
          // ctrl.activeColor = color;
          // ctrl.reloadAll();
      };
      ctrl.setFilterPrinter = function (type) {
          ctrl.activeFilterPrinter = type;
          ctrl.reloadPrinters()
      };
      ctrl.setActiveMaker = function (maker) {
          ctrl.activeMaker = maker;
          ctrl.reloadAll();
      };


    ctrl.setActiveType = function (type) {
      ctrl.activeType = type;
      ctrl.reloadAll();
    };

    ctrl.setActiveSameDay = function (isSame) {
      ctrl.activeSameDay = isSame;
      ctrl.reloadAll();
    };

    ctrl.setActiveDelivery = function (delivery) {
      ctrl.activeDeliveryType = delivery;
      ctrl.reloadAll();
    };

      ctrl.setActiveTotalQuantity = function (isTotal) {
          ctrl.activeTotalQuantity = isTotal;
          ctrl.reloadAll();
      };

      ctrl.setActiveNumberItemTasks = function (number) {
          ctrl.activeNumberItemTasks = number;
          ctrl.reloadAll();
      };

      ctrl.setActiveFilter = function (type) {
          ctrl.activeFilter = type;
          ctrl.reloadAll();
      };
    
    ctrl.loadMoreItems = function () {
      fetchUnplanned();
    };

    ctrl.loadMorePrinters = function () {
      fetchPrinters();
    };
    
    ctrl.reloadUnplanned = function () {
      ctrl.paginationItems.current = 0;
      ctrl.unplanned = [];
      ctrl.allSelected = false;
      ctrl.paginationItems.reloadItem = true;
      loader.start('partial');
      fetchUnplanned();
    };

    ctrl.reloadPrinters = function (isSkipReload) {
      ctrl.paginationPrinters.current = 0;
      ctrl.printers = [];
      
      if (!isSkipReload) {
        loader.start('partial');
      }
      
      fetchPrinters();
      fetchRecommendations();
    };
    
    ctrl.reloadAll = function () {
      ctrl.autodone = true;
      ctrl.selectedItemObjs = [];
      ctrl.selectedItems = [];
      loader.set(2);
      ctrl.reloadUnplanned();
      ctrl.reloadPrinters();
    };
    
    ctrl.onDateChange = function () {
      ctrl.reloadPrinters();
    };
    
    ctrl.onItemSelect = function () {
      ctrl.selectedItems = getSelected();
      ctrl.selectedItemObjs = getSelectedObj();
      ctrl.selectedProducts = getSelectedProducts();
      ctrl.reloadPrinters(true)
    };

    ctrl.search = function () {
      if (lastSearch === ctrl.searchQuery) return;
      lastSearch = ctrl.searchQuery;
      ctrl.reloadAll();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 350 );
    };
    
    ctrl.processPrinter = function (printer) {
      
      loader.start('partial');
      
      ProductionPlanning.processToPrinter({
        date: moment(ctrl.date).format('YYYY-MM-DD'),
        printerId: printer.Printer.id,
        items: ctrl.selectedItems,
        type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null,
        factory_id: ctrl.factory_id != null ? ctrl.factory_id : ctrl.factorySelected,
      })
        .then(function (data) {
            if(data.error_message !== null) {
                alert(data.error_message);
            }
          Store.event('CreatePlan:Update').emit();
          ctrl.reloadAll();
        })
        .catch(function () {
          loader.stop();
        });
      
    };

    ctrl.processRecommendations = function () {

      loader.start('partial');
      
      ProductionPlanning.processToRecommendations({
        date: moment(ctrl.date).format('YYYY-MM-DD'),
        items: ctrl.selectedItems,
        type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null
      })
        .then(function () {
          Store.event('CreatePlan:Update').emit();
          ctrl.reloadAll();
        })
        .catch(function () {
          loader.stop();
        });
      
    };
    
    ctrl.close = function () {
      component.hide();
      ctrl.onClose();
    };

    ctrl.getPercent = function (printer) {
      var num = printer.workload / printer.capacity;
      return num > 1 ? 100 : num * 100;
    };
    
    ctrl.toggleAll = function () {

      _.forEach(ctrl.unplanned, function (item) {
        item.selected = ctrl.allSelected;
      });

      ctrl.onItemSelect();
      
    };
    
    /******* private **********/
    function openComponent() {
      loader.start();

      fetchUnplanned();
      fetchFactoryFilter();
      fetchUnplannedTypes();
      fetchUnplannedFilter();
      fetchPrinters();

      component.show();
    }
    
    function fetchUnplanned() {
      ctrl.paginationItems.disabled = true;
      if (ctrl.paginationItems.reloadItem) {
          ctrl.paginationItems.loadingItem = false;
          loader.start(false);
      } else {
         ctrl.paginationItems.loadingItem = true;
      }

      ProductionPlanning.fetchUnplanned({
        paging_size: ctrl.paginationItems.perPage,
        paging_offset: ctrl.paginationItems.current * ctrl.paginationItems.perPage,
        type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null,
        keywords: ctrl.searchQuery,
        sort_name: ctrl.activeSort ? ctrl.activeSort : null,
        email_client: ctrl.emailClient,
        size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
        customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
        maker: ctrl.chooseMaker.length > 0 ? ctrl.chooseMaker.join(',') : null,
        date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
        upload_date: ctrl.chooseUploadDate.length > 0 ? ctrl.chooseUploadDate.join(',') : null,
        pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
        color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
        sku: ctrl.chooseSku.length > 0 ? ctrl.chooseSku.join(',') : null,
        is_emb: ctrl.is_emb,
        email_client_kornit: ctrl.emailClientKornit,
        number_item_tasks: ctrl.chooseNumberItemTask.length > 0 ? ctrl.chooseNumberItemTask.join(',') : null,
        is_battle: ctrl.isBattle,
          is_same_day: ctrl.chooseSameDay.length > 0 ? ctrl.chooseSameDay.join(',') : null,
          delivery_type: ctrl.chooseDeliveryType.length > 0 ? ctrl.chooseDeliveryType.join(',') : null,
          is_total_quantity: ctrl.chooseTotalQuantity.length > 0 ? ctrl.chooseTotalQuantity.join(',') : null,
          factory_id: ctrl.factory_id != null ? ctrl.factory_id : ctrl.factorySelected,
      })
        .then(function (data) {
          
          ctrl.paginationItems.current++;
          
          ctrl.unplanned = ctrl.unplanned.concat(data.unplanned);
          ctrl.side_count = data.side_count;
          ctrl.paginationItems.empty = !ctrl.unplanned.length;
          ctrl.paginationItems.reloadItem = false;
          ctrl.paginationItems.loadingItem = false;
          ctrl.paginationItems.disabled = ctrl.unplanned.length >= data.total_count;

          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });

    }
    
    function fetchPrinters() {

      ctrl.paginationPrinters.disabled = true;

      ProductionPlanning.fetchPrinters({
        paging_size: ctrl.paginationPrinters.perPage,
        paging_offset: ctrl.paginationPrinters.current * ctrl.paginationPrinters.perPage,
        date: moment(ctrl.date).format('YYYY-MM-DD'),
        type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null,
        type: ctrl.activeFilterPrinter ? ctrl.activeFilterPrinter : null,
        keywords: ctrl.searchQuery,
        'order_item_ids[]': ctrl.selectedItems,
        'product_ids[]': ctrl.selectedProducts,
          factory_id: ctrl.factory_id != null ? ctrl.factory_id : ctrl.factorySelected,
      })
        .then(function (data) {

          ctrl.paginationPrinters.current++;

          ctrl.printers = ctrl.printers.concat(data.printers);
          
          ctrl.paginationPrinters.empty = !ctrl.printers.length;

          ctrl.paginationPrinters.disabled = ctrl.printers.length >= data.total_count;

          if(ctrl.autodone === true) {
              loader.stop();
          }
        })
        .catch(function () {
            if(ctrl.autodone === true) {
                loader.stop();
            }
        });
      
    }
    
    function fetchRecommendations() {
      
      ctrl.recommendations = [];
      
      ProductionPlanning.fetchRecommendations({
        date: moment(ctrl.date).format('YYYY-MM-DD'),
        'order_item_ids[]': ctrl.selectedItems,
        type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null
      })
        .then(function (data) {
          ctrl.recommendations = data.recommendations;
        });
      
    }

    function fetchUnplannedTypes() {

      ProductionPlanning.fetchUnplannedTypes()
        .then(function (data) {
          ctrl.unplannedTypes = data.types;

          ctrl.activeType = _.find(ctrl.unplannedTypes, function (type) {
            return type.ProductType.code === 'all';
          });
          
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
    }

    function fetchUnplannedFilter(){
        ProductionPlanning.fetchUnplannedFilter()
            .then(function (data) {
                ctrl.unplannedFilter = data.filters;
                ctrl.activeSize = _.find(ctrl.unplannedFilter.Size, function (size) {
                    return size.Size.code === 'all';
                });
                ctrl.activeProductCode = _.find(ctrl.unplannedFilter.ProductCode, function (pcode) {
                    return pcode.ProductCode.code === 'all';
                });
                ctrl.activeUploadDate = _.find(ctrl.unplannedFilter.UploadDate, function (upload_date) {
                    return upload_date.UploadDate.code === 'all';
                });
                ctrl.activeDate = _.find(ctrl.unplannedFilter.Date, function (date) {
                    return date.Date.code === 'all';
                });
                ctrl.activeCustomer = _.find(ctrl.unplannedFilter.Customer, function (customer) {
                    return customer.Customer.code === 'all';
                });
                ctrl.activeColor = _.find(ctrl.unplannedFilter.Color, function (color) {
                    return color.Color.code === 'all';
                });
                ctrl.activeSku = _.find(ctrl.unplannedFilter.Sku, function (sku) {
                    return sku.Sku.code === 'all';
                });
                ctrl.activeMaker = _.find(ctrl.unplannedFilter.Maker, function (maker) {
                    return maker.Maker.code === 'all';
                });
                ctrl.activeSameDay = _.find(ctrl.unplannedFilter.SameDay, function (isSame) {
                    return isSame.SameDay.code === 1;
                });
                ctrl.activeDeliveryType = _.find(ctrl.unplannedFilter.DeliveryType, function (delivery) {
                    return delivery.DeliveryType.code === 'all';
                });
                ctrl.activeTotalQuantity = _.find(ctrl.unplannedFilter.TotalQuantity, function (isTotal) {
                    return isTotal.TotalQuantity.code === 1;
                });
                ctrl.activeNumberItemTasks = _.find(ctrl.unplannedFilter.NumberItemTask, function (number) {
                    return number.NumberItemTask.code === 'all';
                });
                console.log(ctrl.activeNumberItemTasks);

                loader.stop();
            })
            .catch(function () {
                loader.stop();
            });
    }

      function fetchFactoryFilter() {

          ProductionPlanning.fetchFactoryAll()
              .then(function (data) {
                  ctrl.factoryFilter = data.factories;
                  ctrl.factorySelected = data.factorySelected;
                  ctrl.specificFactories = data.specificFactories;

                  loader.stop();
              })
              .catch(function () {
                  loader.stop();
              });
      }
    
    function setDefault() {
      ctrl.unplanned = [];
      ctrl.unplannedTypes = [];
      ctrl.printers = [];
      ctrl.recommendations = [];
      ctrl.selectedItems = [];
      ctrl.selectedProducts = [];
      ctrl.selectedItemObjs = [];
      ctrl.unplannedFilter = [];
      
      ctrl.activeType = null;
      ctrl.date = moment().toDate();
      ctrl.allSelected = false;

      ctrl.paginationItems.disabled = true;
      ctrl.paginationItems.current = 0;

      ctrl.paginationPrinters.disabled = true;
      ctrl.paginationPrinters.current = 0;

      ctrl.chooseCustomer = [];
      ctrl.chooseNumberItemTask = [];
      ctrl.chooseColor = [];
      ctrl.chooseSize = [];
      ctrl.chooseProductCode = [];
      ctrl.chooseDate = [];
      ctrl.chooseUploadDate = [];
      ctrl.chooseSku = [];
      ctrl.chooseMaker = [];
      ctrl.chooseSameDay = [];
      ctrl.chooseDeliveryType = [];
      ctrl.chooseTotalQuantity = [];

      loader.stop();
      loader.default();

      ctrl.searchQuery = null;
      lastSearch = null;
    }
    
    function getSelected() {

      var selectedItems = [];

      _.forEach(ctrl.unplanned, function (item) {

        if (item.selected) {
          selectedItems.push(item.OrderItem.id);
        }

      });
      
      // if (ctrl.allSelected && (selectedItems.length === ctrl.unplanned.length)) {
      //   return 'all';
      // } else {
        return selectedItems;
      // }
      
    }

      function getSelectedObj() {

          var selectedItems = [];

          _.forEach(ctrl.unplanned, function (item) {

              if (item.selected) {
                  selectedItems.push(item);
              }

          });

          return selectedItems;

      }

      function getSelectedProducts() {

          var selectedProducts = [];

          _.forEach(ctrl.unplanned, function (product) {

              if (product.selected) {
                  selectedProducts.push(product.Product.id);
              }

          });

          return selectedProducts;

      }

    ctrl.reloadFilter = function(ele ,title, name){
        if($.inArray(title , ['カラー','会社名','品番','サイズ','アップロード日','納期日', '指定','メーカー', 'all', '面数']) === -1) {
            var element = "ctrl.choose" + name;
            console.log(element);
            if (ele.selected && ($.inArray(title , eval(element)) === -1)) {
                eval(element).push(title);
            } else {
                for (var i = 0; i < eval(element).length; i++) {
                    if (eval(element)[i] === title) {
                        eval(element).splice(i, 1);
                    }
                }
            }
        } else {
            var unplanned = "ctrl.unplannedFilter." + name;
            var arr = "ctrl.choose" + name;
            defaultArray(arr);
            _.forEach(eval(unplanned), function (item) {
                if (ele.selected) {
                    item.selected = true;
                    if(title === 'all'){
                        var itemTitle = "item." + name + ".code";
                    } else {
                        var itemTitle = "item." + name + ".title";
                    }

                    if($.inArray(eval(itemTitle) , ['カラー','会社名','品番','サイズ','アップロード日','納期日', '指定','メーカー', 'all', '面数']) === -1) {
                        eval(arr).push(eval(itemTitle));
                    }
                } else {
                    item.selected = false;
                }
            });
        }
        ctrl.reloadAll();
    };

    function defaultArray(arr){
        while(eval(arr).length > 0 ){
            eval(arr).pop();
        }
    }

    ctrl.autoPlan = function(){
        if(ctrl.selectedItemObjs.length === 0) {
            return;
        } else {

            loader.start('partial');

            ctrl.autodone = false;

            if(ctrl.allSelected){
                var filterData = {
                    type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null,
                    keywords: ctrl.searchQuery,
                    sort_name: ctrl.activeSort ? ctrl.activeSort : null,
                    size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
                    customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
                    maker: ctrl.chooseMaker.length > 0 ? ctrl.chooseMaker.join(',') : null,
                    date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
                    upload_date: ctrl.chooseUploadDate.length > 0 ? ctrl.chooseUploadDate.join(',') : null,
                    pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
                    color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
                    sku: ctrl.chooseSku.length > 0 ? ctrl.chooseSku.join(',') : null,
                    email_client: ctrl.emailClient,
                    email_client_kornit: ctrl.emailClientKornit,
                    number_item_tasks: ctrl.chooseNumberItemTask.length > 0 ? ctrl.chooseNumberItemTask.join(',') : null,
                    is_same_day: ctrl.chooseSameDay.length > 0 ? ctrl.chooseSameDay.join(',') : null,
                    delivery_type: ctrl.chooseDeliveryType.length > 0 ? ctrl.chooseDeliveryType.join(',') : null,
                    is_total_quantity: ctrl.chooseTotalQuantity.length > 0 ? ctrl.chooseTotalQuantity.join(',') : null,
                }
            }

            ProductionPlanning.autoPlanning({
                date: moment(ctrl.date).format('YYYY-MM-DD'),
                items: ctrl.selectedItemObjs,
                filterData: filterData,
                factory_id: ctrl.factory_id != null ? ctrl.factory_id : ctrl.factorySelected,
            })
                .then(function (data) {
                    if(data.error){
                        var message = data.error_message + '<br/>';
                        var i = 1;

                        $.each(data.message, function(code, data) {
                            var printer = data.printer.join('<br/>');

                            message += '<br/>' + i + '.' + data.customer + ' - ' + data.code + ' - ' + data.color + '<br/>' + printer + '<br/>';
                            i++;
                        });

                        message += '<br/>設定を確認してください。';

                        var confirm = $mdDialog.confirm({
                            template: utils.infoTemplate(message),
                            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                                $scope.cancel = function () {
                                    $mdDialog.cancel();
                                };
                            }]
                        }).targetEvent();

                        $mdDialog.show(confirm);

                    } else {
                        Store.event('CreatePlan:Update').emit();
                    }
                    ctrl.reloadAll();
                })
                .catch(function () {
                    ctrl.reloadAll();
                });
        }
    };

    ctrl.autoPlanKornit = function(){
        if(ctrl.selectedItemObjs.length === 0) {
            return;
        } else {

            loader.start('partial');

            ctrl.autodone = false;

            if(ctrl.allSelected){
                var filterData = {
                    type_code: ctrl.activeType ? ctrl.activeType.ProductType.code : null,
                    keywords: ctrl.searchQuery,
                    sort_name: ctrl.activeSort ? ctrl.activeSort : null,
                    size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
                    customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
                    maker: ctrl.chooseMaker.length > 0 ? ctrl.chooseMaker.join(',') : null,
                    date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
                    upload_date: ctrl.chooseUploadDate.length > 0 ? ctrl.chooseUploadDate.join(',') : null,
                    pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
                    color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
                    sku: ctrl.chooseSku.length > 0 ? ctrl.chooseSku.join(',') : null,
                    email_client: ctrl.emailClient,
                    email_client_kornit: ctrl.emailClientKornit,
                    number_item_tasks: ctrl.chooseNumberItemTask.length > 0 ? ctrl.chooseNumberItemTask.join(',') : null,
                    is_same_day: ctrl.chooseSameDay.length > 0 ? ctrl.chooseSameDay.join(',') : null,
                    delivery_type: ctrl.chooseDeliveryType.length > 0 ? ctrl.chooseDeliveryType.join(',') : null,
                    is_total_quantity: ctrl.chooseTotalQuantity.length > 0 ? ctrl.chooseTotalQuantity.join(',') : null,
                }
            }

            ProductionPlanning.autoPlanning({
                date: moment(ctrl.date).format('YYYY-MM-DD'),
                items: ctrl.selectedItemObjs,
                filterData: filterData,
                kornit: true,
                factory_id: ctrl.factory_id != null ? ctrl.factory_id : ctrl.factorySelected,
            })
                .then(function (data) {
                    if(data.error){
                        var message = data.error_message + '<br/>';

                        var confirm = $mdDialog.confirm({
                            template: utils.infoTemplate(message),
                            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                                $scope.cancel = function () {
                                    $mdDialog.cancel();
                                };
                            }]
                        }).targetEvent();

                        $mdDialog.show(confirm);

                    } else {
                        Store.event('CreatePlan:Update').emit();
                    }
                    ctrl.reloadAll();
                })
                .catch(function () {
                    ctrl.reloadAll();
                });
        }
    };

      ctrl.filterEmbroidery = function(){
          ctrl.is_emb = ctrl.is_emb ? null : true;
          ctrl.reloadAll();
      };

      ctrl.onFactoryChange = function (factoryId) {
          ctrl.factory_id = factoryId;

          if(ctrl.specificFactories.includes(ctrl.factory_id)){
              ctrl.selectedItems = [];
              ctrl.printers = [];
              ctrl.unplanned = [];
              ctrl.selectedProducts = [];

              ctrl.reloadUnplanned();
              ctrl.reloadPrinters();
          }else {
              ctrl.reloadAll();
          }

          if(ctrl.selectedItems.length !== 0) {
              ctrl.reloadPrinters();
          }
      }

  }
  
})();