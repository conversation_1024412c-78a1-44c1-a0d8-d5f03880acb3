(function () {

  angular.module('printty')
    .component('adminUserDetails', {
      controller: AdminUserDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/users/user-details.html',
      bindings: {
        isShown: '<',
        type: '<',
        active: '<',
        
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&'
      }
    });

  function AdminUserDetailsCtrl($scope, utils, $mdDialog, Store, $mdSidenav, $timeout, Users,Factory) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.user = null;
    
    ctrl.isAdd = true;
    ctrl.showMode = true;
    
    ctrl.loadingType = 'full';

    ctrl.customers = null;
    ctrl.roles = null;

    var userForm, userClone;
    
    var subscriptions = [];
    var sidenav = {
      open: function () {
        $mdSidenav('user-details').open();
      },
      close: function () {
        $mdSidenav('user-details').close();
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );
    
    /****** methods ******/
    ctrl.$onInit = function() {

      subscriptions.push(
        
        Store.data('UserRoles').subscribe(function (data) {
          ctrl.roles = data;
        }),

        Store.data('UserCustomers').subscribe(function (data) {
          ctrl.customers = data;
        }),

        Store.data('UserFactories').subscribe(function (data) {
          if(data){
            ctrl.factories = data;
            if(data[0].is_franchise == 1){
              ctrl.check_is_franchise = true;
            }else{
              ctrl.check_is_franchise = false;
            }
          }
        })
        
      )

    };

    ctrl.$postLink = function () {

      userForm = $scope.userForm;
      
      $mdSidenav('user-details').onClose(function () {
        $timeout(function () { ctrl.onClose(); }, 400);
      });

    };
    
    ctrl.$onChanges = function (changes) {
      
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        userClone = _.cloneDeep(ctrl.user);
        ctrl.showMode = false;
      } else {
        ctrl.user = userClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      userForm.$setSubmitted();

      if (userForm.$invalid) return;
      
      if  ( _.isEqual(ctrl.user, userClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      loader.start('partial');

      Users.updateUser(ctrl.user)
        .then(function () {
          updateOriginal();
          
          setFormPristine();
          ctrl.showMode = true;
        })
        .finally(function () {
          ctrl.user.User.password = null;
          loader.stop();
        });

    };

    ctrl.save = function () {

      userForm.$setSubmitted();

      if (userForm.$invalid) return;

      loader.start('partial');

      Users.createUser(ctrl.user)
        .then(function () {
          ctrl.onCreate();
          ctrl.close();
        })
        .catch(function () {
          loader.stop();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('ユーザーを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteUser);

    };

    ctrl.close = function () {
      sidenav.close();
    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          loader.start();
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          loader.stop();
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd) {
        fetchUser();
      }

      sidenav.open();

    }

    function fetchUser() {

      loader.start();

      Users.fetchUser(ctrl.active.userId)
        .then(function (data) {
          ctrl.user = data;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    }

    function updateOriginal() {

      var userData = _.cloneDeep(ctrl.user);

      ctrl.onUpdate({
        user: userData
      });

    }

    function deleteUser() {

      loader.start('partial');

      Users.deleteUser(ctrl.user.User.id)
        .then(function () {

          ctrl.onDelete({
            userId: ctrl.user.User.id
          });

          ctrl.close();
        })
        .catch(function () {
          loader.stop();
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.user = null;

      setFormPristine();

      loader.stop();
    }

    function setFormPristine() {
      if (userForm) {
        userForm.$setPristine();
        userForm.$setUntouched();
      }
    }

  }

})();