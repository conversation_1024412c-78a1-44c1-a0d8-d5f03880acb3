(function () {

  angular.module('printty')
    .component('adminUsers', {
      controller: AdminUsersCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/users/users.html'
    });

  function AdminUsersCtrl($timeout, Auth, $state, Store, utils, Users, Customers,Factory) {
    
    "ngInject";

    if (!Auth.isAuthorized('users')) {
      return $state.go( Auth.defaultRoute );
    }

    /****** variables ********/
    var ctrl = this;
    
    ctrl.users = [];
    ctrl.userRoles = [];

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      role_id: null,

      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };
    
    ctrl.userDetails = {
      isShown: false,
      type: 'add',
      active: null
    };
    
    var subscriptions = [];

    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      4
    );

    ctrl.infiniteUsers = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.users[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchUsers(index);
      }
    };

    /****** methods ******/
    ctrl.$onInit = function () {

      loader.start();

      getRolesCond();
      getRolesChange();
      getCustomers();
      getFactories();

      subscriptions.push(
        Store.event('admin:create:user').subscribe(function () { ctrl.viewCreate(); }),

        Store.event('sidebarActiveStatus')
          .subscribe(function (roleId) {
            ctrl.pagination.role_id = roleId;
            ctrl.reload();
          })
        
      );
      
    };
    
    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.users = [];

      ctrl.infiniteUsers.numLoaded_ = 0;
      ctrl.infiniteUsers.toLoad_ = 0;
      ctrl.infiniteUsers.getItemAtIndex(1);
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };
    
    ctrl.viewDetails = function (user) {
      ctrl.userDetails.type = 'details';
      ctrl.userDetails.isShown = true;
      ctrl.userDetails.active = {
        userId: user.User.id
      }
    };
    
    ctrl.viewCreate = function () {
      ctrl.userDetails.type = 'add';
      ctrl.userDetails.isShown = true;
      ctrl.userDetails.active = {
        userId: null
      }
    };

    ctrl.userDetails.onClose = function () {
      this.isShown = false;
    };

    ctrl.userDetails.onCreate = function () {
      ctrl.reload();
    };

    ctrl.userDetails.onUpdate = function (user) {
      var originalUser = _.find(ctrl.users, function (userItem) {
        return userItem.User.id == user.User.id;
      });
      
      var originalRole = _.find(ctrl.userRoles, function (role) {
        return role.Role.id == user.User.role_id;
      });

      user.User.role_title = originalRole.Role.title;
      
      _.extend(originalUser, user);
    };

    ctrl.userDetails.onDelete = function (userId) {
      _.remove(ctrl.users, function(user) {
        return user.User.id == userId;
      });
    };

    ctrl.setUserRole = function (user, role) {

      var originalRole = {
        id: user.User.role_id,
        title: user.User.role_title
      };

      Users.updateUser({
        User: {
          id: user.User.id,
          role_id: role.Role.id
        }
      })
        .catch(function() {
          user.User.role_id = originalRole.id;
          user.User.role_title = originalRole.title;
        });

      user.User.role_id = role.Role.id;
      user.User.role_title = role.Role.title;

    };

    ctrl.downloadDeliveryCompany = function () {
      utils.listLoading.show();

      Users.downloadDeliveryCompanyCSV().then(function () {
        utils.listLoading.hide();
      })
          .catch(function () {
            utils.listLoading.hide();
          })
    }

    /****** private ******/
    function fetchUsers(index) {

      if (ctrl.infiniteUsers.toLoad_ >= index || ctrl.pagination.disabled) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        $timeout(function () {
          ctrl.pagination.loading = false;
          loader.start(false);
        });
      } else {
        $timeout(function () {
          ctrl.pagination.loading = true;
        });
      }

      ctrl.infiniteUsers.toLoad_ += ctrl.pagination.perPage;

      Users.fetchUsers(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
          keywords: ctrl.pagination.searchQuery,
          role_id: ctrl.pagination.role_id
        }
      ).then(
        function (data) {

          ctrl.pagination.current++;

          ctrl.infiniteUsers.numLoaded_ = ctrl.infiniteUsers.toLoad_ - ctrl.pagination.perPage + data.users.length;
          ctrl.users = ctrl.users.concat(data.users);

          ctrl.pagination.empty = !ctrl.users.length;
          ctrl.pagination.reload = false;
          ctrl.pagination.loading = false;

          ctrl.pagination.disabled = data.total_count <= ctrl.users.length;

          $timeout(function () {
            loader.stop();
          }, 120);

        }
      );

    }
    
    function getRolesCond() {

      Users.fetchRolesCond().then(function (data) {
        var roles = _.flatMap(data.roles, function(role) {
          return role.Role;
        });

        Store.data('statuses').update(roles);

        loader.stop();
      });
      
    }
    
    function getRolesChange() {

      Users.fetchRolesChange().then(function (data) {

        ctrl.userRoles = data.roles;

        Store.data('UserRoles').update(ctrl.userRoles);

        loader.stop();
      });

    }

    function getCustomers() {

      Customers.fetchCustomers().then(function (data) {
        Store.data('UserCustomers').update(data.customers);
        loader.stop();
      });

    }

    function getFactories() {

      Factory.fetchFactoryUser().then(function (data) {
        Store.data('UserFactories').update(data.factories);
        loader.stop();
      });

    }
    
  }
  
})();