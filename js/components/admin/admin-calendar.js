(function() {

  angular.module('printty')
    .component('adminCalendar', {
      controller: adminCalendarCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/admin-calendar.html',
      bindings: {
        viewCalendar: '=openOn'
      }
    });

  function adminCalendarCtrl($mdSidenav, ProductCalendar, $q) {

    "ngInject";

    var vm = this;
    vm.currentMonth = moment();
    vm.months = null;
    vm.highlightDays = [];
    vm.selectedDays = [];

    vm.typesDay = [];
    vm.daysOfWeek = [];
    vm.selectedDayOfWeek = [];
    vm.rules = null;
    vm.showRules = false;

    vm.$onInit = $onInit;
    vm.getSelectedText = getSelectedText;
    vm.typeDaysChange = typeDaysChange;
    vm.weekDaysChange = weekDaysChange;
    vm.onSelectedWeekdaysChange = onSelectedWeekdaysChange;
    vm.addOrDelete = addOrDelete;
    vm.addOrDeleteSpecial = addOrDeleteSpecial;
    vm.dayClick = dayClick;
    vm.onMonthChanged = onMonthChanged;
    vm.close = close;
    vm.deleteRuleFromTable = deleteRuleFromTable;
    vm.addNewRule = addNewRule;
    vm.showForm = showForm;
    vm.daySelect = daySelect;
    vm.hideForm = hideForm;

    var sidenav = {
      open: function() {
        $mdSidenav('admin-calendar').open();
      },
      close: function() {
        $mdSidenav('admin-calendar').close();
      }
    };

    vm.viewCalendar = function() {
      resetVariables();
      initCalendar();
      sidenav.open();
    };

    function $onInit() {
      initWeekDays();
    }

    function resetVariables() {
      vm.selectedDayOfWeek = [];
      vm.selectedDays = [];
      vm.rules = null;
      vm.showRules = false;
    }

    function close() {
      resetVariables();
      sidenav.close();
    }


    function initWeekDays() {
      vm.typesDay = {
        weekdays: {name: "平日", special: true},
        holidays: {name: "祝日", special: true}
      };
      vm.daysOfWeek = {
        monday: {dayOfWeek: 1, name: '月曜日', isWeekDay: true},
        tuesday: {dayOfWeek: 2, name: '火曜日', isWeekDay: true},
        wednesday: {dayOfWeek: 3, name: '水曜日', isWeekDay: true},
        thursday: {dayOfWeek: 4, name: '木曜日', isWeekDay: true},
        friday: {dayOfWeek: 5, name: '金曜日', isWeekDay: true},
        saturday: {dayOfWeek: 6, name: '土曜日', isWeekDay: false},
        sunday: {dayOfWeek: 0, name: '日曜日', isWeekDay: false}
      };
    }

    function getSelectedText() {
      var res = [];
      var hasHolidays = vm.selectedDayOfWeek.indexOf(vm.typesDay.holidays) > -1;
      var hasWeekdays = vm.selectedDayOfWeek.indexOf(vm.typesDay.weekdays) > -1;
      if (hasWeekdays)
        res.push(vm.typesDay.weekdays);
      else {
        res = res.concat(_.filter(vm.selectedDayOfWeek, function(val) {
          return val.isWeekDay && val.special;
        }));
      }
      if (hasHolidays)
        res.push(vm.typesDay.holidays);
      else {
        res = res.concat(_.filter(vm.selectedDayOfWeek, function(val) {
          return !val.isWeekDay && !val.special;
        }));
        res = Array.from(new Set(res));
      }
      return res.length === 0 ? "営業なし曜日" : _.map(res, 'name').join(",");
    }

    function initCalendar() {
      vm.loadingType = 'partial';
      ProductCalendar.list()
        .then(function(resp) {
          angular.forEach(resp.rules, function(r) {
            var rule = r.ProductionCalendarRule;
            if (vm.typesDay[rule.key]) {
              typeDaysChange(vm.typesDay[rule.key], true);
              vm.selectedDayOfWeek.push(vm.typesDay[rule.key]);
            }
            if (vm.daysOfWeek[rule.key]) {
              weekDaysChange(vm.daysOfWeek[rule.key]);
              vm.selectedDayOfWeek.push(vm.daysOfWeek[rule.key]);
            }
          });
          vm.rules = resp.rules;
          fillSelectedDays();
        })
        .finally(function() {
          vm.loadingType = 'stopped';
        });
    }

    function dayClick(event, day) {
      vm.highlightDays.push({date: day.date, css: "is-loading"});
      day.loading = true;
      addDeleteDayClick(day)
        .finally(function() {
          vm.highlightDays.length = 0;
        });
    }

    function addDeleteDayClick(day) {
      var deferred = $q.defer();
      var formatValue = day.date.format("YYYY-MM-DD");
      var isDeleted = false;
      angular.forEach(vm.rules, function(r) {
        var rule = r.ProductionCalendarRule;
        if (rule.key === "date" && rule.value === formatValue) {
          var weekdays = _.map(vm.selectedDayOfWeek, 'dayOfWeek');
          if (weekdays.indexOf(day.date.day()) > -1) {
            var promise = deleteRule(day.date, false);
            var existDay = hasDay(day.date);
            if (existDay)
              vm.selectedDays.splice(vm.selectedDays.indexOf(existDay), 1);
            if (rule.type === "include") {
              promise
                .then(function() {
                  addRule(day.date, 'exclude', false).then(deferred.resolve).catch(deferred.reject);
                })
                .catch(deferred.reject);
            }
            else {
              promise.then(deferred.resolve).catch(deferred.reject);
            }
          }
          else {
            var promise = deleteRule(day.date, false);
            if (rule.type === "exclude") {
              promise.then(function() {
                addRule(day.date, 'include', false).then(deferred.resolve).catch(deferred.reject);
              }).catch(deferred.reject);
              var existDay = hasDay(day.date);
              if (!existDay)
                vm.selectedDays.push(day.date);
            }
            else {
              promise.then(deferred.resolve).catch(deferred.reject);
            }
          }
          isDeleted = true;
          return false;
        }
      });
      if (!isDeleted) {
        var weekdays = _.map(vm.selectedDayOfWeek, 'dayOfWeek');
        if (weekdays.indexOf(day.date.day()) > -1) {
          addRule(day.date, 'exclude', false).then(deferred.resolve).catch(deferred.reject);
        } else {
          addRule(day.date, 'include', false).then(deferred.resolve).catch(deferred.reject);
        }
      }
      return deferred.promise;
    }

    function onMonthChanged(newMonth) {
      vm.currentMonth = newMonth;
      fillSelectedDays();
    }

    function fillSelectedDays() {
      vm.selectedDays = [];
      angular.forEach(vm.selectedDayOfWeek, function(day) {
        if (day === vm.typesDay.holidays) {
          fillDays(6); // Saturday
          fillDays(0); // Sunday
        } else if (day === vm.typesDay.weekdays) {
          fillDays(1);
          fillDays(2);
          fillDays(3);
          fillDays(4);
          fillDays(5);
        } else {
          fillDays(day.dayOfWeek);
        }
      });
      fillDaysByDate();
    }

    function hasDay(day) {
      return _.find(vm.selectedDays, function(d) {
        return d.isSame(day, 'date');
      });
    }

    function fillDaysByDate() {
      _(vm.rules)
        .filter(function(r) {
          return r.ProductionCalendarRule.key === "date";
        })
        .each(function(r) {
          var rule = r.ProductionCalendarRule;
          var momentDate = moment(rule.value, 'YYYY-MM-DD');
          if (rule.type === "include") {
            if (!(!!hasDay(momentDate)))
              vm.selectedDays.push(momentDate);
          } else if (rule.type === "exclude") {
            var existDay = hasDay(momentDate);
            if (existDay)
              vm.selectedDays.splice(vm.selectedDays.indexOf(existDay), 1);
          }
        });
    }

    function fillDays(dayOfWeek) {
      var day = moment(vm.currentMonth).startOf('month').day(dayOfWeek);
      var countWeeks = 6;
      for (var i = 0; i <= countWeeks; i++) {
        var newDay = moment(day);
        if (!(!!hasDay(newDay)))
          vm.selectedDays.push(newDay);
        day.add(1, 'w');
      }
    }

    function onSelectedWeekdaysChange() {
      fillSelectedDays();
    }


    function typeDaysChange(day, isInit) {
      var isHoliday = day === vm.typesDay.holidays;
      var isWeekdays = day === vm.typesDay.weekdays;
      if (isHoliday && (vm.selectedDayOfWeek.indexOf(vm.typesDay.holidays) === -1)) {
        addSelectedDayOfWeek(vm.daysOfWeek.saturday);
        addSelectedDayOfWeek(vm.daysOfWeek.sunday);
      } else {
        if (!isWeekdays && !isInit) {
          removeSelectedDayOfWeek(vm.daysOfWeek.saturday);
          removeSelectedDayOfWeek(vm.daysOfWeek.sunday);
        }
      }

      if (isWeekdays && (vm.selectedDayOfWeek.indexOf(vm.typesDay.weekdays) === -1)) {
        angular.forEach(vm.daysOfWeek, function(d) {
          if (d.isWeekDay)
            addSelectedDayOfWeek(d);
        })
      } else {
        if (!isHoliday) {
          angular.forEach(vm.daysOfWeek, function(d) {
            if (d.isWeekDay && !isInit)
              removeSelectedDayOfWeek(d);
          });
        }
      }
    }

    function weekDaysChange(day) {
      var isHolidayClick = day === vm.daysOfWeek.saturday || day === vm.daysOfWeek.sunday;
      if (isHolidayClick) {
        if (vm.selectedDayOfWeek.indexOf(vm.daysOfWeek.saturday) > -1 && vm.selectedDayOfWeek.indexOf(vm.daysOfWeek.sunday) > -1) {
          removeSelectedDayOfWeek(vm.typesDay.holidays);
        }
        else {
          if (vm.selectedDayOfWeek.indexOf(vm.daysOfWeek.sunday) > -1 && day === vm.daysOfWeek.saturday)
            addSelectedDayOfWeek(vm.typesDay.holidays);
          if (vm.selectedDayOfWeek.indexOf(vm.daysOfWeek.saturday) > -1 && day === vm.daysOfWeek.sunday)
            addSelectedDayOfWeek(vm.typesDay.holidays);
        }
      }

      var isWeekDaysClick = day.isWeekDay;
      var isAdding = vm.selectedDayOfWeek.indexOf(day) === -1;
      if (isWeekDaysClick) {
        var cntWeekDays = 0;
        angular.forEach(vm.selectedDayOfWeek, function(d) {
          if (d.isWeekDay) {
            cntWeekDays++;
          }
        });
        if (cntWeekDays === 5) {
          removeSelectedDayOfWeek(vm.typesDay.weekdays);
        }
        else {
          if (cntWeekDays === 4 && isAdding) {
            addSelectedDayOfWeek(vm.typesDay.weekdays);
          }
        }
      }
    }


    //private
    function addSelectedDayOfWeek(day) {
      if (vm.selectedDayOfWeek.indexOf(day) === -1)
        vm.selectedDayOfWeek.push(day);
    }

    function removeSelectedDayOfWeek(day) {
      var indx = vm.selectedDayOfWeek.indexOf(day);
      if (indx !== -1)
        vm.selectedDayOfWeek.splice(indx, 1);
    }

    function getKeyBayValue(val) {
      for (var p in vm.typesDay) {
        if (vm.typesDay[p] === val)
          return p;
      }
      for (var p in vm.daysOfWeek) {
        if (vm.daysOfWeek[p] === val)
          return p;
      }
    }

    //Api

    function addOrDelete(day) {
      if (vm.selectedDayOfWeek.indexOf(day) > -1) {
        deleteRule(day, true);
      }
      else {
        addRule(day, 'include', true);
      }
    }

    function addOrDeleteSpecial(day) {
      var isHoliday = day === vm.typesDay.holidays;
      var isWeekdays = day === vm.typesDay.weekdays;
      var isAdding = vm.selectedDayOfWeek.indexOf(day) === -1;
      if (isHoliday) {
        if (isAdding) {
          addRule(vm.daysOfWeek.sunday, 'include', true);
          addRule(vm.daysOfWeek.saturday, 'include', true);
        }
        else {
          deleteRule(vm.daysOfWeek.sunday, true);
          deleteRule(vm.daysOfWeek.saturday, true);
        }
      }
      if (isWeekdays) {
        if (isAdding) {
          addRule(vm.daysOfWeek.monday, 'include', true);
          addRule(vm.daysOfWeek.tuesday, 'include', true);
          addRule(vm.daysOfWeek.wednesday, 'include', true);
          addRule(vm.daysOfWeek.thursday, 'include', true);
          addRule(vm.daysOfWeek.friday, 'include', true);
        }
        else {
          deleteRule(vm.daysOfWeek.monday, true);
          deleteRule(vm.daysOfWeek.tuesday, true);
          deleteRule(vm.daysOfWeek.wednesday, true);
          deleteRule(vm.daysOfWeek.thursday, true);
          deleteRule(vm.daysOfWeek.friday, true);
        }
      }
    }

    function addRule(day, type, isShowloading) {
      var sendData = null;
      if (moment.isMoment(day)) {
        sendData = {
          "ProductionCalendarRule": {
            "type": type,
            "key": "date",
            "value": day.format("YYYY-MM-DD")
          }
        };
      } else {
        sendData = {
          "ProductionCalendarRule": {
            "type": "include",
            "key": getKeyBayValue(day),
            "value": 1
          }
        };
      }
      // if(isShowloading)
      //   vm.loadingType = 'partial';
      return ProductCalendar.create(sendData)
        .then(function(resp) {
          sendData.ProductionCalendarRule.id = resp.ProductionCalendarRule.id;
          vm.rules.push(sendData);
        }).finally(function() {
          vm.loadingType = 'stopped';
        });
    }

    function deleteRule(day, isShowloading) {
      var ruleToDelete = null;
      var value = null;
      var key = null;
      if (moment.isMoment(day)) {
        key = 'date';
        value = day.format("YYYY-MM-DD");
      }
      else {
        key = getKeyBayValue(day);
        value = 1;
      }
      angular.forEach(vm.rules, function(r) {
        if (r.ProductionCalendarRule.value == value && r.ProductionCalendarRule.key == key) {
          ruleToDelete = r;
          return false;
        }
      });
      if (ruleToDelete) {
        // if(isShowloading)
        //   vm.loadingType = 'partial';
        return ProductCalendar.delete(ruleToDelete.ProductionCalendarRule.id)
          .finally(function() {
            vm.rules.splice(vm.rules.indexOf(ruleToDelete), 1);
            vm.loadingType = 'stopped';
          });
      }
      else {
        return $q.resolve(true);
      }
    }

    function deleteRuleFromTable(rule) {
      rule.loading = true;
      if (rule.ProductionCalendarRule.key === "date") {
        var momentDate = moment(rule.ProductionCalendarRule.value, "YYYY-MM-DD");
        var promise = deleteRule(momentDate, true);
        if (promise) {
          promise.then(function() {
            rule.loading = false;
            fillSelectedDays();
          });
        }
      }
      else {
        var weekDay = vm.daysOfWeek[rule.ProductionCalendarRule.key];
        if (weekDay) {
          var promise = deleteRule(weekDay, true);
          if (promise) {
            promise.then(function() {
              if (weekDay.isWeekDay)
                removeSelectedDayOfWeek(vm.typesDay.weekdays);
              else
                removeSelectedDayOfWeek(vm.typesDay.holidays);
              rule.loading = false;
              removeSelectedDayOfWeek(weekDay);
              fillSelectedDays();
            });
          }
        }
      }
    }

    function showForm() {
      if (vm.showFormRule)
        return;
      vm.newRule = {};
      vm.showFormRule = true;
    }

    function addNewRule(form) {
      if (!vm.newRule.date)
        return;
      vm.loadingType = 'partial';
      dayClick(null, vm.newRule.day)
        .then(function() {
          fillSelectedDays();
          vm.newRule = {};
          vm.showFormRule = false;
        })
        .finally(function() {
          vm.loadingType = 'stopped';
        });
    }

    vm.newRule = {};
    function daySelect(event, day) {
      event.preventDefault();
      if (vm.newRule.date && !vm.newRule.notDelete) {
        var indx = vm.selectedDays.indexOf(hasDay(moment(vm.newRule.date, "YYYY-MM-DD")));
        if (indx > -1)
          vm.selectedDays.splice(indx, 1);
      }
      vm.newRule.date = day.date.format("YYYY-MM-DD");
      vm.newRule.day = day;
      var indx = vm.selectedDays.indexOf(hasDay(day.date));
      if (indx == -1) {
        vm.newRule.type = "include";
        vm.selectedDays.push(day.date);
        vm.newRule.notDelete = false;
      } else {
        vm.newRule.notDelete = true;
        vm.newRule.type = "exclude";
      }
    }

    function hideForm() {
      var indx = vm.selectedDays.indexOf(hasDay(vm.newRule.date));
      if (indx !== -1 && !vm.newRule.notDelete) {
        vm.selectedDays.splice(indx, 1);
      }
      vm.newRule = {};
      vm.showFormRule = false;
    }


  }

})();