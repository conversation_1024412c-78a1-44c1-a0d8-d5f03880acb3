(function () {

    angular.module('printty')
        .component('adminOrderStatusSearch', {
            controller: OrderStatusSearchCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/admin/order-status-search/order-status-search.html'
        });

    function OrderStatusSearchCtrl($scope, Store, utils, OrderStatusSearch, $timeout, $mdDialog) {

        'ngInject';

        var vm = this;

        /**** variables ******/
        vm.orderStatusSearch = [];

        vm.pagination = {
            perPage: 40,

            searchQuery: null,
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,
            disabled: false
        };

        vm.taskDetails = {
            isShown: false,
            active: {}
        };

        vm.statusChange = [
            {'key': 'qr_waiting', 'value' : 'QR印刷待ち' },
            {'key': 'print_waiting', 'value' : '印刷待ち' },
            {'key': 'inspection_waiting', 'value' : '検品待ち' },
            {'key': 'delivery_request_waiting', 'value' : '発送準備中' },
            {'key': 'delivery_preparing_complite', 'value' : '配送準備済' },
            {'key': 'delivery_complite', 'value' : '発送完了' },
        ];

        vm.infiniteOrderStatusSearch = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {
                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return vm.orderStatusSearch[index];
            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                fetchOrderStatusSearch(index);
            }
        };

        vm.total_count = '';
        vm.tasks = [];
        vm.order_number;
        vm.have_tasks;

        var subscriptions = [];

        /****** methods ******/
        vm.$onInit = function () {
            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('admin:create:error-log').subscribe(function () {
                    vm.viewCreate();
                })
            );
        };

        vm.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) {
                subscription.unsubscribe();
            });
        };

        vm.reload = function() {
            vm.pagination.reload = true;
            vm.pagination.disabled = false;

            vm.orderStatusSearch = [];

            vm.infiniteOrderStatusSearch.numLoaded_ = 0;
            vm.infiniteOrderStatusSearch.toLoad_ = 0;
            vm.infiniteOrderStatusSearch.getItemAtIndex(1);
        };

        vm.search = function () {
            if (vm.pagination.lastSearch === vm.pagination.searchQuery) {
                return;
            }
            vm.pagination.lastSearch = vm.pagination.searchQuery;
            vm.reload();
        };

        vm.liveSearch = function () {
            utils.delay(function() {
                vm.search();
            }, 350 );
        };

        vm.clearSearch = function () {
            vm.pagination.searchQuery = '';
            vm.search();
        };

        vm.onTaskClose = function () {
            vm.taskDetails.isShown = false;
        };

        vm.onTaskDelete = function(taskId) {
            _.remove(vm.tasks, function(task) {
                return task.Task.id == taskId;
            });
        };

        vm.onTaskCreate = function () {
            vm.reload();
        };

        var loader = new utils.loadCounter(
            function(isBlocking) {
                utils.listLoading.show(isBlocking);
            },
            function() {
                utils.listLoading.hide();
            },
            2
        );

        vm.viewDetails = function(task) {
            vm.taskDetails.isShown = true;
            vm.taskDetails.active = {
                taskId: task.task_id,
                taskStepId: task.TaskStepItem[0].step_id,
            };
        };

        vm.changeStatusTask = function(task, statusKey) {
            return changeStatus(task.task_id, task.id,statusKey);
        };

        function changeStatus(tasks,taskStepId,statusKey) {
            var confirm = $mdDialog.confirm({
                template: utils.changeStatusTemplate('本当に変更してもよいですか?'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
            });

            $mdDialog.show(confirm).then(function () {
                loader.start(false);
                return OrderStatusSearch.changeStatus(tasks, taskStepId, statusKey)
                    .then(function() {
                        allSelected = false;
                        vm.reload();
                    })
                    .finally(loader.stop);
            });
        }
        /****** private ******/
        function fetchOrderStatusSearch(index) {

            if (vm.infiniteOrderStatusSearch.toLoad_ >= index || vm.pagination.disabled) {
                return;
            }

            vm.pagination.disabled = true;

            if (vm.pagination.reload) {
                $timeout(function () {
                    vm.pagination.loading = false;
                    utils.listLoading.show(false);
                });
            } else {
                $timeout(function () {
                    vm.pagination.loading = true;
                });
            }

            vm.infiniteOrderStatusSearch.toLoad_ += vm.pagination.perPage;

            OrderStatusSearch.fetchOrderStatusSearch(
                {
                    paging_size: vm.pagination.perPage,
                    paging_offset: vm.orderStatusSearch.length,
                    keywords: vm.pagination.searchQuery,
                }
            ).then(
                function (data) {

                        vm.infiniteOrderStatusSearch.numLoaded_ = vm.infiniteOrderStatusSearch.toLoad_ - vm.pagination.perPage + data.orders.length;
                        vm.orderStatusSearch = vm.orderStatusSearch.concat(data.orders);
                        vm.total_count = data.total_count;
                        vm.have_tasks = data.have_tasks;
                        vm.order_number = data.order_number;

                        vm.tasks = data.orders.Tasks
                        vm.pagination.empty = !vm.orderStatusSearch.length;
                        vm.pagination.reload = false;
                        vm.pagination.loading = false;

                        vm.pagination.disabled = data.total_count <= vm.orderStatusSearch.length;

                        $timeout(function () {
                            utils.listLoading.hide();
                        }, 120);

                }
            );
        }

        vm.cancelOrder = function(ev) {
            var orderNumber;

            orderNumber = vm.order_number;

            if (typeof orderNumber == 'undefined') {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.cancelTemplate('注文をキャンセルしますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {

                var params = {
                    customer_order_number : orderNumber,
                };

                utils.globalLoading.show();

                OrderStatusSearch.cancel(params).then(
                    function () {

                        utils.globalLoading.hide();
                        vm.reload();
                    },
                    function() {
                        utils.globalLoading.hide();
                    }
                );
            });
        }
    }

})();