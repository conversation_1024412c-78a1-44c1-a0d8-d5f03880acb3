(function () {

    angular.module('printty')
        .component('adminBodyOrder', {
            controller: BodyOrderCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/admin/body-order/body-order.html'
        });

    function BodyOrderCtrl(Store, utils, BodyOrderApi, $timeout) {

        'ngInject';

        var vm = this;

        /**** variables ******/
        vm.body_order = [];

        vm.date = moment().toDate();

        vm.startDate = moment(vm.date, 'YYYY/MM/DD').toDate();
        vm.endDate = moment(vm.date, 'YYYY-MM-DD').toDate();

        vm.startHour = '00';
        vm.startMinute = '00';

        vm.endHour = '00';
        vm.endMinute = '00';

        vm.pagination = {
            searchQuery: null,
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,

            customer_id: null,
            source_id: null,
        };

        /****** methods ******/
        vm.$onInit = function () {
            utils.listLoading.show(true);

            fetchBodyOrder();
        };

        vm.reload = function() {
            vm.pagination.reload = true;
            vm.body_order = [];
            vm.startDate = moment(vm.date, 'YYYY/MM/DD').toDate();
            vm.endDate = moment(vm.date, 'YYYY-MM-DD').toDate();
            vm.startHour = '00';
            vm.startMinute = '00';

            vm.endHour = '00';
            vm.endMinute = '00';
            fetchBodyOrder();
        };

        vm.search = function () {
            if (vm.pagination.lastSearch === vm.pagination.searchQuery) {
                return;
            }
            vm.pagination.lastSearch = vm.pagination.searchQuery;
            vm.reload();
        };

        vm.liveSearch = function () {
            utils.delay(function() {
                vm.search();
            }, 350 );
        };

        vm.clearSearch = function () {
            vm.pagination.searchQuery = '';
            vm.search();
        };

        /****** private ******/
        function fetchBodyOrder() {
            if (vm.pagination.reload) {
                $timeout(function () {
                    vm.pagination.loading = false;
                    utils.listLoading.show(false);
                });
            } else {
                $timeout(function () {
                    vm.pagination.loading = true;
                });
            }

            BodyOrderApi.fetchBodyOrder(
                {
                    conditions_keywords: vm.pagination.searchQuery,
                }
            ).then(
                function (data) {
                    vm.body_order = data.body_order;

                    vm.pagination.empty = !vm.body_order.length;
                    vm.pagination.reload = false;
                    vm.pagination.loading = false;

                    utils.listLoading.hide();
                }
            );
        }

        vm.downloadBodyOrderCSV = function () {
            var selectedCustomer;
            var selectedSource;

            selectedCustomer = getSelectedCustomer();
            selectedSource = getSelectedSource();

            if ((_.isArray(selectedCustomer) && !selectedCustomer.length) || (_.isArray(selectedSource) && !selectedSource.length)) {
                return;
            }

            if(typeof vm.startHour === 'undefined' || typeof vm.startMinute === 'undefined' || typeof vm.endHour === 'undefined' || typeof vm.endMinute === 'undefined'){
                return;
            }

            var startTime = moment(vm.startDate).format('YYYY-MM-DD') + ' ' + vm.startHour + ':' + vm.startMinute + ':00';
            var endTime = moment(vm.endDate).format('YYYY-MM-DD') + ' ' + vm.endHour + ':' + vm.endMinute + ':59';
            var params = {
                'customer_id[]' : selectedCustomer,
                'source_id[]': selectedSource,
                start_time: startTime,
                end_time: endTime,
            };

            utils.globalLoading.show();

            BodyOrderApi.fetchBodyOrderCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                    vm.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });
        };

        vm.downloadItemPendingCSV = function () {
            var selectedCustomer;
            var selectedSource;

            selectedCustomer = getSelectedCustomer();
            selectedSource = getSelectedSource();

            if ((_.isArray(selectedCustomer) && !selectedCustomer.length) || (_.isArray(selectedSource) && !selectedSource.length)) {
                return;
            }

            if(typeof vm.startHour === 'undefined' || typeof vm.startMinute === 'undefined' || typeof vm.endHour === 'undefined' || typeof vm.endMinute === 'undefined'){
                return;
            }

            var startTime = moment(vm.startDate).format('YYYY-MM-DD') + ' ' + vm.startHour + ':' + vm.startMinute + ':00';
            var endTime = moment(vm.endDate).format('YYYY-MM-DD') + ' ' + vm.endHour + ':' + vm.endMinute + ':59';
            var params = {
                'customer_id[]' : selectedCustomer,
                'source_id[]': selectedSource,
                start_time: startTime,
                end_time: endTime,
                pending: true,
            };

            utils.globalLoading.show();

            BodyOrderApi.fetchBodyOrderCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                    vm.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });
        };

        function getSelectedCustomer() {

            var selectedItems = [];

            _.forEach(vm.body_order.customers, function (customer) {

                if (customer.selectedCustomer) {
                    selectedItems.push(customer.Customer.id);
                }

            });

            return selectedItems;

        }

        function getSelectedSource() {

            var selectedItems = [];

            _.forEach(vm.body_order.sources, function (source) {

                if (source.selectedSource) {
                    selectedItems.push(source.ProductLinkedSource.id);
                }

            });

            return selectedItems;

        }

    }

})();