(function () {

  angular.module('printty')
    .component('errorLogDetails', {
      controller: ErrorLogDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/error-log/error-log-details.html',
      bindings: {
        isShown: '<',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        type: '<',
        active: '='
      }
    });

  function ErrorLogDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, ErrorLog) {

    'ngInject';

    /****** variables ******/
    var vm = this;

    vm.log = null;

    vm.isAdd = true;
    vm.showMode = true;

    vm.loadingType = 'full';

    var logForm,
        logClone;

    var sidenav = {
      open: function () {
        $mdSidenav('error-log-details').open();
      },
      close: function () {
        $mdSidenav('error-log-details').close();
      }
    };

    /****** methods ******/
    vm.$postLink = function () {
      logForm = $scope.errorLogForm;

      $mdSidenav('error-log-details').onClose(function () {
        $timeout(function () {
          vm.onClose();
        }, 400);
      });
    };

    vm.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (vm.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    vm.edit = function () {
      if (vm.showMode) {
        logClone = _.cloneDeep(vm.log);
        vm.showMode = false;
      } else {
        vm.log = logClone;
        setFormPristine();
        vm.showMode = true;
      }
    };

    vm.update = function () {
      logForm.$setSubmitted();

      if (logForm.$invalid) {
        return;
      }

      if ( _.isEqual(vm.log, logClone) ) {
        vm.showMode = true;
        setFormPristine();
        return;
      }

      vm.loadingType = 'partial';

      ErrorLog.updateLog(vm.log)
        .then(function () {
          updateOriginal();

          setFormPristine();
          vm.showMode = true;

          vm.loadingType = 'stopped';
        }, function () {
          vm.loadingType = 'stopped';
        });
    };

    vm.save = function () {
      logForm.$setSubmitted();

      if (logForm.$invalid) {
        return;
      }

      vm.loadingType = 'partial';

      ErrorLog.createLog(vm.log)
        .then(function () {
          vm.onCreate();
          vm.close();
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    };

    vm.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('エラーログを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteLog);
    };

    vm.close = function () {
      sidenav.close();
    };

    /******** private **********/
    function initComponent() {
      switch (vm.type) {
        case 'details':
          vm.loadingType = 'full';
          vm.isAdd = false;
          vm.showMode = true;
          break;
        case 'add':
        default:
          vm.loadingType = 'stopped';
          vm.isAdd = true;
          vm.showMode = false;
      }

      if (!vm.isAdd && vm.active.logId) {
        fetchLog();
      }

      sidenav.open();
    }

    function fetchLog() {
      vm.loadingType = 'full';

      ErrorLog.fetchLog(vm.active.logId)
        .then(function (data) {
          vm.log = data.ErrorLogUploadCsv;
          vm.loadingType = 'stopped';
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    }

    function updateOriginal() {
      var logData = _.cloneDeep(vm.log);

      vm.onUpdate({
        log: logData
      });
    }

    function deleteLog() {
      vm.loadingType = 'partial';

      ErrorLog.deleteLog(vm.log.id)
        .then(function () {
          vm.onDelete({
            logId: vm.log.id
          });

          vm.close();
        })
        .catch(function () {
          vm.loadingType = 'stopped';
        });
    }

    function setDefault() {
      vm.isAdd = true;
      vm.item = null;

      setFormPristine();

      vm.loadingType = 'stopped';
    }

    function setFormPristine() {
      if (logForm) {
        logForm.$setPristine();
        logForm.$setUntouched();
      }
    }

  }

})();