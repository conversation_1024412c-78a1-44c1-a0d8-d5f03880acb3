(function () {

  angular.module('printty')
    .component('adminErrorLog', {
      controller: ErrorLogsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/error-log/error-logs.html'
    });

  function ErrorLogsCtrl(Store, utils, ErrorLog, $timeout) {

    'ngInject';

    var vm = this;

    /**** variables ******/
    vm.logs = [];

    vm.pagination = {
      perPage: 40,

      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    vm.logDetails = {
      isShown: false,
      type: 'add',
      active: {}
    };

    vm.infiniteLogs = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {
        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return vm.logs[index];
      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchLogs(index);
      }
    };

    vm.firstDate = 'date';

    var subscriptions = [];

    /****** methods ******/
    vm.$onInit = function () {
      utils.listLoading.show(true);

      subscriptions.push(
        Store.event('admin:create:error-log').subscribe(function () {
          vm.viewCreate();
        })
      );
    };

    vm.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) {
        subscription.unsubscribe();
      });
    };

    vm.reload = function() {
      vm.pagination.reload = true;
      vm.pagination.disabled = false;

      vm.logs = [];

      vm.infiniteLogs.numLoaded_ = 0;
      vm.infiniteLogs.toLoad_ = 0;
      vm.infiniteLogs.getItemAtIndex(1);
    };

    vm.search = function () {
      if (vm.pagination.lastSearch === vm.pagination.searchQuery) {
        return;
      }
      vm.pagination.lastSearch = vm.pagination.searchQuery;
      vm.reload();
    };

    vm.liveSearch = function () {
      utils.delay(function() {
        vm.search();
      }, 1000 );
    };

    vm.clearSearch = function () {
      vm.pagination.searchQuery = '';
      vm.search();
    };

    vm.viewDetails = function (log) {
      vm.logDetails.type = 'details';
      vm.logDetails.isShown = true;
      vm.logDetails.active = {
        logId: log.ErrorLogUploadCsv.id
      };
    };

    vm.viewCreate = function () {
      vm.logDetails.type = 'add';
      vm.logDetails.isShown = true;
      vm.logDetails.active = {};
    };

    vm.onLogClose = function () {
      vm.logDetails.isShown = false;
    };

    vm.onLogCreate = function () {
      vm.reload();
    };

    vm.onLogUpdate = function (log) {
      var originalLog = _.find(vm.logs, function (logItem) {
        return +logItem.ErrorLogUploadCsv.id === +log.id;
      });

      _.extend(originalLog.ErrorLogUploadCsv, log);
    };

    vm.onLogDelete = function (logId) {
      _.remove(vm.logs, function(log) {
        return +log.ErrorLogUploadCsv.id === +logId;
      });
    };

    /****** private ******/
    function fetchLogs(index) {
      if (vm.infiniteLogs.toLoad_ >= index || vm.pagination.disabled) {
        return;
      }

      vm.pagination.disabled = true;

      if (vm.pagination.reload) {
        $timeout(function () {
          vm.pagination.loading = false;
          utils.listLoading.show(false);
        });
      } else {
        $timeout(function () {
          vm.pagination.loading = true;
        });
      }

      vm.infiniteLogs.toLoad_ += vm.pagination.perPage;

      ErrorLog.fetchLogs(
        {
          paging_size: vm.pagination.perPage,
          paging_offset: vm.logs.length,
          conditions_keywords: vm.pagination.searchQuery,
          first_date: vm.firstDate,
        }
      ).then(
        function (data) {

          vm.infiniteLogs.numLoaded_ = vm.infiniteLogs.toLoad_ - vm.pagination.perPage + data.error_logs.length;
          vm.firstDate = data.first_date;
          vm.logs = vm.logs.concat(data.error_logs);

          vm.pagination.empty = !vm.logs.length;
          vm.pagination.reload = false;
          vm.pagination.loading = false;

          vm.pagination.disabled = data.total_count <= vm.logs.length;

          $timeout(function () {
            utils.listLoading.hide();
          }, 120);
        }
      );
    }

  }

})();