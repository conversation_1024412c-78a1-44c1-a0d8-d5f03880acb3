(function () {

  angular.module('printty')
    .component('changePrice', {
      controller: ChangePriceCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/change-price.html',
      bindings: {
        open: '=open'
      }
    });
  
  function ChangePriceCtrl(Customers, $mdSidenav, utils, $timeout) {

    "ngInject";
    
    var ctrl = this;

    ctrl.products = [];
    ctrl.loadingType = 'full';
    ctrl.customer = null;

    ctrl.pagination = {
      current: 0,
      perPage: 50,

      searchQuery: null,
      lastSearch: null,
      
      disabled: true
    };
    
    ctrl.priceDetails = {
      isShown: false,
      active: null,
      onClose: function () {
        this.active = null;
        this.isShown = false;
      }
    };
    
    var sidenav = {
      open: function () {
        $mdSidenav('change-price').open();
      },
      close: function () {
        $mdSidenav('change-price').close();
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    /****** methods ******/
    ctrl.$postLink = function () {
      $mdSidenav('change-price').onClose(function () {
        $timeout(function () { setDefault(); }, 400);
      });
    };
    
    ctrl.open = function (customer) {
      loader.start();
      ctrl.customer = customer;
      
      fetchProducts();
      sidenav.open();

      $('#fileInput').on('change', function (e) {
        ctrl.uploadCSV(e.target.files[0]);
      }).on('click', function () {
        this.value = null;
      });
    };

    ctrl.reload = function() {
      loader.start('partial');
      
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.products = [];
      
      fetchProducts();
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };
    
    ctrl.close = function () {
      sidenav.close();
    };

    ctrl.loadMoreItems = function () {
      fetchProducts();
    };

    ctrl.viewPriceDetails = function (product) {
      ctrl.priceDetails.active = {
        customerId: ctrl.customer.Customer.id,
        productId: product.Product.id
      };
      
      ctrl.priceDetails.isShown = true;
    };

    ctrl.downloadPriceCSV = function () {
      utils.globalLoading.show();

      Customers.fetchPriceCSV({
          customer_id: ctrl.customer.Customer.id,
      })
          .then(function () {
            utils.globalLoading.hide();
          })
          .catch(function () {
            utils.globalLoading.hide();
          });
    };

    ctrl.uploadCSV = function (file) {

      if (!file) {
        $('#fileInput').click();
        return;
      }

      var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

      if ( fileType !== 'csv' ) {
        alert('File.UnexpectedFormat');
        return;
      }

      utils.globalLoading.show();

      var params = {
        csv : file,
        customer_id: ctrl.customer.Customer.id,
      };

      Customers.uploadPriceCSV(params).then(function(){
        utils.globalLoading.hide();
      },function () {
        utils.globalLoading.hide();
      });
    };

    /******** private **********/
    function setDefault() {
      ctrl.products = [];
      ctrl.customer = null;
      
      ctrl.pagination.disabled = true;
      ctrl.pagination.current = 0;
      
      ctrl.priceDetails.isShown = false;

      loader.stop();
    }

    function fetchProducts() {

      ctrl.pagination.disabled = true;

      Customers.fetchProducts({
        paging_size: ctrl.pagination.perPage,
        paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
        conditions_keywords : ctrl.pagination.searchQuery
      })
        .then(function (data) {
          ctrl.pagination.current++;
          ctrl.products = ctrl.products.concat(data.products);
          $timeout(function () {
            ctrl.pagination.disabled = !data.products.length || (ctrl.products.length >= data.total_count);
          });
        })
        .finally(function () {
          loader.stop();
        })

    }

  }

})();