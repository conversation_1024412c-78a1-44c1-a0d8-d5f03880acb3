(function () {

  function IncomeCompaniesCtrl() {

    "ngInject";
    
    var ctrl = this;
    
    ctrl.companies = [
      {
        'name': 'Mitsubishi UFJ',
        'amount': '20 000円',
        'status': '支払い済み',
        'date': '2016-06-06 14:00',
        'selected': false
      },
      {
        'name': 'Sumit<PERSON> Mitsui',
        'amount': '500 000円',
        'status': '支払い待ち',
        'date': '2016-06-06 14:00',
        'selected': false
      },
      {
        'name': '<PERSON><PERSON><PERSON>',
        'amount': '70 000円',
        'status': '予定',
        'date': '2016-06-06 14:00',
        'selected': false
      },
      {
        'name': 'Resona Holdings',
        'amount': '21 000円',
        'status': '支払い済み',
        'date': '2016-06-06 14:00',
        'selected': false
      },
      {
        'name': 'Nomura Holdings',
        'amount': '39 000円',
        'status': '予定',
        'date': '2016-06-06 14:00',
        'selected': false
      }
    ];

    for (var i=0; i<=3; i++) {
      ctrl.companies = ctrl.companies.concat(
        [
          {
            'name': 'Mitsubishi UFJ',
            'amount': '20 000円',
            'status': '支払い済み',
            'date': '2016-06-06 14:00',
            'selected': false
          },
          {
            'name': 'Sumitomo Mitsui',
            'amount': '500 000円',
            'status': '支払い待ち',
            'date': '2016-06-06 14:00',
            'selected': false
          },
          {
            'name': 'Mizuho',
            'amount': '70 000円',
            'status': '予定',
            'date': '2016-06-06 14:00',
            'selected': false
          },
          {
            'name': 'Resona Holdings',
            'amount': '21 000円',
            'status': '支払い済み',
            'date': '2016-06-06 14:00',
            'selected': false
          },
          {
            'name': 'Nomura Holdings',
            'amount': '39 000円',
            'status': '予定',
            'date': '2016-06-06 14:00',
            'selected': false
          }
        ]
      )
    }

    var allSelected = false;
    
    ctrl.selectAll = function () {

      ctrl.companies.forEach(function (order) {
        order.selected = !allSelected;
      });

      allSelected = !allSelected;

    };

  }

  angular.module('printty')

    .component('adminIncomeCompanies', {
      controller: IncomeCompaniesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/companies.html'
    });

})();