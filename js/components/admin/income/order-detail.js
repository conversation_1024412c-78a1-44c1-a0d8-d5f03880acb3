(function () {

  angular.module('printty')
    .component('customerOrderDetail', {
      bindings: {
        open: '=openOn',
        viewMode: '<',
        guid:'@'
      },
      controller: OrderDetailCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/order-detail/order-detail.html'
    });
  
  function OrderDetailCtrl($element, $timeout, Store, utils, CustomersOrders) {

    "ngInject";


    var ctrl = this,
        $body = angular.element('body'),
        subscription,
        component = {
          show: function () {
            this.sidebar.scrollTop(0);
            $element.addClass('shown');
            $body.addClass('not-scrollable-x');
          },
          hide: function () {
            $element.removeClass('shown').addClass('hiding');
            $timeout(function () {
              $element.removeClass('hiding');
              $body.removeClass('not-scrollable-x');
              ctrl.onClose();
            }, 300)
          },
          sidebar: $element.find('.sidebar')
        },
        loadCount = 2;

    ctrl.order = null;
    ctrl.orderId = null;
    ctrl.periods = [];
    ctrl.items = [];
    ctrl.viewMode = !!ctrl.viewMode;
    ctrl.loadingType = 'full';
    ctrl.deliveryService = null;


    var pagination = {
      perPage: 40,
      current: 0,
      
      searchFailed: false,

      searchQuery: null,
      lastSearch: null,
      
      busy: false,
      end: false
    };
    ctrl.pagination = pagination;
    
    subscription = Store.data('DeliveryPeriods').subscribe(function (data) {
      ctrl.periods = data;
    });
    
    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };
    
    ctrl.open = function (order) {

      startLoading();

      ctrl.orderId = order.Order.id;
      ctrl.moreBilling = false;
      
      fetchOrder();
      
      ctrl.infiniteItems.numLoaded_ = 0;
      ctrl.infiniteItems.toLoad_ = 0;
      ctrl.infiniteItems.getItemAtIndex(1);
      
      component.show();
    };

    ctrl.infiniteItems = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.items[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {

        if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

        pagination.busy = true;

        this.toLoad_ += pagination.perPage;

        pagination.current++;

        CustomersOrders.fetchOrderItems(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            order_id: ctrl.orderId,
            keywords: ctrl.pagination.searchQuery
          }
        ).then(
          function (data) {
            pagination.searching = false;

            ctrl.infiniteItems.numLoaded_ = ctrl.infiniteItems.toLoad_ - pagination.perPage + data.items.length;
            ctrl.items = ctrl.items.concat(data.items);

            pagination.searchFailed = !ctrl.items.length;
            pagination.end = !data.items.length || (ctrl.items.length >= data.total_count);
            pagination.busy = false;

            stopLoading(250);
            
          }
        );

      }
    };
    
    ctrl.close = function () {
      component.hide();
    };

    ctrl.onClose = function () {
      setDefault();
    };

    ctrl.reload = function() {

      startLoading('partial');
      
      ctrl.pagination.current = 0;
      ctrl.pagination.end = false;
      ctrl.pagination.busy = false;

      ctrl.items = [];

      ctrl.infiniteItems.numLoaded_ = 0;
      ctrl.infiniteItems.toLoad_ = 0;
      ctrl.infiniteItems.getItemAtIndex(1);
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 1000 );
    };
    
    ctrl.getDeliveryPeriodById = function (deliveryId) {
      
      var deliveryPeriod = _.find(ctrl.periods, function (period) {
        return period.OrderDeliveryPeriod.id == deliveryId;
      });
      
      return deliveryPeriod ? deliveryPeriod.OrderDeliveryPeriod.title : null;
    };
    
    /******* private *********/
    
    function setDefault() {
      ctrl.order = null;
      ctrl.orderId = null;
      ctrl.items = [];

      loadCount = 2;
      
      pagination.end = false;
      pagination.busy = false;
      pagination.current = 0;
    }
    
    function startLoading(type) {
      
      switch (type) {
        case 'partial':
          ctrl.loadingType = 'partial';
          break;
        case 'full':
        default:
          ctrl.loadingType = 'full'
      }
      
    }
    
    function stopLoading(defer) {
      
      if (loadCount > 1) {
        loadCount--;
        return;
      }
      
      $timeout(function () {
        ctrl.loadingType = 'stopped'
      }, defer || 0);
      
    }
    
    function fetchOrder() {
      
      CustomersOrders.fetchOrder(ctrl.orderId)
        .then(function (data) {
          ctrl.order = data.Order;
          ctrl.deliveryService = data.DeliveryServiceJoin;
          ctrl.key = data.Key;
          ctrl.moreBilling = !ctrl.order.is_billing_shipping_same;

          stopLoading(250);
        });
      
    }
    
  }
  
  
  /**** 
   * 
   * User detail sidenavs 
   * 
   * *****/
  
  angular.module('printty')
    .component('customerOrderProductDetailSidenav', {
      require: {
        ordersDetailCtrl: '^^customerOrderDetail'
      },
      controller: OrderProductDetailSidenavCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/order-detail/product-detail.html'
    });

  function OrderProductDetailSidenavCtrl($mdSidenav, CustomersOrders) {

    "ngInject";

    // variables
    var ctrl = this;
    var sidenav = {
      open: function () {
        $mdSidenav(ctrl.guid).open();
      },
      close: function () {
        $mdSidenav(ctrl.guid).close();

      }
    };


    ctrl.item = null;
    ctrl.loadingType = 'stopped';
    ctrl.close = sidenav.close;

    ctrl.viewItemDetail = function (item) {

      ctrl.loadingType = 'full';

      ctrl.item = null;
      ctrl.sides = [];

      CustomersOrders.fetchOrderItem(item.OrderItem.id)
        .then(function (itemData) {
          ctrl.item = itemData;

          ctrl.sides = _.map(ctrl.item.tasks, function (task) {

            return {
              title: task.ProductColorSide.title,
              image_url: task.OrderItemTask.image_url
            };

          });

          ctrl.loadingType = 'stopped';

        });

      sidenav.open();
    };

    /**** show mode *****/
    ctrl.isShow = true;

    ctrl.toggleEditMode = function () {
      ctrl.isShow = !ctrl.isShow;
    };

    /**** modify parent controller ****/
    ctrl.$onInit = function() {
      ctrl.ordersDetailCtrl.viewItemDetail = ctrl.viewItemDetail;
      ctrl.viewMode = ctrl.ordersDetailCtrl.viewMode;
      ctrl.guid = ctrl.ordersDetailCtrl.guid
    };

  }

})();