(function () {

  angular.module('printty')
    .component('changePriceDetails', {
      controller: PriceDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/price-details.html',
      bindings: {
        isShown: '<ngShow',
        active: '<',
        loadingType: '=',
        // onUpdate: '&',
        onClose: '&',
        onDelete: '&'
      }
    });

  function PriceDetailsCtrl($scope, utils, Customers, Store, $mdDialog, $timeout) {

    "ngInject";

    utils.appLoaded();

    /****** variables ******/
    var ctrl = this;

    ctrl.prices = [];
    ctrl.showMode = true;
    ctrl.sides = null;

    ctrl.priceCustomerDetails = {
      isShown: false,
      type: 'add',
      active: {}
    };

    ctrl.pagination = {
      current: 0,
      perPage: 50,
      empty: false,
      disabled: true
    };

    ctrl.original_body_price = null;
    ctrl.bodyPrice = null;

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    var subscriptions = [];

    /****** methods ******/
    ctrl.$onChanges = function (changes) {

      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function() {
      loader.start('partial');

      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.prices = [];

      fetchPrices();
    };

    ctrl.loadMoreItems = function () {
      fetchPrices();
    };

    ctrl.back = function () {
      ctrl.onClose();
    };

    ctrl.viewPriceCustomerDetails = function (price) {
      ctrl.priceCustomerDetails.type = 'details';
      ctrl.priceCustomerDetails.isShown = true;
      ctrl.priceCustomerDetails.active = {
        priceId: price.ProductPrice.id
      };
    };

    ctrl.viewCreate = function () {
      ctrl.priceCustomerDetails.type = 'add';
      ctrl.priceCustomerDetails.isShown = true;
      ctrl.priceCustomerDetails.active = {
          customerId: ctrl.active.customerId,
          productId: ctrl.active.productId,
      };
    };

    ctrl.viewBodyPriceDetails = function () {
      var confirm = $mdDialog.confirm({
        template: utils.changeBodyPrice(),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelInput = function () {
            $mdDialog.cancel();
          };
          $scope.confirmInput = function () {
            var bodyPrice = $('#input-price').val();
            if(bodyPrice > 0) {
              ctrl.bodyPrice = bodyPrice;
              angular.element('.global-loading').show();
              Customers.updateBodyPrice({
                customerId: ctrl.active.customerId,
                productId: ctrl.active.productId,
                bodyPrice: ctrl.bodyPrice,
              }).then(function (data){
                ctrl.original_body_price = data.bodyPrice;
                angular.element('.global-loading').hide();
                $mdDialog.cancel();
              });
            } else {
              $('#input-error').html('error');
            }
          };
        }],
        onComplete : function(scope, element){
          var body_price = ctrl.original_body_price;
          $('#input-price').val(body_price);
        },
        clickOutsideToClose: true
      });

      $mdDialog.show(confirm);
    };

    ctrl.onPriceCustomerClose = function () {
      ctrl.priceCustomerDetails.isShown = false;
    };

    ctrl.onPriceCustomerCreate = function () {
      ctrl.reload();
    };

    ctrl.onPriceCustomerUpdate = function (price) {
      var originalPrice = _.find(ctrl.prices, function (priceItem) {
        return priceItem.ProductPrice.id == price.ProductPrice.id;
      });

      _.extend(originalPrice, price);

    };

    ctrl.onPriceCustomerDelete = function (priceToDelete) {
      _.remove(ctrl.prices, function(price) {
        return price.ProductPrice.id == priceToDelete;
      });
    };

    /******** private **********/

    function initComponent() {

      loader.start();

      if (!ctrl.isAdd && ctrl.active) {
        fetchPrices();

        // fetch colors sizes sides
        Customers.fetchColorSizeSide({
          productId: ctrl.active.productId
        }).then(function (data) {
          Store.data('ColorSizeSide').update(data);
        });
      }

    }

    function fetchPrices() {

      ctrl.pagination.disabled = true;

      Customers.fetchProductPrices({
        customer_id: ctrl.active.customerId,
        product_id: ctrl.active.productId,
        paging_size: ctrl.pagination.perPage,
        paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
      })
        .then(function (data) {
          ctrl.pagination.current++;
          ctrl.prices = ctrl.prices.concat(data.prices);
          ctrl.original_body_price = data.body_price;
          ctrl.pagination.empty = !ctrl.prices.length;
          $timeout(function () {
            ctrl.pagination.disabled = !data.prices.length || (ctrl.prices.length >= data.total_count);
          });
        })
        .finally(function () {
          loader.stop();
        });
    }

    function setDefault() {
      ctrl.prices = [];

      ctrl.pagination.disabled = true;
      ctrl.pagination.current = 0;

      ctrl.priceCustomerDetails.isShown = false;
      ctrl.showMode = true;

      loader.stop();
    }
  }

})();