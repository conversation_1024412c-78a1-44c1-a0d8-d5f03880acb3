(function() {

    function PriceCustomerDetailsCtrl($scope, $mdSidenav, $mdDialog, Customers, Store, utils, $filter, $timeout) {

        "ngInject";

        var ctrl = this;

        ctrl.price = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var priceCustomerForm,
            priceCustomerClone;
        var subscriptions = [];

        var sidenav = {
            open: function () {
                $mdSidenav('price-customer-details').open();
            },
            close: function () {
                $mdSidenav('price-customer-details').close();
            }
        };

        // fetch types and colors
        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('ColorSizeSide').subscribe(function (data) {
                    ctrl.details = data;
                })
            );
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            priceCustomerForm = $scope.priceCustomerFrameForm;

            $mdSidenav('price-customer-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function(){
            if (ctrl.showMode) {
                priceCustomerClone = _.cloneDeep(ctrl.price);
                ctrl.showMode = false;
            } else {
                ctrl.price = priceCustomerClone;
                setFormPristine();
                ctrl.showMode = true;
            }
        };

        ctrl.update = function () {
            priceCustomerForm.$setSubmitted();

            if (priceCustomerForm.$invalid) {
                return;
            }

            // do nothing if printer didn't change
            if (_.isEqual(ctrl.price, priceCustomerClone)) {
                ctrl.showMode = true;
                setFormPristine();
                // toggleShowMode();
                return;
            }

            ctrl.loadingType = 'partial';

            Customers.updateProductPrices(ctrl.price.ProductPrice)
                .then(function () {
                    updateOriginal();
                    ctrl.showMode = true;
                })
                .finally(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {
            priceCustomerForm.$setSubmitted();

            if (priceCustomerForm.$invalid) {
                return;
            }

            ctrl.loadingType = 'partial';

            Customers.createProductPrices({
                price: ctrl.price.ProductPrice,
                customer_id: ctrl.active.customerId,
                product_id: ctrl.active.productId,
            }).then(function () {
                ctrl.onCreate();
                ctrl.close();
            }, function () {
                ctrl.loadingType = 'stopped';
            });
        };

        ctrl.delete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('置場を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function() {

                ctrl.loadingType = 'partial';

                Customers.deleteProductPrice(ctrl.price.ProductPrice.id).then(
                    function () {
                        // call onDelete event
                        ctrl.onDelete({
                            priceId: ctrl.price.ProductPrice.id
                        });
                        // close sidenav
                        ctrl.close();

                    }, function () {
                        ctrl.loadingType = 'stopped';
                    }
                );

            });

        };

        ctrl.close = function () {
            sidenav.close();
        };

        ctrl.fetchSidesOfColor = function (color_id) {
            fetchSides(color_id);
        };

        /******** private **********/
        function initComponent() {
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.priceId) {
                fetchPrice();
            }

            sidenav.open();
        }

        function fetchPrice() {
            ctrl.loadingType = 'full';

            Customers.detailsPrice(ctrl.active.priceId)
                .then(function (data) {
                    ctrl.price = data;
                    ctrl.price.ProductPrice.price = _.toInteger(data.ProductPrice.price);
                    fetchSides(ctrl.price.ProductPrice.color_id);
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });
        }

        function updateOriginal() {

            var originalPrice = _.cloneDeep(ctrl.price);

            originalPrice.ProductPrice.size = _.find(ctrl.details.sizes, function (size) {
                return size.ProductSize.id == ctrl.price.ProductPrice.size_id;
            }).ProductSize.title;

            originalPrice.ProductPrice.color = _.find(ctrl.details.colors, function (color) {
                return color.ProductColor.id == ctrl.price.ProductPrice.color_id;
            }).ProductColor.title;

            originalPrice.ProductPrice.side = _.find(ctrl.sides, function (side) {
                return side.id == ctrl.price.ProductPrice.side_id;
            }).title;

            ctrl.onUpdate({
                price: originalPrice
            });

        }

        /*** helpers ***/
        function setDefault() {
            ctrl.isAdd = true;
            ctrl.price = null;

            originalPrice = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (priceCustomerForm) {
                priceCustomerForm.$setPristine();
                priceCustomerForm.$setUntouched();
            }
        }

        function fetchSides(color_id) {
            _.forEach(ctrl.details.colors, function (color) {
                if(color.ProductColor.id === color_id){
                    ctrl.sides = color.ProductColorSide;
                }
            });
        }

    }

    angular.module('printty')
        .component('priceCustomerDetails', {
            controller: PriceCustomerDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/admin/income/price-customer-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

})();
