(function () {

  angular.module('printty')
    .component('clientDetails', {
      controller: ClientInfoCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/client-details.html',
      bindings: {
        isShown: '<',
        type: '<',
        active: '<',
        
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        guid    : '@'
      }
    });
  
  function ClientInfoCtrl($scope, utils, $mdDialog, $mdSidenav, $timeout, Customers, Store) {

    "ngInject";
    
    var ctrl = this;
    
    /****** variables ******/
    ctrl.customer = null;

    ctrl.isAdd = true;
    ctrl.showMode = true;

    ctrl.loadingType = 'full';
    ctrl.clipSuccessMsg = false;
    
    ctrl.deliveryServices = [];

    var customerForm, customerClone;
    var subscriptions = [];

    var sidenav = {
      open: function () {
        $mdSidenav(ctrl.guid).open();
      },
      close: function () {
        $mdSidenav(ctrl.guid).close();
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    ctrl.optionBlock = {
      isShown: false,
      active: {}
    };

    /****** methods ******/
    ctrl.$onInit = function () {
      subscriptions.push(
        Store.data('DeliveryServices')
          .subscribe(function (data) {
            ctrl.deliveryServices = data;
          })
      );
    };
    
    ctrl.$postLink = function () {

      customerForm = $scope.customerForm;

      $mdSidenav(ctrl.guid).onClose(function () {
        $timeout(function () { ctrl.onClose(); }, 400);
      });
    };

    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {
        (ctrl.isShown ? initComponent : setDefault)();
      }
    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        customerClone = _.cloneDeep(ctrl.customer);
        ctrl.showMode = false;
      } else {
        ctrl.customer = customerClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      customerForm.$setSubmitted();

      if (customerForm.$invalid) return;

      if  ( _.isEqual(ctrl.customer, customerClone) ) {
        ctrl.showMode = true;
        setFormPristine();
        return;
      }

      loader.start('partial');

      Customers.updateCustomer(ctrl.customer)
        .then(function () {
          updateOriginal();

          setFormPristine();
          ctrl.showMode = true;
        })
        .finally(function () {
          loader.stop();
        });

    };

    ctrl.save = function () {

      customerForm.$setSubmitted();

      if (customerForm.$invalid) return;

      loader.start('partial');

      Customers.createCustomer(ctrl.customer)
        .then(function (data) {
          
          ctrl.customer.Customer.id = data.Customer.id;
          ctrl.onCreate();
          
          ctrl.isAdd = false;
          ctrl.showMode = true;
          
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('発注者を削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteCustomer);

    };

    ctrl.onSuccessCopy = function(e) {
      e.clearSelection();

      ctrl.clipSuccessMsg = true;
      $timeout(function () {
        ctrl.clipSuccessMsg = false;
      }, 500);
    };

    ctrl.close = function () {
      sidenav.close();
    };

    ctrl.optionBlock.show = function (key, value) {
      ctrl.optionBlock.type = 'details';
      ctrl.optionBlock.active = {
        key: key,
        value: value,
        object: ctrl.customer.Customer.api_callback_options
      };
      ctrl.optionBlock.isShown = true;
    };

    ctrl.optionBlock.add = function () {
      ctrl.optionBlock.type = 'add';
      ctrl.optionBlock.active = {
        key: null,
        value: null,
        object: ctrl.customer.Customer.api_callback_options
      };
      ctrl.optionBlock.isShown = true;
    };

    ctrl.optionBlock.onClose = function () {
      ctrl.optionBlock.isShown = false;
    };

    ctrl.optionBlock.onCreate = function (key, value) {
      ctrl.customer.Customer.api_callback_options[key] = value;
      
      Customers.updateCustomer(ctrl.customer)
        .catch(function () {
          delete ctrl.customer.Customer.api_callback_options[key];
        })
        .finally(function () {
          loader.stop();
          ctrl.optionBlock.onClose();
        });
    };

    ctrl.optionBlock.onUpdate = function (original, key, value) {
      
      if (original.key === key) {
        ctrl.customer.Customer.api_callback_options[key] = value;
      } else {
        ctrl.customer.Customer.api_callback_options[key] = value;
        delete ctrl.customer.Customer.api_callback_options[original.key];
      }

      Customers.updateCustomer(ctrl.customer)
        .catch(function () {

          if (original.key === key) {
            ctrl.customer.Customer.api_callback_options[key] = original.value;
          } else {
            delete ctrl.customer.Customer.api_callback_options[key];
            ctrl.customer.Customer.api_callback_options[original.key] = original.value;
          }
          
        })
        .finally(function () {
          loader.stop();
          ctrl.optionBlock.onClose();
        });
    };

    ctrl.optionBlock.onDelete = function (key) {
      
      var original = {
        key: key,
        value: ctrl.customer.Customer.api_callback_options[key]
      };
      
      delete ctrl.customer.Customer.api_callback_options[key];

      Customers.updateCustomer(ctrl.customer)
        .catch(function () {
          ctrl.customer.Customer.api_callback_options[original.key] = original.value;
        })
        .finally(function () {
          loader.stop();
          ctrl.optionBlock.onClose();
        });
      
    };

    /******** private **********/

    function initComponent() {
      
      switch (ctrl.type) {
        case 'details':
          loader.start();
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          loader.stop();
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active.customerId) {
        fetchCustomer();
      }

      sidenav.open();

    }

    function fetchCustomer() {
      
      loader.start();

      Customers.fetchCustomer(ctrl.active.customerId)
        .then(function (data) {
          ctrl.customer = data;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    }

    function updateOriginal() {

      var customerData = _.cloneDeep(ctrl.customer);

      ctrl.onUpdate({
        customer: customerData
      });

    }

    function deleteCustomer() {
      
      loader.start('partial');
      
      Customers.deleteCustomer(ctrl.customer.Customer.id)
        .then(function () {

          ctrl.onDelete({
            customerId: ctrl.customer.Customer.id
          });

          ctrl.close();
        })
        .catch(function () {
          loader.stop();
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.customer = {
        Customer: {
          billing: {
            address: {}
          },
          api_callback_options: {}
        }
      };

      setFormPristine();

      loader.stop();
    }

    function setFormPristine() {
      if (customerForm) {
        customerForm.$setPristine();
        customerForm.$setUntouched();
      }
    }
  
  }
  
})();