(function () {

  function IncomeCtrl($state, Auth) {

    "ngInject";
    
    var ctrl = this;

    ctrl.state = $state;
    ctrl.initialState = ctrl.state.current.name;

    var date = new Date(), y = date.getFullYear(), m = date.getMonth();
    ctrl.date = new Date(y, m, 1);
    
  }

  angular.module('printty')

    .component('adminIncome', {
      controller: IncomeCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/income.html'
    });

})();