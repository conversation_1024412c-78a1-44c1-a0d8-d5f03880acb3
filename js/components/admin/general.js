(function () {

  angular.module('printty')
    .component('adminComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: AdminCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/index.html'
    });
  
  function AdminCtrl($scope, ExportDataApi, Auth, utils, $state, Store, $mdDialog, $timeout,Factory) {

    "ngInject";
    
    var ctrl = this;
    
    ctrl.state = $state;
    ctrl.statuses = [];
    ctrl.export_data = [];
    ctrl.showTab = false;
    ctrl.date = moment().toDate();
    ctrl.pagination = {
      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
    };

    var start_date = moment().startOf('month').format('YYYY-MM-DD');
    var end_date = moment().format('YYYY-MM-DD');

    ctrl.startDate = moment(start_date, 'YYYY-MM-DD').toDate();
    ctrl.endDate = moment(end_date, 'YYYY/MM/DD').toDate();

    var subscription;
    var loader = new utils.loadCounter(
        function (type) {
          if (!type) type = 'full';
          ctrl.loadingType = type;
        },
        function () {
          ctrl.loadingType = 'stopped';
        }
    );
    
    $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });
    
    ctrl.$onInit = function () {
      utils.appLoaded();
      fetchCSVTypes();
      Store.data('statuses').update(ctrl.statuses);
      subscription = Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); });
      Store.data('Mode').subscribe(function (data){
        if(data.is_planning){
          ctrl.showCreatePlan = true;
        }
      });
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.export_data = [];
      ctrl.startDate = moment(ctrl.date, 'YYYY/MM/DD').toDate();
      ctrl.endDate = moment(ctrl.date, 'YYYY/MM/DD').toDate();
      fetchCSVTypes();
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };
    
    ctrl.createUser = function () {
      Store.event('admin:create:user').emit();
    };
    
    ctrl.createPlan = function () {
      Store.event('admin:create:plan').emit();
    };

    ctrl.createCustomer = function () {
      Store.event('admin:create:customer').emit();
    };

    ctrl.createError = function () {
      Store.event('admin:create:error-log').emit();
    };

    ctrl.showNav = function (title){
      var found = false;
      Store.data('Mode').subscribe(function (data){
        if(data && data.mode_sub){
          found =  Auth.contains(data.mode_sub,title);
        }
      });
     return found;
    }

    function fetchCSVTypes() {
      if (ctrl.pagination.reload) {
        $timeout(function () {
          ctrl.pagination.loading = false;
          utils.listLoading.show(false);
        });
      } else {
        $timeout(function () {
          ctrl.pagination.loading = true;
        });
      }

      ExportDataApi.fetchCSVTypes(
          {
            conditions_keywords: ctrl.pagination.searchQuery,
          }
      ).then(
          function (data) {
            ctrl.export_data = data;
            if(ctrl.export_data.customers.length || !ctrl.export_data.external) ctrl.showTab = true;
            ctrl.pagination.empty = !ctrl.export_data.length;
            ctrl.pagination.reload = false;
            ctrl.pagination.loading = false;
            utils.listLoading.hide();
          }
      );
    }

    ctrl.downloadExportCSV = function (ev) {
      var is_checkedCSVType = false;
      var is_checkedFactory = false;
      var is_checkedDateType = false;
      var selectedCustomers = [];
      var selectedItems = [];
      ctrl.factory_type = null;
      ctrl.csv_type = null;
      ctrl.printer_type = null;
      ctrl.customer = null;
      ctrl.date_type = null;
      var confirm = $mdDialog.show({
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          if(ctrl.export_data.external == true){
              for (var i = 0; i < ctrl.export_data.customers.length; i++) {
                selectedCustomers.push(ctrl.export_data.customers[i].Customer.id);
                ctrl.customer = selectedCustomers;
              }
          }
          if(typeof ctrl.export_data.selectedType != "undefined" ){
            $scope.csv_type = ctrl.export_data.selectedType.ModeSubTab.id;
          }
          $scope.selectCSVType = function(type) {
            is_checkedCSVType = true;
            ctrl.csv_type = type;
            $scope.csv_type = ctrl.csv_type;
          };

          $scope.selectFactoryType = function(type) {
            is_checkedFactory = true;
            ctrl.factory_type = type;
          };

          $scope.selectDateType = function(type) {
            is_checkedDateType = true;
            ctrl.date_type = type;
          };

          $scope.selectCustomer = function(customer) {
            var index = selectedCustomers.indexOf(customer);
            if (index === -1) {
              selectedCustomers.push(customer);
            } else {
              selectedCustomers.splice(index, 1);
            }
            ctrl.customer = selectedCustomers;
          };

          $scope.selectPrinterType = function(type) {
            var index = selectedItems.indexOf(type);
            if (index === -1) {
              selectedItems.push(type);
            } else {
              selectedItems.splice(index, 1);
            }
            ctrl.printer_type = selectedItems;
          };

          $scope.showSubModeTab = function (title){
            var found = false;
            Store.data('Mode').subscribe(function (data){
              if(data && data.mode_sub_tab){
                found =  Auth.contains(data.mode_sub_tab,title);
              }
            });
            return found;
          }

          $scope.startDate = ctrl.startDate;
          $scope.endDate = ctrl.endDate;
          $scope.downloadCSV = function () {
            var startTime = moment($scope.startDate).format('YYYY/MM/DD');
            var endTime = moment($scope.endDate).format('YYYY/MM/DD');

            if(ctrl.csv_type == 7) {
              var params = {
                start_time: startTime,
                end_time: endTime,
              };

              utils.globalLoading.show();

              ExportDataApi.fetchDownloadBillingCSV(params)
                  .then(function (){
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  })
                  .catch(function () {
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  });
            } else if(ctrl.csv_type == 8) {

              if(!is_checkedDateType) {
                ctrl.date_type = 1;
              }
              var params = {
                'customer[]': ctrl.customer,
                'date_type': ctrl.date_type,
                start_time: startTime,
                end_time: endTime,
              };

              utils.globalLoading.show();

              ExportDataApi.fetchDownloadBillingSaleCSV(params)
                  .then(function (){
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  })
                  .catch(function () {
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  });
            }else if(ctrl.csv_type == 21){
              var params = {
                'printer_type_id[]' : ctrl.printer_type,
                start_time: startTime,
                end_time: endTime,
              };

              utils.globalLoading.show();

              ExportDataApi.fetchDownloadTotalQuantityCSV(params)
                  .then(function (){
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  })
                  .catch(function () {
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  });
            } else {

              if(!is_checkedCSVType && is_checkedFactory) {
                ctrl.csv_type = 5;
              }else if(!is_checkedFactory && is_checkedCSVType) {
                ctrl.factory_type = ctrl.export_data.factorySelected;
              }else if (!is_checkedCSVType && !is_checkedFactory) {
                ctrl.csv_type = 5;
                ctrl.factory_type = ctrl.export_data.factorySelected;
              }

              var params = {
                'printer_type_id[]' : ctrl.printer_type,
                'factory_type': ctrl.factory_type,
                'csv_type': ctrl.csv_type,
                start_time: startTime,
                end_time: endTime,
              };

              utils.globalLoading.show();

              ExportDataApi.fetchDownloadCSV(params)
                  .then(function (){
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  })
                  .catch(function () {
                    $mdDialog.cancel();
                    angular.element('.global-loading').hide();
                  });
            }

          };

          $scope.export_data = ctrl.export_data;
        }],
        templateUrl: 'views/admin/export-data/export-data.html',
        clickOutsideToClose:true,
      });

    };

    /*** private ****/
    function onStatusesChange(statuses) {
      ctrl.statuses = statuses;
    }
    
  }

})();