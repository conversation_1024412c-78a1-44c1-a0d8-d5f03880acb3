(function () {

  function LoginCtrl(utils, $state, Auth) {

    "ngInject";
    
    var ctrl = this;
    
    if (Auth.isAuthenticated()) {
      return $state.go( Auth.defaultRoute );
    } else {
      utils.appLoaded();
    }
    
    ctrl.loginData = {
      email: null,
      password: null
    };

    ctrl.login = function () {
      Auth.signin(ctrl.loginData).then(
        function () {
          
          Auth.update().then(function () {
            $state.go( Auth.defaultRoute );
          });
          
        }
      );
    };
    
    ctrl.signup = function () {

      Auth.signup(ctrl.loginData).then(
        function () {
          
          Auth.update().then(function () {
            $state.go( Auth.defaultRoute );
          });
          
        }
      );
      
    }
    
  }

  angular.module('printty')
    .component('loginComponent', {
      templateUrl: 'views/login/index.html',
      controller: LoginCtrl,
      controllerAs: 'vm'
    });

})();