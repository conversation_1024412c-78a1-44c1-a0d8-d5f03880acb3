(function () {

  angular.module('printty')
    .component('printDownload', {
      bindings: {
        task: '<',
        taskInfo: '<',
        download: '=',
        print: '=',
        onDownload: '=',
        onPrint: '&',
        source: '='
      },
      controller: PrintDownloadCtrl,
      controllerAs: 'vm'
    });
  
  function PrintDownloadCtrl(PrintApi, electron, ElectronUtils, $timeout,utils) {
    
    var vm = this;
    var downloadClick = false;
    var printClick = false;

    vm.source = null;
    vm.isMultiple = vm.task.taskType === 'Multiple';
    
    if (vm.isMultiple) {
      fetchMultipleSource();
    } else {
      fetchSingleSource();
      fetchNewImageName();
    }
    
    vm.download = function () {
      
      // if clicked download before source was loaded
      if (!vm.source) {
        downloadClick = true;
        return;
      }

      if (vm.source.type === 'Image' || vm.source.type === 'PDF') {
        utils.globalLoading.show();
          if(vm.task.type_printer_local == 'b'){
              electron.ipcRenderer.send('save-imagesB', {
                  url: vm.source.image_url
              });
              electron.ipcRenderer.on('save-imageB:saved', function () {
                  $timeout(vm.onDownload());
                  utils.globalLoading.hide();
              });
          }else {
              ElectronUtils.getFile(vm.source.image_url, null, vm.task.product_code, vm.task)
                  .finally(function () {
                      $timeout(vm.onDownload());
                      utils.globalLoading.hide();
                  });
          }
      }
      
    };
    
    vm.print = function () {

      if (!vm.source) {
        printClick = true;
        return;
      }
        // just download image
        if (vm.source.type === 'Image' && vm.task.type_printer_local == 'b') {
            electron.ipcRenderer.send('simple:print-image', {url: vm.source.image_url});
            electron.ipcRenderer.on('simple:print-image:printed', function () {
                $timeout(vm.onPrint);
            });
        } else {
            var save_image = false;

            // just download image
            if (vm.source.type === 'Image') {
                utils.globalLoading.show();
                electron.ipcRenderer.send(vm.task.type_printer_local == 'e' ? 'save-imagesE' : 'save-images', {
                    url: vm.source.image_url,
                    isPrint: true,
                    product_code: vm.task.product_code,
                    product_color_code: vm.task.product_color_code,
                    title: vm.task.current_step.TaskStep.title,
                    name: vm.task.current_step.detail.product_title,
                });

                electron.ipcRenderer.on(vm.task.type_printer_local == 'e' ? 'save-imageE:saved' : 'save-image:saved', function () {
                    if (save_image === false) {
                        save_image = true;
                        $timeout(vm.onPrint);
                        utils.globalLoading.hide();
                    }
                    // electron.ipcRenderer.send('simple:print-image', {url: vm.source.image_url});
                });
            }
        }

    };

    vm.$onDestroy = function () {
       electron.ipcRenderer.removeAllListeners('save-image:saved');
    };
    
    /*** helpers ***/
    function fetchSingleSource() {
      
      PrintApi.fetchSingleTaskSource({
        task_id: vm.taskInfo.Task.id,
        step_id: vm.taskInfo.TaskStep.id,
        is_original: false,
      }).then(function (data) {
        
        vm.source = data.TaskSource;

        // trigger download func if previously was clicked
        if (downloadClick) {
          downloadClick = false;
          vm.download();
        }

        if (printClick) {
          printClick = false;
          vm.print();
        }

      });
      
    }

    function fetchMultipleSource() {

      PrintApi.fetchMultipleTaskSource({
        task_group_step_id: vm.task.current_step.TaskGroupStep.id
      }).then(function (data) {

        vm.source = data.TaskSource;

        // trigger download func if previously was clicked
        if (downloadClick) {
          downloadClick = false;
          vm.download();
        }

        if (printClick) {
          printClick = false;
          vm.print();
        }

      });
      
    }

    function fetchNewImageName() {
        PrintApi.fetchNewImageName({
            step_id: vm.taskInfo.TaskStep.id
        }).then(function (data) {

            vm.task.current_step.new_image_name = data.new_name;

        });
    }
    
  }
  
})();