(function () {

  function PrintCtrl(utils, Auth, $state) {

    "ngInject";

    if (!Auth.isAuthorized('print')) {
      return $state.go( Auth.defaultRoute );
    }
    
    var ctrl = this;

    utils.appLoaded();
    
    ctrl.state = {
      
      name: 'scan',
      history: [],
      embroidery_info: false,
      
      is: function () {
        
        if (!arguments.length) return;
        
        if (arguments.length === 1) {
          return arguments[0] === this.name;
        }
        
        if (arguments.length > 1) {
          
          for (var i = 0; i < arguments.length; i++) {
            
            if (arguments[i] === this.name) {
              return true;
            }
            
          }
          
          return false
          
        }
        
      },
      back: function () {

        var last = this.history[this.history.length - 1];

        this.set(last, true);

        if (last == 'scan') {
          this.history = [];
        } else {
          this.history.pop();
        }

        ctrl.state.embroidery_info = false;
      },
      set: function (name, forget) {

        if (!forget && this.name != name) {
          this.history.push(this.name);
        }
        
        this.name = name;
        
        // emit event
        this._onChangeCallbacks.forEach(function(callback) {
          callback();
        });
        
      },
      
      _onChangeCallbacks: [],
      onChange: function(callback) {
        if (angular.isFunction(callback)) {
          this._onChangeCallbacks.push(callback);
        }
      }
      
    };
    
  }

  angular.module('printty')

    .component('printComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: PrintCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/print/print.html'
    });


  /******************
   *******************
   * ******* LEFT ******
   ****************/
  
  
  function PrintLeftCtrl(Store, $mdDialog) {

    "ngInject";
    
    var ctrl = this,
        subscription;
    
    ctrl.$onInit = function () {
      
      ctrl.state = ctrl.printCtrl.state;

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
        }
      });
      
    };
    
    // subscribe to print task change
    subscription = Store.data('PrintTask').subscribe(function (data) {
      
      if (!data) return;
      
      ctrl.isMultiple = data.taskType === 'Multiple';
      ctrl.task = data;
      
    });

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };
    
    /******** helpers ***********/
    function setDefault() {
      ctrl.isMultiple = false;
      ctrl.task = null;
    }

    ctrl.isForWomen = function () {
        if (typeof  ctrl.task !== 'undefined' && ctrl.task !== null){
          if (typeof ctrl.task.current_step.detail.for_women !== 'undefined' && ctrl.task.current_step.detail.for_women == '1') {

              return true;

          } else {

              return false;

          }
        }
    };

      ctrl.showLargeImage = function (ev, item) {
          var confirm = $mdDialog.show({
              controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                  $scope.cancelDelete = function () {
                      $mdDialog.cancel();
                  };
                  $scope.confirmDelete = function () {
                      $mdDialog.hide();
                  };

                  $scope.image_large = item;
              }],
              templateUrl: 'views/print/print-dialog.html',
              clickOutsideToClose:true,
          });
      };
    
  }

  angular.module('printty')

    .component('printLeft', {
      require: {
        printCtrl: '^^printComponent'
      },
      controller: PrintLeftCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/print/print-left.html'
    });


  /******************
   *******************
   * ******* RIGHT ******
   ****************/


  function PrintRightCtrl($element, PrintApi, Store, electron, $timeout, utils, ElectronUtils, $mdDialog) {

    "ngInject";
    
    var ctrl = this;

    ctrl.isElectron = !!electron;
    
    ctrl.scannerInput = null;
    ctrl.qrCodeInput = null;
    
    ctrl.taskInfo = null;
    ctrl.task = null;
    ctrl.isPrintStep = true;
    ctrl.source = null;
    ctrl.clipSuccessMsg = null;
    ctrl.typePinterLocal = null;
    ctrl.titlePinterLocal = null;


    ctrl.$onInit = function () {
      ctrl.state = ctrl.printCtrl.state;
      if(electron){
          PrintApi.getPrinterTypeLocal().then(function (data) {
              if (data && data.printer && data.printer.type) {
                  ctrl.typePinterLocal = data.printer.type.toLowerCase();
                  ctrl.titlePinterLocal = data.printer.title ? data.printer.title : null;
              }
          });
      }
      ctrl.focusScanField();
      
      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
          $element.removeClass('single multiple');
        }
        
        if (ctrl.state.is('scan')) {
          ctrl.focusScanField();
        }
      });
      
    };

    ctrl.openConditionNote = function(url) {
        //open links externally by default
        electron.ipcRenderer.send('openConditionNote', {url:url});
    };
    
    ctrl.findQr = function () {
      if (!ctrl.qrCodeInput) return;
      search(ctrl.qrCodeInput);
    };

    ctrl.focusScanField = function () {
      $timeout(function () {
        $('#scanner-field').focus();
      });
    };

    ctrl.findQrScan = function () {
      utils.delay(function () {
        if (!ctrl.scannerInput) return;
        search(ctrl.scannerInput);
      }, 250);
    };
    
    ctrl.clearInput = function () {
      ctrl.qrCodeInput = null;
      ctrl.errorText = null;
    };

    ctrl.onDownload = function () {
        if(ctrl.typePinterLocal && ctrl.typePinterLocal == 'b'){
            ctrl.state.set('finish', true);
        } else if (ctrl.isMultiple) {
            processMultiple();
        } else {
            processSingle();
        }
      // ctrl.state.set('finish', true);
    };
    
    ctrl.onPrint = function () {
        if(ctrl.typePinterLocal && ctrl.typePinterLocal == 'b'){
            ctrl.state.set('finish', true);
        } else if (ctrl.isMultiple) {
            processMultiple();
        } else {
            processSingle();
        }
      // ctrl.state.set('finish', true);
    };
    
    ctrl.downloadItemImage = function (item) {

      utils.globalLoading.show();
      
      PrintApi.fetchMultipleTaskSource({
        task_group_step_id: ctrl.taskInfo.TaskGroupStep.id,
        item_id: item.TaskStepItem.id
      })
        .then(function (data) {
          ElectronUtils.getFile(data.TaskSource.image_url)
            .finally(function () {
              utils.globalLoading.hide();
            })
        })
        .catch(function () {
          utils.globalLoading.hide();
        })
      
    };
    
    ctrl.press = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.printSuccess = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

        deleteDownloadImage();

      if (ctrl.isMultiple) {
        processMultiple();
      } else {
        processSingle();
      }
      
    };

    ctrl.printFail = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

        deleteDownloadImage();
      
      if (ctrl.isMultiple) {
        ctrl.state.set('fail', true);
      } else {
        failSingle();
      }
      
    };

    function deleteDownloadImage(){
        utils.globalLoading.show();

        electron.ipcRenderer.send('delete-images', {
            url: ctrl.source.image_url
        });

        electron.ipcRenderer.on('delete-image:deleted', function () {
            utils.globalLoading.hide();
        });
    }
    
    ctrl.retryPrint = function () {

      if (ctrl.isMultiple) {
        retryMultiple();
      } else {
        retrySingle();
      }
      
    };
    
    ctrl.failMultiple = function () {
      
      var itemsFailed = [];
      
      _.forEach(ctrl.task.current_step.items, function (item) {
        
        if (item.selected) {
          
          itemsFailed.push({
            TaskStepItem: {
              id: item.TaskStepItem.id
            }
          });
        
        }
        
      });

      PrintApi.failMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id,
        failedItems: itemsFailed
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });
      
    };
    
    ctrl.cancelFail = function () {

      _.forEach(ctrl.task.current_step.items, function (item) {
        item.selected = false;
      });

      ctrl.state.set('finish', true);
      
    };

    ctrl.embroideryInfo = function () {
        ctrl.state.embroidery_info = !ctrl.state.embroidery_info;
    };

    ctrl.onSuccessCopy = function(e) {
        e.clearSelection();

        ctrl.clipSuccessMsg = e.text;
        $timeout(function() {
            ctrl.clipSuccessMsg = null;
        }, 500);
    };
    
    /******** helpers ***********/
    function setDefault() {
      ctrl.scannerInput = null;
      
      ctrl.taskInfo = null;
      ctrl.task = null;
      
      ctrl.isMultiple = false;
      ctrl.isPrintStep = true;
      ctrl.errorText = null;
    }

      function preDownload (data) {
          if(data.download_only === true) {
              var confirm = $mdDialog.confirm({
                  template: utils.confirmDownloadImage(),
                  controller: ['$scope', '$mdDialog', function ($scope, $mdDialog) {
                      $scope.cancelDownload = function () {
                          $mdDialog.cancel();
                      };
                      $scope.confirmDownload = function () {
                          $mdDialog.hide();
                      };
                  }]
              });
          }

          if(data.print_again === true) {
              var confirm = $mdDialog.confirm({
                  template: utils.confirmDownloadImage('もう1度印刷してもよろしいですか？'),
                  controller: ['$scope', '$mdDialog', function ($scope, $mdDialog) {
                      $scope.cancelDownload = function () {
                          $mdDialog.cancel();
                      };
                      $scope.confirmDownload = function () {
                          $mdDialog.hide();
                      };
                  }]
              });
          }

              $mdDialog.show(confirm).then(function () {
                  ctrl.taskInfo = data;

                  ctrl.isMultiple = data.type === 'Multiple';

                  if (ctrl.isMultiple) {
                      $element.removeClass('single').addClass('multiple');
                  } else {
                      $element.addClass('single').removeClass('multiple');
                  }

                  // task found => fetch task
                  if (ctrl.isMultiple) {
                      fetchMultiple(data, false);
                  } else {
                      fetchSingle(data, false);
                  }

              }, function (error) {

                  if (error.status === 400 && angular.isDefined(error.data.message)) {
                      ctrl.errorText = error.data.message;
                  }

                  ctrl.scannerInput = null;
              });

      }

    function search(input) {
      PrintApi.search(input).then(function (data) {

        if(data.download_only === true || data.print_again === true){
            ctrl.errorText = data.error_text;
            ctrl.qrCodeInput = null;
            ctrl.scannerInput = null;
            preDownload(data);
            return;
        }

        ctrl.taskInfo = data;
        
        ctrl.isMultiple = data.type === 'Multiple';

        if (ctrl.isMultiple) {
          $element.removeClass('single').addClass('multiple');
        } else {
          $element.addClass('single').removeClass('multiple');
        }

        // task found => fetch task
        if (ctrl.isMultiple) {
          fetchMultiple(data, false);
        } else {
          fetchSingle(data, false);
        }

      }, function (error) {

        if (error.status === 400 && angular.isDefined(error.data.message)) {
          ctrl.errorText = error.data.message;
        }

        ctrl.scannerInput = null;

      });
    }
    
    function fetchSingle(taskData, forget) {

      PrintApi.fetchSingleTask({
        task_id: taskData.Task.id,
        step_id: taskData.TaskStep.id
      }).then(function (data) {
        
        ctrl.errorText = null;

        data.taskType = 'Single';
        
        Store.data('PrintTask').update(data);

        ctrl.task = data;
        ctrl.task.type_printer_local = ctrl.typePinterLocal;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });
      
    }
    
    function fetchMultiple(taskData, forget) {

      PrintApi.fetchMultipleTask({
        task_group_id: taskData.TaskGroupStep.id,
      }).then(function (data) {
        
        ctrl.errorText = null;
        
        data.taskType = 'Multiple';
        
        Store.data('PrintTask').update(data);

        ctrl.isPrintStep = data.current_step.TaskStepType.code === 'print';

        ctrl.task = data;
        ctrl.task.type_printer_local = ctrl.typePinterLocal;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });
      
    }

      // /*** helpers ***/
      // function fetchSingleSource() {
      //
      //     PrintApi.fetchSingleTaskSource({
      //         task_id: ctrl.taskInfo.Task.id,
      //         step_id: ctrl.taskInfo.TaskStep.id
      //     }).then(function (data) {
      //         ctrl.source = data.TaskSource;
      //     });
      //
      // }
      //
      // function fetchMultipleSource() {
      //
      //     PrintApi.fetchMultipleTaskSource({
      //         task_group_step_id: ctrl.task.current_step.TaskGroupStep.id
      //     }).then(function (data) {
      //         vm.source = data.TaskSource;
      //     });
      //
      // }
    
    function processSingle() {

      PrintApi.processSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id,
        printer_title: ctrl.titlePinterLocal,
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });
      
    }

    function processMultiple() {

      PrintApi.processMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }
    
    function failSingle() {

      PrintApi.failSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function (data) {
          ctrl.state.set('scan', true);
        });
      
    }
    
    function retrySingle() {

      PrintApi.retrySingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          fetchSingle(ctrl.taskInfo, true);
        });
      
    }
    
    function retryMultiple() {

      PrintApi.retryMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          fetchMultiple(ctrl.taskInfo, true);
        });
      
    }
    
  }

  angular.module('printty')

    .component('printRight', {
      require: {
        printCtrl: '^^printComponent'
      },
      controller: PrintRightCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/print/print-right.html'
    });

})();