(function() {

    angular.module('printty')

        .component('productDetails', {
            bindings: {
                isShown: '<',
                active: '<',
                type: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&'
            },
            controller: ImportEditProductCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/product-detail.html'
        });

    function ImportEditProductCtrl($scope, $timeout, $mdSidenav, $mdDialog, Product, ProductLinkedCode, Store, utils) {

        'ngInject';

        /****** variables ******/
        var ctrl = this;

        ctrl.product = null;
        ctrl.codes = [];
        ctrl.printingBases = [];
        ctrl.uvFrames = [];
        ctrl.cpFrames = [];
        ctrl.showButton = false;

        ctrl.loadingType = 'full';
        ctrl.showMode = true;
        ctrl.isAdd = true;
        ctrl.error = false;
        ctrl.text = null;

        ctrl.linkCodeBlock = {};

        var subscriptions = [],
            productForm,
            productClone,
            sidenav = {
                open: function () {
                    $mdSidenav('edit-product').open();
                    clearValidate();
                },
                close: function () {
                    $mdSidenav('edit-product').close();
                    clearValidate();
                }
            },
            loadCount = 4;

        /****** methods ******/
        ctrl.$onInit = function() {

            subscriptions.push(
                Store.data('ProductTypes').subscribe(function (data) {
                    ctrl.types = data;
                }),
                Store.data('ProductCategories').subscribe(function (data) {
                    ctrl.categories = data;
                }),
                Store.data('ProductPrintingBases').subscribe(function (data) {
                    ctrl.printingBases = data;
                }),
                Store.data('ProductUvFrames').subscribe(function (data) {
                    ctrl.uvFrames = data;
                }),
                Store.data('ProductCpFrames').subscribe(function (data) {
                    ctrl.cpFrames = data;
                }),
                Store.data('Mode').subscribe(function (data){
                    ctrl.showButton = !data.is_franchise;
                })
            );

        };

        ctrl.$postLink = function () {

            productForm = $scope.editProductForm;

            $mdSidenav('edit-product').onClose(function () {
                $timeout(function () { ctrl.onClose(); }, 400);
            });

        };

        ctrl.$onChanges = function (changes) {

            if ( !('isShown' in changes) ) {
                return;
            }

            if (ctrl.isShown) {
                openComponent();
            } else {
                setDefault();
            }

        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            });

        };

        ctrl.close = function () {
            sidenav.close();
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                productClone = _.cloneDeep(ctrl.product);
                ctrl.showMode = false;
            } else {
                ctrl.product = productClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            // form validation
            productForm.$setSubmitted();
            if(ctrl.product.Product.type_id == 3){
                ctrl.product.Product.printing_base_id = null;
                ctrl.product.Product.cp_frame = null;
                !ctrl.product.Product.uv_frame ? setTextValidate('【UVフレーム 】入力して下さい。') : clearValidate();
            }else if(ctrl.product.Product.type_id == 5){
                ctrl.product.Product.cp_frame = null;
                ctrl.product.Product.uv_frame = null;
                !ctrl.product.Product.printing_base_id ? setTextValidate('【ジグ 】入力して下さい。') : clearValidate();
            }else if(ctrl.product.Product.type_id == 8){
                ctrl.product.Product.printing_base_id = null;
                if((ctrl.product.Product.cp_frame && ctrl.product.Product.uv_frame) || (!ctrl.product.Product.cp_frame && !ctrl.product.Product.uv_frame)){
                    setTextValidate('UVフレーム、CPフレームはどちらかのみ選択して下さい。');
                }else{
                    clearValidate();
                }
            }else if(ctrl.product.Product.type_id == 12) {
                ctrl.product.Product.uv_frame = null;
                ctrl.product.Product.printing_base_id = null;
                !ctrl.product.Product.cp_frame ? setTextValidate('【CPフレーム 】入力して下さい。') : clearValidate();
            }else{
                resetFrame();
            }

            if (productForm.$invalid || ctrl.error) {
                return
            }

            if ( _.isEqual(ctrl.product, productClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Product.update(ctrl.product)
                .then(function (data) {
                    ctrl.product.Product.updated_at = data;
                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {
            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('連携設定を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteProduct);
        };

        ctrl.makeColorArr = function (hexArr) {
            if (hexArr && angular.isString(hexArr)) {
                return hexArr.replace(/ /g,'').split(',');
            }
        };

        ctrl.onTypeChange = function() {
            resetFrame();
            clearValidate();
        }

        ctrl.onFrameChange = function (type){
            type === 'uv' ? ctrl.product.Product.cp_frame = null : ctrl.product.Product.uv_frame = null;
        }

        ////////// Product link codes //////////
        ctrl.linkCodeBlock = {
            isShown: false,
            type: 'add',
            activeItem: null
        };

        ctrl.linkCodeBlock.show = function (code) {
            this.type = 'details';

            this.activeItem = {
                productId: ctrl.product.Product.id,
                codeId: code.ProductLinkedCode.id
            };

            this.isShown = true;
        };

        ctrl.linkCodeBlock.add = function () {
            this.type = 'add';

            this.activeItem = {
                productId: ctrl.product.Product.id,
                codeId: null
            };

            this.isShown = true;
        };

        ctrl.linkCodeBlock.onClose = function () {
            this.isShown = false;
        };

        ctrl.linkCodeBlock.onCreate = function () {
            getLinkedCodes(ctrl.product.Product.id);
        };

        ctrl.linkCodeBlock.onUpdate = function (codeData) {

            var originalCode = _.find(ctrl.codes, function (code) {
                return code.ProductLinkedCode.id == codeData.id;
            });

            originalCode.ProductLinkedCode = codeData;

        };

        ctrl.linkCodeBlock.onDelete = function (codeId) {
            _.remove(ctrl.codes, function (code) {
                return code.ProductLinkedCode.id == codeId;
            });
        };

        function clearValidate(){
            ctrl.error = false;
            ctrl.text = null;
        }

        function setTextValidate(text){
            ctrl.error = true;
            ctrl.text = text;
        }

        function resetFrame(){
            ctrl.product.Product.cp_frame = null;
            ctrl.product.Product.uv_frame = null;
            ctrl.product.Product.printing_base_id = null;
        }

        /******** private **********/

        function openComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active) {
                getProductDetails(ctrl.active);
                getLinkedCodes(ctrl.active.Product.id);
            }

            sidenav.open();

        }

        function getProductDetails(product, toUpdateOriginal) {

            Product.details(product.Product.id)
                .then(function (data) {

                    ctrl.product = data;

                    if (toUpdateOriginal) updateOriginal();

                    stopLoading();
                })
                .catch(function () {
                    stopLoading();
                })
        }

        function getLinkedCodes(productId) {

            ProductLinkedCode.fetchCodes(productId)
                .then(function (data) {
                    ctrl.codes = data.codes;
                    stopLoading();
                }, function () {
                    stopLoading();
                });

        }

        function deleteProduct() {

            ctrl.loadingType = 'partial';

            Product.delete(ctrl.product.Product.id)
                .then(function () {

                    ctrl.onDelete({
                        productId: ctrl.product.Product.id
                    });

                    ctrl.close();
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.close();
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.showMode = true;

            ctrl.product = null;
            ctrl.codes = [];

            ctrl.loadingType = 'stopped';
            ctrl.linkCodeBlock.isShown = false;
            ctrl.linkCodeBlock.type = 'add';

            loadCount = 2;

            ctrl.printingBase = null;

            setFormPristine();
        }

        function setFormPristine() {
            if (productForm) {
                productForm.$setPristine();
                productForm.$setUntouched();
            }
        }

        function stopLoading() {
            if (loadCount) {
                loadCount--;
            }
            if (!loadCount) {
                ctrl.loadingType = 'stopped';
            }
        }

        function updateOriginal() {

            var productData = _.cloneDeep(ctrl.product);

            var category = _.find(ctrl.categories, function (category) {
                return category.ProductCategory.id == ctrl.product.Product.category_id;
            });

            var type = _.find(ctrl.types, function (type) {
                return type.ProductType.id == ctrl.product.Product.type_id;
            });
            productData.Product.category = category ? category.ProductCategory.title : null;
            productData.Product.type = type ? type.ProductType.title : null;

            ctrl.onUpdate({
                product: productData
            });

        }

    }

})();