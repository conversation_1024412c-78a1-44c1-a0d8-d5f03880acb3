(function () {

    angular.module('printty')
        .component('importItemCpframe', {
            controller: CpFramesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/cpframe.html'
        });

    function CpFramesCtrl(Store, utils, CpFrames) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.cps = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.cpDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:cp-frame').subscribe(function () { ctrl.viewCreate(); })
            );

            getFrames();

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.cps = [];

            getFrames();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (cp) {
            ctrl.cpDetails.type = 'details';
            ctrl.cpDetails.isShown = true;
            ctrl.cpDetails.active = {
                cpId: cp.CPFrame.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.cpDetails.type = 'add';
            ctrl.cpDetails.isShown = true;
            ctrl.cpDetails.active = {};
        };

        ctrl.onCpFrameClose = function () {
            ctrl.cpDetails.isShown = false;
        };

        ctrl.onCpFrameCreate = function () {
            ctrl.reload();
        };

        ctrl.onCpFrameUpdate = function (cp) {
            var originalCp = _.find(ctrl.cps, function (cpItem) {
                return cpItem.CPFrame.id == cp.id;
            });

            _.extend(originalCp.CPFrame, cp);
        };

        ctrl.onCpFrameDelete = function (cpID) {
            _.remove(ctrl.cps, function(cp) {
                return cp.CPFrame.id == cpID;
            });
        };

        /****** private ******/
        function getFrames() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            CpFrames.fetchCps({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.cps = data.cp_frames.reverse();
                    ctrl.pagination.empty = !ctrl.cps.length;
                    ctrl.pagination.reload = false;
                    Store.data('instruction_paper_size_list').update(data.instruction_paper_size_list);
                    utils.listLoading.hide();

                });

        }

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            CpFrames.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    ctrl.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : ctrl.pagination.searchQuery
            };

            CpFrames.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadTemplate = function () {
            utils.globalLoading.show();

            CpFrames.CSVdownloadTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

    }

})();