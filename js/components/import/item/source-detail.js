(function () {

    angular.module('printty')
        .component('importItemSourceDetails', {
            controller: ImportSourceDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/source-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ImportSourceDetailsCtrl($scope, utils, Source, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.source = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var sourceForm,
            sourceClone;

        var sidenav = {
            open: function () {
                $mdSidenav('source-details').open();
            },
            close: function () {
                $mdSidenav('source-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            sourceForm = $scope.sourceForm;

            $mdSidenav('source-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                sourceClone = _.cloneDeep(ctrl.source);
                ctrl.showMode = false;
            } else {
                ctrl.source = sourceClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            sourceForm.$setSubmitted();

            if (sourceForm.$invalid) return;

            if  ( _.isEqual(ctrl.source, sourceClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Source.updateSource(ctrl.source)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            sourceForm.$setSubmitted();

            if (sourceForm.$invalid) return;

            ctrl.loadingType = 'partial';

            Source.createSource(ctrl.source)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('UV冶具を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteSource);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.sourceId) {
                fetchSource();
            }

            sidenav.open();

        }

        function fetchSource() {

            ctrl.loadingType = 'full';

            Source.fetchSource(ctrl.active.sourceId)
                .then(function (data) {
                    ctrl.source = data.Source.ProductLinkedSource;
                    ctrl.products = data.Products;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var sourceData = _.cloneDeep(ctrl.source);

            ctrl.onUpdate({
                source: sourceData
            });

        }

        function deleteSource() {

            ctrl.loadingType = 'partial';

            Source.deleteSource(ctrl.source.id)
                .then(function () {

                    ctrl.onDelete({
                        sourceId: ctrl.source.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.source = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (sourceForm) {
                sourceForm.$setPristine();
                sourceForm.$setUntouched();
            }
        }

    }

})();