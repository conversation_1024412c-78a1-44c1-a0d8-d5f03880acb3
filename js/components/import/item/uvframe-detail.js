(function () {

    angular.module('printty')
        .component('uvframeDetails', {
            controller: UvFrameDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/uvframe-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function UvFrameDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, UvFrames) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.uv = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var uvForm,
            uvClone;

        var sidenav = {
            open: function () {
                $mdSidenav('uvframe-details').open();
            },
            close: function () {
                $mdSidenav('uvframe-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            uvForm = $scope.uvFrameForm;

            $mdSidenav('uvframe-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                uvClone = _.cloneDeep(vm.uv);
                vm.showMode = false;
            } else {
                vm.uv = uvClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            uvForm.$setSubmitted();

            if (uvForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.uv, uvClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            UvFrames.updateUv(vm.uv)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.save = function () {
            uvForm.$setSubmitted();

            if (uvForm.$invalid) {
                return;
            }

            vm.loadingType = 'partial';

            UvFrames.createUv(vm.uv)
                .then(function () {
                    vm.onCreate();
                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('UVを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteUv);
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.uvId) {
                fetchUv();
            }

            sidenav.open();
        }

        function fetchUv() {
            vm.loadingType = 'full';

            UvFrames.fetchUv(vm.active.uvId)
                .then(function (data) {
                    vm.uv = data.UVFrame;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var uvData = _.cloneDeep(vm.uv);

            vm.onUpdate({
                uv: uvData
            });
        }

        function deleteUv() {
            vm.loadingType = 'partial';

            UvFrames.deleteUv(vm.uv.id)
                .then(function () {
                    vm.onDelete({
                        uvId: vm.uv.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.uv = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (uvForm) {
                uvForm.$setPristine();
                uvForm.$setUntouched();
            }
        }

    }

})();