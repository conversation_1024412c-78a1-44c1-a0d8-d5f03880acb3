(function () {

    function ImportItemCtrl($state, Auth, Store) {

        "ngInject";

        if (!Auth.isAuthorized('production')) {
            return $state.go( Auth.defaultRoute );
        }

        var ctrl = this;

        ctrl.state = $state;
        ctrl.initialState = ctrl.state.current.name;

        ctrl.date = moment().toDate();

        ctrl.$onInit = function () {

        };

        ctrl.$onDestroy = function () {

        };

        ctrl.onCreatePlanClose = function () {
            ctrl.isCreatePlanShown = false;
        };

        ctrl.onDateChange = function () {

        };

        ctrl.showSubModeTab = function (title){
            var found = false;
            Store.data('Mode').subscribe(function (data){
                if(data && data.mode_sub_tab){
                    found = Auth.contains(data.mode_sub_tab,title);
                }
            });
            return found;
        }


    }

    angular.module('printty')

        .component('importItem', {
            controller: ImportItemCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/item.html'
        });

})();