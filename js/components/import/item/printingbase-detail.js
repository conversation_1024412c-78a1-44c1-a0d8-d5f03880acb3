(function () {

    angular.module('printty')
        .component('importItemBaseDetails', {
            controller: ImportBaseDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/printingbase-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ImportBaseDetailsCtrl($scope, utils, PrintingBase, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.base = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var baseForm,
            baseClone;

        var sidenav = {
            open: function () {
                $mdSidenav('base-details').open();
            },
            close: function () {
                $mdSidenav('base-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            baseForm = $scope.baseForm;

            $mdSidenav('base-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                baseClone = _.cloneDeep(ctrl.base);
                ctrl.showMode = false;
            } else {
                ctrl.base = baseClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            baseForm.$setSubmitted();

            if (baseForm.$invalid) return;

            if  ( _.isEqual(ctrl.base, baseClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            PrintingBase.updateBase(ctrl.base)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            baseForm.$setSubmitted();

            if (baseForm.$invalid) return;

            ctrl.loadingType = 'partial';

            PrintingBase.createBase(ctrl.base)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('UV冶具を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteBase);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.baseId) {
                fetchBase();
            }

            sidenav.open();

        }

        function fetchBase() {

            ctrl.loadingType = 'full';

            PrintingBase.fetchBase(ctrl.active.baseId)
                .then(function (data) {
                    ctrl.base = data.PrintingBase;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var baseData = _.cloneDeep(ctrl.base);

            ctrl.onUpdate({
                base: baseData
            });

        }

        function deleteBase() {

            ctrl.loadingType = 'partial';

            PrintingBase.deleteBase(ctrl.base.id)
                .then(function () {

                    ctrl.onDelete({
                        baseId: ctrl.base.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.base = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (baseForm) {
                baseForm.$setPristine();
                baseForm.$setUntouched();
            }
        }

    }

})();