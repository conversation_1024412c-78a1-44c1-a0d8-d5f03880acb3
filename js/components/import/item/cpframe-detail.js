(function () {

    angular.module('printty')
        .component('cpframeDetails', {
            controller: CpFrameDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/cpframe-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function CpFrameDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, CpFrames,Store) {

        'ngInject';

        /****** variables ******/
        var vm = this;

        vm.cp = null;

        vm.isAdd = true;
        vm.showMode = true;

        vm.loadingType = 'full';

        var cpForm, cpClone;

        var sidenav = {
            open: function () {
                Store.data('instruction_paper_size_list').subscribe(function (data){
                    vm.listPaper = data;
                });
                vm.check_instruction_paper_size_id = false;
                $mdSidenav('cp-frame-details').open();
            },
            close: function () {
                vm.check_instruction_paper_size_id = false;
                $mdSidenav('cp-frame-details').close();
            }
        };

        /****** methods ******/
        vm.$postLink = function () {
            cpForm = $scope.cpFrameForm;

            $mdSidenav('cp-frame-details').onClose(function () {
                $timeout(function () {
                    vm.onClose();
                }, 400);
            });
        };

        vm.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (vm.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        vm.edit = function () {
            if (vm.showMode) {
                cpClone = _.cloneDeep(vm.cp);
                vm.showMode = false;
            } else {
                vm.cp = cpClone;
                setFormPristine();
                vm.showMode = true;
            }
        };

        vm.update = function () {
            cpForm.$setSubmitted();

            if (cpForm.$invalid) {
                return;
            }

            if ( _.isEqual(vm.cp, cpClone) ) {
                vm.showMode = true;
                setFormPristine();
                return;
            }

            vm.loadingType = 'partial';

            CpFrames.updateCp(vm.cp)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    vm.showMode = true;

                    vm.loadingType = 'stopped';
                }, function () {
                    vm.loadingType = 'stopped';
                });
        };

        vm.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('CPを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteCp);
        };

        vm.close = function () {
            sidenav.close();
        };

        /******** private **********/
        function initComponent() {
            switch (vm.type) {
                case 'details':
                    vm.loadingType = 'full';
                    vm.isAdd = false;
                    vm.showMode = true;
                    break;
                case 'add':
                default:
                    vm.loadingType = 'stopped';
                    vm.isAdd = true;
                    vm.showMode = false;
            }

            if (!vm.isAdd && vm.active.cpId) {
                fetchCp();
            }

            sidenav.open();
        }

        function fetchCp() {
            vm.loadingType = 'full';

            CpFrames.fetchCp(vm.active.cpId)
                .then(function (data) {
                    vm.cp = data.CPFrame;
                    vm.loadingType = 'stopped';
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function updateOriginal() {
            var cpData = _.cloneDeep(vm.cp);

            vm.onUpdate({
                cp: cpData
            });
        }

        function deleteCp() {
            vm.loadingType = 'partial';

            CpFrames.deleteCp(vm.cp.id)
                .then(function () {
                    vm.onDelete({
                        cpId: vm.cp.id
                    });

                    vm.close();
                })
                .catch(function () {
                    vm.loadingType = 'stopped';
                });
        }

        function setDefault() {
            vm.isAdd = true;
            vm.cp = null;

            setFormPristine();

            vm.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (cpForm) {
                cpForm.$setPristine();
                cpForm.$setUntouched();
            }
        }

    }

})();