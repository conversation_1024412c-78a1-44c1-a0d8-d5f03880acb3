(function () {

    angular.module('printty')
        .component('importComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: ImportCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/import.html'
        });

    function ImportCtrl($scope, Auth, utils, $state, Store) {

        "ngInject";

        var ctrl = this;

        ctrl.state = $state;
        ctrl.statuses = [];

        $scope.$on('$stateChangeStart', function () { });

        ctrl.$onInit = function () {
            utils.appLoaded();
        };

        ctrl.$onDestroy = function () {

        };

        ctrl.showNav = function (title){
            var found = false;
            Store.data('Mode').subscribe(function (data){
                if(data && data.mode_sub){
                    found =  Auth.contains(data.mode_sub,title);
                }
            });
            return found;
        }

        ctrl.handleUrl = function (title,main_title){
            var href = null;
            Store.data('Mode').subscribe(function (data){
                if(data && data.mode_sub){
                    var mode_sub = data.mode_sub.find(function(item) {
                        return item.title === title;
                    });
                    href = data.mode_sub_tab.find(function(item) {
                        return item.mode_sub_id === mode_sub.id;
                    });
                }
            });
            return  href ? href.key : main_title;
        }

    }

})();