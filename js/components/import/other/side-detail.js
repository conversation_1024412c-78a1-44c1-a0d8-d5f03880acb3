(function () {

    angular.module('printty')
        .component('sideDetails', {
            controller: SideDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/side-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function SideDetailsCtrl($scope, utils, Side, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.side = null;
        ctrl.products = null;
        ctrl.uploadImg = '';

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var sideForm,
            sideClone;

        var sidenav = {
            open: function () {
                $mdSidenav('side-details').open();
            },
            close: function () {
                $mdSidenav('side-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            sideForm = $scope.sideForm;

            $mdSidenav('side-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                sideClone = _.cloneDeep(ctrl.side);
                ctrl.showMode = false;
            } else {
                ctrl.side = sideClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            sideForm.$setSubmitted();

            if (sideForm.$invalid) return;

            if  ( _.isEqual(ctrl.side, sideClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Side.updateSide(ctrl.side)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            sideForm.$setSubmitted();

            if (sideForm.$invalid) return;

            ctrl.loadingType = 'partial';

            side.createSide(ctrl.side)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('SIDEを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteSide);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.sideId) {
                fetchSide();
            }

            $('#imgInput').on('click',function(e){
                this.value = null;
            });

            $('#imgInput').on('change', function (e) {
                ctrl.editImage(e.target.files[0]);
            });

            sidenav.open();

        }

        function fetchSide() {

            ctrl.loadingType = 'full';

            Side.fetchSide(ctrl.active.sideId)
                .then(function (data) {
                    ctrl.side = data.ProductColorSide;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var sideData = _.cloneDeep(ctrl.side);

            ctrl.onUpdate({
                side: sideData
            });

        }

        function deleteSide() {

            ctrl.loadingType = 'partial';

            Side.deleteSide(ctrl.side.id)
                .then(function () {

                    ctrl.onDelete({
                        sideId: ctrl.side.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.side = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (sideForm) {
                sideForm.$setPristine();
                sideForm.$setUntouched();
            }
        }

        ctrl.editImage = function (file) {

            if (!file) {
                $('#imgInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'png' && fileType !== 'jpg' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            ctrl.loadingType = 'partial';

            var sideId = ctrl.side.id;

            Side.uploadImg(file ,sideId)
                .then(function (url) {
                    ctrl.side.medium_image_url = url;
                    ctrl.loadingType = 'stopped';
                    updateOriginal();

                    setFormPristine();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

    }

})();