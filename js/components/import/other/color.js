(function () {

    angular.module('printty')
        .component('importOtherColor', {
            controller: ImportOtherColorCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/color.html'
        });

    function ImportOtherColorCtrl(Auth, $state, Store, utils, Color, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.colors = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.colors = [];

            ctrl.infiniteColors.numLoaded_ = 0;
            ctrl.infiniteColors.toLoad_ = 0;
            ctrl.infiniteColors.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.colorDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteColors = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.colors[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Color.fetchColors(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.colors.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteColors.numLoaded_ = ctrl.infiniteColors.toLoad_ - pagination.perPage + data.colors.length;
                        ctrl.colors = ctrl.colors.concat(data.colors);

                        pagination.searchFailed = !ctrl.colors.length;
                        pagination.end = ctrl.colors.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteColors.numLoaded_ = 0;
            ctrl.infiniteColors.toLoad_ = 0;
            ctrl.infiniteColors.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (color) {
            ctrl.colorDetails.type = 'details';
            ctrl.colorDetails.isShown = true;
            ctrl.colorDetails.active = {
                colorId: color.ProductColor.id
            };
        };

        ctrl.onColorClose = function () {
            ctrl.colorDetails.isShown = false;
        };

        ctrl.onColorCreate = function () {
            pagination.reload();
        };

        ctrl.onColorUpdate = function (color) {
            var originalColor = _.find(ctrl.colors, function (colorItem) {
                return colorItem.ProductColor.id == color.id;
            });

            _.extend(originalColor.ProductColor, color);
        };

        ctrl.onColorDelete = function (colorId) {
            _.remove(ctrl.colors, function(color) {
                return color.ProductColor.id == colorId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Color.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            Color.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Color.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();