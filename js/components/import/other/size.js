(function () {

    angular.module('printty')
        .component('importOtherSize', {
            controller: ImportOtherSizeCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/size.html'
        });

    function ImportOtherSizeCtrl(Auth, $state, Store, utils, Size, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.sizes = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.sizes = [];

            ctrl.infiniteSizes.numLoaded_ = 0;
            ctrl.infiniteSizes.toLoad_ = 0;
            ctrl.infiniteSizes.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.sizeDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteSizes = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.sizes[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Size.fetchSizes(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.sizes.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteSizes.numLoaded_ = ctrl.infiniteSizes.toLoad_ - pagination.perPage + data.sizes.length;
                        ctrl.sizes = ctrl.sizes.concat(data.sizes);

                        pagination.searchFailed = !ctrl.sizes.length;
                        pagination.end = ctrl.sizes.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteSizes.numLoaded_ = 0;
            ctrl.infiniteSizes.toLoad_ = 0;
            ctrl.infiniteSizes.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (size) {
            ctrl.sizeDetails.type = 'details';
            ctrl.sizeDetails.isShown = true;
            ctrl.sizeDetails.active = {
                sizeId: size.ProductSize.id
            };
        };

        ctrl.onSizeClose = function () {
            ctrl.sizeDetails.isShown = false;
        };

        ctrl.onSizeCreate = function () {
            pagination.reload();
        };

        ctrl.onSizeUpdate = function (size) {
            var originalSize = _.find(ctrl.sizes, function (sizeItem) {
                return sizeItem.ProductSize.id == size.id;
            });

            _.extend(originalSize.ProductSize, size);
        };

        ctrl.onSizeDelete = function (sizeId) {
            _.remove(ctrl.sizes, function(size) {
                return size.ProductSize.id == sizeId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Size.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            Size.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Size.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();