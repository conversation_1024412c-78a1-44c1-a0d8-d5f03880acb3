(function () {

    angular.module('printty')
        .component('cpDetails', {
            controller: CpDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/cp-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function CpDetailsCtrl($scope, utils, Cp, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.cp = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var cpForm,
            cpClone;

        var sidenav = {
            open: function () {
                $mdSidenav('cp-details').open();
            },
            close: function () {
                $mdSidenav('cp-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            cpForm = $scope.cpForm;

            $mdSidenav('cp-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                cpClone = _.cloneDeep(ctrl.cp);
                ctrl.showMode = false;
            } else {
                ctrl.cp = cpClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            cpForm.$setSubmitted();

            if (cpForm.$invalid) return;

            if  ( _.isEqual(ctrl.cp, cpClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Cp.updateCp(ctrl.cp)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            cpForm.$setSubmitted();

            if (cpForm.$invalid) return;

            ctrl.loadingType = 'partial';

            cp.createCp(ctrl.cp)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('CPオプションを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteCp);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {
            console.log(ctrl.type)
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }
            console.log(ctrl.active);
            if (!ctrl.isAdd && ctrl.active.cpId) {
                fetchCp();
            }

            sidenav.open();

        }

        function fetchCp() {

            ctrl.loadingType = 'full';

            Cp.fetchCp(ctrl.active.cpId)
                .then(function (data) {
                    ctrl.cp = data.ProductCPOption;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var cpData = _.cloneDeep(ctrl.cp);

            ctrl.onUpdate({
                cp: cpData
            });

        }

        function deleteCp() {

            ctrl.loadingType = 'partial';

            Cp.deleteCp(ctrl.cp.id)
                .then(function () {

                    ctrl.onDelete({
                        cpId: ctrl.cp.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.cp = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (cpForm) {
                cpForm.$setPristine();
                cpForm.$setUntouched();
            }
        }

    }

})();