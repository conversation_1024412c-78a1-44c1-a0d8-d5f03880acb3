(function () {

    angular.module('printty')
        .component('sizeDetails', {
            controller: SizeDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/size-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function SizeDetailsCtrl($scope, utils, Size, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.size = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var sizeForm,
            sizeClone;

        var sidenav = {
            open: function () {
                $mdSidenav('size-details').open();
            },
            close: function () {
                $mdSidenav('size-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            sizeForm = $scope.sizeForm;

            $mdSidenav('size-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                sizeClone = _.cloneDeep(ctrl.size);
                ctrl.showMode = false;
            } else {
                ctrl.size = sizeClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        function validateInput(coller_open_back, coller_open_front) {
            function isInt(value) {
                var x = parseFloat(value);
                return !isNaN(value) && (x | 0) === x;
            }

            function isValid(value) {
                return value !== '' &&
                    value !== null &&
                    value !== undefined &&
                    value !== 'undefined';
            }

            const isValidBack = isValid(coller_open_back) && isInt(coller_open_back) && coller_open_back >= 0 && coller_open_back <= 100;
            const isValidFront = isValid(coller_open_front) && isInt(coller_open_front) && coller_open_front >= 0 && coller_open_front <= 100;

            return {
                check_back: !isValidBack,
                check_front: !isValidFront
            };
        }

        ctrl.update = function () {

            sizeForm.$setSubmitted();

            var check = validateInput(sizeForm.coller_open_back.$viewValue,sizeForm.coller_open_front.$viewValue)
            ctrl.check_back = check.check_back;
            ctrl.check_front = check.check_front;

            if (sizeForm.$invalid || ctrl.check_back || ctrl.check_front) return;

            if  ( _.isEqual(ctrl.size, sizeClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Size.updateSize(ctrl.size)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            sizeForm.$setSubmitted();

            var check = validateInput(sizeForm.coller_open_back.$viewValue,sizeForm.coller_open_front.$viewValue)
            ctrl.check_back = check.check_back;
            ctrl.check_front = check.check_front;

            if (sizeForm.$invalid || ctrl.check_back || ctrl.check_front) return;

            ctrl.loadingType = 'partial';

            size.createSize(ctrl.size)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('サイズを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteSize);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    ctrl.check_back = false;
                    ctrl.check_front = false;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
                    ctrl.check_back = false;
                    ctrl.check_front = false;
            }

            if (!ctrl.isAdd && ctrl.active.sizeId) {
                fetchSize();
            }

            sidenav.open();

        }

        function fetchSize() {

            ctrl.loadingType = 'full';

            Size.fetchSize(ctrl.active.sizeId)
                .then(function (data) {
                    ctrl.size = data.ProductSize;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var sizeData = _.cloneDeep(ctrl.size);

            ctrl.onUpdate({
                size: sizeData
            });

        }

        function deleteSize() {

            ctrl.loadingType = 'partial';

            Size.deleteSize(ctrl.size.id)
                .then(function () {

                    ctrl.onDelete({
                        sizeId: ctrl.size.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.size = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (sizeForm) {
                sizeForm.$setPristine();
                sizeForm.$setUntouched();
            }
        }

    }

})();