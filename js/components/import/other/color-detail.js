(function () {

    angular.module('printty')
        .component('colorDetails', {
            controller: ColorDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/color-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function ColorDetailsCtrl($scope, utils, Color, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.color = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var colorForm,
            colorClone;

        var sidenav = {
            open: function () {
                $mdSidenav('color-details').open();
            },
            close: function () {
                $mdSidenav('color-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            colorForm = $scope.colorForm;

            $mdSidenav('color-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                colorClone = _.cloneDeep(ctrl.color);
                ctrl.showMode = false;
            } else {
                ctrl.color = colorClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            colorForm.$setSubmitted();

            if (colorForm.$invalid) return;

            if  ( _.isEqual(ctrl.color, colorClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Color.updateColor(ctrl.color)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            colorForm.$setSubmitted();

            if (colorForm.$invalid) return;

            ctrl.loadingType = 'partial';

            color.createColor(ctrl.color)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('カラーを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteColor);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.colorId) {
                fetchColor();
            }

            sidenav.open();

        }

        function fetchColor() {

            ctrl.loadingType = 'full';

            Color.fetchColor(ctrl.active.colorId)
                .then(function (data) {
                    ctrl.color = data.ProductColor;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var colorData = _.cloneDeep(ctrl.color);

            ctrl.onUpdate({
                color: colorData
            });

        }

        function deleteColor() {

            ctrl.loadingType = 'partial';

            Color.deleteColor(ctrl.color.id)
                .then(function () {

                    ctrl.onDelete({
                        colorId: ctrl.color.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.color = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (colorForm) {
                colorForm.$setPristine();
                colorForm.$setUntouched();
            }
        }

    }

})();