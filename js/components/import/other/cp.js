(function () {

    angular.module('printty')
        .component('importOtherCp', {
            controller: ImportOtherCpCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/cp.html'
        });

    function ImportOtherCpCtrl(Auth, $state, Store, utils, Cp, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.cps = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.cps = [];

            ctrl.infiniteCps.numLoaded_ = 0;
            ctrl.infiniteCps.toLoad_ = 0;
            ctrl.infiniteCps.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.cpDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteCps = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.cps[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Cp.fetchCps(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.cps.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteCps.numLoaded_ = ctrl.infiniteCps.toLoad_ - pagination.perPage + data.cps.length;
                        ctrl.cps = ctrl.cps.concat(data.cps);

                        pagination.searchFailed = !ctrl.cps.length;
                        pagination.end = ctrl.cps.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteCps.numLoaded_ = 0;
            ctrl.infiniteCps.toLoad_ = 0;
            ctrl.infiniteCps.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (cp) {
            ctrl.cpDetails.type = 'details';
            ctrl.cpDetails.isShown = true;
            ctrl.cpDetails.active = {
                cpId: cp.ProductCPOption.id
            };
            console.log(cp);
        };

        ctrl.onCpClose = function () {
            ctrl.cpDetails.isShown = false;
        };

        ctrl.onCpCreate = function () {
            pagination.reload();
        };

        ctrl.onCpUpdate = function (cp) {
            var originalCp = _.find(ctrl.cps, function (cpItem) {
                return cpItem.ProductCPOption.id == cp.id;
            });

            _.extend(originalCp.ProductCPOption, cp);
        };

        ctrl.onCpDelete = function (cpId) {
            _.remove(ctrl.cps, function(cp) {
                return cp.ProductCPOption.id == cpId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Cp.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            Cp.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Cp.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();