(function () {

    angular.module('printty')
        .component('importOtherSide', {
            controller: ImportOtherSideCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/side.html'
        });

    function ImportOtherSideCtrl(Auth, $state, Store, utils, Side, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';
        ctrl.uploadImgaaa = '';

        ctrl.sides = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.sides = [];

            ctrl.infiniteSides.numLoaded_ = 0;
            ctrl.infiniteSides.toLoad_ = 0;
            ctrl.infiniteSides.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.sideDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteSides = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.sides[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Side.fetchSides(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.sides.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteSides.numLoaded_ = ctrl.infiniteSides.toLoad_ - pagination.perPage + data.sides.length;
                        ctrl.sides = ctrl.sides.concat(data.sides);

                        pagination.searchFailed = !ctrl.sides.length;
                        pagination.end = ctrl.sides.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteSides.numLoaded_ = 0;
            ctrl.infiniteSides.toLoad_ = 0;
            ctrl.infiniteSides.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (side) {
            ctrl.sideDetails.type = 'details';
            ctrl.sideDetails.isShown = true;
            ctrl.sideDetails.active = {
                sideId: side.ProductColorSide.id
            };
        };

        ctrl.onSideClose = function () {
            ctrl.sideDetails.isShown = false;
        };

        ctrl.onSideCreate = function () {
            pagination.reload();
        };

        ctrl.onSideUpdate = function (side) {
            var originalSide = _.find(ctrl.sides, function (sideItem) {
                return sideItem.ProductColorSide.id == side.id;
            });

            _.extend(originalSide.ProductColorSide, side);
        };

        ctrl.onSideDelete = function (sideId) {
            _.remove(ctrl.sides, function(side) {
                return side.ProductColorSide.id == sideId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Side.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords: pagination.searchQuery
            };

            Side.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Side.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();