(function () {

    angular.module('printty')
        .component('importOtherOptionItem', {
            controller: OptionItemCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/option-item.html'
        });

    function OptionItemCtrl(Auth, $state, Store, utils, OptionItems, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.option_items = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.option_items = [];

            ctrl.infiniteData.numLoaded_ = 0;
            ctrl.infiniteData.toLoad_ = 0;
            ctrl.infiniteData.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.optionItemsDetail = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteData = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.option_items[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                OptionItems.fetchOptionItems(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.option_items.length,
                        conditions_keywords: pagination.searchQuery,
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteData.numLoaded_ = ctrl.infiniteData.toLoad_ - pagination.perPage + data.option_items.length;
                        ctrl.option_items = ctrl.option_items.concat(data.option_items);

                        pagination.searchFailed = !ctrl.option_items.length;
                        pagination.end = ctrl.option_items.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteData.numLoaded_ = 0;
            ctrl.infiniteData.toLoad_ = 0;
            ctrl.infiniteData.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (option_item) {
            ctrl.optionItemsDetail.type = 'details';
            ctrl.optionItemsDetail.isShown = true;
            ctrl.optionItemsDetail.active = {
                optionItemId: option_item.id
            };
        };

        ctrl.onOptionItemClose = function () {
            ctrl.optionItemsDetail.isShown = false;
        };


        ctrl.onOptionItemUpdate = function (option_item) {
            var originalOptionItem = _.find(ctrl.option_items, function (optionItem) {
                return option_item.id == optionItem.id;
            });

            _.extend(originalOptionItem, option_item);
        };

        ctrl.onOptionItemDelete = function (optionItemId) {
            _.remove(ctrl.option_items, function(option_item) {
                return option_item.id == optionItemId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            OptionItems.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords: pagination.searchQuery
            };

            OptionItems.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            OptionItems.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();