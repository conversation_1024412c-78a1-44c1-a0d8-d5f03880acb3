(function () {

    angular.module('printty')
        .component('vakuumDetails', {
            controller: VakuumDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/vakuum-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function VakuumDetailsCtrl($scope, utils, Vakuum, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.vakuum = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var vakuumForm,
            vakuumClone;

        var sidenav = {
            open: function () {
                $mdSidenav('vakuum-details').open();
            },
            close: function () {
                $mdSidenav('vakuum-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            vakuumForm = $scope.vakuumForm;

            $mdSidenav('vakuum-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                vakuumClone = _.cloneDeep(ctrl.vakuum);
                ctrl.showMode = false;
            } else {
                ctrl.vakuum = vakuumClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            vakuumForm.$setSubmitted();

            if (vakuumForm.$invalid) return;

            if  ( _.isEqual(ctrl.vakuum, vakuumClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Vakuum.updateVakuum(ctrl.vakuum)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            vakuumForm.$setSubmitted();

            if (vakuumForm.$invalid) return;

            ctrl.loadingType = 'partial';

            vakuum.createVakuum(ctrl.vakuum)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('Vakuumオプションを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteVakuum);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.vakuumId) {
                fetchVakuum();
            }

            sidenav.open();

        }

        function fetchVakuum() {

            ctrl.loadingType = 'full';

            Vakuum.fetchVakuum(ctrl.active.vakuumId)
                .then(function (data) {
                    ctrl.vakuum = data.ProductVakuumOption;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var vakuumData = _.cloneDeep(ctrl.vakuum);

            ctrl.onUpdate({
                vakuum: vakuumData
            });

        }

        function deleteVakuum() {

            ctrl.loadingType = 'partial';

            Vakuum.deleteVakuum(ctrl.vakuum.id)
                .then(function () {

                    ctrl.onDelete({
                        vakuumId: ctrl.vakuum.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.vakuum = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (vakuumForm) {
                vakuumForm.$setPristine();
                vakuumForm.$setUntouched();
            }
        }

    }

})();