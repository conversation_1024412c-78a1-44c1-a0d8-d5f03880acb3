(function () {

    angular.module('printty')
        .component('uvDetails', {
            controller: UvDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/uv-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function UvDetailsCtrl($scope, utils, Uv, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.uv = null;
        ctrl.products = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var uvForm,
            uvClone;

        var sidenav = {
            open: function () {
                $mdSidenav('uv-details').open();
            },
            close: function () {
                $mdSidenav('uv-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            uvForm = $scope.uvForm;

            $mdSidenav('uv-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                uvClone = _.cloneDeep(ctrl.uv);
                ctrl.showMode = false;
            } else {
                ctrl.uv = uvClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            uvForm.$setSubmitted();

            if (uvForm.$invalid) return;

            if  ( _.isEqual(ctrl.uv, uvClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            Uv.updateUv(ctrl.uv)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            uvForm.$setSubmitted();

            if (uvForm.$invalid) return;

            ctrl.loadingType = 'partial';

            uv.createUv(ctrl.uv)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('UVオプションを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteUv);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.uvId) {
                fetchUv();
            }

            sidenav.open();

        }

        function fetchUv() {

            ctrl.loadingType = 'full';

            Uv.fetchUv(ctrl.active.uvId)
                .then(function (data) {
                    ctrl.uv = data.ProductUVOption;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var uvData = _.cloneDeep(ctrl.uv);

            ctrl.onUpdate({
                uv: uvData
            });

        }

        function deleteUv() {

            ctrl.loadingType = 'partial';

            Uv.deleteUv(ctrl.uv.id)
                .then(function () {

                    ctrl.onDelete({
                        uvId: ctrl.uv.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.uv = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (uvForm) {
                uvForm.$setPristine();
                uvForm.$setUntouched();
            }
        }

    }

})();