(function () {

    angular.module('printty')
        .component('plateDetails', {
            controller: PlateDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/plate-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function PlateDetailsCtrl($scope, utils, ProductPlate, $mdDialog, $mdSidenav, $timeout, Plate) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.plate = null;
        ctrl.proPlates = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        var plateForm,
            plateClone;

        var sidenav = {
            open: function () {
                $mdSidenav('plate-details').open();
            },
            close: function () {
                $mdSidenav('plate-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {

            plateForm = $scope.plateForm;

            $mdSidenav('plate-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                plateClone = _.cloneDeep(ctrl.plate);
                ctrl.showMode = false;
            } else {
                ctrl.plate = plateClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            plateForm.$setSubmitted();

            if (plateForm.$invalid) return;

            if  ( _.isEqual(ctrl.plate, plateClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            ctrl.loadingType = 'partial';

            ProductPlate.updatePlate(ctrl.plate)
                .then(function () {

                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;

                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.save = function () {

            plateForm.$setSubmitted();

            if (plateForm.$invalid) return;

            ctrl.loadingType = 'partial';

            ProductPlate.createPlate(ctrl.plate)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('PLATEを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deletePlate);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.plateId) {
                fetchPlate();
            }

            Plate.fetchPlates().then(function(data){
                ctrl.proPlates = data.plates;
            });

            sidenav.open();

        }

        function fetchPlate() {

            ctrl.loadingType = 'full';

            ProductPlate.fetchPlate(ctrl.active.plateId)
                .then(function (data) {
                    ctrl.plate = data.ProductColorSideSizeLinkedPlate;
                    ctrl.plate.size = data.ProductSize;
                    ctrl.plate.side = data.ProductColorSide;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function updateOriginal() {

            var plateData = _.cloneDeep(ctrl.plate);

            var plate = _.find(ctrl.proPlates, function (plateItem) {
                return plateItem.Plate.id == plateData.plate_id;
            });

            plateData.title = plate.Plate.title;

            ctrl.onUpdate({
                plate: plateData
            });

        }

        function deletePlate() {

            ctrl.loadingType = 'partial';

            ProductPlate.deletePlate(ctrl.plate.id)
                .then(function () {

                    ctrl.onDelete({
                        plateId: ctrl.plate.id
                    });

                    ctrl.close();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.plate = null;

            setFormPristine();

            ctrl.loadingType = 'stopped';
        }

        function setFormPristine() {
            if (plateForm) {
                plateForm.$setPristine();
                plateForm.$setUntouched();
            }
        }

    }

})();