(function () {
    function RepairCtrl(utils, Auth, $state, RepairApi) {

        "ngInject";

        if (!Auth.isAuthorized('repair')) {
            return $state.go( Auth.defaultRoute );
        }

        var ctrl = this;

        utils.appLoaded();

        ctrl.count = '-';
        ctrl.date = moment().toDate();



        ctrl.isHide = false;
        ctrl.state = {

            count: '-',
            date: moment().toDate(),
            name: 'scan',
            history: [],
            isInfo: false,
            repairText: null,

            getNumberQrcode: function(){
                RepairApi.fetchNumberQrCodeByDate({
                    date: moment(ctrl.state.date).format('YYYY-MM-DD'),
                })
                    .then(function (data) {
                        ctrl.state.count = data.count;
                    })
                    .catch(function () {
                        utils.globalLoading.hide();
                    })
            },

            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

    }

    angular.module('printty')

        .component('repairComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: RepairCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/repair/repair.html'
        });


    /******************
     *******************
     * ******* LEFT ******
     ****************/


    function RepairLeftCtrl(Store) {

        "ngInject";

        var ctrl = this;
        // subscription;

        ctrl.$onInit = function () {

            ctrl.state = ctrl.repairCtrl.state;

            ctrl.state.getNumberQrcode();

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                }
            });

        };

        ctrl.onDateChange = function () {
            ctrl.state.getNumberQrcode();
        }


        // subscribe to print task change
        Store.data('RepairTask').subscribe(function (data) {

            if (!data) return;

            if (ctrl && ctrl.state)
            {
                //ctrl.isMultiple = data.taskType === 'Multiple';
                ctrl.task = data;
                ctrl.state.isInfo = true;
            }
            else
            {
                ctrl.state = {

                    name: 'scan',
                    history: [],
                    isInfo: true,

                    is: function () {

                        if (!arguments.length) return;

                        if (arguments.length === 1) {
                            return arguments[0] === this.name;
                        }

                        if (arguments.length > 1) {

                            for (var i = 0; i < arguments.length; i++) {

                                if (arguments[i] === this.name) {
                                    return true;
                                }

                            }

                            return false

                        }

                    },
                    back: function () {

                        var last = this.history[this.history.length - 1];

                        this.set(last, true);

                        if (last == 'scan') {
                            this.history = [];
                        } else {
                            this.history.pop();
                        }

                    },
                    set: function (name, forget) {

                        if (!forget && this.name != name) {
                            this.history.push(this.name);
                        }

                        this.name = name;

                        // emit event
                        this._onChangeCallbacks.forEach(function(callback) {
                            callback();
                        });

                    },

                    _onChangeCallbacks: [],
                    onChange: function(callback) {
                        if (angular.isFunction(callback)) {
                            this._onChangeCallbacks.push(callback);
                        }
                    }

                };
            }

        });

        // ctrl.$onDestroy = function () {
        //   subscription.unsubscribe();
        // };

        /******** helpers ***********/
        function setDefault() {
            ctrl.isMultiple = false;
            ctrl.task = null;
        }

    }

    angular.module('printty')

        .component('repairLeft', {
            require: {
                repairCtrl: '^^repairComponent'
            },
            controller: RepairLeftCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/repair/repair-left.html'
        });


    /******************
     *******************
     * ******* RIGHT ******
     ****************/


    function RepairRightCtrl($element, RepairApi, Store, electron, $timeout, utils, ElectronUtils) {

        "ngInject";

        var ctrl = this;

        ctrl.isElectron = !!electron;

        ctrl.scannerInput = null;
        ctrl.qrCodeInput = null;

        ctrl.taskInfo = null;
        ctrl.task = null;
        ctrl.isRepairStep = true;

        ctrl.$onInit = function () {
            ctrl.state = ctrl.repairCtrl.state;

            ctrl.focusScanField();

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                    $element.removeClass('single multiple');
                }

                if (ctrl.state.is('scan')) {
                    ctrl.focusScanField();
                }
            });

        };

        ctrl.getNumberQrcode = function() {
            ctrl.repairCtrl.state.getNumberQrcode();
        }

        ctrl.findQr = function () {
            if (!ctrl.qrCodeInput) return;
            search(ctrl.qrCodeInput);
        };

        ctrl.focusScanField = function () {
            $timeout(function () {
                $('#scanner-field').focus();
            });
        };

        ctrl.findQrScan = function () {
            utils.delay(function () {
                if (!ctrl.scannerInput) return;
                search(ctrl.scannerInput);
            }, 250);
        };

        ctrl.clearInput = function () {
            ctrl.qrCodeInput = null;
            ctrl.wait = null;
            ctrl.errorText = null;
            ctrl.state.repairText = null;
        };

        ctrl.onDownload = function () {
            ctrl.state.set('finish', true);
        };

        ctrl.onRepair = function () {
            ctrl.state.set('finish', true);
        };

        ctrl.downloadItemImage = function (item) {

            utils.globalLoading.show();

            RepairApi.fetchMultipleTaskSource({
                task_group_step_id: ctrl.taskInfo.TaskGroupStep.id,
                item_id: item.TaskStepItem.id
            })
                .then(function (data) {
                    console.log(data)
                })
                .catch(function () {
                    utils.globalLoading.hide();
                })

        };

        ctrl.press = function () {
            ctrl.state.set('finish', true);
        };

        ctrl.repairSuccess = function () {
            ctrl.clearInput();
            ctrl.scannerInput = null;

            if (ctrl.isMultiple) {
                processMultiple();
            } else {
                processSingle();
            }

        };

        ctrl.repairFail = function () {
            ctrl.clearInput();
            ctrl.scannerInput = null;

            if (ctrl.isMultiple) {
                ctrl.state.set('fail', true);
            } else {
                failSingle();
            }

        };

        ctrl.retryRepair = function () {

            if (ctrl.isMultiple) {
                retryMultiple();
            } else {
                retrySingle();
            }

        };

        ctrl.cancelFail = function () {

            _.forEach(ctrl.task.current_step.items, function (item) {
                item.selected = false;
            });

            ctrl.state.set('finish', true);

        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.scannerInput = null;

            ctrl.taskInfo = null;
            ctrl.task = null;

            ctrl.isMultiple = false;
            ctrl.isRepairStep = true;
            ctrl.wait = null;
            ctrl.errorText = null;
            ctrl.state.repairText = null;
        }

        function search(input) {
            ctrl.clearInput();
            ctrl.wait = '補修中ですので、お待ちください。';
            RepairApi.search(input).then(function (data) {
                ctrl.getNumberQrcode();
                ctrl.wait = '';
                ctrl.scannerInput = null;
                ctrl.state.repairText = data.message;

            }, function (error) {

                ctrl.wait = '';
                ctrl.state.repairText = null;

                if (error.status === 400 && angular.isDefined(error.data.message)) {
                    ctrl.errorText = error.data.message;
                }

                ctrl.scannerInput = null;

            });
        }

    }

    angular.module('printty')

        .component('repairRight', {
            require: {
                repairCtrl: '^^repairComponent'
            },
            controller: RepairRightCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/repair/repair-right.html'
        });

})();