(function () {

    angular.module('printty')
        .component('imagePlanning', {
            controller: ImagePlanningCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/planning.html'
        });

    function ImagePlanningCtrl(Auth, $state, Store, utils, electron , ImagePlanning, $mdDialog) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';
        var subscriptions = [];
        ctrl.total_waiting = 0;
        ctrl.total_error = 0;
        var allSelected = false;

        ctrl.Planning = [];

        var pagination = {
            init: true,
            perPage: 40,
            status_id: 0,

            date: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.planning = [];

            ctrl.infinitePlanning.numLoaded_ = 0;
            ctrl.infinitePlanning.toLoad_ = 0;
            ctrl.infinitePlanning.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.imageDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infinitePlanning = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.planning[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                ImagePlanning.fetchSentPlanning(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.planning.length,
                        conditions_keywords: pagination.searchQuery,
                        status_id: pagination.status_id,
                        date: pagination.date,
                    }
                ).then(
                    function (data) {
                        allSelected = false;
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infinitePlanning.numLoaded_ = ctrl.infinitePlanning.toLoad_ - pagination.perPage + data.planning.length;
                        ctrl.planning = ctrl.planning.concat(data.planning);
                        ctrl.total_waiting = data.total_waiting;
                        ctrl.total_error = data.total_error;

                        pagination.searchFailed = !ctrl.planning.length;
                        pagination.end = ctrl.planning.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            getStatuses();

            Store.data('types').update([]);

            subscriptions.push(

                Store.event('sidebarActiveStatus')
                    .subscribe(function (status) {
                        ctrl.pagination.status_id = status;
                        ctrl.reload();
                    })

            );

            subscriptions.push(

                Store.data('Image:HeaderDate')
                    .subscribe(function (date) {
                        ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
                        ctrl.reload();
                    })

            );

            utils.appLoaded();
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        function getStatuses() {
            var statuses = [
                {id:0, title:'すべて'},
                {id:1, title:'完了'},
                {id:2, title:'待機中'},
                {id:3, title:'処理中'},
                {id:4, title:'エラー'}
            ];
            Store.data('statuses').update(statuses);
        }

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.planning = [];

            ctrl.infinitePlanning.numLoaded_ = 0;
            ctrl.infinitePlanning.toLoad_ = 0;
            ctrl.infinitePlanning.getItemAtIndex(1);
        };

        ctrl.reloadPlanning = function(){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var params = {
                planning_id : selected
            };

            utils.globalLoading.show();

            ImagePlanning.reloadPlanning(params).then(
                function () {

                    utils.globalLoading.hide();
                    ctrl.reload();
                },
                function() {
                    utils.globalLoading.hide();
                }
            );
        };

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.planning, function (planning) {

                if (planning.selected) {
                    selectedItems.push(planning.PlanningQueue.id);
                }

            });

            return selectedItems;

        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.planning, function (planning) {
                planning.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

    }

})();