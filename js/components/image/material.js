(function () {

    angular.module('printty')
        .component('imageMaterial', {
            controller: ImageMaterialCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/material.html'
        });

    function ImageMaterialCtrl(Auth, $state, Store, utils, electron , Image, $mdDialog, Customers,Factory) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';
        var subscriptions = [];
        ctrl.total_waiting = 0;
        ctrl.total_error = 0;
        ctrl.total_material = 0;
        var allSelected = false;

        ctrl.material = [];
        ctrl.printers = [];

        var pagination = {
            init: true,
            perPage: 40,
            customer_id: 0,

            date: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;
        ctrl.statusFilter = [
            {'key': '', 'value' : 'すべて'},
            {'key': 'qr_waiting', 'value' : 'QR印刷待ち' },
            {'key': 'print_waiting', 'value' : '印刷待ち' },
            {'key': 'inspection_waiting', 'value' : '検品待ち' },
            {'key': 'delivery_request_waiting', 'value' : '発送準備中' },
            {'key': 'delivery_preparing_complite', 'value' : '配送準備済' },
            {'key': 'delivery_complite', 'value' : '発送完了' },
        ];

        ctrl.activeFilter = ctrl.statusFilter[0];

        ctrl.setActiveFilter = function (filter) {
            ctrl.activeFilter = filter;
            ctrl.reload();
        };

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.material = [];
            ctrl.printers = [];

            ctrl.infiniteMaterial.numLoaded_ = 0;
            ctrl.infiniteMaterial.toLoad_ = 0;
            ctrl.infiniteMaterial.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.imageDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteMaterial = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.material[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Image.fetchImagesSublimationDtf(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.material.length,
                        conditions_keywords: pagination.searchQuery,
                        status: ctrl.activeFilter ? ctrl.activeFilter.key : '',
                        customer_id: pagination.customer_id,
                        date: pagination.date,
                        material: true,
                        factory_id: ctrl.factory,
                    }
                ).then(
                    function (data) {
                        allSelected = false;
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteMaterial.numLoaded_ = ctrl.infiniteMaterial.toLoad_ - pagination.perPage + data.total_count;
                        ctrl.total_material = data.total_count;
                        ctrl.printers = data.printers;

                        pagination.searchFailed = !ctrl.total_material;
                        pagination.end = ctrl.material.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            // getStatuses();
            getCustomers();

            Store.data('types').update([]);
            Store.data('statuses').update([]);

            subscriptions.push(

                Store.event('sidebarActiveCustomers')
                    .subscribe(function (customer) {
                        ctrl.pagination.customer_id = customer;
                        ctrl.reload();
                    })

            );

            subscriptions.push(

                Store.data('Image:HeaderDate')
                    .subscribe(function (date) {
                        ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
                        ctrl.reload();
                    })

            );
            utils.appLoaded();
        };

        ctrl.viewDetails = function (image) {
            ctrl.imageDetails.type = 'details';
            ctrl.imageDetails.isShown = true;
            ctrl.imageDetails.active = {
                imageId: image.OrderItemTask.id
            };
        };

        ctrl.onImageClose = function () {
            ctrl.imageDetails.isShown = false;
        };

        ctrl.onImageCreate = function () {
            pagination.reload();
        };

        ctrl.onChange = function (){
            ctrl.reload();
        }

        ctrl.onImageUpdate = function (image) {
            var originalImage = _.find(ctrl.material, function (imageItem) {
                return imageItem.OrderItemTask.id == image.id;
            });

            _.extend(originalImage.OrderItemTask, image);
        };

        ctrl.onImageDelete = function (imageId) {
            _.remove(ctrl.material, function(image) {
                return image.OrderItemTask.id == imageId;
            });
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        function getCustomers() {
            var customers = [
                {
                    Customer:{
                        id : 0,
                        title: 'すべて'
                    }
                },
            ];
            Customers.fetchCustomers([]).then(function(data){
                customers = customers.concat(data.customers);
                Store.data('customers').update(customers);
            });
        }

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.material = [];
            ctrl.printers = [];

            ctrl.infiniteMaterial.numLoaded_ = 0;
            ctrl.infiniteMaterial.toLoad_ = 0;
            if(ctrl.factories){
                ctrl.infiniteMaterial.getItemAtIndex(1);
            }else{
                getFactories();
            }
        };

        ctrl.reloadImage = function(){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var params = {
                order_item_task_id : selected
            };

            utils.globalLoading.show();

            Image.reloadImage(params).then(
                function () {

                    utils.globalLoading.hide();
                    ctrl.reload();
                },
                function() {
                    utils.globalLoading.hide();
                }
            );
        };

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.printers, function (printer) {
                _.forEach(printer.data, function (dtf) {

                    if (dtf.selected) {
                        selectedItems.push(dtf.OrderItemTask.id);
                    }

                });
            });

            return selectedItems;

        }

        function getFactories(){
            Factory.fetchFactoryUser().then(function(data){
                ctrl.factories = data.factories;
                ctrl.factory = data.factorySelected;
                ctrl.infiniteMaterial.getItemAtIndex(1);
            });
        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.printers, function (printer) {
                printer.selected = !allSelected;
                _.forEach(printer.data, function (dtf) {
                    dtf.selected = printer.selected;

                });
            });

            allSelected = !allSelected;

        };

        ctrl.selectAllByPrinter = function (printer){
            var itemsWithSameId = _.filter(ctrl.printers, { 'id': printer.id });
            _.forEach(itemsWithSameId, function (item) {
                _.forEach(item.data, function (dtf) {
                    dtf.selected = printer.selected;
                });
            });
        }

        ctrl.selectTask = function (dtf){
            var countSelected = 0;
            var itemsWithSameId = _.filter(ctrl.printers, { 'id': dtf.PrinterJoin.id });
            _.forEach(itemsWithSameId, function (dtf) {
                _.forEach(dtf.data, function (item) {
                    if(item.selected){
                        countSelected++;
                    }
                });
            });
            var printerToUpdate = _.find(ctrl.printers, { 'id': dtf.PrinterJoin.id });
            if (printerToUpdate && countSelected === printerToUpdate.data.length ) {
                printerToUpdate.selected = true;
            }else{
                printerToUpdate.selected = false;
            }
        }

        ctrl.showLargeImage = function (ev, material) {
            $mdDialog.show({
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };

                    $scope.image_large = material.OrderItemTask.medium_preview_image_url;
                }],
                templateUrl: 'views/image/image-dialog.html',
                clickOutsideToClose:true,
            });
        };

        ctrl.deleteMaterial = function(ev) {
            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('タスクを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                var params = {
                    order_item_task_id : selected
                };

                utils.globalLoading.show();

                Image.deleteImage(params).then(
                    function () {

                        utils.globalLoading.hide();
                        ctrl.reload();
                    },
                    function() {
                        utils.globalLoading.hide();
                    }
                );
            });
        };

        ctrl.getPrintMaterial = function(ev){
            var selectedTasks;

            selectedTasks = getSelected();
            if (_.isArray(selectedTasks) && !selectedTasks.length) {
                return;
            }
            var confirm = $mdDialog.confirm({
                template: utils.changeStatusTemplate('資材印刷画像の取得'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                var params = {
                    'order_item_task_id': selectedTasks,
                    date: pagination.date,
                };

                utils.globalLoading.show();

                Image.getPrintSublimationImage(params)
                    .then(function (data) {
                        if(data.order_item_task_download){
                            _.forEach(ctrl.printers, function (printer) {
                                _.forEach(printer.data, function (material) {
                                    if (data.order_item_task_download.includes(material.OrderItemTask.id.toString())) {
                                        material.OrderItemTask.download_time = data.time_download;
                                        material.UserJoin.username = data.user_download;
                                    }
                                });
                            });
                        }
                        utils.globalLoading.hide();
                    })
                    .catch(function () {
                        utils.globalLoading.hide();
                    });
            });
        }

    }

})();