(function () {

    angular.module('printty')
        .component('imageComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: ImageCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/image.html'
        });

    function ImageCtrl($scope, Auth, utils, $state, Store) {

        "ngInject";

        var ctrl = this;
        var subscriptions = [];

        ctrl.state = $state;
        ctrl.imageStatuses = [];
        ctrl.imageCustomers = [];
        ctrl.types = null;
        ctrl.date = moment().toDate();

        $scope.$on('$stateChangeStart', function () { });

        ctrl.$onInit = function () {
            utils.appLoaded();
            Store.data('statuses').update(ctrl.imageStatuses);
            subscriptions.push(
                Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); })
            );
            subscriptions.push(
                Store.data('customers').subscribe(function (customers) { onCustomersChange(customers); })
            );
            subscriptions.push(
                Store.data('types').subscribe(function (types) { onTypesChange(types); })
            );
            Store.data('Image:HeaderDate').update(ctrl.date);
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        /*** private ****/
        function onStatusesChange(statuses) {
            ctrl.imageStatuses = statuses;
        }

        function onCustomersChange(customers) {
            ctrl.imageCustomers = customers;
        }
        function onTypesChange(types) {
            ctrl.types = types;
        }

        ctrl.onDateChange = function () {
            Store.data('Image:HeaderDate').update(ctrl.date);
        };

        ctrl.showNav = function (title){
            var found = false;
            Store.data('Mode').subscribe(function (data){
                if(data && data.mode_sub){
                    found = Auth.contains(data.mode_sub,title);
                }
            });
            return found;
        }

    }

})();