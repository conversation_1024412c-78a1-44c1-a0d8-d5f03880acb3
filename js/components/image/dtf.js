(function () {

    angular.module('printty')
        .component('imageDtf', {
            controller: ImageDtfCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/dtf.html'
        });

    function ImageDtfCtrl(Auth, $state) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.state = $state;
        ctrl.initialState = ctrl.state.current.name;

        /****** methods ******/
        ctrl.$onInit = function () {

        };

    }

})();