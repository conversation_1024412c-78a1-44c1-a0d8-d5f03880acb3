(function () {

    angular.module('printty')
        .component('imagePaperPrint', {
            controller: PaperPrintCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/paper_print.html'
        });

    function PaperPrintCtrl(Auth, $state, Store, utils, electron, Image, $mdDialog, Factory) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/
        ctrl.isElectron = !!electron;
        ctrl.state = $state;
        ctrl.initialState = ctrl.state.current.name;

        var subscriptions = [];
        var allSelected = false;

        ctrl.paper_prints = [];

        var pagination = {
            init: true,
            perPage: 40,

            date: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };

        ctrl.pagination = pagination;

        pagination.reload = function () {
            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.paper_prints = [];

            ctrl.infinitePaperPrint.numLoaded_ = 0;
            ctrl.infinitePaperPrint.toLoad_ = 0;
            ctrl.infinitePaperPrint.getItemAtIndex(1);
        }

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function () {
                ctrl.search();
            }, 350)
        }

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        }

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            ctrl.search();
        }

        ctrl.infinitePaperPrint = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function (index) {
                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }
                return ctrl.paper_prints[index];
            },
            getLength: function () {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function (index) {
                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Image.fetchOrderPaperPrint(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.paper_prints.length,
                        conditions_keywords: pagination.searchQuery,
                        factory_id: ctrl.factory,
                        date: pagination.date,
                    }
                ).then(
                    function (data) {
                        allSelected = false;
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infinitePaperPrint.numLoaded_ = ctrl.infinitePaperPrint.toLoad_ - pagination.perPage + data.orders.length;
                        ctrl.paper_prints = ctrl.paper_prints.concat(data.orders);

                        pagination.searchFailed = !ctrl.paper_prints.length;
                        pagination.end = ctrl.paper_prints.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                )

            }
        }

        /****** methods ******/
        ctrl.$onInit = function () {
            Store.data('customers').update([]);
            Store.data('statuses').update([]);
            Store.data('types').update([]);
            subscriptions.push(
                Store.data('Image:HeaderDate')
                    .subscribe(function (date) {
                        ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
                        ctrl.reload();
                    })
            )

            utils.appLoaded();
        };

        ctrl.onChange = function (){
            ctrl.reload();
        }

        ctrl.reload = function () {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.paper_prints = [];

            ctrl.infinitePaperPrint.numLoaded_ = 0;
            ctrl.infinitePaperPrint.toLoad_ = 0;

            if(ctrl.factories){
                ctrl.infinitePaperPrint.getItemAtIndex(1);
            }else{
                getFactories();
            }
        }

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        function getFactories() {
            pagination.busy = true;
            Factory.fetchFactoryUser().then(function (data) {
                ctrl.factories = data.factories;
                ctrl.factory = data.factorySelected;
                pagination.busy = false;
                ctrl.infinitePaperPrint.getItemAtIndex(1);
            })
        }


        ctrl.getPrintPaper = function (ev) {
            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.changeStatusTemplate('印刷画像の取得'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                var params = {
                    order_ids : selected
                }
                utils.globalLoading.show();

                Image.getPrintPaper(params)
                    .then(function (response) {
                        if (response.url) {
                            window.location.href = response.url;
                        }
                        utils.globalLoading.hide();
                    })
                    .catch(function (error) {
                        utils.globalLoading.hide();
                    })
            });


        }

        function getSelected() {
            var selectedItems = [];

            _.forEach(ctrl.paper_prints, function (paper_print) {
                if (paper_print.selected) {
                    selectedItems.push(paper_print.Order.id);
                }
            })

            return selectedItems;
        }

        ctrl.selectAll = function () {
            _.forEach(ctrl.paper_prints, function (paper_print) {
                paper_print.selected = !allSelected;
            });

            allSelected = !allSelected;
        };

    }

})();