(function () {

    angular.module('printty')
        .component('imageSublimation', {
            controller: ImageSublimationCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/sublimation.html'
        });

    function ImageSublimationCtrl(Auth, $state) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.state = $state;
        ctrl.initialState = ctrl.state.current.name;

        /****** methods ******/
        ctrl.$onInit = function () {

        };

    }

})();