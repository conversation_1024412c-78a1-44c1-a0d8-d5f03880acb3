(function () {
    function RandomUVCtrl(utils, Auth, $state, RandomUvApi, $interval, $mdDialog) {

        "ngInject";

        if (!Auth.isAuthorized('randomuv')) {
            return $state.go( Auth.defaultRoute );
        }


        var ctrl = this;
        ctrl.data = [];
        ctrl.date = moment().toDate();
        ctrl.quantity = 0;

        utils.appLoaded();

        ctrl.isHide = false;
        ctrl.state = {

            name: 'scan',
            history: [],
            isInfo: false,


            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

        ctrl.$onInit = function () {
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getRandomData(date);
            /*interval = $interval(function() {
                date = moment(ctrl.date).format('YYYY-MM-DD');
                getRandomData(date);
            }, 300000);*/
        };

        ctrl.$onDestroy = function () {
            $interval.cancel(interval);
        }

        ctrl.onDateChange = function(){
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getRandomData(date);
        }

        function getRandomData(date){
            RandomUvApi.getRandomData(date).then(function (data) {
                ctrl.data = data;
            });
        }

        ctrl.editOrderTarget = function(){
            var confirm = $mdDialog.confirm({
                template: utils.inputOrderTarget(),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelInput = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmInput = function () {
                        var quantity = $('#input-quantity').val();
                        if(quantity > 0) {
                            ctrl.quantity = quantity;
                            $mdDialog.hide();
                        } else {
                            $('#input-error').html('error');
                        }
                    };
                }],
                onComplete : function(scope, element){
                    var order_target = ctrl.data.customers.order_target;
                    $('#input-quantity').val(order_target);
                },
                clickOutsideToClose: true
            });

            $mdDialog.show(confirm).then(function () {
                RandomUvApi.updateOrderTarget(ctrl.quantity,ctrl.date).then(function (data){
                    ctrl.data.customers.order_target = data.OrderUvTarget.quantity;
                });
            }, function (error) {

            });
        }

        var tick = function() {
            ctrl.clock = Date.now();
        };

        tick();
        $interval(tick, 1000);

    }

    angular.module('printty')

        .component('randomuvComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: RandomUVCtrl,
            controllerAs: 'rc',
            templateUrl: 'views/randomuv/randomuv.html'
        });



})();