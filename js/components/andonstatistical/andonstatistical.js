(function () {
    function AndonStatisticalCtrl(AndonApi, utils, Auth, $state,  $interval, $mdDialog,Factory,electron) {

        "ngInject";

        if (!Auth.isAuthorized('andonkai')) {
            return $state.go( Auth.defaultRoute );
        }

        var ctrl = this;
        ctrl.data = [];
        ctrl.date = moment().toDate();
        ctrl.quantity = 0;
        ctrl.styleHeader = '40px';
        var chartInstances = [];

        utils.appLoaded();

        ctrl.$onInit = function () {
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getData(date);
            interval = $interval(function() {
                date = moment(ctrl.date).format('YYYY-MM-DD');
                getData(date);
            }, 600000);



        };
        var tick = function() {
            ctrl.clock = Date.now();
        };
        ctrl.compareDate = function (date){
            return ctrl.clock > new Date(date);
        }

        tick();
        $interval(tick, 1000);


        ctrl.onChange = function(){
            var date = moment(ctrl.date).format('YYYY-MM-DD');
            getData(date);
        }
        ctrl.hover = function (data){
            ctrl[data] = true;
        }
        ctrl.removeHover = function (data){
            ctrl[data] = false;
        }
        function getData(date){
            ctrl.loading = true;
            if(ctrl.factory){
                getAndonData(date);
            }else{
                Factory.fetchFactoryUser().then(function (data) {
                    ctrl.factory = data.factorySelected;
                    ctrl.factories = data.factories;
                    getAndonData(date);
                });
            }
        }
        function getAndonData(date){
            AndonApi.getAndonStatistical(
                {
                    date: date,
                    factory_id: ctrl.factory
                }
            ).then(function (data) {
                ctrl.data = data;

                setTimeout( function() {
                    renderChart(data.chart_order,'chartItem','進捗状況')
                    renderChart(data.chart_delivery,'chartDelivery','進捗状況');
                },1000);
                ctrl.loading = false;
            });
        }

        function renderChart(data, idChart, title) {
            const canvas = document.getElementById(idChart);
            if (!canvas) {
                console.error('Canvas element not found');
                return;
            }

            if (chartInstances[idChart]) {
                chartInstances[idChart].destroy();
            }

            var ctx = canvas.getContext('2d');

            if(electron){
                var scales = {
                    yAxes: [{
                        id: 'y',
                        position: 'left',
                        gridLines: {
                            display: true,
                        },
                        scaleLabel: {
                            display: true,
                            labelString: '時間計',
                            fontSize: 18,
                            fontStyle: 'bold',
                        }
                    }, {
                        id: 'y1',
                        position: 'right',
                        gridLines: {
                            display: false
                        },
                        scaleLabel: {
                            display: true,
                            labelString: '累計',
                            fontSize: 18,
                            fontStyle: 'bold',
                        }
                    }]
                }
            }else{
                var scales = {
                    x: {
                        beginAtZero: true
                    },
                    y: {
                        beginAtZero: true,
                        position: 'left',

                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                    }
                }
            }
            var customTitle = {
                id: 'customTitle',
                afterDraw: function (chart, args, opts) {
                    var ctx = chart.ctx;
                    var top = chart.chartArea.top;
                    var right = chart.chartArea.right;

                    if (opts.x.display) {
                        ctx.font = opts.x.font
                        ctx.fillStyle = opts.x.color || Chart.defaults.color
                        ctx.fillText(opts.x.text, (right + (opts.x.offsetX || 0)), (top -20))
                    }

                    if(opts.y.display) {
                        ctx.font = opts.y.font
                        ctx.fillStyle = opts.x.color || Chart.defaults.color
                        ctx.fillText(opts.y.text, opts.y.offsetX || 3, (top -20))
                    }
                }
            }
            chartInstances[idChart] = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: title
                        },
                        legend: {
                            position: 'bottom',
                        },
                        customTitle: {
                            y: {
                                display: true,
                                text: '時間計',
                                font: "16px bold Comic Sans MS",
                                color: '#212121',
                            },
                            x: {
                                display: true,
                                text: '累計',
                                color: '#212121',
                                font: "16px bold Comic Sans MS"
                            }
                        },
                    },
                    responsive: true,
                    scales: scales,
                    elements: {
                        line: {
                            tension: 0.4
                        }
                    }
                },
                plugins: [customTitle]
            });
        }

    }

    angular.module('printty')

        .component('andonstatisticalComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: AndonStatisticalCtrl,
            controllerAs: 'rc',
            templateUrl: 'views/andonstatistical/andonstatistical.html'
        });
})();