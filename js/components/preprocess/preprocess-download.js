(function () {

    angular.module('printty')
        .component('preprocessDownload', {
            bindings: {
                task: '<',
                taskInfo: '<',
                download: '=',
                preprocess: '=',
                onDownload: '=',
                onPreprocess: '&',
                source: '='
            },
            controller: PreprocessDownloadCtrl,
            controllerAs: 'vm'
        });

    function PreprocessDownloadCtrl(PreprocessApi, electron, ElectronUtils, $timeout,utils) {

        var vm = this;
        var downloadClick = false;
        var preprocessClick = false;

        vm.source = null;
        vm.isMultiple = vm.task.taskType === 'Multiple';

        if (vm.isMultiple) {
            fetchMultipleSource();
        } else {
            fetchSingleSource();
            fetchNewImageName();
        }

        vm.download = function () {

            // if clicked download before source was loaded
            if (!vm.source) {
                downloadClick = true;
                return;
            }

            if (vm.source.type === 'Image' || vm.source.type === 'PDF') {
                utils.globalLoading.show();

                ElectronUtils.getFile(vm.source.image_url, null, vm.task.product_code, vm.task)
                    .finally(function () {
                        $timeout(vm.onDownload());
                        utils.globalLoading.hide();
                    });
            }

        };

        vm.preprocess = function () {

            if (!vm.source) {
                preprocessClick = true;
                return;
            }

            var save_image = false;

            // just download image
            if (vm.source.type === 'Image') {
                utils.globalLoading.show();
                electron.ipcRenderer.send('save-images', {
                    url: vm.source.image_url,
                    isPreprocess: true,
                    product_code: vm.task.product_code,
                    product_color_code: vm.task.product_color_code,
                    title: vm.task.current_step.TaskStep.title,
                    name: vm.task.current_step.detail.product_title,
                });

                electron.ipcRenderer.on('save-image:saved', function () {
                    if(save_image === false) {
                        save_image = true;
                        $timeout(vm.onPreprocess);
                        utils.globalLoading.hide();
                    }
                    // electron.ipcRenderer.send('simple:print-image', {url: vm.source.image_url});
                });
            }

        };

        vm.$onDestroy = function () {
            electron.ipcRenderer.removeAllListeners('save-image:saved');
        };

        /*** helpers ***/
        function fetchSingleSource() {

            PreprocessApi.fetchSingleTaskSource({
                task_id: vm.taskInfo.Task.id,
                step_id: vm.taskInfo.TaskStep.id,
                is_original: false,
            }).then(function (data) {

                vm.source = data.TaskSource;

                // trigger download func if previously was clicked
                if (downloadClick) {
                    downloadClick = false;
                    vm.download();
                }

                if (preprocessClick) {
                    preprocessClick = false;
                    vm.preprocess();
                }

            });

        }

        function fetchMultipleSource() {

            PreprocessApi.fetchMultipleTaskSource({
                task_group_step_id: vm.task.current_step.TaskGroupStep.id
            }).then(function (data) {

                vm.source = data.TaskSource;

                // trigger download func if previously was clicked
                if (downloadClick) {
                    downloadClick = false;
                    vm.download();
                }

                if (preprocessClick) {
                    preprocessClick = false;
                    vm.preprocess();
                }

            });

        }

        function fetchNewImageName() {
            PreprocessApi.fetchNewImageName({
                step_id: vm.taskInfo.TaskStep.id
            }).then(function (data) {

                vm.task.current_step.new_image_name = data.new_name;

            });
        }

    }

})();