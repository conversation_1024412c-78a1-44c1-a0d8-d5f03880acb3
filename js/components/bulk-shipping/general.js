(function () {

    angular.module('printty')
        .component('shippingComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: ShippingCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/index.html'
        });

    function ShippingCtrl($scope, Auth, utils, $state, Store) {

        "ngInject";

        var ctrl = this;

        ctrl.state = $state;
        ctrl.statuses = [];
        ctrl.customers = [];

        var subscriptions = [];

        $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });

        ctrl.$onInit = function () {
            utils.appLoaded();
            Store.data('BoxStatuses').update(ctrl.statuses);
            subscriptions.push(
                Store.data('BoxStatuses').subscribe(function (statuses) { onStatusesChange(statuses); }),

                Store.data('BoxCustomersFilter').subscribe(function (customers) { onCustomerChange(customers); })
            );
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.createBox = function () {
            Store.event('shipping:create:box').emit();
        };

        /*** private ****/
        function onStatusesChange(statuses) {
            ctrl.statuses = statuses;
        }

        function onCustomerChange(customers) {
            ctrl.customers = customers;
        }

    }

})();