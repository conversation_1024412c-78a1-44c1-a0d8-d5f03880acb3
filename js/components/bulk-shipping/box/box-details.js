(function () {

    angular.module('printty')
        .component('boxDetails', {
            controller: BoxDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/box/box-details.html',
            bindings: {
                isShown: '<',
                type: '<',
                active: '<',

                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&'
            }
        });

    function BoxDetailsCtrl($scope, utils, $mdDialog, Store, $mdSidenav, $timeout, BulkShippingApi) {
        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.box = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;
        ctrl.loadingType = 'full';

        ctrl.customers = null;
        ctrl.statuses = null;

        var boxForm, boxClone;

        var subscriptions = [];
        var sidenav = {
            open: function () {
                $mdSidenav('box-details').open();
            },
            close: function () {
                $mdSidenav('box-details').close();
            }
        };

        var loader = new utils.loadCounter(
            function (type) {
                if (!type) type = 'full';
                ctrl.loadingType = type;
            },
            function () {
                ctrl.loadingType = 'stopped';
            }
        );

        /****** methods ******/
        ctrl.$onInit = function() {

            subscriptions.push(

                Store.data('BoxCustomers').subscribe(function (data) {
                    ctrl.customers = data;
                }),
                Store.data('BoxStatusesChange').subscribe(function (data) {
                    ctrl.statuses = data;
                })

            )

        };

        ctrl.$postLink = function () {

            boxForm = $scope.boxForm;

            $mdSidenav('box-details').onClose(function () {
                $timeout(function () { ctrl.onClose(); }, 400);
            });

        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                boxClone = _.cloneDeep(ctrl.box);
                ctrl.showMode = false;
            } else {
                ctrl.box = boxClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {

            boxForm.$setSubmitted();

            if (boxForm.$invalid) return;

            if(!isNaN(ctrl.date.getTime())){
                ctrl.box.Box.shipping_date = moment(ctrl.date).format('YYYY-MM-DD');
            }
            if  ( _.isEqual(ctrl.box, boxClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            loader.start('partial');

            BulkShippingApi.updateBox(ctrl.box)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;
                })
                .finally(function () {
                    loader.stop();
                });

        };

        ctrl.save = function () {

            boxForm.$setSubmitted();

            if (boxForm.$invalid) return;

            loader.start('partial');

            BulkShippingApi.createBox(ctrl.box)
                .then(function () {
                    ctrl.onCreate();
                    ctrl.close();
                })
                .catch(function () {
                    loader.stop();
                });

        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('ユーザーを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteBox);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    loader.start();
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    loader.stop();
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd) {
                fetchBox();
            }

            sidenav.open();

        }

        function fetchBox() {

            loader.start();

            BulkShippingApi.fetchBox(ctrl.active.boxId)
                .then(function (data) {
                    ctrl.box = data;
                    if(data.Box.shipping_date == null){
                        ctrl.date = null;
                    } else {
                        ctrl.date = moment(data.Box.shipping_date).toDate();
                    }
                    loader.stop();
                })
                .catch(function () {
                    loader.stop();
                });

        }

        function updateOriginal() {

            var boxData = _.cloneDeep(ctrl.box);

            ctrl.onUpdate({
                box: boxData
            });

        }

        function deleteBox() {

            loader.start('partial');

            BulkShippingApi.deleteBox(ctrl.box.Box.id)
                .then(function () {

                    ctrl.onDelete({
                        boxId: ctrl.box.Box.id
                    });
                    ctrl.close();
                })
                .catch(function () {
                    loader.stop();
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.box = null;

            setFormPristine();

            loader.stop();
        }

        function setFormPristine() {
            if (boxForm) {
                boxForm.$setPristine();
                boxForm.$setUntouched();
            }
        }

    }

})();