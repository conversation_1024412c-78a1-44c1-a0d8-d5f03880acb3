(function () {

    angular.module('printty')
        .component('shippingBulkComponent', {
            controller: BoxesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/box/boxes.html'
        });

    function BoxesCtrl($timeout, Auth, $state, Store, utils, BulkShippingApi, Customers, $scope, $mdDialog) {

        "ngInject";

        utils.appLoaded();

        if (!Auth.isAuthorized('bulkshipping')) {
            return $state.go( Auth.defaultRoute );
        }

        /****** variables ********/
        var ctrl = this;

        ctrl.boxes = [];
        ctrl.boxStatuses = [];
        ctrl.max_capacity = 0;

        ctrl.pagination = {
            init: true,
            perPage: 40,
            status_id: null,
            customer_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false,
        };

        ctrl.boxDetails = {
            isShown: false,
            type: 'add',
            active: null
        };

        ctrl.registerBoxDetail = {
            isShown: false,
            type: 'add',
            active: null
        };

        ctrl.startDate = moment().toDate();
        ctrl.endDate = moment().toDate();

        var subscriptions = [];

        var allSelected = false;

        ctrl.infiniteBoxes = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.boxes[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                fetchBoxes(index);
            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            getBoxStatus();
            getBoxStatusChange();
            getCustomers();
            getCustomersFilter();

            subscriptions.push(
                Store.event('shipping:create:box').subscribe(function () { ctrl.viewCreate(); }),

                Store.event('sidebarActiveTypes')
                    .subscribe(function (customerId) {
                        ctrl.pagination.customer_id = customerId;
                        ctrl.reload();
                    }),

                Store.event('sidebarActiveStatus')
                    .subscribe(function (statusId) {
                        ctrl.pagination.status_id = statusId;
                        ctrl.reload();
                    }),
                Store.data('BoxStatusesChange').subscribe(function (data) {
                    ctrl.boxStatuses = data;
                })
            );

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.boxes = [];

            ctrl.infiniteBoxes.numLoaded_ = 0;
            ctrl.infiniteBoxes.toLoad_ = 0;
            ctrl.infiniteBoxes.getItemAtIndex(1);
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.pagination.searching = true;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (box) {
            ctrl.boxDetails.type = 'details';
            ctrl.boxDetails.isShown = true;
            ctrl.boxDetails.active = {
                boxId: box.Box.id
            }
        };

        ctrl.viewCreate = function () {
            ctrl.boxDetails.type = 'add';
            ctrl.boxDetails.isShown = true;
            ctrl.boxDetails.active = {
                boxId: null
            }
        };

        ctrl.boxDetails.onClose = function () {
            this.isShown = false;
        };

        ctrl.boxDetails.onCreate = function () {
            ctrl.reload();
        };

        ctrl.boxDetails.onUpdate = function (box) {
            var originalBox = _.find(ctrl.boxes, function (boxItem) {
                return boxItem.Box.id == box.Box.id;
            });

            var originalStatus = _.find(ctrl.boxStatuses, function (status) {
                return status.BoxStatus.id == box.Box.boxes_status_id;
            });

            box.Box.status_title = originalStatus.BoxStatus.title;

            _.extend(originalBox, box);
        };

        ctrl.boxDetails.onDelete = function (boxId) {
            _.remove(ctrl.boxes, function(box) {
                return box.Box.id == boxId;
            });
        };

        ctrl.registerBoxDetail.onReload = function () {
            ctrl.reload();
        };

        /****** private ******/
        function fetchBoxes(index) {

            if (ctrl.infiniteBoxes.toLoad_ >= index || ctrl.pagination.end || ctrl.pagination.busy) return;

            ctrl.pagination.busy = true;

            if (ctrl.pagination.init) {
                utils.listLoading.show(!ctrl.pagination.searching);
            } else {
                utils.moreItemsLoad.show()
            }

            ctrl.infiniteBoxes.toLoad_ += ctrl.pagination.perPage;

            BulkShippingApi.fetchBoxes(
                {
                    paging_size: ctrl.pagination.perPage,
                    paging_offset: ctrl.boxes.length,
                    keywords: ctrl.pagination.searchQuery,
                    status_id: ctrl.pagination.status_id,
                    customer_id: ctrl.pagination.customer_id,
                }
            ).then(
                function (data) {
                    ctrl.pagination.init = false;
                    ctrl.pagination.searching = false;

                    ctrl.infiniteBoxes.numLoaded_ = ctrl.infiniteBoxes.toLoad_ - ctrl.pagination.perPage + data.boxes.length;
                    ctrl.boxes = ctrl.boxes.concat(data.boxes);

                    if (allSelected) {
                        _.forEach(data.boxes, function(box) {
                            box.selected = allSelected;
                        });
                    }

                    ctrl.pagination.searchFailed = !ctrl.boxes.length;
                    ctrl.pagination.end = ctrl.boxes.length >= data.total_count;
                    ctrl.pagination.busy = false;

                    utils.listLoading.hide();
                    utils.moreItemsLoad.hide();

                }
            );

        }

        function getBoxStatus() {

            BulkShippingApi.fetchStatus().then(function (data) {
                var status = _.flatMap(data.statuses, function(status) {
                    return status.BoxStatus;
                });

                Store.data('BoxStatuses').update(status);

            });

        }

        function getBoxStatusChange() {
            BulkShippingApi.fetchStatusChange().then(function (data) {
                var status = _.flatMap(data.statuses, function(status) {
                    return status;
                });

                Store.data('BoxStatusesChange').update(status);

            });
        }

        function getCustomers() {

            Customers.fetchCustomers().then(function (data) {
                Store.data('BoxCustomers').update(data.customers);
            });

        }

        function getCustomersFilter(){
            Customers.fetchAllCustomers().then(function (data) {
                var customer = _.flatMap(data.customers, function(customer) {
                    return customer.Customer;
                });
                Store.data('BoxCustomersFilter').update(customer);
            });
        }

        ctrl.selectAll = function() {

            _.forEach(ctrl.boxes, function(box) {
                box.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

        ctrl.collectToShip = function() {
            var selectedBoxs = _.chain(ctrl.boxes)
                .filter({selected: true})
                .map('Box.id')
                .value();
            if (selectedBoxs.length === 0)
                return;
            return ctrl.ship(selectedBoxs);
        };

        ctrl.ship = function (boxIds) {
            utils.listLoading.show();

            BulkShippingApi.prepareBoxToShip({
                'boxs_ids[]': allSelected ? null : boxIds,
                'select_all': allSelected ? allSelected : null,
            }).then(function (data) {
                utils.listLoading.hide();

                var confirm = $mdDialog.confirm({
                    template: utils.shipBoxTemplate(data),
                    controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                        $scope.confirmShip = function () {
                            angular.element('.global-loading').show();
                            BulkShippingApi.decidedToShip({
                                'boxs_ids[]': allSelected ? null : boxIds,
                                'select_all': allSelected ? allSelected : null,
                            }).then(function (){
                                ctrl.reload();
                                angular.element('.global-loading').hide();
                                $mdDialog.cancel();
                            });
                        };
                        $scope.cancelShip = function () {
                            $mdDialog.hide();
                        };
                    }],

                    clickOutsideToClose: true
                });

                $mdDialog.show(confirm);
            })
            .catch(function () {
                utils.listLoading.hide();
            });

        };

        ctrl.downloadBoxCSV = function () {
            utils.globalLoading.show();

            var params = {
                customer_id: ctrl.pagination.customer_id,
                start_date : moment(ctrl.startDate).format('YYYY-MM-DD'),
                end_date : moment(ctrl.endDate).format('YYYY-MM-DD'),
            };

            BulkShippingApi.downloadCSVBox(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });
        }

    }

})();