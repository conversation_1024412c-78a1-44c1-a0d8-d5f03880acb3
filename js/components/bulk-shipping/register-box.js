(function () {

    function RegisterBoxCtrl($element, $timeout, utils, $scope, $state, Auth, BulkShippingApi, Store) {

        "ngInject";

        if (!Auth.isAuthorized('print')) {
            return $state.go( Auth.defaultRoute );
        }

        $element.addClass('new-order-wrapper');

        var ctrl = this,
            $body = angular.element('body'),
            subscription,
            component = {
                show: function () {
                    $element.addClass('shown');
                    $body.addClass('not-scrollable-x');
                },
                hide: function () {

                    $element.removeClass('shown').addClass('hiding');

                    $timeout(function () {
                        $element.removeClass('hiding');
                        $body.removeClass('not-scrollable-x');

                    }, 300);

                },
            };

        ctrl.box = null;
        ctrl.boxId = null;

        ctrl.$onDestroy = function () {
            subscription.unsubscribe();
        };

        ctrl.open = function (box) {
            utils.globalLoading.show();

            ctrl.boxId = box.Box.id;

            fetchBoxInfo();
            component.show();
        };
        ctrl.close = function() {
            component.hide();
            ctrl.box = null;
            Store.data('BoxInfo').update(ctrl.box);
            ctrl.state.repairText = null;
            ctrl.state.errorText = null;
            ctrl.onReload();
        };

        ctrl.closeAndUpdateStatus = function() {
            utils.globalLoading.show();
            BulkShippingApi.updateStatus(ctrl.box.Box).then(function () {
                utils.globalLoading.hide();
                component.hide();
                ctrl.box = null;
                Store.data('BoxInfo').update(ctrl.box);
                ctrl.state.repairText = null;
                ctrl.state.errorText = null;
                ctrl.onReload();
            });
        };

        utils.appLoaded();

        function fetchBoxInfo() {
            BulkShippingApi.fetchBoxInfo(ctrl.boxId)
                .then(function (data) {
                    ctrl.box = data;
                    ctrl.state.numbers = data.orders_number;
                    ctrl.state.enclosed = data.Box.total_enclosed;
                    Store.data('BoxInfo').update(ctrl.box);
                    utils.globalLoading.hide();
                });
        }

        ctrl.state = {

            name: 'scan',
            history: [],
            repairText: null,
            numbers: null,
            enclosed: null,
            errorText: null,

            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };
    }

    angular.module('printty')

        .component('registerBox', {
            bindings: {
                open: '=openOn',
                onReload: '&',
            },
            controller: RegisterBoxCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/box/register-box.html'
        });


    /******************
     *******************
     * ******* LEFT ******
     ****************/


    function RegisterBoxLeftCtrl(Store, $scope, $mdDialog, utils, BulkShippingApi) {

        "ngInject";

        var ctrl = this;
        var subscriptions = [];

        ctrl.box = null;

        ctrl.$onInit = function () {
            ctrl.state = ctrl.registerCtrl.state;
        };

        subscriptions.push(
            Store.data('BoxInfo').subscribe(function (data) {
                ctrl.box = data;
            })
        );

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.sortNumber = function () {
            ctrl.state.numbers.reverse()
        };

        ctrl.deleteOrderNumber = function (order) {
            var confirm = $mdDialog.confirm({
                template: utils.deleteNumberTemplate(order),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.confirmDelete = function () {
                        utils.globalLoading.show();
                        BulkShippingApi.deleteOrderNumber({
                            'order_number': order,
                        }).then(function (){
                            utils.globalLoading.hide();
                            _.forEach(ctrl.state.numbers, function (number, key) {
                                if(typeof number != "undefined" && order === number.value){
                                    var indexDel = ctrl.state.numbers[key].value.indexOf(order);
                                    ctrl.state.numbers.splice(indexDel, 1);
                                    ctrl.state.enclosed = ctrl.state.enclosed - number.item_enclosed;
                                }
                            });
                            $mdDialog.cancel();
                        }, function (error) {
                            utils.globalLoading.hide();
                            $mdDialog.cancel();
                        });
                    };
                    $scope.cancelDelete = function () {
                        $mdDialog.hide();
                    };
                }],

                clickOutsideToClose: true
            });

            $mdDialog.show(confirm);
        }
    }

    angular.module('printty')

        .component('registerBoxLeft', {
            require: {
                registerCtrl: '^^registerBox'
            },
            controller: RegisterBoxLeftCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/box/register-box-left.html'
        });


    /******************
     *******************
     * ******* RIGHT ******
     ****************/


    function RegisterBoxRightCtrl($element, BulkShippingApi, Store, electron, $timeout, utils, $mdDialog) {

        "ngInject";

        var ctrl = this;

        ctrl.scannerInput = null;
        ctrl.qrCodeInput = null;

        ctrl.isPrintStep = true;
        ctrl.box = null;
        var subscriptions = [];


        subscriptions.push(
            Store.data('BoxInfo').subscribe(function (data) {
                ctrl.box = data;
            })
        );

        ctrl.$onInit = function () {
            ctrl.state = ctrl.registerCtrl.state;

            ctrl.focusScanField();

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                    $element.removeClass('single multiple');
                }

                if (ctrl.state.is('scan')) {
                    ctrl.focusScanField();
                }
            });

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.findQr = function () {
            if (!ctrl.qrCodeInput) return;

            var params = {
                code: ctrl.qrCodeInput,
                box_id: ctrl.box.Box.id,
            };

            searchTask(params);
        };

        ctrl.focusScanField = function () {
            $timeout(function () {
                $('#scanner-field').focus();
            });
        };

        ctrl.findQrScan = function () {
            utils.delay(function () {
                if (!ctrl.scannerInput) return;

                var params = {
                    code: ctrl.scannerInput,
                    box_id: ctrl.box.Box.id,
                };
                searchTask(params);
            }, 250);
        };

        ctrl.clearInput = function () {
            ctrl.qrCodeInput = null;
            ctrl.state.errorText = null;

            ctrl.wait = null;
            ctrl.state.repairText = null;
        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.scannerInput = null;
            ctrl.isPrintStep = true;
            ctrl.wait = null;
            ctrl.state.errorText = null;
            ctrl.state.repairText = null;
        }

        function searchTask(input) {
            ctrl.clearInput();
            ctrl.wait = 'お待ちください。';
            BulkShippingApi.isMultipleItems(input).then(function (data) {
                if(!data.isMultiple){
                    input.item_id = data.items[0].OrderItem.id;
                    registerBox(input);
                } else {
                    var confirm = $mdDialog.confirm({
                        template: utils.showItemsTemplete(data),
                        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                            $scope.sellectItem = function () {
                                input.item_id = $('.item-id-radio:checked').val();
                                ctrl.scannerInput = null;
                                $mdDialog.cancel();
                                registerBox(input);
                            };
                            $scope.cancelSellectItem = function () {
                                ctrl.scannerInput = null;
                                $mdDialog.hide();
                            };
                        }],
                        clickOutsideToClose: true
                    });
                    $mdDialog.show(confirm);
                }
            });
        }

        function registerBox(input) {
            BulkShippingApi.registerBoxByCode(input).then(function (data) {

                ctrl.wait = '';
                ctrl.scannerInput = null;
                ctrl.state.repairText = data.message;
                function isOrderNumber(num){
                    return num.value === data.order_number.value
                }
                if(typeof ctrl.state.numbers.find(isOrderNumber) != "undefined"){
                    _.forEach(ctrl.state.numbers, function (number, key) {
                        if(typeof number !== "undefined" && data.order_number.value === number.value) {
                            ctrl.state.numbers[key].item_enclosed++;
                        }
                    });
                } else {
                    ctrl.state.numbers.unshift(data.order_number);
                }
                ctrl.state.enclosed++;

            }, function (error) {

                ctrl.wait = '';
                ctrl.state.repairText = null;

                if (error.status === 400 && angular.isDefined(error.data.message)) {
                    ctrl.state.errorText = error.data.message;
                }

                ctrl.scannerInput = null;

            });
        }

    }

    angular.module('printty')

        .component('registerBoxRight', {
            require: {
                registerCtrl: '^^registerBox'
            },
            controller: RegisterBoxRightCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/bulk-shipping/box/register-box-right.html'
        });

})();
