(function () {
     function BusinessCtrl(Auth, $state, Store, utils, $mdDialog, BusinessApi) {

        "ngInject";

        utils.appLoaded();

         var ctrl = this;

         ctrl.isShown = false;
         ctrl.unplanned = [];
         ctrl.unplannedTypes = [];
         ctrl.unplannedFilter = [];
         ctrl.loadingType = 'full';
         ctrl.activeType = null;
         ctrl.activeSort = null;
         ctrl.emailClient = null;
         ctrl.activeCustomer = null;
         ctrl.activeColor = null;
         ctrl.activeSize = null;
         ctrl.activeProductCode = null;
         ctrl.activeDate = null;
         ctrl.activeFilterPrinter = null;
         ctrl.chooseCustomer = [];
         ctrl.chooseColor = [];
         ctrl.chooseSize = [];
         ctrl.chooseProductCode = [];
         ctrl.chooseDate = [];
         ctrl.allSelected = false;
         ctrl.plannedSorts = [
             {'key': '', 'value' : '品番'},
             {'key': 'ProductJoin.title', 'value' : '商品名'},
             {'key': 'ProductSizeJoin.title', 'value' : 'サイズ'},
             {'key': 'ProductColorJoin.title', 'value' : 'カラー'},
             {'key': 'Order.production_date_preferred', 'value' : '納期日'},
             {'key': 'CustomerJoin.title', 'value' : '会社名'},
         ];

         ctrl.autodone = true;

         ctrl.printers = [];
         ctrl.recommendations = [];

         ctrl.selectedItems = [];
         ctrl.selectedItemObjs = [];

         ctrl.searchQuery = null;

         ctrl.paginationItems = {
             current: 0,
             perPage: 50,

             empty: false,
             disabled: true
         };

         ctrl.paginationPrinters = {
             current: 0,
             perPage: 50,

             empty: false,
             disabled: true
         };

         ctrl.date = moment().toDate();

         var $body = angular.element('body');
         var lastSearch = null;

         var loader = new utils.loadCounter(
             function (type) {
                 if (!type) type = 'full';
                 ctrl.loadingType = type;
             },
             function () {
                 ctrl.loadingType = 'stopped';
             },
             3
         );

         ctrl.$onInit = function () {
             loader.start();
             setTimeout(function(){
                 getSaleOrder();
                 fetchPrinters();
                 fetchUnplannedTypes();
                 fetchUnplannedFilter();
             }, 100)
         };

         ctrl.setActiveSort = function (sort) {
             ctrl.activeSort = sort;
             ctrl.reload();
         };
         ctrl.setEmailClient = function () {
             ctrl.emailClient = ctrl.emailClient ? null : true;
             ctrl.reload();
         };
         ctrl.setActiveSize = function (size) {
             ctrl.activeSize = size;
             ctrl.reload();
         };
         ctrl.setActiveCustomer = function (customer) {
             ctrl.activeCustomer = customer;
             ctrl.reload();
         };
         ctrl.setActiveDate = function (date) {
             ctrl.activeDate = date;
             ctrl.reload();
         };
         ctrl.setActivePCode = function (code) {
             ctrl.activePCode = code;
             ctrl.reload();
         };
         ctrl.setFilterPrinter = function (type) {
             ctrl.activeFilterPrinter = type;
             ctrl.reloadPrinters()
         };


         ctrl.setActiveType = function (type) {
             ctrl.activeType = type;
             ctrl.reload();
         };

         ctrl.setActiveFilter = function (type) {
             ctrl.activeFilter = type;
             ctrl.reload();
         };

        ctrl.reload = function () {
            ctrl.autodone = true;
            ctrl.selectedItemObjs = [];
            ctrl.selectedItems = [];
            loader.set(2);
            ctrl.reloadUnplanned();
            ctrl.reloadPrinters();
        };

        ctrl.reloadUnplanned = function () {
            ctrl.paginationItems.current = 0;
            ctrl.unplanned = [];
            ctrl.allSelected = false;
            loader.start('partial');
            getSaleOrder();
        };

        ctrl.reloadPrinters = function (isSkipReload) {
            ctrl.paginationPrinters.current = 0;
            ctrl.printers = [];

            if (!isSkipReload) {
                loader.start('partial');
            }

            fetchPrinters();
            fetchRecommendations();
        };

         ctrl.onDateChange = function () {
             ctrl.reloadPrinters();
         };

         ctrl.onItemSelect = function () {
             ctrl.selectedItems = getSelected();
             ctrl.selectedItemObjs = getSelectedObj();
             ctrl.reloadPrinters(true)
         };
        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 1000 );
        };

        ctrl.search = function () {
            if (ctrl.lastSearch === ctrl.searchQuery) return;

            ctrl.lastSearch = ctrl.searchQuery;
            ctrl.searching = true;
            ctrl.reload();
        };

        ctrl.clearSearch = function () {
            ctrl.searchQuery = '';
            ctrl.search();
        };

         ctrl.processPrinter = function (printer) {

             loader.start('partial');
             utils.globalLoading.show();

             BusinessApi.processToPrinter({
                 date: moment(ctrl.date).format('YYYY-MM-DD'),
                 printerId: printer.Printer.id,
                 items: ctrl.selectedItems,
                 type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null})
                 .then(function () {
                     Store.event('CreatePlan:Update').emit();
                     ctrl.reload();
                     utils.globalLoading.hide();
                 })
                 .catch(function () {
                     loader.stop();
                     utils.globalLoading.hide();
                 });

         };

         ctrl.processRecommendations = function () {

             loader.start('partial');

             BusinessApi.processToRecommendations({
                 date: moment(ctrl.date).format('YYYY-MM-DD'),
                 items: ctrl.selectedItems,
                 type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null
             })
                 .then(function () {
                     Store.event('CreatePlan:Update').emit();
                     ctrl.reload();
                 })
                 .catch(function () {
                     loader.stop();
                 });

         };

         // ctrl.close = function () {
         //     component.hide();
         //     ctrl.onClose();
         // };

         ctrl.getPercent = function (printer) {
             var num = printer.workload / printer.capacity;
             return num > 1 ? 100 : num * 100;
         };

         ctrl.toggleAll = function () {

             _.forEach(ctrl.unplanned, function (item) {
                 item.selected = ctrl.allSelected;
             });

             ctrl.onItemSelect();

         };

        ctrl.$onDestroy = function () {
        };

        ctrl.loadMoreItems = function () {
            getSaleOrder();
        };

         ctrl.loadMorePrinters = function () {
             fetchPrinters();
         };

        ctrl.importCSV = function () {
            var date_current = moment(ctrl.date).format('YYYY-MM-DD');

            utils.globalLoading.show();

            BusinessApi.createOrderCustomer(date_current).then(function(){
                utils.globalLoading.hide();
                ctrl.reload();
            },function () {
                utils.globalLoading.hide();
            });
        };

        function  getSaleOrder() {

            ctrl.paginationItems.disabled = true;

            BusinessApi.fetchOrderCustomer({
                paging_size: ctrl.paginationItems.perPage,
                paging_offset: ctrl.paginationItems.current * ctrl.paginationItems.perPage,
                type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null,
                keywords: ctrl.searchQuery,
                sort_name: ctrl.activeSort ? ctrl.activeSort : null,
                email_client: ctrl.emailClient,
                size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
                customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
                date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
                pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
                color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
            })
                .then(function (data) {
                    ctrl.paginationItems.current++;
                    ctrl.unplanned = ctrl.unplanned.concat(data.unplanned);
                    ctrl.paginationItems.empty = !ctrl.unplanned.length;

                    ctrl.paginationItems.disabled = ctrl.unplanned.length >= data.total_count;

                    loader.stop();
                })
                .catch(function () {
                        loader.stop();
                 });

        }

         function fetchPrinters() {

             ctrl.paginationPrinters.disabled = true;

             BusinessApi.fetchPrinters({
                 paging_size: ctrl.paginationPrinters.perPage,
                 paging_offset: ctrl.paginationPrinters.current * ctrl.paginationPrinters.perPage,
                 date: moment(ctrl.date).format('YYYY-MM-DD'),
                 type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null,
                 type: ctrl.activeFilterPrinter ? ctrl.activeFilterPrinter : null,
                 keywords: ctrl.searchQuery,
                 'order_item_ids[]': ctrl.selectedItems
             })
                 .then(function (data) {

                     ctrl.paginationPrinters.current++;

                     ctrl.printers = ctrl.printers.concat(data.printers);

                     ctrl.paginationPrinters.empty = !ctrl.printers.length;

                     ctrl.paginationPrinters.disabled = ctrl.printers.length >= data.total_count;

                     if(ctrl.autodone === true) {
                         loader.stop();
                     }
                 })
                 .catch(function () {
                     if(ctrl.autodone === true) {
                         loader.stop();
                     }
                 });

         }

         function fetchRecommendations() {

             ctrl.recommendations = [];

             BusinessApi.fetchRecommendations({
                 date: moment(ctrl.date).format('YYYY-MM-DD'),
                 'order_item_ids[]': ctrl.selectedItems,
                 type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null
             })
                 .then(function (data) {
                     ctrl.recommendations = data.recommendations;
                 });

         }

         function fetchUnplannedTypes() {

             BusinessApi.fetchUnplannedTypes()
                 .then(function (data) {
                     ctrl.unplannedTypes = data.types;

                     ctrl.activeType = _.find(ctrl.unplannedTypes, function (type) {
                         return type.ProductionType.code === 'all';
                     });

                     loader.stop();
                 })
                 .catch(function () {
                     loader.stop();
                 });
         }

         function fetchUnplannedFilter(){
             BusinessApi.fetchUnplannedFilter()
                 .then(function (data) {
                     ctrl.unplannedFilter = data.filters;
                     ctrl.activeSize = _.find(ctrl.unplannedFilter.Size, function (size) {
                         return size.Size.code === 'all';
                     });
                     ctrl.activeProductCode = _.find(ctrl.unplannedFilter.ProductCode, function (pcode) {
                         return pcode.ProductCode.code === 'all';
                     });
                     ctrl.activeDate = _.find(ctrl.unplannedFilter.Date, function (date) {
                         return date.Date.code === 'all';
                     });
                     ctrl.activeCustomer = _.find(ctrl.unplannedFilter.Customer, function (customer) {
                         return customer.Customer.code === 'all';
                     });
                     ctrl.activeColor = _.find(ctrl.unplannedFilter.Color, function (color) {
                         return color.Color.code === 'all';
                     });

                     loader.stop();
                 })
                 .catch(function () {
                     loader.stop();
                 });
         }

         function getSelected() {

             var selectedItems = [];

             _.forEach(ctrl.unplanned, function (item) {

                 if (item.selected) {
                     selectedItems.push(item.OrderItem.id);
                 }

             });

             return selectedItems;
         }

         function getSelectedObj() {

             var selectedItems = [];

             _.forEach(ctrl.unplanned, function (item) {

                 if (item.selected) {
                     selectedItems.push(item);
                 }

             });

             return selectedItems;

         }

         ctrl.reloadFilter = function(ele ,title, name){
             if($.inArray(title , ['カラー','会社名','品番','サイズ','納期日']) === -1) {
                 var element = "ctrl.choose" + name;
                 if (ele.selected && ($.inArray(title , eval(element)) === -1)) {
                     eval(element).push(title);
                 } else {
                     for (var i = 0; i < eval(element).length; i++) {
                         if (eval(element)[i] === title) {
                             eval(element).splice(i, 1);
                         }
                     }
                 }
             } else {
                 var unplanned = "ctrl.unplannedFilter." + name;
                 var arr = "ctrl.choose" + name;
                 defaultArray(arr);
                 _.forEach(eval(unplanned), function (item) {
                     if (ele.selected) {
                         item.selected = true;
                         var itemTitle = "item." + name + ".title";
                         if($.inArray(eval(itemTitle) , ['カラー','会社名','品番','サイズ','納期日']) === -1) {
                             eval(arr).push(eval(itemTitle));
                         }
                     } else {
                         item.selected = false;
                     }
                 });
             }
             ctrl.reload();
         }

         function defaultArray(arr){
             while(eval(arr).length > 0 ){
                 eval(arr).pop();
             }
         }

         ctrl.autoPlan = function(){
             if(ctrl.selectedItemObjs.length === 0) {
                 return;
             } else {

                 loader.start('partial');
                 utils.globalLoading.show();

                 ctrl.autodone = false;

                 if(ctrl.allSelected){
                     var filterData = {
                         type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null,
                         keywords: ctrl.searchQuery,
                         sort_name: ctrl.activeSort ? ctrl.activeSort : null,
                         size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
                         customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
                         date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
                         pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
                         color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
                         email_client: ctrl.emailClient,
                     }
                 }

                 BusinessApi.autoPlanning({
                     date: moment(ctrl.date).format('YYYY-MM-DD'),
                     items: ctrl.selectedItemObjs,
                     filterData: filterData
                 })
                     .then(function (data) {
                         utils.globalLoading.hide();
                         if(data.error){
                             var message = data.error_message + '<br/>';
                             var i = 1;

                             $.each(data.message, function(code, data) {
                                 var printer = data.printer.join('<br/>');

                                 message += '<br/>' + i + '.' + data.customer + ' - ' + data.code + ' - ' + data.color + '<br/>' + printer + '<br/>';
                                 i++;
                             });

                             message += '<br/>設定を確認してください。';

                             var confirm = $mdDialog.confirm({
                                 template: utils.infoTemplate(message),
                                 controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                                     $scope.cancel = function () {
                                         $mdDialog.cancel();
                                     };
                                 }]
                             }).targetEvent();

                             $mdDialog.show(confirm);

                         } else {
                             Store.event('CreatePlan:Update').emit();
                         }
                         ctrl.reload();
                     })
                     .catch(function () {
                         ctrl.reload();
                     });
             }
         }

         ctrl.autoPlanKornit = function(){
             if(ctrl.selectedItemObjs.length === 0) {
                 return;
             } else {

                 loader.start('partial');
                 utils.globalLoading.show();

                 ctrl.autodone = false;

                 if(ctrl.allSelected){
                     var filterData = {
                         type_code: ctrl.activeType ? ctrl.activeType.ProductionType.code : null,
                         keywords: ctrl.searchQuery,
                         sort_name: ctrl.activeSort ? ctrl.activeSort : null,
                         size: ctrl.chooseSize.length > 0 ? ctrl.chooseSize.join(',') : null,
                         customer: ctrl.chooseCustomer.length > 0 ? ctrl.chooseCustomer.join(',') : null,
                         date: ctrl.chooseDate.length > 0 ? ctrl.chooseDate.join(',') : null,
                         pcode: ctrl.chooseProductCode.length > 0 ? ctrl.chooseProductCode.join(',') : null,
                         color: ctrl.chooseColor.length > 0 ? ctrl.chooseColor.join(',') : null,
                         email_client: ctrl.emailClient,
                     }
                 }

                 BusinessApi.autoPlanning({
                     date: moment(ctrl.date).format('YYYY-MM-DD'),
                     items: ctrl.selectedItemObjs,
                     filterData: filterData,
                     kornit: true
                 })
                     .then(function (data) {
                         utils.globalLoading.hide();
                         if(data.error){
                             var message = data.error_message + '<br/>';

                             var confirm = $mdDialog.confirm({
                                 template: utils.infoTemplate(message),
                                 controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                                     $scope.cancel = function () {
                                         $mdDialog.cancel();
                                     };
                                 }]
                             }).targetEvent();

                             $mdDialog.show(confirm);

                         } else {
                             Store.event('CreatePlan:Update').emit();
                         }
                         ctrl.reload();
                     })
                     .catch(function () {
                         ctrl.reload();
                     });
             }
         }

    }
    angular.module('printty')
        .component('businessComponent', {
            controller: BusinessCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/business/business.html'
        });

})();