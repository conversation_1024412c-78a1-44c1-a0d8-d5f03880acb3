(function () {

  function DeliveryCtrl(utils, Delivery) {

    'ngInject';

    var vm = this;
    vm.date = moment().toDate();
    vm.placeData = [];
    vm.factoryFilter = [];
    vm.memo = '';
    vm.tasks = [];
    vm.giftset = 0;
    vm.factoryId = null;

    utils.appLoaded();

    vm.state = {
      name: 'scan',
      history: [],

      is: function () {
        if (!arguments.length) {
          return;
        }

        if (arguments.length === 1) {
          return arguments[0] === this.name;
        }

        if (arguments.length > 1) {
          for (var i = 0; i < arguments.length; i++) {
            if (arguments[i] === this.name) {
              return true;
            }
          }
          return false
        }
      },

      back: function () {

        var last = this.history[this.history.length - 1];

        this.set(last, true);

        if (last === 'scan') {
          this.history = [];
        } else {
          this.history.pop();
        }

      },

      set: function (name, forget) {
        if (!forget && this.name !== name) {
          this.history.push(this.name);
        }

        this.name = name;

        // emit event
        this._onChangeCallbacks.forEach(function(callback) {
          callback();
        });
      },

      _onChangeCallbacks: [],

      onChange: function(callback) {
        if (angular.isFunction(callback)) {
          this._onChangeCallbacks.push(callback);
        }
      }
    };

    vm.$onInit = function(){
        getFactoryData();
    };

      vm.onDateChange = function(){
          var date = moment(vm.date).format('YYYY-MM-DD');
          getPlaceData(date,vm.factoryId);
      };

      vm.onFactoryChange = function(factoryId){
          var date = moment(vm.date).format('YYYY-MM-DD');
          vm.factoryId = factoryId;
          getPlaceData(date,vm.factoryId);
      };

      vm.updatePlaceData = function (date,factoryId) {
          var params = {
              date: date,
              factory_id: factoryId,
          };
          Delivery.getPlaceData(params).then( function(data) {
              vm.placeData = data;
          });
      };

      function getPlaceData(date, factoryId) {
          var params = {
              date: date,
              factory_id: factoryId,
          };
          Delivery.getPlaceData(params).then( function(data) {
              vm.placeData = data;
          });
      };

      function getFactoryData() {
          var date = moment(vm.date).format('YYYY-MM-DD');
          Delivery.getFactories().then( function(data) {
              vm.factoryFilter = data.factories;
              vm.factoryId = data.factorySelected;
              getPlaceData(date,vm.factoryId);
          });
      };
  }

  angular.module('printty')

    .component('deliveryComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: DeliveryCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/delivery/delivery.html'
    });


  /******************
   *******************
   * ******* LEFT ******
   ****************/


  function DeliveryLeftCtrl() {
    'ngInject';

    var ctrl = this;
    ctrl.placeData = [];
      ctrl.factoryFilter = [];

    ctrl.$onInit = function () {
      ctrl.state = ctrl.printCtrl.state;
      ctrl.placeData = ctrl.printCtrl.placeData;
      ctrl.factoryFilter = ctrl.printCtrl.factoryFilter;
    };


      // getRandomData(date);
  }

  angular.module('printty')

    .component('deliveryLeft', {
      require: {
        printCtrl: '^^deliveryComponent'
      },
      controller: DeliveryLeftCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/delivery/delivery-left.html'
    });


  /******************
   *******************
   * ******* RIGHT ******
   ****************/


  function DeliveryRightCtrl($element, Delivery, Store, electron, $timeout, utils, $mdDialog) {

    'ngInject';

    var ctrl = this;

    ctrl.isElectron = !!electron;

    ctrl.scannerInput = null;
    ctrl.qrCodeInput = null;
    ctrl.factoryFilter = [];

    ctrl.$onInit = function () {

      ctrl.state = ctrl.printCtrl.state;
      ctrl.factoryFilter = ctrl.printCtrl.factoryFilter;
      ctrl.focusScanField();

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
        }

        if (ctrl.state.is('scan')) {
          ctrl.focusScanField();
        }
      });
    };

    ctrl.findQr = function () {
      if (!ctrl.qrCodeInput) {
        return;
      }
      process(ctrl.qrCodeInput);
    };

    ctrl.focusScanField = function () {
      $timeout(function () {
        $('#scanner-field').focus();
      });
    };

    ctrl.findQrScan = function () {
      utils.delay(function () {
        if (!ctrl.scannerInput) {
          return;
        }
        process(ctrl.scannerInput);
      }, 250);
    };

    ctrl.clearInput = function () {
      ctrl.qrCodeInput = null;
      ctrl.errorText = null;
    };

    ctrl.end = function() {
      ctrl.clearInput();
      ctrl.scannerInput = null;
      ctrl.state.set('scan', true);
    }

    /******** helpers ***********/
    function setDefault() {
      ctrl.scannerInput = null;
      ctrl.errorText = null;
    }

    function process(code) {
      ctrl.printCtrl.memo = '';
      ctrl.printCtrl.tasks = [];
      ctrl.printCtrl.giftset = 0;
      ctrl.is_qrCode = 0;

      if(code.indexOf('PRINTTY') !== -1){
          ctrl.is_qrCode = 1;
      }
      Delivery.process({
          code: code,
          factory_id: ctrl.printCtrl.factoryFilter.id,
          is_qrCode: ctrl.is_qrCode,
      })
        .then(
          function(data) {

            ctrl.printCtrl.memo = data.memo;
            ctrl.printCtrl.giftset = data.giftset;

              ctrl.state.set('finish', true);

              utils.delay(function() {
                  ctrl.end();
                  ctrl.focusScanField();
              }, 2000 );

            var date = moment(ctrl.printCtrl.date).format('YYYY-MM-DD');
            ctrl.printCtrl.updatePlaceData(date, ctrl.printCtrl.factoryFilter.id);
          }
        )
        .catch(
          function(error) {
            ctrl.state.set('fail', true);
            if(error.data.message[1] === 1) {
                var message = error.data.message[0];
                if(error.data.message[4]) {
                    var message = '検品がまだ完了していません';
                }
                $mdDialog.show(
                    $mdDialog.alert()
                        .parent(angular.element(document.querySelector('#popupContainer')))
                        .clickOutsideToClose(true)
                        .textContent(message)
                        .ok('閉じる')
                );
            }
            if(error.data.message[1] === 1 || error.data.message[1] === 0) {
                ctrl.printCtrl.memo = error.data.message[2];
                ctrl.printCtrl.giftset = error.data.message[3];
                ctrl.printCtrl.tasks = error.data.message[5];
            }
          }
        );
    }

  }

  angular.module('printty')

    .component('deliveryRight', {
      require: {
        printCtrl: '^^deliveryComponent'
      },
      controller: DeliveryRightCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/delivery/delivery-right.html'
    });

})();