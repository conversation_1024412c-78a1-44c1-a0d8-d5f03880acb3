(function () {

    angular.module('printty')
        .component('factoryDetails', {
            controller: FactoryDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/factory-order/factory-details.html',
            bindings: {
                isShown: '<',
                type: '<',
                active: '<',

                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&'
            }
        });

    function FactoryDetailsCtrl($scope, utils, $mdDialog, Store, $mdSidenav, $timeout, FactoryOrderApi) {
        "ngInject";

        /****** variables ******/

        var ctrl = this;

        ctrl.vendor = null;

        ctrl.isAdd = true;
        ctrl.showMode = true;
        ctrl.loadingType = 'full';

        ctrl.productCodes = [];
        ctrl.productColors = [];
        ctrl.productSizes = [];
        ctrl.statuses = null;
        ctrl.productId = null;
        ctrl.colorId = null;
        ctrl.sizeId = null;
        ctrl.makerId = null;
        ctrl.makers = [];
        ctrl.factoryType = null;

        var factoryDetailForm, vendorClone;

        var subscriptions = [];
        var sidenav = {
            open: function () {
                ctrl.makers.id = ''
                ctrl.productCodes.product_id = ''
                ctrl.checkMaker  = false;
                ctrl.checkProduct = false;
                ctrl.checkColor = false;
                ctrl.checkSize = false;
                ctrl.checkQuantity = false;
                $mdSidenav('factory-details').open();
            },
            close: function () {
                $mdSidenav('factory-details').close();
            }
        };

        var loader = new utils.loadCounter(
            function (type) {
                if (!type) type = 'full';
                ctrl.loadingType = type;
            },
            function () {
                ctrl.loadingType = 'stopped';
            }
        );

        /****** methods ******/
        ctrl.$onInit = function() {

            subscriptions.push(

                Store.data('MakerCustomers').subscribe(function (data) {
                    ctrl.makers = data;
                }),
                Store.data('ProductColors').subscribe(function (data) {
                    ctrl.productColors = data;
                }),
                Store.data('FactoryType').subscribe(function (data) {
                    ctrl.factoryType = data;
                })
            )

        };

        ctrl.$postLink = function () {

            factoryDetailForm = $scope.factoryDetailForm;

            $mdSidenav('factory-details').onClose(function () {
                $timeout(function () { ctrl.onClose(); }, 400);
            });

        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.edit = function () {

            if (ctrl.showMode) {
                vendorClone = _.cloneDeep(ctrl.vendor);
                ctrl.showMode = false;
            } else {
                ctrl.vendor = vendorClone;
                setFormPristine();
                ctrl.showMode = true;
            }

        };

        ctrl.update = function () {
            ctrl.checkQuantity = false;
            ctrl.checkMinQuantity = false;
            factoryDetailForm.$setSubmitted();

            if (factoryDetailForm.$invalid) return;

            if  ( _.isEqual(ctrl.vendor, vendorClone) ) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }

            if(typeof factoryDetailForm.quantity.$viewValue == "undefined" || factoryDetailForm.quantity.$viewValue == '' || factoryDetailForm.quantity.$viewValue == null || isNaN(factoryDetailForm.quantity.$viewValue) ) {
                ctrl.checkQuantity = true;
            }else if(factoryDetailForm.quantity.$viewValue < 0){
                ctrl.checkMinQuantity = true;
            }

            if(ctrl.checkQuantity || ctrl.checkMinQuantity){
                return;
            }

            loader.start('partial');

            FactoryOrderApi.updateVendor(ctrl.vendor)
                .then(function () {
                    updateOriginal();

                    setFormPristine();
                    ctrl.showMode = true;
                })
                .finally(function () {
                    loader.stop();
                });

        };

        ctrl.save = function () {
            ctrl.checkMaker  = false;
            ctrl.checkProduct = false;
            ctrl.checkColor = false;
            ctrl.checkSize = false;
            ctrl.checkQuantity = false;
            ctrl.checkMinQuantity = false;

            factoryDetailForm.$setSubmitted();
            if(typeof factoryDetailForm.maker_id.$viewValue == "undefined" || factoryDetailForm.maker_id.$viewValue == '' || factoryDetailForm.maker_id.$viewValue == null){
                ctrl.checkMaker = true;
            }

            if(typeof factoryDetailForm.product_id.$viewValue == "undefined" || factoryDetailForm.product_id.$viewValue == '' || factoryDetailForm.product_id.$viewValue == null){
                ctrl.checkProduct = true;
            }

            if(typeof factoryDetailForm.color_id.$viewValue == "undefined" || factoryDetailForm.color_id.$viewValue == '' || factoryDetailForm.color_id.$viewValue == null){
                ctrl.checkColor = true;
            }

            if(typeof factoryDetailForm.size_id.$viewValue == "undefined" || factoryDetailForm.size_id.$viewValue == '' || factoryDetailForm.size_id.$viewValue == null){
                ctrl.checkSize = true;
            }

            if(typeof factoryDetailForm.quantity.$viewValue == "undefined" || factoryDetailForm.quantity.$viewValue == '' || factoryDetailForm.quantity.$viewValue == null || isNaN(factoryDetailForm.quantity.$viewValue) ) {
                ctrl.checkQuantity = true;
            }else if(factoryDetailForm.quantity.$viewValue < 0){
                ctrl.checkMinQuantity = true;
            }

            if(!ctrl.checkMaker && !ctrl.checkProduct && !ctrl.checkColor && !ctrl.checkSize && !ctrl.checkQuantity && !ctrl.checkMinQuantity){
                loader.start('partial');
                var data = {
                    'maker_id': factoryDetailForm.maker_id.$viewValue,
                    'product_id': factoryDetailForm.product_id.$viewValue,
                    'color_id': factoryDetailForm.color_id.$viewValue,
                    'size_id': factoryDetailForm.size_id.$viewValue,
                    'quantity':factoryDetailForm.quantity.$viewValue,
                    'factory_type': ctrl.factoryType,
                };

                FactoryOrderApi.createVendor(data)
                    .then(function () {
                        ctrl.onCreate();
                        ctrl.close();
                    })
                    .catch(function () {
                        loader.stop();
                    });
            }
        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('データを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteVendor);

        };

        ctrl.selectProduct = function (maker) {
            ctrl.makerId = maker;
            FactoryOrderApi.fetchProductCodes({
                maker_id: maker
            }).then(function(data){
                ctrl.productCodes = data.productCodes;
            });
        };

        ctrl.selectProductColor = function (product) {
            ctrl.productId = product;
            FactoryOrderApi.fetchProductColors({
                product_id: product,
                maker_id: ctrl.makerId
            }).then(function(data){
                ctrl.productColors = data.productColors;
            });

            FactoryOrderApi.fetchProductSizes({
                product_id: ctrl.productId,
                maker_id: ctrl.makerId,
            }).then(function(data){
                ctrl.productSizes = data.productSizes;
            });
        };

        ctrl.updateColorId = function (colorId) {
            ctrl.colorId = colorId;

            if(ctrl.colorId && ctrl.sizeId){
                getOrderBeforeQuantity()
            }
        };

        ctrl.updateSizeId = function (sizeId) {
            ctrl.sizeId = sizeId;

            if(ctrl.colorId && ctrl.sizeId){
                getOrderBeforeQuantity()
            }
        };

        function getOrderBeforeQuantity(){
            FactoryOrderApi.fetchOrderBefore({
                factory_type: ctrl.factoryType,
                maker_id: ctrl.makerId,
                product_id: ctrl.productId,
                color_id: ctrl.colorId,
                size_id: ctrl.sizeId
            }).then(function(data){
                ctrl.vendor.VendorOrderItem.order_before_quantity = data.quantity;
            });
        }

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    loader.start();
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    loader.stop();
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd) {
                fetchVendor();
            }

            sidenav.open();

        }

        function fetchVendor() {

            loader.start();

            FactoryOrderApi.fetchVendor(ctrl.active.order_item_id)
                .then(function (data) {
                    ctrl.vendor = data;
                    loader.stop();
                })
                .catch(function () {
                    loader.stop();
                });

        }

        function updateOriginal() {

            var vendorData = _.cloneDeep(ctrl.vendor);

            ctrl.onUpdate({
                vendor: vendorData
            });

        }

        function deleteVendor() {

            loader.start('partial');

            FactoryOrderApi.deleteVendor(ctrl.vendor.VendorOrderItem.id)
                .then(function () {

                    ctrl.onDelete({
                        vendorId: ctrl.vendor.VendorOrderItem.id
                    });
                    ctrl.close();
                })
                .catch(function () {
                    loader.stop();
                });

        }

        function setDefault() {
            ctrl.isAdd = true;
            ctrl.vendor = null;

            setFormPristine();

            loader.stop();
        }

        function setFormPristine() {
            if (factoryDetailForm) {
                factoryDetailForm.$setPristine();
                factoryDetailForm.$setUntouched();
            }
        }

    }

})();