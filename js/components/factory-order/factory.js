(function () {

    angular.module('printty')
        .component('factoryComponent', {
            controller: FactoryCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/factory-order/factory.html'
        });

    function FactoryCtrl(Auth, FactoryOrderApi, utils, Store) {
        "ngInject";

        utils.appLoaded();

        var ctrl = this;

        /**** variables ******/

        var subscriptions = [];

        ctrl.factoryItems = [];
        ctrl.makerFilter = [];
        ctrl.productCodes = [];
        ctrl.factorySelected = null;
        ctrl.pagination = {
            init: true,
            perPage: 40,
            maker_id: null,

            startDate: null,
            endDate: null,

            factory_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };

        ctrl.factoryDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var allSelected = false;

        ctrl.infiniteFactoryItems = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.factoryItems[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {
                fetchFactoryOrders(index);
            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            getMakersFilter();
            getFactoriesFilter();

            subscriptions.push(
                Store.event('factoryorder:create:item').subscribe(function () {
                    ctrl.viewCreate();
                }),

                Store.event('sidebarActiveStatus')
                    .subscribe(function (status) {
                        ctrl.pagination.maker_id = status;
                        ctrl.reload();
                    }),

                Store.data('MakerCustomers').subscribe(function (data) {
                    ctrl.makerCustomers = data;
                }),

                Store.data('FactoriesFilter').subscribe(function (data) {
                    ctrl.factoriesFilter = data;
                }),

                Store.data('FactoryType').subscribe(function (data) {
                    ctrl.factorySelected = data;
                }),

                Store.data('ProductCodes').subscribe(function (data) {
                    ctrl.productCodes = data;
                }),

                Store.data('Image:HeaderFactory')
                    .subscribe(function (data) {
                        ctrl.pagination.factory_id = data;
                        ctrl.reload();
                    }),

                Store.data('Image:HeaderFilter').subscribe(function (data) {
                    ctrl.pagination.startDate = data.start;
                    ctrl.pagination.endDate   = data.end;
                    ctrl.reload();
                })
            );
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.factoryItems = [];

            ctrl.infiniteFactoryItems.numLoaded_ = 0;
            ctrl.infiniteFactoryItems.toLoad_ = 0;
            ctrl.infiniteFactoryItems.getItemAtIndex(1);
        };

        ctrl.viewCreate = function () {
            ctrl.factoryDetails.type = 'add';
            ctrl.factoryDetails.isShown = true;
            ctrl.factoryDetails.active = {
                order_item_id: null
            }
        };

        ctrl.viewDetailsVendor = function (item) {
            console.log('factory')
            ctrl.factoryDetails.type = 'details';
            ctrl.factoryDetails.isShown = true;
            ctrl.factoryDetails.active = {
                order_item_id: item.VendorOrderItem.id
            }
            console.log(ctrl.factoryDetails)
        };

        ctrl.factoryDetails.onClose = function () {
            this.isShown = false;
        };

        ctrl.factoryDetails.onCreate = function () {
            ctrl.reload();
        };

        ctrl.onUpdate = function (vendor) {
            var originalItem = _.find(ctrl.factoryItems, function (factory) {
                return factory.VendorOrderItem.id == vendor.VendorOrderItem.id;
            });

            vendor.VendorOrderItem.color_title = originalItem.VendorOrderItem.color_title;
            vendor.VendorOrderItem.image_url = originalItem.VendorOrderItem.image_url;
            vendor.VendorOrderItem.item_code = originalItem.VendorOrderItem.item_code;
            vendor.VendorOrderItem.maker = originalItem.VendorOrderItem.maker;
            vendor.VendorOrderItem.size_title = originalItem.VendorOrderItem.size_title;
            vendor.VendorOrderItem.customer_order_number = originalItem.VendorOrderItem.customer_order_number;
            vendor.VendorOrderItem.place_title = originalItem.VendorOrderItem.place_title;
            vendor.VendorOrderItem.delivery_late = originalItem.VendorOrderItem.delivery_late;
            _.extend(originalItem, vendor);
        };

        ctrl.onDelete = function (vendorId) {
            _.remove(ctrl.factoryItems, function(item) {
                return item.VendorOrderItem.id == vendorId;
            });
        };

        ctrl.factoryDetails.onReload = function () {
            ctrl.reload();
        };

        function getMakersFilter() {
            FactoryOrderApi.fetchMakers().then(function(data){
                var maker =_.flatMap(data.makers, function(maker) {
                    return maker.ProductLinkedSource;
                });
                Store.data('MakerCustomers').update(maker);
            });
        }

        function getFactoriesFilter() {
            FactoryOrderApi.fetchFactories().then(function(data){
                var factory =_.flatMap(data.factories, function(factory) {
                    return factory;
                });
                ctrl.factorySelected = data.factorySelected;
                Store.data('FactoriesFilter').update(factory);
                Store.data('FactoryType').update(ctrl.factorySelected);
            });
        }

        function fetchFactoryOrders(index) {

            if (ctrl.infiniteFactoryItems.toLoad_ >= index || ctrl.pagination.end || ctrl.pagination.busy) return;
            if(!ctrl.pagination.factory_id || !ctrl.pagination.startDate|| !ctrl.pagination.endDate) return;
            ctrl.pagination.busy = true;

            if (ctrl.pagination.init) {
                utils.globalLoading.show();
            } else {
                utils.moreItemsLoad.show()
            }

            ctrl.infiniteFactoryItems.toLoad_ += ctrl.pagination.perPage;

            FactoryOrderApi.fetchFactoryOrders(
                {
                    paging_size: ctrl.pagination.perPage,
                    paging_offset: ctrl.factoryItems.length,
                    keywords: ctrl.pagination.searchQuery,
                    maker_id: ctrl.pagination.maker_id,
                    start_date: ctrl.pagination.startDate,
                    end_date: ctrl.pagination.endDate,
                    factory_id: ctrl.pagination.factory_id != null ? ctrl.pagination.factory_id : ctrl.factorySelected,
                }
            ).then(
                function (data) {

                    ctrl.pagination.init = false;
                    ctrl.pagination.searching = false;

                    ctrl.infiniteFactoryItems.numLoaded_ = ctrl.infiniteFactoryItems.toLoad_ - ctrl.pagination.perPage + data.order_items.length;
                    var allItems = ctrl.factoryItems.concat(data.order_items);
                    var seenIds = {};
                    ctrl.factoryItems = allItems.filter(function (item) {
                        var id = item.VendorOrderItem && item.VendorOrderItem.id;
                        if (!id || seenIds[id]) {
                            return false;
                        }
                        seenIds[id] = true;
                        return true;
                    });
                    if (allSelected) {
                        _.forEach(data.order_items, function(factoryData) {
                            factoryData.selected = allSelected;
                        });
                    }

                    ctrl.pagination.searchFailed = !ctrl.factoryItems.length;
                    ctrl.pagination.end = ctrl.factoryItems.length >= data.total_count;
                    ctrl.pagination.busy = false;

                    utils.globalLoading.hide();
                    utils.moreItemsLoad.hide();
                }
            );

        }

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.factoryItems, function (item) {

                if (item.selected) {
                    selectedItems.push(item.VendorOrderItem.id);
                }

            });

            return selectedItems;

        }

        function removeSelected(){
            _.forEach(ctrl.factoryItems, function (item) {
                item.selected = false;
            });
        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.factoryItems, function (item) {
                item.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

        ctrl.downloadCSV = function () {
            const selectedIds = getSelected();

            if (_.isArray(selectedIds) && !selectedIds.length) {
                alert('対象データを選択して下さい。')
                return;
            }

            utils.globalLoading.show();

            var params = {
                data : selectedIds,
                maker_id: ctrl.pagination.maker_id,
            };

            FactoryOrderApi.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                    removeSelected();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        }
    }

})();