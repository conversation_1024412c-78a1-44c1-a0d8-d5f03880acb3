(function () {
  function KenpinICtrl(utils, Auth, $state) {

    "ngInject";

    if (!Auth.isAuthorized('kenpini')) {
      return $state.go( Auth.defaultRoute );
    }

    var ctrl = this;

      utils.appLoaded();

      ctrl.isHide = false;
    ctrl.state = {

      name: 'scan',
      history: [],
      isInfo: false,


      is: function () {

        if (!arguments.length) return;

        if (arguments.length === 1) {
          return arguments[0] === this.name;
        }

        if (arguments.length > 1) {

          for (var i = 0; i < arguments.length; i++) {

            if (arguments[i] === this.name) {
              return true;
            }

          }

          return false

        }

      },
      back: function () {

        var last = this.history[this.history.length - 1];

        this.set(last, true);

        if (last == 'scan') {
          this.history = [];
        } else {
          this.history.pop();
        }

      },
      set: function (name, forget) {

        if (!forget && this.name != name) {
          this.history.push(this.name);
        }

        this.name = name;

        // emit event
        this._onChangeCallbacks.forEach(function(callback) {
          callback();
        });

      },

      _onChangeCallbacks: [],
      onChange: function(callback) {
        if (angular.isFunction(callback)) {
          this._onChangeCallbacks.push(callback);
        }
      }

    };

  }

  angular.module('printty')

    .component('kenpiniComponent', {
      require: {
        rootCtrl: '^^printtyApp'
      },
      controller: KenpinICtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpini/kenpini.html'
    });


  /******************
   *******************
   * ******* LEFT ******
   ****************/


  function KenpinILeftCtrl(Store) {

    "ngInject";

    var ctrl = this;
        // subscription;

    ctrl.$onInit = function () {

      ctrl.state = ctrl.kenpiniCtrl.state;

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
        }
      });

    };

    // subscribe to print task change
    Store.data('KenpinITask').subscribe(function (data) {

      if (!data) return;

      if (ctrl && ctrl.state)
      {
          //ctrl.isMultiple = data.taskType === 'Multiple';
          ctrl.task = data;
          ctrl.state.isInfo = true;
      }
      else
      {
          ctrl.state = {

              name: 'scan',
              history: [],
              isInfo: true,

              is: function () {

                  if (!arguments.length) return;

                  if (arguments.length === 1) {
                      return arguments[0] === this.name;
                  }

                  if (arguments.length > 1) {

                      for (var i = 0; i < arguments.length; i++) {

                          if (arguments[i] === this.name) {
                              return true;
                          }

                      }

                      return false

                  }

              },
              back: function () {

                  var last = this.history[this.history.length - 1];

                  this.set(last, true);

                  if (last == 'scan') {
                      this.history = [];
                  } else {
                      this.history.pop();
                  }

              },
              set: function (name, forget) {

                  if (!forget && this.name != name) {
                      this.history.push(this.name);
                  }

                  this.name = name;

                  // emit event
                  this._onChangeCallbacks.forEach(function(callback) {
                      callback();
                  });

              },

              _onChangeCallbacks: [],
              onChange: function(callback) {
                  if (angular.isFunction(callback)) {
                      this._onChangeCallbacks.push(callback);
                  }
              }

          };
      }

    });

    // ctrl.$onDestroy = function () {
    //   subscription.unsubscribe();
    // };

    /******** helpers ***********/
    function setDefault() {
      ctrl.isMultiple = false;
      ctrl.task = null;
    }

  }

  angular.module('printty')

    .component('kenpiniLeft', {
      require: {
        kenpiniCtrl: '^^kenpiniComponent'
      },
      controller: KenpinILeftCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpini/kenpini-left.html'
    });


  /******************
   *******************
   * ******* RIGHT ******
   ****************/


  function KenpinIRightCtrl($element, KenpinIApi, Store, electron, $timeout, utils, ElectronUtils) {

    "ngInject";

    var ctrl = this;

    ctrl.isElectron = !!electron;

    ctrl.scannerInput = null;
    ctrl.qrCodeInput = null;

    ctrl.taskInfo = null;
    ctrl.task = null;
    ctrl.isKenpinIStep = true;


    ctrl.$onInit = function () {
      ctrl.state = ctrl.kenpiniCtrl.state;

      ctrl.focusScanField();

      ctrl.state.onChange(function() {
        if (ctrl.state.is('scan', 'manual')) {
          setDefault();
          $element.removeClass('single multiple');
        }

        if (ctrl.state.is('scan')) {
          ctrl.focusScanField();
        }
      });

    };

    ctrl.findQr = function () {
      if (!ctrl.qrCodeInput) return;
      search(ctrl.qrCodeInput);
    };

    ctrl.focusScanField = function () {
      $timeout(function () {
        $('#scanner-field').focus();
      });
    };

    ctrl.findQrScan = function () {
      utils.delay(function () {
        if (!ctrl.scannerInput) return;
        search(ctrl.scannerInput);
      }, 250);
    };

    ctrl.clearInput = function () {
      ctrl.qrCodeInput = null;
      ctrl.errorText = null;
    };

    ctrl.onDownload = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.onKenpinI = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.downloadItemImage = function (item) {

      utils.globalLoading.show();

      KenpinIApi.fetchMultipleTaskSource({
        task_group_step_id: ctrl.taskInfo.TaskGroupStep.id,
        item_id: item.TaskStepItem.id
      })
        .then(function (data) {
          ElectronUtils.getFile(data.TaskSource.image_url)
            .finally(function () {
              utils.globalLoading.hide();
            })
        })
        .catch(function () {
          utils.globalLoading.hide();
        })

    };

    ctrl.press = function () {
      ctrl.state.set('finish', true);
    };

    ctrl.kenpiniSuccess = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

      if (ctrl.isMultiple) {
        processMultiple();
      } else {
        processSingle();
      }

    };

    ctrl.kenpiniFail = function () {
      ctrl.clearInput();
      ctrl.scannerInput = null;

      if (ctrl.isMultiple) {
        ctrl.state.set('fail', true);
      } else {
        failSingle();
      }

    };

    ctrl.retryKenpinI = function () {

      if (ctrl.isMultiple) {
        retryMultiple();
      } else {
        retrySingle();
      }

    };

    ctrl.failMultiple = function () {

      var itemsFailed = [];

      _.forEach(ctrl.task.items, function (item) {

        if (item.selected) {

          itemsFailed.push({
            TaskStepItem: {
              id: item.TaskStepItem.id
            }
          });

        }

      });

      KenpinIApi.failMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id,
        failedItems: itemsFailed
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    };

    ctrl.cancelFail = function () {

      _.forEach(ctrl.task.items, function (item) {
        item.selected = false;
      });

      ctrl.state.set('finish', true);

    };

    /******** helpers ***********/
    function setDefault() {
      ctrl.scannerInput = null;

      ctrl.taskInfo = null;
      ctrl.task = null;
      ctrl.print_positions = [];

      ctrl.isMultiple = false;
      ctrl.isKenpinIStep = true;
      ctrl.errorText = null;
    }

    function search(input) {
      KenpinIApi.search(input).then(function (data) {

        ctrl.taskInfo = data;
        ctrl.print_positions = data.TaskStep.print_position;
        Store.data('PrintPosition').update(ctrl.print_positions);

        ctrl.isMultiple = data.type === 'Multiple';

        if (ctrl.isMultiple) {
          $element.removeClass('single').addClass('multiple');
        } else {
          $element.addClass('single').removeClass('multiple');
        }

        // task found => fetch task
        if (ctrl.isMultiple) {
          fetchMultiple(data, false);
        } else {
          fetchSingle(data, false);
        }

      }, function (error) {

        if (error.status === 400 && angular.isDefined(error.data.message)) {
          ctrl.errorText = error.data.message;
        }

        ctrl.scannerInput = null;

      });
    }

    function fetchSingle(taskData, forget) {

      KenpinIApi.fetchSingleTask({
        task_id: taskData.Task.id,
        step_id: taskData.TaskStep.id
      }).then(function (data) {

        ctrl.errorText = null;

        //data[0].taskType = 'Single';

        Store.data('KenpinITask').update(data);

        // ctrl.task = data;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });

    }

    function fetchMultiple(taskData, forget) {

      KenpinIApi.fetchMultipleTask({
        task_group_id: taskData.TaskGroupStep.id
      }).then(function (data) {

        ctrl.errorText = null;

        //data.taskType = 'Multiple';

        Store.data('KenpinITask').update(data);

        ctrl.isKenpinIStep = data.TaskStepType.code === 'kenpini';

        ctrl.task = data;

        ctrl.state.set('product', forget);

        ctrl.scannerInput = null;
      });

    }

    function processSingle() {

      KenpinIApi.processSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function processMultiple() {

      KenpinIApi.processMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function failSingle() {

      KenpinIApi.failSingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          ctrl.state.set('scan', true);
        });

    }

    function retrySingle() {

      KenpinIApi.retrySingleTask({
        taskId: ctrl.taskInfo.Task.id,
        stepId: ctrl.taskInfo.TaskStep.id
      })
        .then(function () {
          fetchSingle(ctrl.taskInfo, true);
        });

    }

    function retryMultiple() {

      KenpinIApi.retryMultipleTask({
        stepId: ctrl.taskInfo.TaskGroupStep.id
      })
        .then(function () {
          fetchMultiple(ctrl.taskInfo, true);
        });

    }

  }

  angular.module('printty')

    .component('kenpiniRight', {
      require: {
        kenpiniCtrl: '^^kenpiniComponent'
      },
      controller: KenpinIRightCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/kenpini/kenpini-right.html'
    });


  // KenpinI info
    function KenpinIInfoCtrl(Store, $mdDialog, KenpinIApi, Auth, $state, utils, $timeout, ProductionTasks, electron) {

        "ngInject";

        var ctrl = this,
            subscription;

        ctrl.subscriptions = [];
        ctrl.isShowReasion = false;
        ctrl.orderItemId = null;
        ctrl.taskId = null;
        ctrl.print_positions = [];
        ctrl.loadingType = 'full';
        ctrl.taskTitle = ['おもて', 'うら', 'ひだり', 'みぎ'];
        ctrl.numberCheck = 0;
        ctrl.isDisabled = false;
        ctrl.statusOk = 55;
        ctrl.delivery_late = false;
        ctrl.is_embroidery = false;
        ctrl.isElectron = !!electron;

        ctrl.closeKenpinI = function () {
            ctrl.state.isInfo= false;
            ctrl.state.back();
            $state.go( 'kenpini' );
            clearInterval(intervalTime);
        };

        var loader = new utils.loadCounter(
            function(isBlocking) {
                utils.listLoading.show(isBlocking);
            },
            function() {
                utils.listLoading.hide();
            },
            2
        );

        ctrl.$onInit = function () {

            ctrl.subscriptions = [];

            var kenpiniData = {
                typeId: 3
            };
            KenpinIApi.fetchKenpinStatusByType(kenpiniData)
                .then(function (data) {
                    ctrl.statusOk = data[0].KenpinStatus.id;
                })
                .catch(function () {
                    ctrl.statusOk = 55;
                });

            ctrl.state = ctrl.kenpiniCtrl.state;

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                }
            });

            //get numbercheck
            getNumberChecked(ctrl.task.detail.task_ids);

            checkCurrentStatus();
            if(ctrl.task['countdown'].inspection_countdown !== 0 && !ctrl.task['countdown'].all_checked) {
                startTime (ctrl.task['countdown'].inspection_countdown);
            }

        };

        var z = parseInt(document.getElementById("count-down").innerHTML);
        var intervalTime;
        ctrl.clickChange = function (step,keyStep) {
            if(step.TaskStep.inspection_countdown !== 0){
                setTimeout(function() {
                    var checkOK = document.getElementById("OK-" + keyStep);
                    var checkNG = document.getElementById("NG-" + keyStep);
                    var checkedOK = checkOK.classList.contains('md-checked');
                    var checkedNG = checkNG.classList.contains('md-checked');
                    var checked = checkItemUncheck();

                    if((!checkedNG && !checkedOK) || !checked) {
                        startTime (step.TaskStep.inspection_countdown);
                    }else {
                        clearInterval(intervalTime);
                        document.getElementById("count-down").style.display = 'none';
                    }
                },10);
            }
        }

        function startTime (s) {
            z = parseInt(s);
            var sec = z - z + parseInt(s);
            if (intervalTime) clearInterval(intervalTime);
            intervalTime = setInterval(function () {
                document.getElementById("count-down").innerHTML = sec;
                if (0 <= sec && sec <= 5) {
                    document.getElementById("count-down").style.display = 'block';
                    document.getElementById("count-down").style.color = 'red';
                } else if (sec > 5) {
                    document.getElementById("count-down").style.display = 'block';
                    document.getElementById("count-down").style.color = 'green';
                }
                if (sec === 0) {
                    clearInterval(intervalTime);
                } else if (sec === 0) {
                    sec = s;
                }
                sec--;
            }, 1000);
        }

        function checkItemUncheck(){
            var check = true;
            angular.forEach(ctrl.task,function(item,key) {
                if(typeof item.TaskStep !== 'undefined' && (key != "detail" || key != "embroidery_info" || key != "countdown")) {
                    if ((typeof (item.selected) == 'undefined' && typeof (item.okSelected) == 'undefined') ||
                        (!item.selected && typeof (item.okSelected) == 'undefined') ||
                        (!item.okSelected && typeof (item.selected) == 'undefined') ||
                        (!item.selected && !item.okSelected)) {
                        console.log('item2',item);
                        check = false;
                    }
                }

            });

            return check;
        }

        function getNumberChecked(taskId) {
            KenpinIApi.getOrderCheck(taskId)
                .then(function (data) {
                    if(data) {
                        ctrl.numberCheck = data;
                    } else {
                        ctrl.numberCheck = 0;
                    }
                })
                .catch(function () {
                    ctrl.numberCheck = 0;
                });
        }

        ctrl.confirmDelete = function (ev) {
            if(ctrl.subscriptions.length == 0) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('オプションを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                ctrl.subscriptions = [];
            });

        };

        ctrl.save = function () {
            ctrl.isDisabled = true;
            clearInterval(intervalTime);
            document.getElementById("count-down").style.display = 'none';
            if(ctrl.subscriptions.length == 0) {
                return;
            }

            if(ctrl.task.detail.is_include){
                if(!checkSelectedInclude()){
                    alert('同梱オプションを確認して下さい。')
                    return;
                }
            }

            var kenpiniData = {
                kenpini: ctrl.subscriptions,
                late: ctrl.delivery_late ? 1:0,
                orderItemId: ctrl.orderItemId,
            };

            ctrl.loadingType = 'partial';
            KenpinIApi.createKenpinI(kenpiniData)
                .then(function (data) {
                    ctrl.subscriptions = [];
                    ctrl.numberCheck = ctrl.numberCheck + 1;
                    ctrl.loadingType = 'stopped';
                    ctrl.isDisabled = false;
                    ctrl.state.set('scan', true);
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                    ctrl.isDisabled = false;
                });

        };

        ctrl.selectOKAll = function (side) {
            var sideItem = side;
            _.forEach(ctrl.task, function(item) {
                if(typeof(item.TaskStep) != "undefined" && (item.TaskStep.title === sideItem)){
                    item.okSelected = true;
                    item.selected = false;
                }
            });

            _.forEach(ctrl.task, function(item) {
                if(typeof(item.TaskStep) != "undefined" && item.TaskStep !== null && (item.TaskStep.title === sideItem)) {
                    ctrl.updateSubscribe(item);
                }
            });
            if(ctrl.checked == true || ctrl.task.detail.is_check_all == true){
                document.getElementById("count-down").style.display = 'none';
            }
        };

        ctrl.selectOKCertificate = function () {
            _.forEach(ctrl.task, function(item) {
                if(typeof(item.TaskStep) != "undefined" && item.TaskStep.certificate_stamp !== null ){
                    item.okSelectedCertificate = !item.okSelectedCertificate;
                    item.selected = false;
                }
            });

            _.forEach(ctrl.task, function(item) {
                if(typeof(item.TaskStep) != "undefined" && (item.TaskStep.certificate_stamp !== null )){
                    ctrl.updateSubscribe(item);
                }
            });
        };

        ctrl.embroideryInfo = function () {
            ctrl.is_embroidery = !ctrl.is_embroidery;
        };

        ctrl.onSuccessCopy = function(e) {
            e.clearSelection();

            ctrl.clipSuccessMsg = e.text;
            $timeout(function() {
                ctrl.clipSuccessMsg = null;
            }, 500);
        };

        function setCheckboxDefault(){
            _.forEach(ctrl.task, function (item) {
                if (item.selected) {
                    item.selected = false;
                }
                if (item.okSelected) {
                    item.okSelected = false;
                }
            });
        }

        ctrl.updateSubscribe = function(item) {
            var taskId = item.TaskStep.id;
            for (var i = 0; i < ctrl.subscriptions.length; i++) {
                if (ctrl.subscriptions[i].taskId === taskId) {
                    ctrl.subscriptions.splice(i, 1);
                }
            }

            if(item.okSelected){
                data = {
                    taskId: taskId,
                    status: [
                        {
                            id: ctrl.statusOk,
                            quantity: 1
                        }
                    ],
                    index: parseInt(item.TaskStep.index.split('/')[0], 10),
                    order_item_id: item.OrderItem.id,
                };

                ctrl.subscriptions.push(data);
            }

            var count = 0;
            for(var i = 0 ; i < ctrl.task.detail.total_task_step; i ++){
                if (ctrl.task[i].okSelected === true)  {
                    count ++;
                }
            }
            if(ctrl.task.detail.total_task_step == count && item.okSelected == true){
                ctrl.checked = true;
            } else {
                ctrl.checked = false;
                ctrl.task.detail.is_check_all = false;
            }

            if(ctrl.checked) {
                clearInterval(intervalTime);
                document.getElementById("count-down").style.display = 'none';
            }else{
                if(ctrl.task['countdown'].inspection_countdown != 0) {
                    startTime(ctrl.task['countdown'].inspection_countdown);
                }

            }
        };

        Store.data('ItemStatus').subscribe(function (data) {
            var count = 1;
            for(var i = 0 ; i < ctrl.subscriptions.length; i ++) {
                if(ctrl.subscriptions[i] !== null && ctrl.subscriptions[i].taskId === data.taskId) {
                    ctrl.subscriptions.splice(i, 1, data);
                    count--;
                }
            }
            if (count === 1 && data) {
                ctrl.subscriptions.push(data);
            }
        });

        Store.data('DeliveryLate').subscribe(function (data) {
            ctrl.delivery_late = data;
        });

        Store.data('PrintPosition').subscribe(function (data) {
            ctrl.print_positions = data;
        });

        subscription = Store.data('KenpinITask').subscribe(function (data) {

            if (!data) return;

            //ctrl.isMultiple = data[0].taskType === 'Multiple';
            ctrl.task = data;
            ctrl.orderItemId = data.detail.order_item_id;
            ctrl.taskId = data.detail.task_id;
        });

        ctrl.$onDestroy = function () {
            subscription.unsubscribe();
        };

        ctrl.range = function(min, max, step) {
            step = step || 1;
            var input = [];
            for (var i = min; i <= max; i += step) {
                input.push(i);
            }
            return input;
        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.isMultiple = false;
            //ctrl.task = null;
            ctrl.state.isInfo = false;
        }

        ctrl.viewReasion = function (task) {
            var categoryId = task.TaskStep.category_id;
            var taskId = task.TaskStep.id;
            var order_item_id = task.OrderItem.id;
            var order_item_task_id = task.TaskStep.order_item_task_id;
            if(task.selected) {
                Store.data('ItemIndex').update(parseInt(task.TaskStep.index.split('/')[0], 10));
                Store.data('Category').update([categoryId, taskId, order_item_id, order_item_task_id]);
                ctrl.isShowReasion = true;
            } else {
                for(var i = 0 ; i < ctrl.subscriptions.length; i ++) {
                    if(ctrl.subscriptions[i].taskId === taskId) {
                        ctrl.subscriptions.splice(i,1);
                    }
                }
            }
        };

        ctrl.onDetailsClose = function () {
            ctrl.isShowReasion = false;
        };

        ctrl.showLargeImage = function (ev, item) {
            var confirm = $mdDialog.show({
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    }
                    $scope.image_large = item.TaskStep.large_image_url;
                }],
                templateUrl: 'views/kenpini/kenpini-dialog.html',
                clickOutsideToClose:true,
            });
        };

        function checkCurrentStatus() {
            _.forEach(ctrl.task, function (item) {
                try{
                    var status = item.TaskStep.current_status;
                    if(typeof(status) !== 'undefined') {
                        if (status === 1) {
                            item.okSelected = true;
                        }
                        if (status === 2) {
                            item.selected = true;
                        }
                    }

                    if(item.TaskStep.has_kenpin &&  ctrl.task.detail.is_include){
                        item.check_include = true;
                    }
                } catch(e){

                }
            });
        }

        function checkSelectedInclude(){
            var check = true;
            _.forEach(ctrl.task, function (item) {
                if(item.TaskStep){
                    if((item.okSelected && typeof(item.check_include === 'undefined')) || (item.check_include && typeof(item.okSelected === 'undefined'))) {
                        check = false;
                    }
                    if((item.okSelected && item.check_include) || item.selected) {
                        check = true;
                    }
                }
            });

            return check;
        }

        ctrl.showIncludeOption = function (ev, item) {
            if(item.check_include){
                $mdDialog.show({
                    controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                        $scope.cancelDelete = function () {
                            $mdDialog.cancel();
                        };
                        $scope.confirmDelete = function () {
                            $mdDialog.hide();
                        };

                        $scope.include_options = ctrl.task.detail.include_option;
                    }],
                    templateUrl: 'views/kenpini/include-option.html',
                    clickOutsideToClose:true,
                });
            }
        };

        ctrl.selectOKOptionAll = function (){
            _.forEach(ctrl.task, function (item) {
                if(item.TaskStep){
                    item.check_include = true;
                }
            });
        }

        ctrl.printQR = function (ev){
            if (!ctrl.isElectron) {
                alert('アプリ外で印刷不可です。');
                return;
            }

            var taskIds = ctrl.task.detail.task_ids;
            var size;
            var confirm = $mdDialog.confirm({
                template: utils.choosePaperSizeTemplateQRTasks('印刷用紙サイズを選択してください。'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelChoose = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmChoose = function () {
                        size = $('.paper-size-radio:checked').val();
                        if(size) {
                            $mdDialog.hide();
                        }
                        return false;
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                ProductionTasks.fetchQRCode({
                    'tasks_ids[]': taskIds,
                    size : size,
                }).then(function (data) {
                    if (data) {
                        electron.ipcRenderer.send('print-images', {
                            urls: data,
                            size: size
                        });
                        loader.start(false);
                    }
                })
            });
        }
    }
    angular.module('printty')

        .component('kenpiniInfo', {
            require: {
                kenpiniCtrl: '^^kenpiniComponent'
            },
            controller: KenpinIInfoCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/kenpini/kenpini-info.html'
        });

})();