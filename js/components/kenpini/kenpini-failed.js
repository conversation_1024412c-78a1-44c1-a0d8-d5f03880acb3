(function() {

    angular.module('printty')

        .component('kenpiniFailed', {
            bindings: {
                isShown: '<',
                onClose: '&'
            },
            controller: KenpinIFailedCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/kenpini/kenpini-failed.html'
        });

    function KenpinIFailedCtrl($scope, $timeout, $mdSidenav, $mdDialog, Product, ProductLinkedCode, Store, utils, ProductSize, ProductColor, KenpinIApi) {
        'ngInject';

        var ctrl = this;

        ctrl.loadingType = 'full';
        ctrl.showMode = true;
        ctrl.categoryId = null;
        ctrl.data = null;
        ctrl.product_cells = [];
        ctrl.defectContentCategories = [];
        ctrl.defectContents = [];
        ctrl.taskId = null;
        ctrl.kenpinId = null;
        ctrl.defectContentCategoryId = null;
        ctrl.defectContentId = null;
        ctrl.productCellId = null;
        ctrl.isVendorOrder = null;
        ctrl.orderItemId = null;
        ctrl.checkProductCell = false;
        ctrl.checkDefectQuantity = false;
        ctrl.checkMinDefectQuantity = false;
        ctrl.checkDefectCategory = false;
        ctrl.checkDefectContent = false;
        ctrl.checkVendor = false;
        ctrl.delivery = false;

        var dataForm, dataClone;
        function getSelected() {

            var selectedDatas = [];
            selectedDatas = ctrl.data;

            return selectedDatas;

        }
        var subscriptions = [];
        var sidenav = {
            open: function () {
                ctrl.checkProductCell = false;
                ctrl.checkDefectQuantity = false;
                ctrl.checkDefectCategory= false;
                ctrl.checkDefectContent = false;
                ctrl.checkVendor = false;
                $mdSidenav('add-kenpinNG').open();
            },
            close: function () {
                $mdSidenav('add-kenpinNG').close();
            }
        };

        var loader = new utils.loadCounter(
            function (type) {
                if (!type) type = 'full';
                ctrl.loadingType = type;
            },
            function () {
                ctrl.loadingType = 'stopped';
            }
        );

        /****** methods ******/
        ctrl.$onInit = function() {

            subscriptions.push(
                Store.data('Category').subscribe(function (data) {
                    if(data) {
                        ctrl.getKenpinIStatusByCategory(data[0],data[1],data[3]);
                        ctrl.taskId = data[1];
                        ctrl.orderItemId = data[2];
                        ctrl.kenpinId = data[4];
                    }
                })
            );

        };

        ctrl.getKenpinIStatusByCategory = function (id,task_id,order_item_task_id) {
            ctrl.loadingType = 'full';
            KenpinIApi.fetchKenpinStatus({
                categoryId: id,
                taskId: task_id,
                orderItemTaskId : order_item_task_id,
            })
                .then(function (data) {
                    ctrl.data = data.result;
                    ctrl.defectContentCategories = data.defectContentCategories;
                    ctrl.product_cells  = data.product_cells;
                    ctrl.loadingType = 'stopped';
                });
        }

        ctrl.$postLink = function () {

            dataForm = $scope.dataForm;

            $mdSidenav('add-kenpinNG').onClose(function () {
                $timeout(function () { ctrl.onClose(); }, 400);
            });

        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.selectProductCell = function(productCellId) {
            ctrl.productCellId = productCellId;
        };

        ctrl.selectDefectContent = function(defectId) {
            ctrl.defectContentCategoryId = defectId;
            KenpinIApi.fetchDefectContent({
                category_id: defectId
            }).then(function(data){
                ctrl.defectContents = data;
            });

        };

        ctrl.selectVendorOrder = function(vendor) {
            ctrl.isVendorOrder= vendor;
        };

        ctrl.selectDelivery = function(delivery) {
            if(delivery) {
                ctrl.delivery = true;
            }
        };

        ctrl.save = function () {
            dataForm.$setSubmitted();
            if(typeof dataForm.product_cell_id.$viewValue == "undefined" || dataForm.product_cell_id.$viewValue === '' || dataForm.product_cell_id.$viewValue == null) {
                ctrl.checkProductCell = true;
            }else {
                ctrl.checkProductCell = false;
            }

            if(typeof dataForm.defect_quantity.$viewValue == "undefined" || dataForm.defect_quantity.$viewValue === '' || isNaN(dataForm.defect_quantity.$viewValue) || dataForm.defect_quantity.$viewValue == null){
                ctrl.checkDefectQuantity = true;
                ctrl.checkMinDefectQuantity = false;
            }else {
                if(dataForm.defect_quantity.$viewValue >0){
                    ctrl.checkDefectQuantity = false;
                    ctrl.checkMinDefectQuantity = false;
                }else {
                    ctrl.checkMinDefectQuantity = true;
                    ctrl.checkDefectQuantity = true;
                }
            }

            if(typeof dataForm.defect_content_category_id.$viewValue == "undefined" || dataForm.defect_content_category_id.$viewValue === '' || dataForm.defect_content_category_id.$viewValue == null){
                ctrl.checkDefectCategory= true;
            }else {
                ctrl.checkDefectCategory= false;
            }

            if(typeof dataForm.defect_content_id.$viewValue == "undefined" || dataForm.defect_content_id.$viewValue === '' || dataForm.defect_content_id.$viewValue == null){
                ctrl.checkDefectContent = true;
            }else {
                ctrl.defectContentId = dataForm.defect_content_id.$viewValue;
                ctrl.checkDefectContent = false;
            }
            if(typeof dataForm.is_vendor_order.$viewValue == "undefined" || dataForm.is_vendor_order.$viewValue == null){
                ctrl.checkVendor = true;
            }else {
                ctrl.checkVendor = false;
                ctrl.isVendorOrder = dataForm.is_vendor_order.$viewValue;
            }
            if(!ctrl.checkVendor && !ctrl.checkDefectContent && !ctrl.checkDefectCategory && !ctrl.checkDefectQuantity && !ctrl.checkProductCell && !ctrl.checkMinDefectQuantity){
                loader.start('partial');
                var dataInfo = getSelected();

                if(dataInfo.length == 0) {
                    ctrl.close();
                    return ;
                }
                var itemIndex = null;
                Store.data('ItemIndex').subscribe(function (data) {
                    if(data) {
                        itemIndex  = data;
                    }
                })
                var itemStatus = {
                    'taskId': ctrl.taskId,
                    'kenpinId':ctrl.kenpinId,
                    'is_vendor_order':ctrl.isVendorOrder,
                    'defectContentCategoryId': ctrl.defectContentCategoryId,
                    'defectContentId': ctrl.defectContentId,
                    'delivery': ctrl.delivery,
                    'index': itemIndex,
                    'order_item_id': ctrl.orderItemId,
                    'kenpinTaskNG' : {
                        'dataInfo' : dataInfo
                    }
                };

                Store.data('ItemStatus').update(itemStatus);
                ctrl.close();
            }
        };

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('ユーザーを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteVendor);

        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {

            switch (ctrl.type) {
                case 'details':
                    loader.start();
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    loader.stop();
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd) {
            }

            sidenav.open();

        }


        function setDefault() {
            ctrl.isAdd = true;
            ctrl.vendor = null;

            setFormPristine();

            loader.stop();
        }

        function setFormPristine() {
            if (dataForm) {
                dataForm.$setPristine();
                dataForm.$setUntouched();
            }
        }
    }

})();
