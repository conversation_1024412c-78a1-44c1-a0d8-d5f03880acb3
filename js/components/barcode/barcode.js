(function () {

    function BarcodeCtrl(utils, Auth, $state) {

        "ngInject";

        if (!Auth.isAuthorized('print')) {
            return $state.go( Auth.defaultRoute );
        }

        var ctrl = this;

        utils.appLoaded();

        ctrl.state = {

            name: 'scan',
            history: [],

            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

    }

    angular.module('printty')

        .component('barcodeComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: BarcodeCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/barcode/barcode.html'
        });


    /******************
     *******************
     * ******* LEFT ******
     ****************/


    function BarcodeLeftCtrl(Store) {

        "ngInject";

        var ctrl = this,
            subscription;

        ctrl.$onInit = function () {

            ctrl.state = ctrl.barcodeCtrl.state;

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                }
            });

        };

        // subscribe to print task change
        subscription = Store.data('BarcodeTask').subscribe(function (data) {

            if (!data) return;

            ctrl.isMultiple = data.taskType === 'Multiple';
            ctrl.task = data;

        });

        ctrl.$onDestroy = function () {
            subscription.unsubscribe();
        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.isMultiple = false;
            ctrl.task = null;
        }
    }

    angular.module('printty')

        .component('barcodeLeft', {
            require: {
                barcodeCtrl: '^^barcodeComponent'
            },
            controller: BarcodeLeftCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/barcode/barcode-left.html'
        });


    /******************
     *******************
     * ******* RIGHT ******
     ****************/


    function BarcodeRightCtrl($element, BarcodeApi, Store, electron, $timeout, utils, ElectronUtils, $mdDialog) {

        "ngInject";

        var ctrl = this;

        ctrl.isElectron = !!electron;

        ctrl.scannerInput = null;
        ctrl.qrCodeInput = null;

        ctrl.taskInfo = null;
        ctrl.task = null;
        ctrl.isPrintStep = true;
        ctrl.source = null;
        ctrl.taskId = null;


        ctrl.$onInit = function () {
            ctrl.state = ctrl.barcodeCtrl.state;

            ctrl.focusScanField();

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                    $element.removeClass('single multiple');
                }

                if (ctrl.state.is('scan')) {
                    ctrl.focusScanField();
                }
            });

        };

        ctrl.openConditionNote = function(url) {
            //open links externally by default
            electron.ipcRenderer.send('openConditionNote', {url:url});
        }

        ctrl.findQr = function () {
            if (!ctrl.qrCodeInput) return;
            search(ctrl.qrCodeInput);
        };

        ctrl.focusScanField = function () {
            $timeout(function () {
                $('#scanner-field').focus();
            });
        };

        ctrl.findQrScan = function () {
            utils.delay(function () {
                if (!ctrl.scannerInput) return;
                search(ctrl.scannerInput);
            }, 250);
        };

        ctrl.clearInput = function () {
            ctrl.qrCodeInput = null;
            ctrl.errorText = null;
        };

        ctrl.cancelFail = function () {

            _.forEach(ctrl.task.current_step.items, function (item) {
                item.selected = false;
            });

            ctrl.state.set('finish', true);

        };

        /******** helpers ***********/
        function setDefault() {
            ctrl.scannerInput = null;

            ctrl.taskInfo = null;
            ctrl.task = null;

            ctrl.isMultiple = false;
            ctrl.isPrintStep = true;
            ctrl.errorText = null;
        }

        function search(input) {
            BarcodeApi.search(input).then(function (data) {

                ctrl.taskInfo = data;

                ctrl.isMultiple = data.type === 'Multiple';

                if (ctrl.isMultiple) {
                    $element.removeClass('single').addClass('multiple');
                } else {
                    $element.addClass('single').removeClass('multiple');
                }

                // task found => fetch task
                fetchSingle(data, false);

            }, function (error) {

                if (error.status === 400 && angular.isDefined(error.data.message)) {
                    ctrl.errorText = error.data.message;
                }

                ctrl.scannerInput = null;

            });
        }

        function fetchSingle(taskData, forget) {

            BarcodeApi.fetchSingleTask({
                task_id: taskData.Task.id,
                step_id: taskData.TaskStep.id
            }).then(function (data) {

                ctrl.errorText = null;

                data.taskType = 'Single';

                Store.data('BarcodeTask').update(data);

                ctrl.task = data;

                ctrl.taskId = data.tasks.detail.task_id;

                ctrl.state.set('product', forget);

                ctrl.scannerInput = null;
            });

        }

        ctrl.printBarcode = function () {
            if (!ctrl.isElectron) {
                alert('プリンターは設定されてません。');
                return;
            }

            utils.globalLoading.show();

            BarcodeApi.fetchBarCode({
                'tasks_ids[]': ctrl.taskId,
            })
                .then(function (data) {

                    if (data) {
                        electron.ipcRenderer.send('print-images', {
                            urls: data,
                            size: 3,
                            barcode: true,
                        });

                        utils.globalLoading.hide();
                        ctrl.state.set('scan', true);
                    }
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });
        }

    }

    angular.module('printty')

        .component('barcodeRight', {
            require: {
                barcodeCtrl: '^^barcodeComponent'
            },
            controller: BarcodeRightCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/barcode/barcode-right.html'
        });

})();