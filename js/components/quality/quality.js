(function () {

    function QualityCtrl($scope, $state, Auth, Store, utils) {

        "ngInject";

        var ctrl = this;

        if (!Auth.isAuthorized('quality')) {
            return $state.go( Auth.defaultRoute );
        }

        ctrl.matrix = {
            show: function () {}
        };

        ctrl.state = $state;
        ctrl.statuses = [];

        var subscriptions = [];

        $scope.$on('$stateChangeStart', function () { onStatusesChange([]); });

        ctrl.$onInit = function () {
            utils.appLoaded();
            Store.data('statuses').update(ctrl.statuses);
            subscriptions.push(
                Store.data('statuses').subscribe(function (statuses) { onStatusesChange(statuses); })
            );
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        /*** private ****/
        function onStatusesChange(statuses) {
            ctrl.statuses = statuses;
        }

    }

    angular.module('printty')

        .component('qualityComponent', {
            controller: QualityCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/quality/quality.html'
        });

})();