(function () {

    angular.module('printty')
        .component('qualityBarChart', {
            controller: BarChartCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/quality/chart/bar.html',
            bindings: {
                startDate: '=',
                endDate: '=',
                startTime: '=',
                endTime: '=',
                typeId: '<',
                clickSearch: '<',
                onlyLate: '<',
                factoryId: '=',
            }
        });


    function BarChartCtrl($element, Quality) {

        "ngInject";

        var ctrl = this;

        /******* variables *******/

        var chart = null;
        var taskDatasets = [0];

        var ctx = $element.find('canvas').get(0).getContext("2d");

        var taskLabels = [];

        var config = {
            type: 'bar',
            data: {
                labels: taskLabels,
                datasets: [{
                    data: taskDatasets,
                    backgroundColor: []
                }]
            },
            options: {
                hover: {
                    animationDuration: 0
                },
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    onComplete: function() {
                        var chartInstance = this.chart,
                            ctx = chartInstance.ctx;

                        ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize, Chart.defaults.global.defaultFontStyle, Chart.defaults.global.defaultFontFamily);
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'bottom';

                        this.data.datasets.forEach(function(dataset, i) {
                            var meta = chartInstance.controller.getDatasetMeta(i);
                            meta.data.forEach(function(bar, index) {
                                var data = dataset.data[index];
                                ctx.fillText(data, bar._model.x, bar._model.y - 5);
                            });
                        });
                    }
                },
                legend: {
                    display: false
                },
                tooltips: {
                    enabled: false,
                },
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            stepSize: 5
                        }
                    }],
                    xAxes: [{
                        ticks: {
                            autoSkip: true,
                            maxRotation: 90,
                            minRotation: 0
                        }
                    }]
                },
                legendCallback: function(chart) {
                    var text = [];
                    text.push('<table id="chart-detail-table">');
                    text.push(
                        '<tr>' +
                        '<td>行ラベル</td>' +
                        '<td>合計/数量</td>' +
                        '</tr>');
                    var total = 0;
                    for (var i = 0; i < chart.data.labels.length; i++) {
                        text.push(
                            '<tr>' +
                            '<td>'+ chart.data.labels[i] +'</td>' +
                            '<td>'+ chart.data.datasets[0].data[i] +'</td>' +
                            '</tr>'
                        );
                        total += chart.data.datasets[0].data[i];
                    }
                    text.push(
                        '<tr>' +
                        '<td>総計</td>' +
                        '<td>'+ total +'</td>' +
                        '</tr>');
                    text.push('</table>');
                    return text.join('');
                }
            }
        };

        /***** methods ******/

        ctrl.$onInit = function () {
            if(!chart && taskDatasets && taskLabels) {
                chart = new Chart(ctx, config);
            }

        };

        ctrl.$onChanges = function () {
            fetchData();
        };

        function fetchData() {
            var start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            var end_date =  moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            Quality.fetchGraph({
                start_date: start_date,
                end_date: end_date,
                type_id: ctrl.typeId,
                only_late: ctrl.onlyLate,
                chart_type: 'bar',
                factory_id: ctrl.factoryId,
            })
                .then(function (data) {
                    taskDatasets = data.datasets;
                    taskLabels = data.labels;
                    createChart();

                });

        }

        function createChart() {

            for(var i = 0; i < taskDatasets.length ; i++) {
                chart.data.datasets[0].backgroundColor.push('#4472c4');
            }

            // if(taskDatasets.length > 0 && taskLabels.length > 0) {
                if(!chart) {
                    chart = new Chart(ctx, config);
                } else {
                    chart.data.labels = taskLabels;
                    chart.data.datasets[0].data = taskDatasets;
                    chart.update();
                }
                document.getElementById('chart-legends-bar').innerHTML = chart.generateLegend();
            // }

        }

    }

})();