(function () {

    angular.module('printty')
        .component('qualityPieChart', {
            controller: PieChartCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/quality/chart/pie.html',
            bindings: {
                startDate: '=',
                endDate: '=',
                startTime: '=',
                endTime: '=',
                typeId: '<',
                clickSearch: '<',
                onlyLate: '<',
                factoryId: '=',
            }
        });


    function PieChartCtrl($element, Quality) {

        "ngInject";

        var ctrl = this;

        /******* variables *******/

        var chart = null;
        var taskDatasets = [0];

        var ctx = $element.find('canvas').get(0).getContext("2d");

        var taskLabels = [];

        var colors = [];
        var colorsReturned = 0;

         var config = {
            type: 'pie',
            data: {
                labels: taskLabels,
                datasets: [{
                    data: taskDatasets,
                    backgroundColor: ['#999999']
                }]
            },
            options: {
                animation: {
                    animateScale: true,
                    animateRotate: true
                },
                legend: {
                    position: 'bottom',
                },
                tooltips: {
                    enabled: true,
                    mode: 'single',
                    callbacks: {
                        label: function(tooltipItem, data) {
                            var allData = data.datasets[tooltipItem.datasetIndex].data;
                            var tooltipLabel = data.labels[tooltipItem.index];
                            var tooltipData = allData[tooltipItem.index];

                            //calculate the total of this data set
                            var total = allData.reduce(function(previousValue, currentValue, currentIndex, array) {
                                return previousValue + currentValue;
                            });
                            //calculate the precentage based on the total and current item, also this does a rough rounding to give a whole number
                            var percentage = Math.floor(((tooltipData/total) * 100)+0.5);

                            return tooltipLabel + ": "+ tooltipData +" (" + percentage + "%)";
                        }
                    }
                },
                legendCallback: function(chart) {
                    var text = [];
                    text.push('<table id="chart-detail-table">');
                    text.push(
                        '<tr>' +
                            '<td>行ラベル</td>' +
                            '<td>合計/数量</td>' +
                        '</tr>');
                    var total = 0;
                    for (var i = 0; i < chart.data.labels.length; i++) {
                        text.push(
                            '<tr>' +
                                '<td>'+ chart.data.labels[i] +'</td>' +
                                '<td>'+ chart.data.datasets[0].data[i] +'</td>' +
                            '</tr>'
                        );
                        total += chart.data.datasets[0].data[i];
                    }
                    text.push(
                        '<tr>' +
                            '<td>総計</td>' +
                            '<td>'+ total +'</td>' +
                        '</tr>');
                    text.push('</table>');
                    return text.join('');
                }
            }
        };

        /***** methods ******/

        ctrl.$onInit = function () {
            if(!chart && taskDatasets && taskLabels) {
                chart = new Chart(ctx, config);
            }

        };

        ctrl.$onChanges = function () {
            fetchData();
        };

        function fetchData() {
            var start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            var end_date =  moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');

            colorsReturned = 0;

            Quality.fetchGraph({
                start_date: start_date,
                end_date: end_date,
                type_id: ctrl.typeId,
                only_late: ctrl.onlyLate,
                chart_type: 'pie',
                factory_id: ctrl.factoryId,
            })
                .then(function (data) {
                    taskDatasets = data.datasets;
                    taskLabels = data.labels;
                    createChart();

                });

        }

        function createChart() {

            for(var i = 0; i < taskDatasets.length ; i++) {
                var background = randomColor(1);
                chart.data.datasets[0].backgroundColor.push(background);
            }

            // if(taskDatasets.length > 0 && taskLabels.length > 0) {
                if(!chart) {
                    chart = new Chart(ctx, config);
                } else {
                    chart.data.labels = taskLabels;
                    chart.data.datasets[0].data = taskDatasets;
                    chart.update();
                }
                document.getElementById('chart-legends-pie').innerHTML = chart.generateLegend();
            // }

        }

        function randomColorFactor() {
            return Math.round(Math.random() * 255);
        }

        function randomColor(opacity) {

            if (colors.length >= colorsReturned + 1) {
                return colors[colorsReturned++];
            }

            return 'rgba(' + randomColorFactor() + ',' + randomColorFactor() + ',' + randomColorFactor() + ',' + (opacity || '.5') + ')';
        }

    }

})();