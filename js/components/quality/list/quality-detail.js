(function () {

    angular.module('printty')
        .component('qualityDetails', {
            controller: QualityDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/quality/quality-detail.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                task: '<',
                taskStepId: '<',
                productTypeCode: '<',
                active: '='
            }
        });

    function QualityDetailsCtrl($scope, utils, Quality, $mdDialog, $mdSidenav, $timeout) {

        "ngInject";

        /****** variables ******/
        var ctrl = this;

        ctrl.categories = [];
        ctrl.delivery_late = false;
        ctrl.kenpinTaskNG = null;

        ctrl.loadingType = 'full';

        var sidenav = {
            open: function () {
                $mdSidenav('quality-details').open();
            },
            close: function () {
                $mdSidenav('quality-details').close();
            }
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            $mdSidenav('quality-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {

            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };

        ctrl.close = function () {
            sidenav.close();
        };

        /******** private **********/

        function initComponent() {
            ctrl.loadingType = 'full';
            Quality.fetchKenpinStatus({
                taskId: ctrl.taskStepId,
                productTypeCode : ctrl.productTypeCode,
            })
                .then(function (data) {
                    ctrl.categories = data.status;
                    ctrl.delivery_late = data.late;
                    ctrl.loadingType = 'stopped';
                });

            Quality.fetchKenpinTaskNG({
                kenpin_task_id: ctrl.task.kenpin_task_id,
                task_id: ctrl.task.task_id,
            }).then(function (data) {
                ctrl.kenpinTaskNG = data;

                ctrl.loadingType = 'stopped';
            }, function () {
                ctrl.loadingType = 'stopped';
            });
            sidenav.open();

        }

        ctrl.confirmDelete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('D 品情報を削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(deleteTask);

        };

        function deleteTask() {

            ctrl.loadingType = 'partial';
            var statuses = getSelectedReson();
            var params = {
                kenpin_task_id :  ctrl.task.kenpin_task_id,
                reason_id : statuses,
                task_id: ctrl.task.task_id,
            };

            Quality.deleteQuality(params)
                .then(function () {
                    ctrl.loadingType = 'stopped';
                    sidenav.close();
                    ctrl.onDelete();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                    sidenav.close();
                    ctrl.onDelete();
                });
        }

        function getSelectedReson() {
            var selectedResons = [];

            _.forEach(ctrl.categories, function (category) {
                if (category.selected && category.KenpinStatus.quantity > 0) {
                    selectedResons.push(category.KenpinStatus.id);
                }
            });
            return selectedResons;

        }

        function getSelected() {
            var selectedItems = [];

            _.forEach(ctrl.categories, function (category) {
                if (category.selected && category.KenpinStatus.quantity > 0) {
                    selectedItems.push(
                        {
                            id: category.KenpinStatus.id,
                            quantity: category.KenpinStatus.quantity
                        });
                }
            });
            return selectedItems;

        }

        ctrl.save = function () {

            var statuses = getSelected();
            if ( statuses.length > 1 && typeof ctrl.kenpinTaskNG == "object" ) {
                alert('欠点内容はいずれか１つのみ選択して下さい。');
                return;
            }
            var data = {
                kenpin: statuses,
                delivery_late: ctrl.delivery_late ? 1: 0,
                kenpin_task_id: ctrl.task.kenpin_task_id,
                task_id: ctrl.task.task_id,
            };

            ctrl.loadingType = 'partial';
            Quality.updateQuality(data)
                .then(function () {
                    ctrl.loadingType = 'stopped';
                    sidenav.close();
                    ctrl.onUpdate();
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                    sidenav.close();
                    ctrl.onUpdate();
                });
        };

        function setDefault() {
            ctrl.loadingType = 'stopped';
        }

    }

})();