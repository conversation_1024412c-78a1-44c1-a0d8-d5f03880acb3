(function () {

    function QualityListCtrl(ProductionPrinters, utils, Auth, Store, Quality, Storage,$mdToast, $mdDialog, $mdpTimePicker,Factory) {

        "ngInject";

        var ctrl = this;

        var subscriptions = [],
            allSelected = false;

        ctrl.total = [];

        ctrl.chartPie = false;
        ctrl.chartBar = false;
        ctrl.isShown = false;
        ctrl.taskDetails = {};
        ctrl.done = true;
        ctrl.doneTask = true;
        ctrl.doneTotal = true;

        ctrl.pagination = {
            type_id: null,
            start_date: null,
            end_date: null,
            only_late: 0,

            searchQuery: '',
            lastSearch: null,

            empty: false,
            reload: false,

            loading: false,
            disabled: true
        };

        // ctrl.pagination = pagination;
        ctrl.tasks = [];

        ctrl.clickSearch = false;

        var start_date = moment().subtract(1, 'day').format('YYYY-MM-DD');
        var end_date = moment().format('YYYY-MM-DD');

        ctrl.startDate = moment(start_date + ' 14:40', 'YYYY-MM-DD HH:mm').toDate();
        ctrl.endDate = moment(end_date + ' 14:40', 'YYYY/MM/DD HH:mm').toDate();

        ctrl.startTime = moment(start_date + ' 14:40', 'YYYY-MM-DD HH:mm').toDate();
        ctrl.endTime = moment(end_date + ' 14:40', 'YYYY/MM/DD HH:mm').toDate();

        ctrl.source_product = null;

        ctrl.reload = function() {
            ctrl.pagination.reload = true;

            ctrl.tasks = [];

            if(ctrl.done) {
                ctrl.done = false;

                getTotal();

                fetchTasks();
            }
        };

        ctrl.showTaskLate = function(){
            ctrl.pagination.only_late = ctrl.pagination.only_late ? 0: 1;
            ctrl.reload();
        };

        ctrl.search = function() {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function() {
            utils.delay(function() {
                ctrl.search();
            }, 350);
        };

        ctrl.pagination.clearSearch = function() {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        /****** private ******/
        function fetchTasks() {
            ctrl.pagination.disabled = true;
            ctrl.doneTask = false;
            // show loading screen if reloading
            if (ctrl.pagination.reload) {
                ctrl.pagination.loading = false;
                utils.listLoading.show();
            } else {
                ctrl.pagination.loading = true;
            }

            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');

            Quality.fetchQuality(
                {
                    start_date: ctrl.pagination.start_date,
                    end_date: ctrl.pagination.end_date,
                    type_id: ctrl.pagination.type_id,
                    keywords: ctrl.pagination.searchQuery,
                    only_late: ctrl.pagination.only_late,
                    factory_id: ctrl.factory,
                }
            ).then(
                function (data) {
                    ctrl.tasks = ctrl.tasks.concat(data.tasks);

                    if (allSelected) {
                        _.forEach(data.tasks, function (task) {
                            task.selected = allSelected;
                        });
                    }
                    ctrl.pagination.empty = !ctrl.tasks.length;
                    ctrl.pagination.loading = false;
                    ctrl.pagination.reload = false;

                    ctrl.pagination.disabled = data.total_count <= ctrl.tasks.length;
                    ctrl.doneTask = true;
                    done();
                    utils.listLoading.hide();
                }
            );
        }

        /*** private ****/
        function getTypes() {

            ProductionPrinters.fetchTypes().then(function(data) {
                var statuses = _.flatMap(data.types, function(type) {
                    return type.PrinterType;
                });

                Store.data('statuses').update(statuses);
            });

        }

        ctrl.onDateChange = function () {
            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            // to Chart catch event
            ctrl.clickSearch = !ctrl.clickSearch;
            ctrl.reload();
        };


        function getTotal(){

            ctrl.doneTotal = false;

            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');

            Quality.fetchTotal(
                {
                    start_date: ctrl.pagination.start_date,
                    end_date: ctrl.pagination.end_date,
                    only_late: ctrl.pagination.only_late,
                    keywords: ctrl.pagination.searchQuery,
                    factory_id: ctrl.factory,
                }
            ).then(
                function (data) {
                    ctrl.doneTotal = true;
                    ctrl.total = data;
                    done();
                }
            );
        };
        function fetchFactory(){
            Factory.fetchFactoryUser().then(
                function (data) {
                    ctrl.factory = data.factorySelected;
                    getTotal();
                    fetchTasks();
                    ctrl.factories  = data.factories;
                    ctrl.chart = true;
                }
            );
        }

        function done(){
            if(ctrl.doneTotal && ctrl.doneTask){
                ctrl.done = true;
            }
        }

        ctrl.$onInit = function () {
            ctrl.pagination.reload = true;
            if(!ctrl.factory) fetchFactory();
            getTypes();

            subscriptions.push(Store.event('sidebarActiveStatus')
                .subscribe(function (status) {
                    onStatusChange(status)
                }));

            ctrl.pagination.start_date = moment(ctrl.startDate).format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(ctrl.endDate).format('YYYY-MM-DD HH:mm');

            utils.appLoaded();
        };

        function onStatusChange(status) {
            ctrl.pagination.type_id = status;
            ctrl.reload();
        }

        ctrl.CSVdownloadWithSource = function (source_name) {
            ctrl.source_product = source_name;
            ctrl.CSVdownload();
            ctrl.source_product = null;
        }

        ctrl.CSVdownload = function () {
            if(!ctrl.factory) return;
            var selected;

            selected = getSelected();

            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');

            if ((_.isArray(selected) && !selected.length) || allSelected) {

                utils.globalLoading.show();

                var params = {
                    start_date: ctrl.pagination.start_date,
                    end_date: ctrl.pagination.end_date,
                    type_id: ctrl.pagination.type_id,
                    keywords: ctrl.pagination.searchQuery,
                    source_product: ctrl.source_product,
                    only_late: ctrl.pagination.only_late,
                    is_seiren: 0,
                    factory_id: ctrl.factory,
                };

                Quality.downloadCSV(params)
                    .then(function () {
                        utils.globalLoading.hide();
                    })
                    .catch(function () {
                        utils.globalLoading.hide();
                    });
            } else {

                utils.globalLoading.show();

                var params = {
                    tasks: selected,
                    source_product: ctrl.source_product,
                    factory_id: ctrl.factory,
                };

                Quality.downloadCSVSelected(params)
                    .then(function () {
                        utils.globalLoading.hide();
                    })
                    .catch(function () {
                        utils.globalLoading.hide();
                    });
            }
        };

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.tasks, function (task) {

                if (task.selected) {
                    selectedItems.push(task);
                }

            });

            return selectedItems;

        }

        ctrl.selectAll = function () {

            ctrl.tasks.forEach(function (task) {
                task.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

        ctrl.qualityPicking = function() {
            var selected;

            selected = getSelected();

            if ((_.isArray(selected) && !selected.length) && !allSelected) return;

            utils.globalLoading.show();
            var params = {};

            ctrl.pagination.start_date = moment(
                moment(ctrl.startDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.startTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');
            ctrl.pagination.end_date = moment(
                moment(ctrl.endDate).format('YYYY-MM-DD') + ' ' + moment(ctrl.endTime).format('HH:mm'),
                'YYYY-MM-DD HH:mm')
                .format('YYYY-MM-DD HH:mm');

            if (allSelected) {
                params = {
                    start_date: ctrl.pagination.start_date,
                    end_date: ctrl.pagination.end_date,
                    type_id: ctrl.pagination.type_id,
                    keywords: ctrl.pagination.searchQuery,
                    only_late: ctrl.pagination.only_late,
                    factory_id: ctrl.factory,
                };
            } else {
                params = {
                    tasks: selected,
                    factory_id: ctrl.factory,
                };
            }

            Storage.picking.checkDuplicate(params).then(function (data) {

                if (data.invalid) {
                    utils.globalLoading.hide();

                    var confirm = $mdDialog.confirm({
                        template: utils.changeStatusTemplate('もう1度D品ピッキングをします。よろしいですか？'),
                        controller: ['$scope', '$mdDialog', function ($scope, $mdDialog) {
                            $scope.cancelDelete = function () {
                                utils.globalLoading.hide();
                                $mdDialog.cancel();
                            };
                            $scope.confirmDelete = function () {
                                $mdDialog.hide();
                            };
                        }]
                    });

                    $mdDialog.show(confirm).then(function () {
                        utils.globalLoading.show();

                        Storage.picking.pick(data).then(function () {

                            $mdToast.show(
                                $mdToast.simple()
                                    .textContent('ピッキング成功')
                                    .position('top left')
                                    .toastClass('printty-toast')
                                    .hideDelay(2000)
                            );

                            utils.globalLoading.hide();
                        }, function () {
                            utils.globalLoading.hide();
                        });
                    });

                } else {
                    Storage.picking.pick(data).then(function () {

                        $mdToast.show(
                            $mdToast.simple()
                                .textContent('ピッキング成功')
                                .position('top left')
                                .toastClass('printty-toast')
                                .hideDelay(2000)
                        );

                        utils.globalLoading.hide();
                    }, function () {
                        utils.globalLoading.hide();
                    });
                }
            }, function () {
                utils.globalLoading.hide();
            });

        }

        ctrl.showPieChart = function() {
            ctrl.chartPie = !ctrl.chartPie;
            ctrl.chartBar = false;
        }

        ctrl.showBarChart = function() {
            ctrl.chartBar = !ctrl.chartBar;
            ctrl.chartPie = false;
        }

        ctrl.loadMore = function () {

            if( ctrl.pagination.disabled) return;

            fetchTasks();
        };

        ctrl.viewDetails = function (task) {
            ctrl.isShown = true;
            ctrl.taskDetails = task.data;
            ctrl.taskDetails.type_id = task.data.type_id;
            ctrl.taskDetails.task_step_id = task.data.task_step_id;
        };

        ctrl.onTaskClose = function () {
            ctrl.isShown = false;
        };

        ctrl.onTaskCreate = function () {
            ctrl.reload();
        };

        ctrl.onTaskUpdate = function (task) {
            ctrl.reload();
        };

        ctrl.onTaskDelete = function (taskId) {
            ctrl.reload();
        };

    }

    angular.module('printty')

        .component('qualityList', {
            require: {
                parentCtrl: '^^qualityComponent'
            },
            controller: QualityListCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/quality/list/list.html'
        });

})();