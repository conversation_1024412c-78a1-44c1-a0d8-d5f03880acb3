(function () {

  angular.module('printty')
    
    .filter('dateJapan', function () {
      return function (input, mode) {
        var date = moment(input);
        date.locale('ja');
        
        if (mode && mode == 'month') {
          return date.format('ll').slice(0, -2);
        } else if (mode && mode == 'day') {
          return date.format('Do(dd)');
        } else {
          return date.format('ll');
        }
        
      };
    })

    .filter('userTime', function($filter) {

      "ngInject";
      window.$filter = $filter;

      return function(input) {
        if(input)
        {
          return moment.utc(input,"YYYY-MM-DD HH:mm:ss").local().format("YYYY.MM.DD HH:mm:ss");
        }
        // if (input) {
        //   return $filter('date')(input.replace(' ', 'T') + '+0000', 'yyyy.MM.dd HH:mm:ss');
        // }
        
      };
    })
    
    .filter('printtyIntegers', function() {

      "ngInject";

      return function(input) {
        
        var num = _.toInteger(input);
        
        if (num > 0) {
          return '+ ' + num;
        } else if (num < 0) {
          return '- ' + num * (-1)
        } else {
          return num;
        }

      };
    });
  
})();