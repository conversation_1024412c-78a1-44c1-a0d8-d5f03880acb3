(function () {

  angular.module('printty', [ 'ngMaterial', 'ngMessages', 'ngAnimate', 'ui.router', 'ngclipboard', 'ngFileUpload', 'ngCookies', 'infinite-scroll', 'electangular', 'monospaced.qrcode','multipleDatePicker', 'bw.paging', 'md.time.picker', 'ui.sortable'])

    .run(function ($rootScope, $state) {

      "ngInject";
      
      /** Adding redirectTo param, usable in route state config **/
      $rootScope.$on('$stateChangeStart', function(evt, to, params) {
        if (to.redirectTo) {
          evt.preventDefault();
          $state.go(to.redirectTo, params)
        }
      });

      $rootScope.$on('$stateChangeError', function (event, toState, toParams, fromState, fromParams, error) {
        
        if (error === 'Session.NotFound') {
          if (!$state.includes('auth')) {
            $state.go('auth.signin');
          }
        }

      });

    });

})();