(function () {

    angular.module('printty')

        .factory('RepairApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                search: search,
                fetchNumberQrCodeByDate: fetchNumberQrCodeByDate,
            };

            function search(code) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    code: code
                };

                ApiService.get('/repair/task/search', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchNumberQrCodeByDate(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/repair/getNumberQr', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
