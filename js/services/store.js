(function () {

  angular.module('printty')

    .factory('Store', function() {

      "ngInject";
      
      var _store = {
        data: [],
        events: []
      };
      
      var _service = {
        data: getData,
        event: getEvent
      };
      
      return _service;
      
      function getData(name) {
        
        var dataStore = findStoreByName('data', name);
        
        if (!dataStore) {
          dataStore = {
            name: name,
            store: new DataStore()
          };
          
          _store.data.push(dataStore);
        }
        
        return dataStore.store;
        
      }
      
      function getEvent(name) {

        var eventStore = findStoreByName('events', name);

        if (!eventStore) {
          eventStore = {
            name: name,
            store: new EventStore()
          };

          _store.events.push(eventStore);
        }
        
        return eventStore.store;
        
      }
      
      /** private ***/
      
      function EventStore() {

        var _subscriptions = [];
        
        this.emit = function (data) {
          callAll(data);
        };

        this.subscribe = function (callback) {
          
          if (!_.isFunction(callback)) return;
          
          var subscription = new Subscription(callback, deleteSubscription);
          
          _subscriptions.push(subscription);

          return subscription;
          
        };

        function callAll(data) {
          _.forEach(_subscriptions, function (subscription) {
            subscription.emit(data);
          });
        }

        function deleteSubscription(objRef) {
          _.remove(_subscriptions, objRef);
        }
        
      }

      function DataStore() {

        var _data = null,
            _subscriptions= [];

        this.update = function (data) {
          _data = _.cloneDeep(data);
          callAll(_data);
        };
        
        this.get = function () {
          return _data;
        };

        this.subscribe = function (callback) {

          if (!_.isFunction(callback)) return;

          var subscription = new Subscription(callback, deleteSubscription);

          _subscriptions.push(subscription);
          subscription.emit(_data);
          
          return subscription;

        };

        function callAll(data) {
          _.forEach(_subscriptions, function (subscription) {
            subscription.emit(data);
          });
        }

        function deleteSubscription(objRef) {
          _.remove(_subscriptions, objRef);
        }
        
      }
      
      function Subscription(callback, deleteFunc) {
        
        this.emit = function(data) {
          callback(data);
        };
        
        this.unsubscribe = function () {
          deleteFunc(this);
        }
        
      }
      
      function findStoreByName(type, name) {
        return _.find(_store[type], function (storeItem) {
          return storeItem.name === name;
        });
      }
      
    });

})();