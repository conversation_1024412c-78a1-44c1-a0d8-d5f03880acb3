(function () {

    angular.module('printty')

        .factory('ImageDelivery', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                deliveries: '/images/deliveries',
                reloadDelivery: '/images/deliveries/reload',
            };

            return {
                fetchSentDelivery: fetchSentDelivery,
                reloadDelivery: reloadDelivery,
                deleteDelivery: deleteDelivery,
            };

            function fetchSentDelivery(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.deliveries, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadDelivery(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadDelivery, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteDelivery(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.deliveries, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
