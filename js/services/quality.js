(function () {

    angular.module('printty')

        .factory('Quality', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                fetchQuality:fetchQuality,
                fetchTotal: fetchTotal,
                downloadCSV: downloadCSV,
                downloadCSVSelected: downloadCSVSelected,
                fetchGraph: fetchGraph,
                deleteQuality: deleteQuality,
                updateQuality: updateQuality,
                fetchKenpinStatus: fetchKenpinStatus,
                fetchKenpinTaskNG: fetchKenpinTaskNG,
                reason : {
                    fetch : fetchReasons,
                    detail : fetchReason,
                    create : createReason,
                    update : updateReason,
                    sort : sortReason
                }
            };

            function fetchQuality(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchReasons(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality/reasons', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchReason(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality/reason', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateReason(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.put('/quality/reason', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function sortReason(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.post('/quality/reason/sort', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createReason(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.post('/quality/reason', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }


            function deleteQuality(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.delete('/quality', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateQuality(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.put('/quality', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchGraph(params) {
                var deferred = $q.defer();

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality/chart', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchTotal(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality/total', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get('/quality/csv', getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVSelected(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.post('/quality/csv/selected', getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchKenpinStatus(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get('/quality/quality', getParams, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    }
                );

                return deferred.promise;
            }

            function fetchKenpinTaskNG(params) {
                var deferred = $q.defer(2000);

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/quality/kenpin/task-NG', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }


        });

})();
