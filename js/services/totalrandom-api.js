(function () {

    angular.module('printty')

        .factory('TotalRandomApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                getTotalRandomData: getTotalRandomData,
            };

            function getTotalRandomData(date) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    date : date
                };

                ApiService.get('/random-mode/total', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });

})();