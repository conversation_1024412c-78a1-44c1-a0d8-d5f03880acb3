(function () {

    angular.module('printty')

        .factory('Color', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                colors: '/colors',
                color: '/colors/color',
                csvFile: '/colors/csv',
                csvFileTemplate: '/colors/csv/template',
            };

            return {
                fetchColors: fetchColors,
                createColor: createColor,
                fetchColor: fetchColor,
                updateColor: updateColor,
                deleteColor: deleteColor,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
            };

            function fetchColors(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.colors, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createColor(colorData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Color: colorData
                };

                ApiService.post(API_MAP.color, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchColor(colorId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    color_id: colorId
                };

                ApiService.get(API_MAP.color, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateColor(colorData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductColor: colorData
                };

                ApiService.put(API_MAP.color, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteColor(colorId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductColor: {
                        id: colorId
                    }
                };

                ApiService.delete(API_MAP.color, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate() {
                var deferred = $q.defer();

                var getParams = {session: Auth.session.get()};

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
