(function () {

    angular.module('printty')

        .factory('Size', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                sizes: '/sizes',
                size: '/sizes/size',
                csvFile: '/sizes/csv',
                csvFileTemplate: '/sizes/csv/template',
            };

            return {
                fetchSizes: fetchSizes,
                createSize: createSize,
                fetchSize: fetchSize,
                updateSize: updateSize,
                deleteSize: deleteSize,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
            };

            function fetchSizes(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.sizes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createSize(sizeData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Size: sizeData
                };

                ApiService.post(API_MAP.size, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSize(sizeId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    size_id: sizeId
                };

                ApiService.get(API_MAP.size, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateSize(sizeData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductSize: sizeData
                };

                ApiService.put(API_MAP.size, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteSize(sizeId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductSize: {
                        id: sizeId
                    }
                };

                ApiService.delete(API_MAP.size, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
