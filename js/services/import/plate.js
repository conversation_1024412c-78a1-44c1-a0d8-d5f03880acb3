(function () {

    angular.module('printty')

        .factory('ProductPlate', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                plates: '/prints',
                plate: '/prints/plate',
                csvFile: '/prints/csv',
                csvFileTemplate: '/prints/csv/template',
                fetchAllPlates: 'prints/proPlate'
            };

            return {
                fetchPlates: fetchPlates,
                createPlate: createPlate,
                fetchPlate: fetchPlate,
                updatePlate: updatePlate,
                deletePlate: deletePlate,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
                fetchAllPlates: fetchAllPlates,
            };

            function fetchAllPlates() {
                var deferred = $q.defer();

                var getParams = { session: Auth.session.get() };

                ApiService.get(API_MAP.fetchAllPlates, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchPlates(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.plates, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createPlate(plateData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Plate: plateData
                };

                ApiService.post(API_MAP.plate, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchPlate(plateId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    plate_id: plateId
                };

                ApiService.get(API_MAP.plate, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updatePlate(plateData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductColorSideSizeLinkedPlate: plateData
                };

                ApiService.put(API_MAP.plate, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deletePlate(plateId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductColorSideSizeLinkedPlate: {
                        id: plateId
                    }
                };

                ApiService.delete(API_MAP.plate, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate() {
                var deferred = $q.defer();

                var getParams = {session: Auth.session.get()};

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
