(function () {

    angular.module('printty')

        .factory('Side', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                sides: '/sides',
                side: '/sides/side',
                csvFile: '/sides/csv',
                csvFileTemplate: '/sides/csv/template',
                imgFile: '/sides/img',
            };

            return {
                fetchSides: fetchSides,
                createSide: createSide,
                fetchSide: fetchSide,
                updateSide: updateSide,
                deleteSide: deleteSide,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
                uploadImg: uploadImg,
                uploadImgCustom: uploadImgCustom,
            };

            function fetchSides(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.sides, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createSide(sideData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Side: sideData
                };

                ApiService.post(API_MAP.side, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSide(sideId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    side_id: sideId
                };

                ApiService.get(API_MAP.side, getParams).then(
                    function(data) {
                        data.ProductColorSide.content = JSON.stringify( JSON.parse(data.ProductColorSide.content), null, '\t' );
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateSide(sideData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductColorSide: sideData
                };

                ApiService.put(API_MAP.side, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteSide(sideId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductColorSide: {
                        id: sideId
                    }
                };

                ApiService.delete(API_MAP.side, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadImg(file, sideId) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    img: file,
                    id : sideId
                };

                ApiService.upload(API_MAP.imgFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadImgCustom(data, url) {
                var deferred = $q.defer();

                var uploadData = _.merge(
                    { session: Auth.session.get() },
                    data
                );

                ApiService.upload(url, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate() {
                var deferred = $q.defer();

                var getParams = {session: Auth.session.get()};

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
