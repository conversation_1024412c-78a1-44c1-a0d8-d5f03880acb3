(function () {

    angular.module('printty')

        .factory('Vakuum', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                vakuums: '/vakuums',
                vakuum: '/vakuums/vakuum',
                csvFile: '/vakuums/csv',
                csvFileTemplate: '/vakuums/csv/template',
            };

            return {
                fetchVakuums: fetchVakuums,
                createVakuum: createVakuum,
                fetchVakuum: fetchVakuum,
                updateVakuum: updateVakuum,
                deleteVakuum: deleteVakuum,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
            };

            function fetchVakuums(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.vakuums, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createVakuum(vakuumData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Vakuum: vakuumData
                };

                ApiService.post(API_MAP.vakuum, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchVakuum(vakuumId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    vakuum_id: vakuumId
                };

                ApiService.get(API_MAP.vakuum, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateVakuum(vakuumData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductVakuumOption: vakuumData
                };

                ApiService.put(API_MAP.vakuum, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteVakuum(vakuumId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductVakuumOption: {
                        id: vakuumId
                    }
                };

                ApiService.delete(API_MAP.vakuum, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate() {
                var deferred = $q.defer();

                var getParams = {session: Auth.session.get()};

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
