(function () {

    angular.module('printty')

        .factory('ImportProduct', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                products: '/import/products',
                product: '/import/products/product',
                csvFile: '/import/products/csv',
                csvFileTemplate: '/import/products/csv/template',
                csvFileBatch: '/import/products/csv/batch',
            };

            return {
                fetchProducts: fetchProducts,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                CSVdownloadTemplate: CSVdownloadTemplate,
                downloadCSVBatch: downloadCSVBatch
            };

            function fetchProducts(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.products, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVBatch(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileBatch, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function CSVdownloadTemplate(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
