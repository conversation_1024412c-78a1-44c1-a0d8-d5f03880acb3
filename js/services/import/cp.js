(function () {

    angular.module('printty')

        .factory('Cp', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                cps: '/cps',
                cp: '/cps/cp',
                csvFile: '/cps/csv',
                csvFileTemplate: '/cps/csv/template',
            };

            return {
                fetchCps: fetchCps,
                createCp: createCp,
                fetchCp: fetchCp,
                updateCp: updateCp,
                deleteCp: deleteCp,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
            };

            function fetchCps(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.cps, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createCp(cpData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Cp: cpData
                };

                ApiService.post(API_MAP.cp, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchCp(cpId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    cp_id: cpId
                };

                ApiService.get(API_MAP.cp, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateCp(cpData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductCPOption: cpData
                };

                ApiService.put(API_MAP.cp, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteCp(cpId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    ProductCPOption: {
                        id: cpId
                    }
                };

                ApiService.delete(API_MAP.cp, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate() {
                var deferred = $q.defer();

                var getParams = {session: Auth.session.get()};

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
