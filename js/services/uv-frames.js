(function () {
    angular.module('printty')

        .factory('UvFrames', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                uvs: '/uvframes',
                uv: '/uvframe',
                csvFile: '/import/uvframes/csv',
                csvFileTemplate: '/import/uvframes/csv/template',
            };

            return {
                fetchUvs: fetchUvs,
                fetchUv: fetchUv,
                createUv: createUv,
                updateUv: updateUv,
                deleteUv: deleteUv,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                CSVdownloadTemplate: CSVdownloadTemplate,
                sort: sort,
            };

            function fetchUvs(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.uvs, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchUv(uvId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    uv_frame_id: uvId
                };

                ApiService.get(API_MAP.uv, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createUv(uv) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    UvFrame: uv
                };

                ApiService.put(API_MAP.uv, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateUv(uv) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    UvFrame: uv
                };

                ApiService.post(API_MAP.uv, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteUv(uvId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    UvFrame: {
                        id: uvId
                    }
                };

                ApiService.delete(API_MAP.uv, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function CSVdownloadTemplate(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function sort(params) {
                var deferred = $q.defer(2000);

                ApiService.post('/uvframes/sort', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();