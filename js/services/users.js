(function () {

  angular.module('printty')

    .factory('Users', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        users: '/users',
        rolesCond: '/users/roles',
        rolesChange: '/users/user/roles',
        user: '/users/user'
      };

      return {
        fetchUsers: fetchUsers,
        
        fetchRolesCond: fetchRoleCond,
        fetchRolesChange: fetchRolesChange,
        
        fetchUser: fetchUser,
        createUser: createUser,
        updateUser: updateUser,
        deleteUser: deleteUser,
        downloadDeliveryCompanyCSV: downloadDeliveryCompany,
      };

      function fetchUsers(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.users, getParams).then(
          function(data) {
            
            _.forEach(data.users, function (user) {
              user.User.is_self = ApiService.toBoolean(user.User.is_self);
              user.User.role_id = ApiService.toNumber(user.User.role_id);
            });
            
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchRoleCond() {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get()
        };

        ApiService.get(API_MAP.rolesCond, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchRolesChange() {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get()
        };

        ApiService.get(API_MAP.rolesChange, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchUser(userId) {
        const deferred = $q.defer();

        const getData = {
          session: Auth.session.get(),
          user_id: userId
        };

        ApiService.get(API_MAP.user, getData).then(
          function(data) {
            data.User.is_self = ApiService.toBoolean(data.User.is_self);
            data.User.role_id = ApiService.toNumber(data.User.role_id);
            data.User.is_direct_export = ApiService.toBoolean(data.User.is_direct_export);
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createUser(userData) {
        var deferred = $q.defer();

        var putData  = {
          session: Auth.session.get(),
          User: userData.User,
          Customer: userData.Customer
        };

        ApiService.put(API_MAP.user, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function updateUser(userData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          User: _.cloneDeep(userData.User),
          Customer: userData.Customer
        };
        
        if (!postData.User.password) {
          delete postData.User.password
        }

        postData.User.is_direct_export = postData.User.is_direct_export ? 1 : 0;

        ApiService.post(API_MAP.user, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteUser(userId) {
        var deferred = $q.defer();

        var deleteData  = {
          session: Auth.session.get(),
          User: {
            id: userId
          }
        };

        ApiService.delete(API_MAP.user, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function downloadDeliveryCompany() {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get(),
            };

            ApiService.get('/users/csv', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
      