(function () {
    angular.module('printty')

        .factory('StorageItemApi', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                storageItems: '/storage-items',
                storageItem: '/storage-item',
                products: '/storage-item/products',
                colorsSizes: '/storage-item/product/colors-sizes',
            };

            return {
                fetchStorageItems: fetchStorageItems,
                fetchProductsByType: fetchProductsByType,
                fetchColorSizeByProduct: fetchColorSizeByProduct,
                fetchStorageItem: fetchStorageItem,
                createStorageItem: createStorageItem,
                updateStorageItem: updateStorageItem,
                deleteStorageItem: deleteStorageItem,
            };

            function fetchStorageItems(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.storageItems, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductsByType(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.products, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchColorSizeByProduct(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.colorsSizes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchStorageItem(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.storageItem, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createStorageItem(factory_id, product_id, colors_sizes) {
                var deferred = $q.defer();

                var param = {
                    session: Auth.session.get(),
                    StorageItem: {
                        factory_id: factory_id,
                        product_id: product_id,
                        colors_sizes: colors_sizes,
                    }
                };

                ApiService.post(API_MAP.storageItem, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateStorageItem(factory_id, product_id, colors_sizes) {
                var deferred = $q.defer();

                var param = {
                    session: Auth.session.get(),
                    StorageItem: {
                        factory_id: factory_id,
                        product_id: product_id,
                        colors_sizes: colors_sizes,
                    }
                };

                ApiService.put(API_MAP.storageItem, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteStorageItem(product_id,colors_sizes) {
                var deferred = $q.defer();

                var param = {
                    session: Auth.session.get(),
                    StorageItem: {
                        product_id: product_id,
                        colors_sizes: colors_sizes,
                    }
                };

                ApiService.delete(API_MAP.storageItem, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();