(function () {

    angular.module('printty')

        .factory('RandomUvApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                getRandomData: getRandomData,
                updateOrderTarget: updateOrderTarget
            };

            function getRandomData(date) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    date : date
                };

                ApiService.get('/randomuv-mode', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateOrderTarget(quantity,date) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    date : date,
                    quantity : quantity
                };

                ApiService.post('/updateOrderUvTarget', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
