(function () {

  angular.module('printty')

    .factory('ApiService', function (API, $http, $q, utils, Upload) {

      "ngInject";

      return {
        get: get,
        post: post,
        put: put,
        delete: deleteApi,
        download: download,
        upload: upload,
        
        toBoolean: toBoolean,
        toNumber: toNumber,
        getFileNameFromUrl: getFileNameFromUrl
      };

      function get(path, params, options) {
        var deferred = $q.defer();
        
        var api = utils.buildUrl(
          API.path + path,
          params
        );
        
        $http.get(api).then(
          function(response) {
            deferred.resolve(response.data);
          },
          function(error) {
            if (!options || !(options.disableErrorHandle === true)) {
              handleError(error);
            }
            
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function post(path, postData, options) {
        var deferred = $q.defer();
        
        $http.post(API.path + path, postData).then(
          function(response) {
            deferred.resolve(response.data);
          },
          function(error) {
            if (!options || !(options.disableErrorHandle === true)) {
              handleError(error);
            }
            
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function put(path, postData, options) {
        var deferred = $q.defer();

        $http.put(API.path + path, postData).then(
          function(response) {
            deferred.resolve(response.data);
          },
          function(error) {
            if (!options || !(options.disableErrorHandle === true)) {
              handleError(error);
            }

            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function deleteApi(path, deleteData, options) {
        var deferred = $q.defer();

        $http.delete(API.path + path, {
          data: deleteData
        }).then(
          function(response) {
            deferred.resolve(response.data);
          },
          function(error) {
            if (!options || !(options.disableErrorHandle === true)) {
              handleError(error);
            }

            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function download(url, fileOptions) {
        var deferred = $q.defer();
        
        $http.get(url, {
          responseType: 'arraybuffer'
        }).then(
          function(response) {

            var blob, name = null, options = {};
            
            if (fileOptions.type) {
              options.type = fileOptions.type;
            } else {
              options.type = 'application/octet-stream';
            }
            
            if (fileOptions.name) {
              name = fileOptions.name;
            }
            
            blob = new Blob([ response.data ], options);

            saveAs(blob, name);

            deferred.resolve(response.data);

          },
          function(error) {
            handleError(error);
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function upload(path, uploadData, options) {
        var deferred = $q.defer();

        Upload.upload({
          url: API.path + path,
          data: uploadData
        })
          .then(function (response) {
            deferred.resolve(response.data);
          })
          .catch(function (error) {
            if (!options || !(options.disableErrorHandle === true)) {
              handleError(error);
            }

            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function toBoolean(value) {

        if (_.isBoolean(value)) {
          return value;
        }

        if (_.isString(value)) {
          return !(value === '0' || value === '' || value === 'false');
        }

        if (_.isNumber(value)) {
          return !!value;
        }

        return false; // default
        
      }
      
      function toNumber(value) {
        return _.toNumber(value);
      }
      
      function getFileNameFromUrl(url) {
        
        var filename = url.replace(/^.*[\\\/]/, '');
        
        if (filename) {
          return filename;
        } else {
          return null;
        }
        
      }
      
      /** helpers **/
      function handleError(error) {

        console.log(error);
        
        if (error.status === -1) {
          alert('Server error');
        } else if (error.status >= 500 && error.status <= 599) {
          alert('Server error (' + error.status + ')');
        } else {
          
          if (error.data.message) {
            alert(error.data.message);
          } else {
            alert('Unexpected error (' + error.status + ')');
          }
          
        }
        
      }
      
    });

})();
      