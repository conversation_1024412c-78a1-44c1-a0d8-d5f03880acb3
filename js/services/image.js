(function () {

    angular.module('printty')

        .factory('Image', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                images: '/images',
                image: '/images/image',
                reloadImage: '/images/reload',
                groupImages: '/images/group',
                reloadGroupImage: '/images/group/reload',
                printImages: '/images/image/print',
                imagesSublimation: '/images/print-image',
                printImagesSublimation: '/images/sublimation/print',
                paperPrint: '/images/paper/print',
                getPaperPrint: '/images/paper/print/csv',
                data3d: '/images/data-3d',
                getData3dZip: '/images/data-3d/zip',
                externalFactoryPrintData: '/images/external-factory-print-data',
                getDataPrint: '/images/print-data/zip',
            };

            return {
                fetchImages: fetchImages,
                reloadImage: reloadImage,
                fetchGroupImages: fetchGroupImages,
                reloadGroupImage: reloadGroupImage,
                deleteImage: deleteImage,
                deleteGroupImage :deleteGroupImage,
                getPrintImage: getPrintImage,
                fetchImagesSublimationDtf: fetchImagesSublimationDtf,
                getPrintSublimationImage: getPrintSublimationImage,
                fetchOrderPaperPrint: fetchOrderPaperPrint,
                getPrintPaper: getPrintPaper,
                fetchOrderData3D: fetchOrderData3D,
                downloadData3DZip: downloadData3DZip,
                fetchExternalFactoryPrintData: fetchExternalFactoryPrintData,
                getDataPrint: downloadDataPrint,
            };

            function fetchImages(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.images, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadImage, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.images, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function getPrintImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.printImages, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchGroupImages(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.groupImages, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadGroupImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadGroupImage, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteGroupImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.groupImages, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchImagesSublimationDtf(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.imagesSublimation, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function getPrintSublimationImage(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.post(API_MAP.printImagesSublimation, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);
                        var dataAll = data

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(dataAll);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOrderPaperPrint(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.paperPrint, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function getPrintPaper(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.getPaperPrint, getParams, {
                    headers: {
                        'Content-Type': 'application/json',
                    }}).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOrderData3D(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.data3d, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadData3DZip(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.getData3dZip, getParams, {
                    headers: {
                        'Content-Type': 'application/json',
                    }}).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }


            function fetchExternalFactoryPrintData(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.externalFactoryPrintData, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadDataPrint(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.getDataPrint, getParams, {
                    headers: {
                        'Content-Type': 'application/json',
                    }}).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });

})();
