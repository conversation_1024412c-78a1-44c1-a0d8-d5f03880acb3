(function () {

  angular.module('printty')

    .factory('Storage', function (ApiService, $q, Auth, $filter) {

      "ngInject";
      
      return {
        list: listStorage,
        details: detailsStorage,
        productActivity: productActivity,
        
        typesAll: typesAll,

        productQuantity: {
          add: addProductQuantity,
          remove: removeProductQuantity
        },

        picking:{
            getPicking :picking,
            pick: pick,
            checkDuplicate : checkDuplicate,
            getPickTaskGroup: getPickTaskGroup
        },

        storage:storage,
        
        activity: storageActivity,
        deleteActivity: deleteActivity,
        csv : {
            prepare: prepareCsv,
            upload: uploadCsv,
            download: downloadCSV,
            downloadShipment: downloadShipmentCSV
        },

        fetchFilters : fetchFilters,
        fetchCertificate : fetchCertificate,
        createCertificate: createCertificate,
        updateCertificateQuantity: updateCertificateQuantity,
        fetchActivities: fetchActivities,
        fetchSizeColorPicking: fetchSizeColorPicking,

        //Certificate Detail
        detailsCertificate : detailsCertificate,
        updateCertificate: updateCertificate,
        deleteCertificate: deleteCertificate,
      };
      
      function listStorage(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/storage', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function detailsStorage(params) {
        var deferred = $q.defer();
        
        _.extend(params, {
          session: Auth.session.get()
        });

        ApiService.get('/storage/product', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function typesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/storage/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function productActivity(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/storage/product/activity', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function picking(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/storage/picking', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchFilters() {
          var deferred = $q.defer();

            params = { session: Auth.session.get() };

            ApiService.get('/storage/filter', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function getPickTaskGroup(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/storage/picking/vakuum', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function pick(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.put('/storage/picking', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function checkDuplicate(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.post('/storage/picking/check', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      function addProductQuantity(data) {
        var deferred = $q.defer();

        _.extend(data, {
          session: Auth.session.get()
        });

        ApiService.put('/storage/product', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function storage(data) {
            var deferred = $q.defer();

            _.extend(data, {
                session: Auth.session.get()
            });

            ApiService.post('/storage', data).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      function removeProductQuantity(data) {
        var deferred = $q.defer();

        _.extend(data, {
          session: Auth.session.get()
        });

        ApiService.delete('/storage/product', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function storageActivity(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/storage/activity', params).then(
          function(data) {
            
            structureActivity(data);
            
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function deleteActivity(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.delete('/storage/activity', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function prepareCsv(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.upload('/storage/csv', params).then(
                function(data) {

                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function uploadCsv(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.upload('/storage/csv', params).then(
                function(data) {

                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadCSV(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get('/storage/csv', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadShipmentCSV(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get('/storage/csv/shipment', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      /**** Helpers *****/
      function structureActivity(dataActivity) {
          console.log(dataActivity);
        var products = dataActivity.products, 
            structured = [],
            lastDate = null,
            date,
            updatedAt;
        
        // use special param to store actual number of products (i.e. without date rows)
        dataActivity.productsLength = products.length;
        
        for (var i = 0; i < dataActivity.productsLength; i++) {

          products[i].mode = 'product';
          
          // adjust date to user date
          // updatedAt = $filter('date')(products[i].StorageActivity.updated_at.replace(' ', 'T') + '+0000', 'yyyy.MM.dd HH:mm:ss');
          
          // set time param
          // products[i].StorageActivity.time = updatedAt.substr(11, 5);
          
          // set trend param (to use as class in view)
          if (+products[i].StorageActivity.quantity > 0) {
            products[i].StorageActivity.trend = 'up';
            products[i].StorageActivity.format_quantity = +products[i].StorageActivity.quantity;
          } else if (+products[i].StorageActivity.quantity < 0) {
            products[i].StorageActivity.trend = 'down';
            products[i].StorageActivity.format_quantity = +products[i].StorageActivity.quantity * (-1);
          } else {
            products[i].StorageActivity.trend = 'ng-hide';
          }
          
          // get date
          date = products[i].StorageActivity.lastDate;
          
          // create date row
          if (lastDate !== date) {
            structured.push({
              mode: 'date',
              date: date
            });

            lastDate = date;
          }
          
          structured.push(products[i]);
          
        }
    
        dataActivity.products = structured;
        dataActivity.lastDate = lastDate;
        
      }

        function fetchCertificate(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/storage/certificate', getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function detailsCertificate(certificateId) {

            var deferred = $q.defer();
            var params = {
                session: Auth.session.get(),
                certificate_id : certificateId
            };
            ApiService.get('/storage/certificate/details', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        ////update Certificate
        function updateCertificate(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );
            ApiService.upload('/storage/certificate/details', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function deleteCertificate(certificateId) {
            var deferred = $q.defer();

            var data  = {
                session: Auth.session.get(),
                Storage: {
                    id: certificateId
                }
            };

            ApiService.delete('/storage/certificate/details', data).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function createCertificate(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.upload('/storage/certificate/create', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchActivities(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/storage/certificate/activity', getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function updateCertificateQuantity(params) {
            var deferred = $q.defer();

            var postData = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.post('/storage/certificate', postData).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchSizeColorPicking(params){
            var deferred = $q.defer();

            var dataGet = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/storage/color-size-product', dataGet).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
    });

})();
      