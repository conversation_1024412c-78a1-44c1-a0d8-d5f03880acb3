(function () {

  angular.module('printty')

    .factory('Customers', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        customers: '/customers',
        customer: '/customers/customer',

        products: '/customers/customer/products',
        prices: '/customers/customer/products/product/prices',
        deliveryServices: '/customers/customer/deliveryservices',
        customersBusiness: '/customers/business',
        detailsPrice: '/customers/customer/products/product/prices/price',
        csv: '/customers/customer/products/csv',
        body_price: '/customers/customer/products/product/body/price',
      };

      return {
        fetchCustomers: fetchCustomers,

        createCustomer: createCustomer,
        fetchCustomer: fetchCustomer,
        updateCustomer: updateCustomer,
        deleteCustomer: deleteCustomer,
        
        fetchProducts: fetchProducts,
        fetchProductPrices: fetchProductPrices,
        updateProductPrices: updateProductPrices,
        createProductPrices: createProductPrices,
        deleteProductPrice: deleteProductPrice,

        fetchDeliveryServices: fetchDeliveryServices,
        fetchAllCustomers: fetchAllCustomers,
        detailsPrice: detailsPrice,
        fetchColorSizeSide: fetchColorSizeSide,
        fetchPriceCSV: fetchPriceCSV,
        uploadPriceCSV: uploadPriceCSV,
        updateBodyPrice: updateBodyPrice,
      };

      function fetchCustomers(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.customers, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function fetchAllCustomers(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get(API_MAP.customersBusiness, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

      function createCustomer(customerData) {
        var deferred = $q.defer();

        customerData = _.cloneDeep(customerData);
        
        var putData  = {
          session: Auth.session.get(),
          Customer: customerData.Customer,
          DeliveryService: customerData.DeliveryService
        };

        putData.Customer.is_production_date_preferred_setting_available = putData.Customer.is_production_date_preferred_setting_available ? 1 : 0;
        putData.Customer.is_bulk_shipping = putData.Customer.is_bulk_shipping ? 1 : 0;
        ApiService.put(API_MAP.customer, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchCustomer(customerId) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          customer_id: customerId
        };

        ApiService.get(API_MAP.customer, getParams).then(
          function(data) {
            data.Customer.is_production_date_preferred_setting_available = ApiService.toBoolean(data.Customer.is_production_date_preferred_setting_available);
            data.Customer.is_bulk_shipping = ApiService.toBoolean(data.Customer.is_bulk_shipping);
            if (_.isArray(data.Customer.api_callback_options)) {
              data.Customer.api_callback_options = {};
            }
            
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateCustomer(customerData) {
        var deferred = $q.defer();

        customerData = _.cloneDeep(customerData);
        
        var postData  = {
          session: Auth.session.get(),
          Customer: customerData.Customer,
          DeliveryService: customerData.DeliveryService
        };

        postData.Customer.is_production_date_preferred_setting_available = postData.Customer.is_production_date_preferred_setting_available ? 1 : 0;
        postData.Customer.is_bulk_shipping = postData.Customer.is_bulk_shipping ? 1 : 0;

        ApiService.post(API_MAP.customer, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteCustomer(customerId) {
        var deferred = $q.defer();

        var deleteData  = {
          session: Auth.session.get(),
          Customer: {
            id: customerId
          }
        };

        ApiService.delete(API_MAP.customer, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchProducts(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.products, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchProductPrices(params) {
        var deferred = $q.defer();

          var getParams = _.merge(
              {session: Auth.session.get()},
              params
          );

        ApiService.get(API_MAP.prices, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateProductPrices(params) {
        var deferred = $q.defer();

        var postData = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.post(API_MAP.prices, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createProductPrices(priceData) {
          var deferred = $q.defer();

          var putData  = {
              session: Auth.session.get(),
              Price: priceData
          };

          ApiService.put(API_MAP.prices, putData).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }
      
      function fetchDeliveryServices() {
        var deferred = $q.defer();

        var getParams = { 
          session: Auth.session.get() 
        };

        ApiService.get(API_MAP.deliveryServices, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function detailsPrice(priceId) {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
                price_id: priceId
            };

            ApiService.get(API_MAP.detailsPrice, params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchColorSizeSide(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get('/customers/customer/products/product/prices/price/details', getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchPriceCSV(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get(API_MAP.csv, getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function uploadPriceCSV(params) {
            var deferred = $q.defer();

            params = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.upload(API_MAP.csv, params).then(
                function(data) {

                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function updateBodyPrice(params) {
            var deferred = $q.defer();

            var postData = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.post(API_MAP.body_price, postData).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function deleteProductPrice(priceId) {
            var deferred = $q.defer();

            var data  = {
                session: Auth.session.get(),
                priceId: priceId,
            };

            ApiService.delete(API_MAP.prices, data).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
      