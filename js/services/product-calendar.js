(function() {

  angular.module('printty')

    .factory('ProductCalendar', function(ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        rules: '/production/calendar/rules',
        rule: '/production/calendar/rules/rule',
      };

      return {
        list: calendarList,
        create: calendarCreate,
        update: calendarUpdate,
        delete: calendarDelete,
      };

      function calendarList() {
        var params = {
          session: Auth.session.get()
        };
        return ApiService.get(API_MAP.rules, params);
      }

      function calendarCreate(sizeData) {
        var postData = angular.merge(
          {session: Auth.session.get()},
          sizeData
        );

        return ApiService.put(API_MAP.rule, postData)
      }

      function calendarUpdate(sizeData) {

        var postData = angular.merge(
          {session: Auth.session.get()},
          sizeData
        );

        return ApiService.post(API_MAP.rule, postData)
      }

      function calendarDelete(id) {
        var data = {
          session: Auth.session.get(),
          ProductionCalendarRule: {
            id: id
          }
        };

        return ApiService.delete(API_MAP.rule, data)
      }


    });

})();