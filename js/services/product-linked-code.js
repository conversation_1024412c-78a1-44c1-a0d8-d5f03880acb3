(function () {

  angular.module('printty')

    .factory('ProductLinkedCode', function (ApiService, $q, Auth) {

      "ngInject";
      
      var API_MAP = {
        codes: '/products/product/linked/codes',
        code: '/products/product/linked/codes/code',
        sources: '/products/product/linked/sources'
      };
      
      return {
        fetchCodes: fetchCodes,
        
        fetchCode: fetchCode,
        createCode: createCode,
        updateCode: updateCode,
        deleteCode: deleteCode,
        
        fetchSources: fetchSources
      };
      
      function fetchCodes(productId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          product_id: productId
        };

        ApiService.get(API_MAP.codes, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchCode(codeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          code_id: codeId
        };

        ApiService.get(API_MAP.code, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function createCode(productId, codeData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          Product: {
            id: productId
          },
          ProductLinkedCode: codeData
        };

        ApiService.put(API_MAP.code, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateCode(codeData) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductLinkedCode: codeData
        };

        ApiService.post(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteCode(codeId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductLinkedCode: {
            id: codeId
          }
        };

        ApiService.delete(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchSources() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get(API_MAP.sources, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
    });

})();
      