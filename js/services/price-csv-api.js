(function () {
    angular.module('printty')

        .factory('PriceCsvApi', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                csvURL: '/price/csv',
            };

            return {
                fetchPriceCsvURL: fetchPriceCsvURL,
            };

            function fetchPriceCsvURL(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.csvURL, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();