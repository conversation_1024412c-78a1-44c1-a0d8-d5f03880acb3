(function () {
    angular.module('printty')

        .factory('CpFrames', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                cps: '/cpframes',
                cp: '/cpframe',
                csvFile: '/import/cpframes/csv',
                csvFileTemplate: '/import/cpframes/csv/template',
            };

            return {
                fetchCps: fetchCps,
                fetchCp: fetchCp,
                createCp: createCp,
                updateCp: updateCp,
                deleteCp: deleteCp,
                uploadCSV: uploadCSV,
                downloadCSV: downloadCSV,
                CSVdownloadTemplate: CSVdownloadTemplate,
                sort: sort,
            };

            function fetchCps(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.cps, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchCp(cpId) {

                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    cp_frame_id: cpId
                };

                ApiService.get(API_MAP.cp, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createCp(cp) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    CpFrame: cp
                };

                ApiService.put(API_MAP.cp, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateCp(cp) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    CpFrame: cp
                };

                ApiService.post(API_MAP.cp, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteCp(cpId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    CpFrame: {
                        id: cpId
                    }
                };

                ApiService.delete(API_MAP.cp, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function CSVdownloadTemplate(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function sort(params) {
                var deferred = $q.defer(2000);

                ApiService.post('/cpframes/sort', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();