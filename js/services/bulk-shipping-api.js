(function () {

    angular.module('printty')

        .factory('BulkShippingApi', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                boxes: '/boxes',
                statuses: '/boxes/statuses',
                statusesChange: '/boxes/box/status',
                box: '/boxes/box',
                registerBox: '/register/box',
                prepareShipBox: '/prepare/boxes/ship',
                shipBox: '/boxes/ship',
                csvBox: '/boxes/csv',
            };

            return {
                fetchBoxes: fetchBoxes,

                fetchStatus: fetchStatus,
                fetchStatusChange: fetchStatusChange,

                fetchBox: fetchBox,
                createBox: createBox,
                updateBox: updateBox,
                deleteBox: deleteBox,
                fetchBoxInfo: fetchBoxInfo,
                registerBoxByCode: registerBoxByCode,
                prepareBoxToShip: prepareBoxToShip,
                decidedToShip: decidedToShip,
                downloadCSVBox: downloadCSVBox,
                deleteOrderNumber: deleteOrderNumber,
                updateStatus: updateStatus,
                isMultipleItems: isMultipleItems,
            };

            function fetchBoxes(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.boxes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchStatus() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.statuses, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchStatusChange() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.statusesChange, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchBox(boxId) {
                const deferred = $q.defer();

                const getData = {
                    session: Auth.session.get(),
                    box_id: boxId
                };

                ApiService.get(API_MAP.box, getData).then(
                    function(data) {
                        data.Box.max_capacity = ApiService.toNumber(data.Box.max_capacity);
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createBox(boxData) {
                var deferred = $q.defer();

                var putData  = {
                    session: Auth.session.get(),
                    Box: boxData.Box,
                    Customer: boxData.Customer
                };

                ApiService.put(API_MAP.box, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateBox(boxData) {
                var deferred = $q.defer();

                var postData  = {
                    session: Auth.session.get(),
                    Box: _.cloneDeep(boxData.Box),
                };

                ApiService.post(API_MAP.box, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteBox(boxId) {
                var deferred = $q.defer();

                var deleteData  = {
                    session: Auth.session.get(),
                    Box: {
                        id: boxId
                    }
                };

                ApiService.delete(API_MAP.box, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchBoxInfo(boxId) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    box_id: boxId
                };

                ApiService.get(API_MAP.registerBox, params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function registerBoxByCode(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/register/box/search', getParams, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function prepareBoxToShip(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.prepareShipBox, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function decidedToShip(params) {
                var deferred = $q.defer();

                var postData = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.shipBox, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVBox(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvBox, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteOrderNumber(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.delete(API_MAP.registerBox, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateStatus(box) {
                var deferred = $q.defer();

                var postData  = {
                    session: Auth.session.get(),
                    Box: box
                };
                ApiService.post(API_MAP.statusesChange, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function isMultipleItems(param) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    param
                );

                ApiService.get(API_MAP.shipBox, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
