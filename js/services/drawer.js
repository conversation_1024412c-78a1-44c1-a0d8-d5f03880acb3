(function () {

  angular.module('printty')

    .factory('Drawer', function (ApiService, $q, Auth) {

      "ngInject";

      return {
        categories: fetchCategories,
        products: fetchProducts,
        product: fetchProduct,
        default: fetchDefault
      };
      
      function fetchCategories() {
        var deferred = $q.defer();

        var params = { session: Auth.session.get() };

        ApiService.get('/drawer/products/categories', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchProducts(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/drawer/products', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchProduct(productId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          product_id: productId
        };

        ApiService.get('/drawer/products/product', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchDefault() {
        var deferred = $q.defer();

        var params = { session: Auth.session.get() };

        ApiService.get('/drawer/default', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });

})();
      