(function () {

    angular.module('printty')

        .factory('ImagePlanning', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                planning: '/images/planning',
                reloadPlanning: '/images/planning/reload',
            };

            return {
                fetchSentPlanning: fetchSentPlanning,
                reloadPlanning: reloadPlanning,
            };

            function fetchSentPlanning(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.planning, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadPlanning(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadPlanning, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
