(function () {
    angular.module('printty')

        .factory('OrderProductCodeApi', function (ApiService, $q, Auth) {
            'ngInject';

            var API_MAP = {
                products_code: '/products/code/color',
                product_code: '/product/code/color',
            };
            return {
                fetchProductsCode: fetchProductsCode,
                fetchProductCode: fetchProductCode,
                updateSku: updateSku,
            };

            function fetchProductsCode(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.products_code, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductCode(params) {
                var deferred = $q.defer();

                var getParams = _.merge({
                    session: Auth.session.get()
                }, params);

                ApiService.get(API_MAP.product_code, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateSku(product_code) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    product_code: product_code
                };

                ApiService.post(API_MAP.product_code, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();