(function () {

    angular.module('printty')

        .factory('SeirenRandomApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                getSeirenRandomData: getSeirenRandomData,
            };

            function getSeirenRandomData(date) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    date : date
                };

                ApiService.get('/random-mode/seiren', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });

})();