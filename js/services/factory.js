(function () {

    angular.module('printty')

        .factory('Factory', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                factories: '/factories',
                factory: '/factories/factory',
            };

            return {
                fetchFactories: fetchFactories,
                createFactory: createFactory,
                fetchFactory: fetchFactory,
                updateFactory: updateFactory,
                deleteFactory: deleteFactory,
                fetchFactoryUser: fetchFactoryUser,
            };

            function fetchFactories(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.factories, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createFactory(factoryData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Factory: factoryData
                };

                ApiService.post(API_MAP.factory, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchFactory(factoryId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    factory_id: factoryId
                };

                ApiService.get(API_MAP.factory, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateFactory(factoryData) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    Factory: factoryData
                };

                ApiService.put(API_MAP.factory, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteFactory(factoryId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    Factory: {
                        id: factoryId
                    }
                };

                ApiService.delete(API_MAP.factory, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchFactoryUser() {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get()
                };
                ApiService.get('/factories/user', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    }
                );

                return deferred.promise;
            }

        });

})();
