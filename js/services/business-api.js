(function () {

    angular.module('printty')

        .factory('BusinessApi', function (ApiService, $q, Auth, $filter) {

            "ngInject";
            var API_MAP = {
                unplannedSaleOrder: '/business/planning/unplanned',
                printers: '/business/planning/printers',
                unplannedTypes: '/business/planning/unplanned/types',
                recommendations: '/business/planning/recommendations',
                processToPrinter: '/business/planning/printers/processing',
                processToRecommendations: '/business/planning/recommendations/processing',
                unplannedFilter: '/business/planning/unplanned/filter',
                autoPlanning: '/business/planning/autoPlanning',
            };

            return {
                fetchOrderCustomer: fetchOrderCustomer,
                createOrderCustomer: createOrderCustomer,
                fetchPrinters: fetchPrinters,
                fetchUnplannedTypes: fetchUnplannedTypes,
                fetchRecommendations: fetchRecommendations,
                processToPrinter: processToPrinter,
                processToRecommendations: processToRecommendations,
                fetchUnplannedFilter: fetchUnplannedFilter,
                autoPlanning:autoPlanning,
            };
            function fetchOrderCustomer(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.unplannedSaleOrder, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createOrderCustomer(date_current) {
                var deferred = $q.defer();

                postData = {
                    session: Auth.session.get(),
                    date_current: date_current
                };

                ApiService.put('/business/create', postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
            function fetchPrinters(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.printers, getParams).then(
                    function(data) {

                        _.forEach(data.printers, function (printer) {
                            printer.note = (+printer.Printer.workload - +printer.Printer.capacity) >= -2;
                        });

                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchRecommendations(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.recommendations, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchUnplannedTypes() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.unplannedTypes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processToPrinter(params) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    date: params.date,
                    Printer: {
                        id: params.printerId
                    },
                    order_items: []
                };

                if (params.type_code) {
                    putData.ProductionType = {
                        code: params.type_code
                    }
                }

                if (_.isString(params.items)) {
                    putData.order_items = params.items;
                } else {

                    _.forEach(params.items, function(itemId) {

                        putData.order_items.push({
                            OrderItem: {
                                id: itemId
                            }
                        })

                    });

                }

                ApiService.put(API_MAP.processToPrinter, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processToRecommendations(params) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    date: params.date,
                    order_items: []
                };

                if (params.type_code) {
                    putData.ProductionType = {
                        code: params.type_code
                    }
                }

                if (_.isString(params.items)) {
                    putData.order_items = params.items;
                } else {

                    _.forEach(params.items, function(itemId) {

                        putData.order_items.push({
                            OrderItem: {
                                id: itemId
                            }
                        })

                    });

                }

                ApiService.put(API_MAP.processToRecommendations, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchUnplannedFilter() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.unplannedFilter, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function autoPlanning(params) {
                var deferred = $q.defer();

                var kornit = (params.kornit !== undefined) ? true : false;

                var putData = {
                    session: Auth.session.get(),
                    date: params.date,
                    order_items: [],
                    filterData: params.filterData,
                    kornit: kornit
                };

                if (_.isString(params.items)) {
                    putData.order_items = params.items;
                } else {

                    _.forEach(params.items, function(itemId) {

                        putData.order_items.push(itemId)

                    });

                }

                ApiService.post(API_MAP.autoPlanning, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();