(function() {

  angular.module('printty')

    .factory('utils', function($httpParamSerializer,$rootScope) {

      "ngInject";

      var appLoader = angular.element('.loader-container');

      var $body = angular.element('body');

      $rootScope.progressValue = 0;

      var delay = (function() {
        var timer = 0;
        return function(callback, ms) {
          clearTimeout(timer);
          timer = setTimeout(callback, ms);
        };
      })();

      return {
        appLoaded: appLoaded,
        buildUrl: buildUrl,
        delay: delay,

        listLoading: {
          show: showListLoading,
          hide: hideListLoading
        },

        moreItemsLoad: {
          show: moreItemsLoadShow,
          hide: moreItemsLoadHide
        },

        globalLoading: {
          show: globalLoadingShow,
          hide: globalLoadingHide
        },
        globalProgressLoading: {
          show: globalProgressLoadingShow,
          hide: globalProgressLoadingHide,
          progress: globalProgressLoadingProgress
        },

        deleteTemplate: deleteTemplate,
        cancelTemplate: cancelTemplate,
        changeStatusTemplate: changeStatusTemplate,
        choosePaperSizeTemplate:choosePaperSizeTemplate,
        choosePaperSizeTemplateNew:choosePaperSizeTemplateNew,
        choosePaperSizeTemplateQRTasks:choosePaperSizeTemplateQRTasks,
        confirmDownloadImage:confirmDownloadImage,
        confirmDownloadDTFTranscriptionImage:confirmDownloadDTFTranscriptionImage,
        notice: notice,
        inputOrderTarget:inputOrderTarget,
        loadCounter: loadCounter,
        confirmDeleteTask: confirmDeleteTask,
        shipBoxTemplate: shipBoxTemplate,
        deleteNumberTemplate: deleteNumberTemplate,
        infoTemplate: infoTemplate,
        downloadCSVOrderTemplate: downloadCSVOrderTemplate,
        changeBodyPrice: changeBodyPrice,
        showItemsTemplete: showItemsTemplete,
        inputReasonForFail: inputReasonForFail,
        contactAboutStorage: contactAboutStorage,
        inputCertificateStamp: inputCertificateStamp,
      };

      function buildUrl(url, params, notStrict) {

        if (!notStrict) { // if strict(default) => delete empty props

          angular.forEach(params, function(value, key, obj) {
            if (!angular.isDefined(value) || value === '') {
              delete obj[key];
            }
          });

        }

        var serializedParams = $httpParamSerializer(params);

        if (serializedParams.length > 0) {
          url += ((url.indexOf('?') === -1) ? '?' : '&') + serializedParams;
        }

        return url;

      }

      function appLoaded() {
        appLoader && appLoader.remove();
      }

      function showListLoading(block) {

        if (block) {
          $body.addClass('list-loads blocking');
        } else {
          $body.addClass('list-loads');
        }

      }

      function hideListLoading() {
        $body.removeClass('list-loads blocking');
      }

      function moreItemsLoadShow(parent) {
        if (parent) {
          parent.find('.pagination-loading').show();
        } else {
          angular.element('.pagination-loading').show();
        }
      }

      function moreItemsLoadHide(parent) {
        if (parent) {
          parent.find('.pagination-loading').hide();
        } else {
          angular.element('.pagination-loading').hide();
        }
      }

      function globalLoadingShow() {
        angular.element('.global-loading').show();
      }

      function globalLoadingHide() {
        angular.element('.global-loading').hide();
      }

      function globalProgressLoadingShow() {
        angular.element('.global-loading-progress').show();
        $rootScope.progressValue = 0;
      }

      function globalProgressLoadingHide() {
        angular.element('.global-loading-progress').hide();
        $rootScope.progressValue = 0;
      }

      function globalProgressLoadingProgress(event) {
        $rootScope.progressValue = Math.round((event.loaded / event.total)*100);
      }

      function deleteTemplate() {

        var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'を削除しますか？';

        return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
          '  <md-dialog-content>' +
          '    <h2 class="md-title text-center">' + title + '</h2>' +
          '  </md-dialog-content>' +
          '  <md-dialog-actions layout="row">' +
          '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDelete()">キャンセル</button></div>' +
          '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDelete()">削除する</button></div>' +
          '  </md-dialog-actions>' +
          '</md-dialog>';
      }

        function cancelTemplate() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'を削除しますか？';

            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDelete()">いいえ</button></div>' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDelete()">キャンセルする</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

    function changeStatusTemplate() {

        var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '本当に変更してもよいですか？';

        return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
            '  <md-dialog-content>' +
            '    <h2 class="md-title text-center">' + title + '</h2>' +
            '  </md-dialog-content>' +
            '  <md-dialog-actions layout="row">' +
            '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDelete()">はい</button></div>' +
            '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDelete()">いいえ</button></div>' +
            '  </md-dialog-actions>' +
            '</md-dialog>';
    }

    function choosePaperSizeTemplate() {

        var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Choose paper size';

        return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

            '  <md-toolbar>' +
            '      <div class="md-toolbar-tools">' +
            '        <h2>'+ title +'</h2>' +
            '      </div>' +
            '    </md-toolbar>' +
            '  <md-dialog-content>' +
            '    <label><input type="radio" value="1" name="paper-size" class="paper-size-radio" style="margin: 10px"> A4</label>' +
            '    <br/><label><input type="radio" value="2" name="paper-size" class="paper-size-radio" style="margin: 10px"> 30mm x 50mm</label>' +
            '  </md-dialog-content>' +
            '  <md-dialog-actions layout="row">' +
            '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmChoose()">印刷する</button></div>' +
            '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelChoose()">キャンセル</button></div>' +
            '  </md-dialog-actions>' +
            '</md-dialog>';
    }

        function choosePaperSizeTemplateNew() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Choose paper size';

            return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

                '  <md-toolbar>' +
                '      <div class="md-toolbar-tools">' +
                '        <h2>'+ title +'</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content>' +
                '    <label><input type="radio" value="1" name="paper-size" class="paper-size-radio" style="margin: 10px"> A4 (30mm x 50mm)  </label>' +
                '    <br/><label><input type="radio" value="2" name="paper-size" class="paper-size-radio" style="margin: 10px"> 30mm x 50mm</label>' +
                '    <br/><label><input type="radio" value="a4_30_100" name="paper-size" class="paper-size-radio" style="margin: 10px"> A4 (30mm x 100mm)</label>' +
                '    <br/><label><input type="radio" value="30_100" name="paper-size" class="paper-size-radio" style="margin: 10px"> 30mm x 100mm</label>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmChoose()">印刷する</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelChoose()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function choosePaperSizeTemplateQRTasks() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Choose paper size';

            return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

                '  <md-toolbar>' +
                '      <div class="md-toolbar-tools">' +
                '        <h2>'+ title +'</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content>' +
                '    <br/><label><input type="radio" value="2" name="paper-size" class="paper-size-radio" style="margin: 10px"> 30mm x 50mm</label>' +
                '    <br/><label><input type="radio" value="30_100" name="paper-size" class="paper-size-radio" style="margin: 10px"> 30mm x 100mm</label>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmChoose()">印刷する</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelChoose()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function confirmDownloadImage() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'このお客様は印刷が終了しています。もう一度印刷しますか？';

            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDownload()">はい</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDownload()">いいえ</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function confirmDownloadDTFTranscriptionImage() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'このお客様は転写が終了しています。もう一度転写しますか？';

            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDownload()">はい</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDownload()">いいえ</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function confirmDeleteTask() {

            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '本当に削除して良いですか？';

            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDownload()">はい</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDownload()">いいえ</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function notice(){
            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Message';
            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancel()" style="width: 70px;float: right;">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function inputOrderTarget(){
            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '目標面数';

            return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

                '  <md-toolbar>' +
                '      <div class="md-toolbar-tools">' +
                '        <h2>'+ title +'</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content>' +
                '<input type="text" name="quantity" value="{{ctrl.quantity}}" id="input-quantity" style="margin: 10px;width: 90%;" onkeypress="return (event.charCode == 8 || event.charCode == 0 || event.charCode == 13) ? null : event.charCode >= 48 && event.charCode <= 57" />' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmInput()">保存</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelInput()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function infoTemplate(message) {
            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content style="width:800px;max-height:800px;">' +
                '    <h2 class="md-title">' + message + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancel()" style="width: 70px;float: right;">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function downloadCSVOrderTemplate() {
            return   '<div style="width: 800px; padding: 2rem; height: 300px;">' +
                      '  <div layout="row" layout-align="space-between center" style="max-width: 750px; margin: auto;">' +
                      '      <div style="width:140px;">' +
                      '          <div pick-date-header>' +
                      '              <div style="width: 85px">{{ startDate | dateJapan : \'year\month\day\' }}</div>' +
                      '              <md-datepicker ng-model="startDate" md-placeholder="Enter date" ng-change="getStartDate(startDate)" autofocus></md-datepicker>' +
                      '          </div>' +
                      '      </div>' +
                      '      <span style="font-size: 1.5rem;">~</span>' +
                      '      <div style="width:140px;margin-left:30px;">' +
                      '          <div pick-date-header>' +
                      '             <div style="width: 85px">{{ endDate | dateJapan : \'year\month\day\' }}</div>' +
                      '              <md-datepicker ng-model="endDate" md-placeholder="Enter date" ng-change="getEndDate(endDate)" md-open-on-focus></md-datepicker>' +
                      '          </div>' +
                      '      </div>' +
                      '  </div>' +
                      '  <div style="margin-top: 9rem; text-align: center;">' +
                      '      <md-button class="btn btn-primary" ng-click="download()">ダウンロード</md-button>' +
                      '  </div>' +
                    ' </div>'
        }

        function changeBodyPrice() {
            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'ボディ価格の設定';

            return '<md-dialog aria-label="choose confirm" class="delete-confirm" style="width: 400px;max-height: 400px;">' +

                '  <md-toolbar>' +
                '      <div class="md-toolbar-tools">' +
                '        <h2>'+ title +'</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content>' +
                '<input type="text" name="bodyPrice" value="{{ctrl.bodyPrice}}" id="input-price" style="margin: 10px;width: 90%;" onkeypress="return (event.charCode == 8 || event.charCode == 0 || event.charCode == 13) ? null : event.charCode >= 48 && event.charCode <= 57" />' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmInput()">保存</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelInput()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function shipBoxTemplate(dataBox){
            var title = '以下の箱をまとめて発送処理しますか？';

            var box_number = '';
            var enclosed = '';
            dataBox.boxes.forEach(function (number) {
                box_number += '<div>' + number.box_number + '</div>';
                enclosed += '<div>' + number.enclosed + '</div>';
            });

            return '<md-dialog aria-label="delete confirm" class="delete-confirm" style="width: 400px;max-height: 400px;">' +
                '  <md-dialog-content style="order: unset;">' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '<div style="justify-content: space-between;display: flex;max-width: 200px;margin-left: 90px;text-align: center;max-height: 250px;overflow-y: scroll;padding-right: 35px;">' +
                    '<div>' +
                        '<span>Box No. </span>' +
                        box_number +
                    '</div>' +
                    '<div>' +
                        '<span>数量</span>' +
                        enclosed +
                    '</div>' +
                '</div>' +
                '<div>' +
                '</div>' +
                '  <md-dialog-actions layout="row" style="order: unset;">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmShip()">発送</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelShip()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function deleteNumberTemplate(order) {
            var title = order + '　をリストから外しますか？';

            return '<md-dialog aria-label="delete confirm" class="delete-confirm">' +
                '  <md-dialog-content>' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelDelete()">キャンセル</button></div>' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmDelete()">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function showItemsTemplete(dataItems) {
            var title = '対象注文にアイテムが複数あります。' + '<br>' + 'どれか１つを選択して「OK」を押してください。';

            var items = '';
            dataItems.items.forEach(function (item) {
                items += '<div style="margin-bottom: 4px">' +
                            '<input type="radio" id="'+item.OrderItem.id+'" value="'+item.OrderItem.id +'" name="item-id" class="item-id-radio" style="">' +
                            '<label for="'+item.OrderItem.id+'">' + item.OrderItem.info + '</label>' +
                        '</div>';
            });

            return '<md-dialog aria-label="delete confirm" class="delete-confirm" style="width: 700px;max-height: 700px;">' +
                '  <md-dialog-content style="order: unset;">' +
                '    <h2 class="md-title text-center">' + title + '</h2>' +
                '  </md-dialog-content>' +
                '<div style="justify-content: space-between;display: flex;max-width: 500px;margin-left: 90px;max-height: 550px;overflow-y: scroll;padding-right: 35px;">' +
                    '<div style="margin-left: 50px;">' +
                        items +
                    '</div>' +
                '</div>' +
                '<div></div>' +
                '  <md-dialog-actions layout="row" style="order: unset;">' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelSellectItem()">キャンセル</button></div>' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="sellectItem()">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }
        
        function inputReasonForFail() {

            return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

                '  <md-toolbar style="min-height: 80px">' +
                '      <div class="md-toolbar-tools" style="flex-direction: column;">' +
                '           <h2 style="margin-bottom: 0">調達失敗理由を入力してください。</h2>' +
                '           <h2 style="padding-top: 0">※この内容は発注者に送信されます。</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content style="margin-top: 10px; padding: 10px">' +
                '       <input style="width: 100%; box-sizing: border-box" type="text" name="reason" value="{{ctrl.reason}}" id="input-reason" />' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="cancelInputReason()">キャンセ</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="confirmInputReason()">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function contactAboutStorage() {
            return '<md-dialog aria-label="choose confirm" class="delete-confirm">' +

                '  <md-toolbar style="min-height: 80px">' +
                '      <div class="md-toolbar-tools" style="flex-direction: column;">' +
                '           <h2 style="margin-bottom: 0">欠品連絡します。</h2>' +
                '           <h2 style="padding-top: 0">※この内容は発注者に送信されます。</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content style="margin-top: 10px; padding: 10px">' +
                '       <input style="width: 100%; box-sizing: border-box" type="text" name="reason" value="{{ctrl.reason}}" id="input-reason" />' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="cancelInputReason()">キャンセ</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="confirmInputReason()">OK</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

        function inputCertificateStamp() {
            var title = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Please input the quantity for this stamp';

            return '<md-dialog aria-label="choose confirm" class="delete-confirm" style="width: 400px;max-height: 400px;">' +

                '  <md-toolbar>' +
                '      <div class="md-toolbar-tools">' +
                '        <h2>'+ title +'</h2>' +
                '      </div>' +
                '    </md-toolbar>' +
                '  <md-dialog-content>' +
                '<input type="text" name="quantityStamp" value="{{ctrl.quantityStamp}}" id="input-stamp" style="margin: 10px;width: 90%;" onkeypress="return (event.charCode == 8 || event.charCode == 0 || event.charCode == 13) ? null : event.charCode >= 48 && event.charCode <= 57" />' +
                '  </md-dialog-content>' +
                '  <md-dialog-actions layout="row">' +
                '    <div flex="50"><button class="btn btn-primary btn-block" ng-click="confirmInput()">保存</button></div>' +
                '    <div flex="50"><button class="btn btn-default btn-block" ng-click="cancelInput()">キャンセル</button></div>' +
                '  </md-dialog-actions>' +
                '</md-dialog>';
        }

      function loadCounter(startFunc, stopFunc, counts) {

        var DEFAULT_COUNTS = counts || 1;
        var _counts = DEFAULT_COUNTS;

        this.start = function() {
          startFunc(arguments[0]);
        };

        this.stop = function() {
          if (_counts) {
            _counts--;
          }
          if (!_counts) {
            stopFunc(arguments[0]);
          }
        };

        this.default = function() {
          _counts = DEFAULT_COUNTS;
        };

        this.set = function(num) {
          _counts = num;
        }

      }

    })

    .factory('orderUtils', function() {

      "ngInject";

      return {
        structure: structureOrders
      };

      function structureOrders(orders) {
        var ordersStructured = [];

        orders.forEach(function(order, index) {

          // order contains only one product
          if (order.products.length === 1) {
            order.rowType = 'single';
            ordersStructured.push(order);
            return;
          }

          // order contains multiple orders

          ordersStructured.push({
            uid: order.uid,
            rowType: 'outer',
            selectedInner: []
          });

          var lastIndex = ordersStructured.length - 1;

          for (var i = 0, len = order.products.length; i < len; ++i) {
            order.products[i].rowType = 'inner';
            order.products[i].forIndex = lastIndex;
            order.products[i].selfIndex = i;
            ordersStructured.push(order.products[i]);
            ordersStructured[lastIndex].selectedInner.push(false);
          }

        });

        return ordersStructured;
      }

    })

    .factory('DrawToolUtils', function() {

      "ngInject";

      return {
        positionByOrigin: positionByOrigin,
        getSize: getSize,
        filterFiles: filterFiles,
        parseContentJSON: parseContentJSON,
        trailingZero: trailingZero,

        getPreviewSide: getPreviewSide
      };

      function positionByOrigin(item, originX, originY) {

        if (!item) return;

        var itemPos = item.item.getPointByOrigin(originX, originY);
        var borderPos = item.side.FabricBorder.getPointByOrigin(originX, originY);

        var position = {
          top: (itemPos.y - borderPos.y) / item.side.cmSize.height,
          left: (itemPos.x - borderPos.x) / item.side.cmSize.width
        };

        return position;

      }

      function getSize(item) {

        if (!item) return;

        var size = {
          width: item.item.scaleX === 1 ? item.item.width / item.side.cmSize.width : item.item.width * item.item.scaleX / item.side.cmSize.width,
          height: item.item.scaleY === 1 ? item.item.height / item.side.cmSize.height : item.item.height * item.item.scaleY / item.side.cmSize.height
        };

        return size;

      }

      function filterFiles(file) {
        var type = '|' + file.type.slice(file.type.lastIndexOf('/') + 1) + '|';

        if ('|jpg|png|jpeg|bmp|gif|'.indexOf(type) !== -1) {
          return 'image';
        } else if ('|svg|svg+xml|'.indexOf(type) !== -1) {
          return 'svg';
        }

      }

      function parseContentJSON(string) {
        return JSON.parse(JSON.parse(JSON.stringify(string)));
      }

      function trailingZero(num) {

        if (num % 1 === 0) {
          return num + '.0';
        } else {
          return num + ''
        }

      }

      function getPreviewSide(side) {
        var multiplier = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.5;

        side.FabricBorder.setVisible(false);

        var position = side.backdrop.getPointByOrigin('left', 'top');

        var preview = side.FabricCanvas.toDataURL({
          multiplier: multiplier,
          left: position.x,
          top: position.y,
          width: side.backdrop.width * side.backdrop.scaleX,
          height: side.backdrop.height * side.backdrop.scaleY
        });

        side.FabricBorder.setVisible(true);

        side.FabricCanvas.renderAll();

        return preview;
      }

    })

    .factory('ElectronUtils',

      function($http, $q, $window) {

        "ngInject";

        return {
          getFile: getFile
        };

        function getFile(url, onProgress, productCode, task) {

          var deferred = $q.defer();

          $http.get(url, {
            responseType: 'arraybuffer',
            eventHandlers: {
              progress: onProgress,
            }
          }).then(
            function(response) {

              var blob, a, urlFile;

              blob = new Blob([response.data], {
                type: 'application/octet-stream'
              });

              urlFile = $window.URL.createObjectURL(blob);

              a = document.createElement("a");
              a.href = urlFile;

              var specially_product = ['00085-CVT', '500101', '594201'];
              if (specially_product.includes(productCode))
              {
                  productCode = productCode.split('-')[0] + '-' + task.product_color_code;
              }

              var urlImage = response.config.url;
              if(productCode) {
                  var imgName = url.substr(url.lastIndexOf('/') + 1);
                  var tail = "";
                  var ext = "";
                  var hoodieZipCode = ['00189-NNZ','00217-MLZ','RSZ-143','HSZ-137','5213-01','SZ2251','SZP111','576201','5203-01','390501','00217-MLZ event','562001','TRZ-120','00348-AFZ','576701'];
                  var sweatshirtHoodieCode = ['00183-NSC','00216-MLH','00347-AFH','P3210','P3220','562001','TRP-119','TRZ-120','00348-AFZ'];
                  var pulloverHoodie = ['00188-NNZ','00216-MLH','RSP-142','HSP-136','521401','SP2250','561801','390701','563101','576301','996M','533101','00188','1412760','5204-01','00347-AFH','00216event','TRP-119','00188-NNH'];
                  var designationCode1 = ['00300-ACT','337-AVT','350-AIT','00117-VPT','FDT-100','P1610','P1620'];
                  var designationCode2 = ['00330-AVP','00335-ALP','00331-ABP','00339-AYP','00351-AIP','202301','202001'];
                  var designationCode3 = ['00337','00350','00304-ALT','00352-AIL'];
                  var poloShirt = ['00212-MCP','00141-NVP','115','202003','BSP-265', 'BDP-262', 'ATP-261', '554301', 'VSN-267', 'MS3113', 'MS3117', '00302-ADP', 'P1005', '00331-ABP', '00339-AYP', '00351-AIP', '505001', '00335-ALP', '505201', '202001', '202501'];
                  if(hoodieZipCode.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表') {
                      tail =  '_1_264_20';
                  }else if (poloShirt.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表') {
                      tail =  '_1_264_30';
                  }
                  else if (sweatshirtHoodieCode.includes(task.product_code) && task.current_step.TaskStep.title && (task.current_step.TaskStep.title == '右袖' || task.current_step.TaskStep.title == '左袖')) {
                      tail =  '_1_-1_0';
                  }
                  else if (task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表') {
                      tail =  '_1_-1_30';
                  }
                  else if (task.current_step.TaskStep.title && (task.current_step.TaskStep.title == '右袖' || task.current_step.TaskStep.title == '左袖')) {
                      tail =  '_1_280_60';
                  }else{
                      tail =  '_1_-1_0';
                  }

                  var name = task.current_step.detail.product_title;
                  if (name.includes('全面')) {
                      ext = '.jpg';
                  }else{
                      ext = '.png';
                  }
                  if(urlImage.includes('zip')){
                      ext = '.zip';
                  } else if(urlImage.split('.').pop() == 'pdf'){
                      ext = '.pdf';
                  }

                  if(sweatshirtHoodieCode.includes(task.product_code) && task.current_step.TaskStep.title && (task.current_step.TaskStep.title == '左袖' || task.current_step.TaskStep.title == '右袖')) {
                      a.download = productCode + '_Standard pallet_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if(hoodieZipCode.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表') {
                      a.download = productCode + '_Dual Baby_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if(poloShirt.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表') {
                      a.download = productCode + '_Dual Baby_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if(task.current_step.TaskStep.title == '左袖' || task.current_step.TaskStep.title == '右袖'){
                      a.download = productCode + 's_Dual Baby_' + imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if(pulloverHoodie.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表'){
                      a.download = productCode + 'o_Standard pallet_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if(pulloverHoodie.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '裏'){
                      a.download = productCode + 'u_Standard pallet_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  } else if (designationCode1.includes(task.product_code) && task.current_step.TaskStep.title && (task.current_step.detail.product_size_title.includes('XS') || task.current_step.detail.product_size_title.includes('SS'))) {
                      a.download = productCode + 'xs' + '_Standard pallet_' + imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if (designationCode2.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '裏' && (task.current_step.detail.product_size_title.includes('XS') || task.current_step.detail.product_size_title.includes('SS'))) {
                      a.download = productCode + 'xs' + '_Standard pallet_' + imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else if (designationCode3.includes(task.product_code) && task.current_step.TaskStep.title && task.current_step.TaskStep.title == '表' && (task.current_step.detail.product_size_title.includes('XS') || task.current_step.detail.product_size_title.includes('SS'))) {
                      a.download = productCode + 'xs' + '_Standard pallet_' + imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }
                  else if(task.current_step.new_image_name) {
                      a.download = productCode + '_' + task.current_step.new_image_name + '_' + imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }else {
                    a.download = productCode + '_Standard pallet_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;
                  }
              } else {
                  a.download = getFileName(url);
              }
              a.click();
              a.remove();

              $window.URL.revokeObjectURL(urlFile);

              deferred.resolve(response.data);
            },
            function(error) {
              deferred.reject(error);
            });

          return deferred.promise;
        }

        function getFileName(url) {
          return _.last(url.split('/'))
        }

      });

})();