(function () {

    angular.module('printty')

        .factory('BarcodeApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                search: search,

                fetchSingleTask: fetchSingleTask,
                fetchBarCode: fetchBarCode,
            };

            function search(code) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    code: code
                };

                ApiService.get('/kenpin/task/search', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSingleTask(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/barcode/task/single', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchBarCode(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/production/printers/barcode', getParams).then(
                    function(data) {
                        var codes = _.flatMap(data.barcodes, function (code) {
                            return code.BarCode.content_url;
                        });

                        deferred.resolve(codes);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
