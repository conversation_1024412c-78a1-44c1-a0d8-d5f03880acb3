(function () {
    angular.module('printty')

        .factory('StorageActivityContent', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                contents: '/storage/activity/contents',
                totalGarment: '/storage/totalGarment',
                fetch: '/storage/activity/contents/fetch',
                detail: '/storage/activity/contents/detail',
                create: '/storage/activity/content',
                update: '/storage/activity/content',
                remove: '/storage/activity/content',
            };

            return {
                fetchContents: fetchContents,
                totalGarment: totalGarment,
                fetch: fetch,
                detail: detail,
                create: create,
                update: update,
                remove: remove,
            };

            function fetchContents(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.contents, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function totalGarment(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.totalGarment, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetch(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.fetch, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function detail(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.detail, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function create(sourceData) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Warehouse: sourceData
                };
                ApiService.post(API_MAP.create, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function update(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.put(API_MAP.update, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function remove(sourceId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    Warehouse: {
                        id: sourceId
                    }
                };

                ApiService.delete(API_MAP.remove, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();