(function () {
    angular.module('printty')

        .factory('DTFTranscription', function (ApiService, $q, Auth) {

            'ngInject';

            return {
                process: process,
                fetchQRCode: fetchQRCode,
            };

            function process(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/dtf-transcription/process', getParams, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchQRCode(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get('/dtf-transcription/qrcodes', getParams).then(
                    function (data) {
                        var codes = _.flatMap(data.qrcodes, function (code) {
                            return code.QRCode.content_url;
                        });

                        deferred.resolve(codes);
                    },
                    function (error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();