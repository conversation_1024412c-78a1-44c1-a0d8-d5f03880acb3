(function () {

  angular.module('printty')

    .factory('OrderItem', function (ApiService, $q, Auth) {

      "ngInject";

      return {
        fetchItems: fetchItems,
        fetchItem: fetchItem,
        deleteItem: deleteItem,
        updateReasonForFail: updateReasonForFail,
      };
    
      function fetchItems(params) {
        var deferred = $q.defer();

        var getParams = angular.merge(
          { session: Auth.session.get() }, 
          params);

        ApiService.get('/orders/order/items', getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchItem(itemId) {
        var deferred = $q.defer();

        var params = { 
          session: Auth.session.get(),
          item_id: itemId
        };

        ApiService.get('/orders/order/items/item', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function deleteItem(itemId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          OrderItem: {
            id: itemId
          }
        };

        ApiService.delete('/orders/order/items/item', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateReasonForFail(params) {
          var deferred = $q.defer();

          var postData = _.merge(
              { session: Auth.session.get() },
              params
          );

          ApiService.post('/orders/order/items/item/fail', postData).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }
      
    });

})();
      