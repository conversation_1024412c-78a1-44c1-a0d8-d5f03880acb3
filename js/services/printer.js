(function () {

  angular.module('printty')

    .factory('Printer', function (ApiService, $q, Auth) {

      "ngInject";

      return {
        list: listPrinters,
        
        details: detailsPrinter,
        create: createPrinter,
        update: updatePrinter,
        delete: deletePrinter,

        typesAll: typesAll,
        types: types,
        colors: colors,
        factoryAll: factoryAll,

        extractColorsFromPrinter: extractColorsFromPrinter,

          getOptionList: getOptionList,
          getOptionAndon: getOptionAndon,
      };

      function listPrinters(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/printers', params).then(
          function(data) {

            data.printers = _.forEach(data.printers, extractColorsFromPrinter);
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function typesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/printers/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function detailsPrinter(printerId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          printer_id: printerId
        };

        ApiService.get('/printers/printer', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function getOptionList() {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
            };

            ApiService.get('/printers/getOptionList', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function getOptionAndon(factoryId) {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
                factory_id: factoryId,
            };

            ApiService.get('/printers/getOptionAndon', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      function createPrinter(printerData,optionList) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          Printer: printerData,
          optionList: optionList
        };

        ApiService.put('/printers/printer', postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function updatePrinter(printerData,optionList) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Printer: printerData,
          optionList: optionList
        };

        ApiService.post('/printers/printer', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function deletePrinter(printerId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Printer: {
            id: printerId
          }
        };

        ApiService.delete('/printers/printer', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function types() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/printers/printer/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function colors() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/printers/printer/colors', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function factoryAll() {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get()
            };

            ApiService.get('/printers/printer/factories', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      /**** helpers ****/
      function extractColorsFromPrinter(printer) {
        printer.Printer.colorsArr = printer.Printer.color.toLowerCase().split('&');
      }

    });

})();
      