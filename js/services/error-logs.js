(function () {
  angular.module('printty')

    .factory('ErrorLog', function (ApiService, $q, Auth) {

      'ngInject';

      var API_MAP = {
        errorLogs: '/error-logs',
        errorLog: '/error-log',
      };

      return {
          fetchLogs: fetchLogs,
          fetchLog: fetchLog,
          createLog: createLog,
          updateLog: updateLog,
          deleteLog: deleteLog,
      };

      function fetchLogs(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.errorLogs, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchLog(logId) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          error_log_id: logId
        };

        ApiService.get(API_MAP.errorLog, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createLog(log) {
        var deferred = $q.defer();

        var putData = {
          session: Auth.session.get(),
          ErrorLogUploadCsv: log
        };

        ApiService.put(API_MAP.errorLog, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateLog(log) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          ErrorLogUploadCsv: log
        };

        ApiService.post(API_MAP.errorLog, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteLog(logId) {
        var deferred = $q.defer();

        var deleteData = {
          session: Auth.session.get(),
            ErrorLogUploadCsv: {
            id: logId
          }
        };

        ApiService.delete(API_MAP.errorLog, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });
})();