(function () {
    angular.module('printty')

        .factory('ImageName', function (ApiService, $q, Auth) {
            'ngInject';

            var API_MAP = {
                images_name: '/images/name',
                image_name: '/image/name',
            };
            return {
                fetchImagesSizeName: fetchImagesSizeName,
                fetchImageSizeName: fetchImageSizeName,
                updateImageSizeName: updateImageSizeName,
            };

            function fetchImagesSizeName(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.images_name, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchImageSizeName(imageNameId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    image_name_id: imageNameId
                };

                ApiService.get(API_MAP.image_name, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateImageSizeName(image_name) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductSize: image_name
                };

                ApiService.post(API_MAP.image_name, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();