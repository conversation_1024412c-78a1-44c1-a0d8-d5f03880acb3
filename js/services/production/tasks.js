(function () {

  angular.module('printty')

    .factory('ProductionTasks', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        tasks: '/production/tasks',
        task: '/production/tasks/task',
        replanTask: '/production/tasks/task/replan',
        csv: '/production/tasks/csv',
        status:"/production/tasks/task/status",
        qrCode:"/production/printers/qrcodes",
        deleteTasks:"/production/tasks/delete",
        downloadCsv: '/production/tasks/business/csv',
      };

      return {
        fetchTasks: fetchTasks,
        fetchTask: fetchTask,
        deleteTask: deleteTask,
        replanTask: replanTask,
        fetchTasksCSV: fetchTasksCSV,
        changeStatus: changeStatus,
        fetchQRCode: fetchQRCode,
        deleteTasks: deleteTasks,
        downloadBusinessCSV: downloadBusinessCSV,
      };

      function fetchTasks(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.tasks, getParams).then(
          function(data) {

            _.forEach(data.tasks, function (task) {
              task.rowType = 'single';
            });

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchTask(task) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          task_id: task.taskId,
          step_id:  task.taskStepId,
        };

        ApiService.get(API_MAP.task, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteTask(taskId) {
        var deferred = $q.defer();

        var deleteData  = {
          session: Auth.session.get(),
          Task: {
            id: taskId
          }
        };

        ApiService.delete(API_MAP.task, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function replanTask(taskId) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          Task: {
            id: taskId
          }
        };

        ApiService.post(API_MAP.replanTask, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchTasksCSV(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.csv, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function changeStatus(data, statusKey) {
        var postData = {
          session: Auth.session.get(),
          Task: {
            id: data
          },
          TaskStepStatus: {
              code: statusKey
          }
        };
        return  ApiService.post(API_MAP.status,postData);
      }

        function deleteTasks(data) {
            var postData = {
                session: Auth.session.get(),
                Task: {
                    id: data
                }
            };
            return  ApiService.post(API_MAP.deleteTasks,postData);
        }

        function fetchQRCode(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get(API_MAP.qrCode, getParams).then(
                function(data) {
                    var codes = _.flatMap(data.qrcodes, function (code) {
                        return code.QRCode.content_url;
                    });

                    deferred.resolve(codes);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadBusinessCSV(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get(API_MAP.downloadCsv, getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/zip'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;

        }

    });

})();
      