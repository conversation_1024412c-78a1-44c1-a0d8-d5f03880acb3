(function () {

    angular.module('printty')

        .factory('ProductionUnplanned', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                unplanned: '/production/unplanned',
                unplannedTypes: '/production/unplanned/types',
                csvFile: '/production/unplanned/factory',
            };

            return {
                fetchUnplanned: fetchUnplanned,
                fetchTypes: fetchTypes,
                downloadCSV: downloadCSV,
            };

            function downloadCSV(date) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    reg: Math.random().toString(36).substring(7),
                    date: date
                };

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function (data) {
                        deferred.resolve(data);
                    },
                    function (error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchUnplanned(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.unplanned, getParams).then(
                    function (data) {
                        deferred.resolve(data);
                    },
                    function (error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchTypes() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.unplannedTypes, getParams).then(
                    function (data) {
                        deferred.resolve(data);
                    },
                    function (error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
      