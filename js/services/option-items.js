(function () {

    angular.module('printty')

        .factory('OptionItems', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                optionItems: '/option-items',
                optionItem: '/option-item',
                csvFile: '/option-items/csv',
                csvFileTemplate: '/option-items/csv/template',
            };

            return {
                fetchOptionItems: fetchOptionItems,
                createOptionItem: createOptionItem,
                fetchOptionItem: fetchOptionItem,
                updateOptionItem: updateOptionItem,
                deleteOptionItem: deleteOptionItem,
                downloadCSV: downloadCSV,
                downloadCSVTemplate: downloadCSVTemplate,
                uploadCSV: uploadCSV,
            };

            function fetchOptionItems(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.optionItems, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createOptionItem(data) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    option_item: data
                };

                ApiService.post(API_MAP.optionItem, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOptionItem(optionItemId){
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    option_item_id: optionItemId
                };

                ApiService.get(API_MAP.optionItem, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateOptionItem(data) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    option_item: data
                };

                ApiService.put(API_MAP.optionItem, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteOptionItem(optionItemId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    option_item_id: optionItemId
                };

                ApiService.delete(API_MAP.optionItem, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFile, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSVTemplate(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCSV(file) {
                var deferred = $q.defer();

                var uploadData = {
                    session: Auth.session.get(),
                    csv: file
                };

                ApiService.upload(API_MAP.csvFile, uploadData).then(
                    function(data) {
                        deferred.resolve(data);
                        if (data['url']) {
                            var name = ApiService.getFileNameFromUrl(data['url']);

                            ApiService.download(data['url'], {
                                name: name,
                                type: 'application/csv'
                            })
                                .then(function(data) {
                                    deferred.resolve(data['url']);
                                })
                                .catch(function(error) {
                                    deferred.reject(error);
                                });
                        }

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
