(function () {
    angular.module('printty')

        .factory('ProductCellApi', function (ApiService, $q, Auth) {
            'ngInject';

            var API_MAP = {
                product_cells: '/product-cells',
                product_cell: '/product-cell',
                options: '/product-cell/options',
            };
            return {
                fetchProductCells: fetchProductCells,
                fetchOptions: fetchOptions,
                fetchProductCell: fetchProductCell,
                createProductCell: createProductCell,
                updateProductCell: updateProductCell,
                deleteProductCell: deleteProductCell,
            };

            function fetchProductCells(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.product_cells, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOptions() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                };

                ApiService.get(API_MAP.options, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductCell(productCellId) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    productCellId: productCellId
                };

                ApiService.get(API_MAP.product_cell, params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createProductCell(data) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    ProductCell: {
                        id: data.id,
                        title: data.title,
                        factory_id: data.factory_id,
                        product_cell_type_id: data.product_cell_type_id,
                    }
                };

                ApiService.put(API_MAP.product_cell, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateProductCell(data) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductCell: {
                        id: data.id,
                        title: data.title,
                        factory_id: data.factory_id,
                        product_cell_type_id: data.product_cell_type_id,
                    }
                };

                ApiService.post(API_MAP.product_cell, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteProductCell(productCellId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    product_cell_id: productCellId,
                };

                ApiService.delete(API_MAP.product_cell, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();