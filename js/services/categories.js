(function () {
    angular.module('printty')

        .factory('Categories', function (ApiService, $q, Auth) {
            'ngInject';

            var API_MAP = {
                    categories: '/product/categories',
                    category: '/product/category',
            };
            return {
                fetchCategories: fetchCategories,
                fetchCategory: fetchCategory,
                createCategory: createCategory,
                updateCategory: updateCategory,
                deleteCategory: deleteCategory,
            };

            function fetchCategories(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.categories, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchCategory(categoryId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    category_id: categoryId
                };

                ApiService.get(API_MAP.category, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createCategory(category) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    ProductCategory: category
                };

                ApiService.put(API_MAP.category, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateCategory(category) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    ProductCategory: category
                };

                ApiService.post(API_MAP.category, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteCategory(categoryId) {
                var deferred = $q.defer();
                var deleteData = {
                    session: Auth.session.get(),
                    ProductCategory: {
                        id: categoryId
                    }
                };

                ApiService.delete(API_MAP.category, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();