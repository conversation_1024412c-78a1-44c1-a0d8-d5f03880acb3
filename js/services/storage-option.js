(function () {

    angular.module('printty')

        .factory('StorageOption', function (ApiService, $q, Auth, $filter) {

            "ngInject";

            return {
                list: listStorage,
                details: detailsStorage,
                productQuantity: {
                    add: addProductQuantity,
                    remove: removeProductQuantity
                },
                productActivity: productActivity,
                csv : {
                    upload: uploadCsv,
                    download: downloadCSV,
                    downloadShipment: downloadShipmentCSV
                },
                picking:{
                    getPicking :picking,
                    pick: pick,
                },
                activity: storageActivity,
                deleteActivity: deleteActivity,
            };

            function listStorage(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/storage-option-item', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function detailsStorage(params) {
                var deferred = $q.defer();

                _.extend(params, {
                    session: Auth.session.get()
                });

                ApiService.get('/storage-option-item/product', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function addProductQuantity(data) {
                var deferred = $q.defer();

                _.extend(data, {
                    session: Auth.session.get()
                });

                ApiService.put('/storage-option-item/product', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function removeProductQuantity(data) {
                var deferred = $q.defer();

                _.extend(data, {
                    session: Auth.session.get()
                });

                ApiService.delete('/storage-option-item/product', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function productActivity(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/storage-option-item/activity', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function uploadCsv(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.upload('/storage-option/csv', params).then(
                    function(data) {

                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }


            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get('/storage-option/csv', getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadShipmentCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.get('/storage-option/csv/shipment', getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function picking(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/storage-option/picking', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function pick(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.put('/storage-option/picking', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function storageActivity(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/storage-option/activity', params).then(
                    function(data) {

                        structureActivity(data);

                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function structureActivity(dataActivity) {

                var products = dataActivity.products,
                    structured = [],
                    lastDate = null,
                    date,
                    updatedAt;

                // use special param to store actual number of products (i.e. without date rows)
                dataActivity.productsLength = products.length;

                for (var i = 0; i < dataActivity.productsLength; i++) {

                    products[i].mode = 'product';

                    // adjust date to user date
                    // updatedAt = $filter('date')(products[i].StorageActivity.updated_at.replace(' ', 'T') + '+0000', 'yyyy.MM.dd HH:mm:ss');

                    // set time param
                    // products[i].StorageActivity.time = updatedAt.substr(11, 5);

                    // set trend param (to use as class in view)
                    if (+products[i].StorageOptionActivity.quantity > 0) {
                        products[i].StorageOptionActivity.trend = 'up';
                        products[i].StorageOptionActivity.format_quantity = +products[i].StorageOptionActivity.quantity;
                    } else if (+products[i].StorageOptionActivity.quantity < 0) {
                        products[i].StorageOptionActivity.trend = 'down';
                        products[i].StorageOptionActivity.format_quantity = +products[i].StorageOptionActivity.quantity * (-1);
                    } else {
                        products[i].StorageOptionActivity.trend = 'ng-hide';
                    }

                    // get date
                    date = products[i].StorageOptionActivity.lastDate;

                    // create date row
                    if (lastDate !== date) {
                        structured.push({
                            mode: 'date',
                            date: date
                        });

                        lastDate = date;
                    }

                    structured.push(products[i]);

                }

                dataActivity.products = structured;
                dataActivity.lastDate = lastDate;

            }

            function deleteActivity(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete('/storage-option/activity', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }



        });

})();
