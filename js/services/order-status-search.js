(function () {
    angular.module('printty')

        .factory('OrderStatusSearch', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                orderStatusSearch: '/order-status-search',
                status:"/production/tasks/task/status",
                cancelOrder:"/external/orders/order/cancel",
            };

            return {
                fetchOrderStatusSearch: fetchOrderStatusSearch,
                changeStatus: changeStatus,
                cancel: cancelOrder,
            };

            function fetchOrderStatusSearch(params) {

                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.orderStatusSearch, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function changeStatus(data, taskStepId, statusKey) {
                var postData = {
                    session: Auth.session.get(),
                    Task: {
                        id: data,
                        step_id:  taskStepId,
                    },
                    TaskStepStatus: {
                        code: statusKey
                    }
                };
                return  ApiService.post(API_MAP.status,postData);
            }

            function cancelOrder(params) {
                var deferred = $q.defer();

                var putData  = {
                    session: Auth.session.get(),
                    Order: params
                };

                ApiService.put(API_MAP.cancelOrder, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();