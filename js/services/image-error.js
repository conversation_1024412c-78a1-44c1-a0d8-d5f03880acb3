(function () {
    angular.module('printty')

        .factory('ImageError', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                imageError: '/image-error',
            };

            return {
                fetchImageError: fetchImageError,
            };

            function fetchImageError(params) {

                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.imageError, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();