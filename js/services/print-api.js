(function () {

  angular.module('printty')

    .factory('PrintApi', function (ApiService, $q, Auth, $http,$window,electron) {

      "ngInject";

      return {
        search: search,
        
        fetchSingleTask: fetchSingleTask,
        fetchMultipleTask: fetchMultipleTask,
        
        fetchSingleTaskSource: fetchSingleTaskSource,
        fetchMultipleTaskSource: fetchMultipleTaskSource,
        
        processSingleTask: processSingleTask,
        failSingleTask: failSingleTask,
        retrySingleTask: retrySingleTask,

        processMultipleTask: processMultipleTask,
        failMultipleTask: failMultipleTask,
        retryMultipleTask: retryMultipleTask,
        fetchNewImageName: fetchNewImageName,
        getPrinterTypeLocal: getPrinterTypeLocal,
      };
      
      function search(code) {
        var deferred = $q.defer();

        var params = {
              session: Auth.session.get(),
              code: code
            };

        ApiService.get('/print/task/search', params, {
          disableErrorHandle: true
        }).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchSingleTask(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/print/task/single', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchMultipleTask(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/print/task/multiple', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchSingleTaskSource(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/print/task/single/source', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchMultipleTaskSource(params) {
        var deferred = $q.defer();

        params = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get('/print/task/multiple/source', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function processSingleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Task: {
            id: params.taskId
          },
          TaskStep: {
            id: params.stepId
          },
          printer_title : params.printer_title,
        };

        ApiService.post('/print/task/single/processed', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function failSingleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Task: {
            id: params.taskId
          },
          TaskStep: {
            id: params.stepId
          }
        };

        ApiService.post('/print/task/single/failed', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function retrySingleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Task: {
            id: params.taskId
          },
          TaskStep: {
            id: params.stepId
          }
        };

        ApiService.post('/print/task/single/retry', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function processMultipleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          TaskGroupStep: {
            id: params.stepId
          }
        };

        ApiService.post('/print/task/multiple/processed', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function failMultipleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          TaskGroupStep: {
            id: params.stepId
          },
          failed_items: params.failedItems
        };

        ApiService.post('/print/task/multiple/failed', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function retryMultipleTask(params) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          TaskGroupStep: {
            id: params.stepId
          }
        };

        ApiService.post('/print/task/multiple/retry', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchNewImageName(params) {
          var deferred = $q.defer();

          var getParams = _.merge(
              { session: Auth.session.get() },
              params
          );

          ApiService.get('/image/name/newname', getParams).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

        function getPrinterTypeLocal() {
            var deferred = $q.defer();
            var url = '/local_config/local_config.ini';
            if (electron) {
                var path = $window.location.pathname;
                url = 'file://' + path.slice(0, path.lastIndexOf('/') + 1) + 'local_config/local_config.ini';
            }
            $http.get(url).then(
                function(response) {
                    try {
                        var parsedData = parseINIString(response.data);
                        deferred.resolve(parsedData);
                    } catch (error) {
                        deferred.reject('Error parsing INI file: ' + error);
                    }
                },
                function(error) {
                    deferred.reject('Error loading INI file: ' + error);
                }
            );
            return deferred.promise;
        }

        //convert file ini;
        function parseINIString(data){
            var regex = {
                section: /^\s*\[\s*([^\]]*)\s*\]\s*$/,
                param: /^\s*([^=]+?)\s*=\s*(.*?)\s*$/,
                comment: /^\s*;.*$/
            };
            var value = {};
            var lines = data.split(/[\r\n]+/);
            var section = null;
            lines.forEach(function(line){
                if(regex.comment.test(line)){
                    return;
                }else if(regex.param.test(line)){
                    var match = line.match(regex.param);
                    if(section){
                        value[section][match[1]] = match[2];
                    }else{
                        value[match[1]] = match[2];
                    }
                }else if(regex.section.test(line)){
                    var match = line.match(regex.section);
                    value[match[1]] = {};
                    section = match[1];
                }else if(line.length == 0 && section){
                    section = null;
                }
            });
            return value;
        }
      
    });

})();
      