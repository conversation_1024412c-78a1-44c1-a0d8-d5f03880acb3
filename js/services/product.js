(function () {

  angular.module('printty')

    .factory('Product', function (ApiService, $q, Auth) {

      'ngInject';

      return {
        list: list,
        listTrims: listTrims,

        typesAll: typesAll,
        typesClothesAll: typesClothesAll,
        types: types,
        categories: categories,
        printingBases: printingBases,
        uvFrames:uvFrames,
        cpFrames: cpFrames,
        details: details,
        create: create,
        update: update,
        updateTrims: updateTrims,
        delete: deleteProduct,
        guideNoteList:guideNoteList,
        delivery : getDeliveries,
        guideNoteQR:guideNoteQR,
        uploadPdfFile:uploadPdfFile,
        fetchProductFee:fetchProductFee,
        saveFeeDesign:saveFeeDesign,
      };

      function list(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/products', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function listTrims(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/products/trims', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function typesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/products/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function typesClothesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/products/type/clothes', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function categories() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/products/product/categories', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function types() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/products/product/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function printingBases() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/products/product/printing/bases', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function uvFrames() {
          var deferred = $q.defer();

          var params = {
              session: Auth.session.get()
          };

          ApiService.get('/products/product/uvFrames', params).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

        function cpFrames() {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get()
            };

            ApiService.get('/products/product/cpFrames', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

      function details(productId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          product_id: productId
        };

        ApiService.get('/products/product', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function guideNoteList() {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
            };

            ApiService.get('/products/guidenote', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function guideNoteQR(id) {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
                id: id
            };

            ApiService.get('/products/guidenoteqr', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

      function create(productData) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          Product: productData.Product,
          ProductPrice: productData.ProductPrice
        };

        ApiService.put('/products/product', postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function update(productData) {
        var deferred = $q.defer();

        var data = {
          session: Auth.session.get(),
          Product: productData.Product,
          ProductPrice: productData.ProductPrice
        };

        ApiService.post('/products/product', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateTrims(trims) {
        var deferred = $q.defer();

        var data = {
          session: Auth.session.get(),
          not_trim: trims.not_trim,
          trim_again: trims.trim_again,
          not_trim_sleeve: trims.not_trim_sleeve,
          trim_again_sleeve: trims.trim_again_sleeve,
        };

        ApiService.post('/products/trims', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteProduct(productId) {
        var deferred = $q.defer();

        var data = {
          session: Auth.session.get(),
          Product: {
            id: productId
          }
        };

        ApiService.delete('/products/product', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function uploadPdfFile(file) {
            var deferred = $q.defer();

            var uploadData = {
                session: Auth.session.get(),
                file: file
            };

            ApiService.upload('/products/guidenote', uploadData).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

        return deferred.promise;
      }
      function getDeliveries(){
          var deferred = $q.defer();

          var params = {
              session: Auth.session.get(),
          };

          ApiService.get('/products/product/delivery', params).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

        function fetchProductFee(productId) {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get(),
                product_id: productId
            };

            ApiService.get('/products/product/fee-design', params).then(
                function (data) {
                    deferred.resolve(data);
                },
                function (error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function saveFeeDesign(product_id,data_fee,customers) {
            var deferred = $q.defer();

            var data = {
                session: Auth.session.get(),
                data_fee: data_fee,
                product_id: product_id,
                customers : customers,
            };

            ApiService.post('/products/product/fee-design', data).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
