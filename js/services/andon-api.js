(function () {

    angular.module('printty')

        .factory('AndonApi', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                andon: '/andon',
                andonReference: '/reference/andon',
                andons: '/andons',
                options: '/andon/options',
                statistical: '/andon-statistical',
            };

            return {
                getAndonData: getAndonData,
                getAndonStatistical: getAndonStatistical,
                fetchAndons: fetchAndons,
                fetchOptions: fetchOptions,
                fetchAndon: fetchAndon,
                createAndon: createAndon,
                updateAndon: updateAndon,
                deleteAndon: deleteAndon,
            };

            function getAndonData(params) {
                var deferred = $q.defer(2000);

                var getParams = _.merge({session: Auth.session.get()}, params);

                ApiService.get(API_MAP.andon, getParams, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function getAndonStatistical(params) {
                var deferred = $q.defer(2000);

                var getParams = _.merge({session: Auth.session.get()}, params);

                ApiService.get(API_MAP.statistical, getParams, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchAndons(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.andons, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOptions() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                };

                ApiService.get(API_MAP.options, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchAndon(andonId) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    andonId: andonId
                };

                ApiService.get(API_MAP.andonReference, params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createAndon(data) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Andon: {
                        id: data.id,
                        title: data.title,
                        factory_id: data.factory_id,
                    }
                };

                ApiService.put(API_MAP.andonReference, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateAndon(data) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    Andon: {
                        id: data.id,
                        title: data.title,
                        factory_id: data.factory_id,
                    }
                };

                ApiService.post(API_MAP.andonReference, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteAndon(andonId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    andon_id: andonId,
                };

                ApiService.delete(API_MAP.andonReference, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
            
        });

})();