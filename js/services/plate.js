(function () {

  angular.module('printty')

    .factory('Plate', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        plates: '/plates',
        plate: '/plates/plate'
      };
      
      return {
        fetchPlates: fetchPlates,
        createPlate: createPlate,
        fetchPlate: fetchPlate,
        updatePlate: updatePlate,
        deletePlate: deletePlate
      };

      function fetchPlates(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() }, 
          params
        );

        ApiService.get(API_MAP.plates, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function createPlate(plateData) {
        var deferred = $q.defer();

        var putData = {
          session: Auth.session.get(),
          Plate: plateData
        };

        ApiService.put(API_MAP.plate, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchPlate(plateId) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          plate_id: plateId
        };

        ApiService.get(API_MAP.plate, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updatePlate(plateData) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          Plate: plateData
        };

        ApiService.post(API_MAP.plate, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deletePlate(plateId) {
        var deferred = $q.defer();

        var deleteData = {
          session: Auth.session.get(),
          Plate: {
            id: plateId
          }
        };

        ApiService.delete(API_MAP.plate, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
    });

})();
      