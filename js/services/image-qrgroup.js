(function () {

    angular.module('printty')

        .factory('ImageQrgroup', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                qrgroups: '/images/qrgroups',
                reloadQrgroup: '/images/qrgroups/reload',
            };

            return {
                fetchSentQrgroup: fetchSentQrgroup,
                reloadQrgroup: reloadQrgroup,
                deleteQrgroup: deleteQrgroup,
            };

            function fetchSentQrgroup(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.qrgroups, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadQrgroup(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadQrgroup, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteQrgroup(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.qrgroups, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
