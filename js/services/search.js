/**
 * Created by <PERSON><PERSON> on 06.07.2017.
 */
(function () {

  angular.module('printty')

    .factory('SearchService', function (ApiService, Auth) {

      "ngInject";

      var API_MAP = {
        search: '/search',
      };

      return {
        search: search,
      };

      function search(params) {
        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

       return ApiService.get(API_MAP.search, getParams);
      }

    });

})();
