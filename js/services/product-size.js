(function () {

  angular.module('printty')

    .factory('ProductSize', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        sizes: '/products/product/sizes',
        size: '/products/product/sizes/size',
        setMain: '/products/product/sizes/size/main/set',

        codes: '/products/product/sizes/size/linked/codes',
        code: '/products/product/sizes/size/linked/codes/code'
      };

      return {
        list: sizesList,
        details: sizeDetails,
        create: sizeCreate,
        update: sizeUpdate,
        delete: sizeDelete,
        
        setAsMain: sizeSetAsMain,
        
        linkCode: {
          fetchCodes: fetchCodes,

          fetchCode: fetchCode,
          createCode: createCode,
          updateCode: updateCode,
          deleteCode: deleteCode
        }
      };
      
      function sizesList(productId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          product_id: productId
        };

        ApiService.get(API_MAP.sizes, params).then(
          function(data) {

            _.forEach(data.sizes, function (size) {
              size.ProductSize.is_main = ApiService.toBoolean(size.ProductSize.is_main);
            });

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function sizeDetails(sizeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          size_id: sizeId
        };

        ApiService.get(API_MAP.size, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function sizeCreate(sizeData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          sizeData
        );

        ApiService.put(API_MAP.size, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function sizeUpdate(sizeData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          sizeData
        );

        ApiService.post(API_MAP.size, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function sizeDelete(sizeId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductSize: {
            id: sizeId
          }
        };

        ApiService.delete(API_MAP.size, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function sizeSetAsMain(sizeId) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          ProductSize: {
            id: sizeId
          }
        };

        ApiService.post(API_MAP.setMain, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchCodes(sizeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          size_id: sizeId
        };

        ApiService.get(API_MAP.codes, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchCode(codeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          code_id: codeId
        };

        ApiService.get(API_MAP.code, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createCode(sizeId, codeData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          ProductSize: {
            id: sizeId
          },
          ProductSizeLinkedCode: codeData
        };

        ApiService.put(API_MAP.code, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateCode(codeData) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductSizeLinkedCode: codeData
        };

        ApiService.post(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteCode(codeId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductSizeLinkedCode: {
            id: codeId
          }
        };

        ApiService.delete(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });

})();