(function () {

    angular.module('printty')

        .factory('KenpinApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                search: search,

                fetchSingleTask: fetchSingleTask,
                fetchMultipleTask: fetchMultipleTask,

                fetchSingleTaskSource: fetchSingleTaskSource,
                fetchMultipleTaskSource: fetchMultipleTaskSource,

                processSingleTask: processSingleTask,
                failSingleTask: failSingleTask,
                retrySingleTask: retrySingleTask,

                processMultipleTask: processMultipleTask,
                failMultipleTask: failMultipleTask,
                retryMultipleTask: retryMultipleTask,
                fetchKenpinStatus:fetchKenpinStatus,
                fetchKenpinStatusByType:fetchKenpinStatusByType,
                fetchDefectContent:fetchDefectContent,
                createKenpin: createKenpin,
                getOrderCheck: getOrderCheck
            };

            function getOrderCheck(id) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    taskId: id
                };

                ApiService.get('/kenpin/checked', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createKenpin(data) {
                var deferred = $q.defer();

                var kenpinData = {
                    session: Auth.session.get(),
                    kenpin: data
                };

                ApiService.put('/kenpin/task', kenpinData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function search(code) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    code: code
                };

                ApiService.get('/kenpin/task/search', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSingleTask(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/kenpin/task/single', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchMultipleTask(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/print/task/multiple', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSingleTaskSource(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/print/task/single/source', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchMultipleTaskSource(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/print/task/multiple/source', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processSingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/print/task/single/processed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function failSingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/print/task/single/failed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function retrySingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/print/task/single/retry', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/print/task/multiple/processed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function failMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    },
                    failed_items: params.failedItems
                };

                ApiService.post('/print/task/multiple/failed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function retryMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/print/task/multiple/retry', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
            
            function fetchKenpinStatus(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/kenpin/task/status', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchDefectContent(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );
                ApiService.get('/kenpin/task/defectContent', getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }


            function fetchKenpinStatusByType(params){
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/kenpin/task/statusByType', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
