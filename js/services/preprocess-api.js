(function () {

    angular.module('printty')

        .factory('PreprocessApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                search: search,

                fetchSingleTask: fetchSingleTask,
                fetchMultipleTask: fetchMultipleTask,

                fetchSingleTaskSource: fetchSingleTaskSource,
                fetchMultipleTaskSource: fetchMultipleTaskSource,

                processSingleTask: processSingleTask,
                failSingleTask: failSingleTask,
                retrySingleTask: retrySingleTask,

                processMultipleTask: processMultipleTask,
                failMultipleTask: failMultipleTask,
                retryMultipleTask: retryMultipleTask,
                fetchNewImageName: fetchNewImageName,
            };

            function search(code) {
                var deferred = $q.defer();

                var params = {
                    session: Auth.session.get(),
                    code: code,
                    is_preprocess: 1
                };

                ApiService.get('/preprocess/task/search', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSingleTask(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/preprocess/task/single', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchMultipleTask(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/preprocess/task/multiple', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSingleTaskSource(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/preprocess/task/single/source', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchMultipleTaskSource(params) {
                var deferred = $q.defer();

                params = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/preprocess/task/multiple/source', params).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processSingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    },
                    is_preprocess: 1,
                };

                ApiService.post('/preprocess/task/single/processed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function failSingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/preprocess/task/single/failed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function retrySingleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    Task: {
                        id: params.taskId
                    },
                    TaskStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/preprocess/task/single/retry', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function processMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/preprocess/task/multiple/processed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function failMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    },
                    failed_items: params.failedItems
                };

                ApiService.post('/preprocess/task/multiple/failed', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function retryMultipleTask(params) {
                var deferred = $q.defer();

                var data  = {
                    session: Auth.session.get(),
                    TaskGroupStep: {
                        id: params.stepId
                    }
                };

                ApiService.post('/preprocess/task/multiple/retry', data).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchNewImageName(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get('/image/name/newname', getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
