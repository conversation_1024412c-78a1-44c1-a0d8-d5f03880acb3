(function () {
    angular.module('printty')

        .factory('Clients', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                clients: '/clients',
                client: '/client',
                client_kornit: '/client/kornit',
            };

            return {
                fetchClients: fetchClients,
                fetchClient: fetchClient,
                createClient: createClient,
                updateClient: updateClient,
                deleteClient: deleteClient,
                createClientKornit: createClientKornit,
            };

            function fetchClients(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.clients, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchClient(clientId) {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get(),
                    client_id: clientId
                };

                ApiService.get(API_MAP.client, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createClient(client) {
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Client: client
                };

                ApiService.put(API_MAP.client, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateClient(client) {
                var deferred = $q.defer();

                var postData = {
                    session: Auth.session.get(),
                    Client: client
                };

                ApiService.post(API_MAP.client, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteClient(clientId) {
                var deferred = $q.defer();

                var deleteData = {
                    session: Auth.session.get(),
                    Client: {
                        id: clientId
                    }
                };

                ApiService.delete(API_MAP.client, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createClientKornit(client){
                var deferred = $q.defer();

                var putData = {
                    session: Auth.session.get(),
                    Client: client
                };

                ApiService.put(API_MAP.client_kornit, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });
})();