(function () {
  angular.module('printty')

    .factory('PrintingBase', function (ApiService, $q, Auth) {

      'ngInject';

      var API_MAP = {
        bases: '/printing/bases',
        base: '/printing/bases/base',
          csvFile: '/import/printing/bases/csv',
          csvFileTemplate: '/import/printing/bases/csv/template',
      };

      return {
        fetchBases: fetchBases,
        fetchBase: fetchBase,
        createBase: createBase,
        updateBase: updateBase,
        deleteBase: deleteBase,
          uploadCSV: uploadCSV,
          downloadCSV: downloadCSV,
          CSVdownloadTemplate: CSVdownloadTemplate,
      };

      function fetchBases(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.bases, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchBase(baseId) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          printing_base_id: baseId
        };

        ApiService.get(API_MAP.base, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createBase(base) {
        var deferred = $q.defer();

        var putData = {
          session: Auth.session.get(),
          PrintingBase: base
        };

        ApiService.put(API_MAP.base, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateBase(base) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          PrintingBase: base
        };

        ApiService.post(API_MAP.base, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteBase(baseId) {
        var deferred = $q.defer();

        var deleteData = {
          session: Auth.session.get(),
          PrintingBase: {
            id: baseId
          }
        };

        ApiService.delete(API_MAP.base, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function uploadCSV(file) {
            var deferred = $q.defer();

            var uploadData = {
                session: Auth.session.get(),
                csv: file
            };

            ApiService.upload(API_MAP.csvFile, uploadData).then(
                function(data) {
                    deferred.resolve(data);
                    if (data['url']) {
                        var name = ApiService.getFileNameFromUrl(data['url']);

                        ApiService.download(data['url'], {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data['url']);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });
                    }

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadCSV(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get(API_MAP.csvFile, getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function CSVdownloadTemplate(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get(API_MAP.csvFileTemplate, getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });
})();