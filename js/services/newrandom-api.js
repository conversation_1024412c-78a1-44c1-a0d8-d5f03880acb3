(function () {

    angular.module('printty')

        .factory('NewRandomApi', function (ApiService, $q, Auth) {

            "ngInject";

            return {
                getNewRandomData: getNewRandomData,
            };

            function getNewRandomData(date) {
                var deferred = $q.defer(2000);

                var params = {
                    session: Auth.session.get(),
                    date : date
                };

                ApiService.get('/random-mode', params, {
                    disableErrorHandle: true
                }).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }
        });

})();