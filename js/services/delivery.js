(function () {
  angular.module('printty')

    .factory('Delivery', function (ApiService, $q, Auth) {

      'ngInject';

      return {
        process: process,
        getPlaceData: getPlaceData,
          getFactories: getFactories
      };
      
      function process(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
            { session: Auth.session.get() },
            params
        );

        ApiService.get('/delivery/process', getParams, {
          disableErrorHandle: true
        }).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function getPlaceData(date){
          var deferred = $q.defer();

          var params = _.merge(
              {session: Auth.session.get()},
              date
          );

          ApiService.get('/delivery/getPlaceData', params, {
              disableErrorHandle: true
          }).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

        function getFactories(){
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get()
            };

            ApiService.get('/factories/user', params, {
                disableErrorHandle: true
            }).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });
})();