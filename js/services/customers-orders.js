(function () {

  angular.module('printty')

    .factory('CustomersOrders', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        orders: '/customers/customer/orders',
        order: '/customers/customer/orders/order',
        
        orderItems: '/customers/customer/orders/order/items',
        orderItem: '/customers/customer/orders/order/items/item'
      };

      return {
        fetchOrders: fetchOrders,
        fetchOrder: fetchOrder,
        
        fetchOrderItems: fetchOrderItems,
        fetchOrderItem: fetchOrderItem
      };

      function fetchOrders(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.orders, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchOrder(orderId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          order_id: orderId
        };

        ApiService.get(API_MAP.order, params).then(
          function(data) {

            data.Order.is_billing_shipping_same = ApiService.toBoolean(data.Order.is_billing_shipping_same);
            data.Order.use_original_tag = ApiService.toBoolean(data.Order.use_original_tag);
            data.Order.use_package = ApiService.toBoolean(data.Order.use_package);

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchOrderItems(params) {
        var deferred = $q.defer();

        var getParams = angular.merge(
          { session: Auth.session.get() },
          params);

        ApiService.get(API_MAP.orderItems, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchOrderItem(itemId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          item_id: itemId
        };

        ApiService.get(API_MAP.orderItem, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });

})();
      