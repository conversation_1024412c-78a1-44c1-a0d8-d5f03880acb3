(function () {

    angular.module('printty')

        .factory('ImageUv', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                uv: '/images/uv',
                reloadUv: '/images/uv/reload',
            };

            return {
                fetchUv: fetchUv,
                reloadUv: reloadUv,
                deleteUv: deleteUv,
            };

            function fetchUv(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.uv, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function reloadUv(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.reloadUv, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteUv(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.uv, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
