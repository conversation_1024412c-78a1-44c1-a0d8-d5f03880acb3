(function () {

  angular.module('printty')

    .factory('Auth', function(ApiService, $window, $q, $cookies, Store, BUILD_VERSION, electron) {

      "ngInject";
      
      var isElectron = !!electron;
      
      var _session = null;
      var mode = null;
      var mode_sub = null;
      var mode_sub_tab = null;
      var is_franchise = null;
      var _defaultPermissions, _permissions;
      
      _defaultPermissions = {
        account: false,
        settings: false,
        orders: false,
        print: false,
        dtftranscription: false,
        printoriginal: false,
        storage: false,
        kenpin: false,
        kenpinuv: false,
        reference: false,
        delivery: false,
        seirendelivery: false,
        sfdelivery: false,
        users: false,
        production: false,
        customers: false,
        random: false,
        newrandom: false,
        preprocess: false,
        andonkai: false,
        andonwr: false,
        andonsf: false,
        seirenrandom: false,
        totalrandom: false,
        randomuv: false,
        image: false,
        import: false,
        quality: false,
        seirenquality: false,
        repair: false,
        business: false,
        barcode: false,
        bulkshipping: false,
        factoryorder: false,
      };

      _permissions = _.cloneDeep(_defaultPermissions);

      var service = {

        session: {
          set: setSession,
          get: getSession,
          destroy: destroySession
        },

        isAuthenticated: isAuthenticated,
        isAuthorized: isAuthorized,

        signin: signin,
        signup: signup,
        signout: signout,
        update: update,

        fetchAccount: fetchAccount,
        updateAccount: updateAccount,
        fetchSettings: fetchSettings,
        updateSettings: updateSettings,
        contains: contains,

        get defaultRoute() {

          if (!this.isAuthenticated()) {
            return 'auth.signin';
          }
          if(mode && mode_sub){
              var mode_sub_item = mode_sub.find(function(item) {
                  return item.mode_id === mode[0].id;
              });
              if(mode_sub && mode_sub_tab && mode_sub_item){
                  var mode_sub_tab_item = mode_sub_tab.find(function(item) {
                      return item.mode_sub_id === mode_sub_item.id;
                  });
                  return mode_sub_tab_item ? mode_sub_tab_item.key : mode_sub_item.key;
              }
          }
          if (_permissions.customers) {
            return 'admin.income.clients';
          }

          if (_permissions.production) {
            return 'admin.production.tasks';
          }

          if (_permissions.users) {
            return 'admin.users';
          }

          if (_permissions.orders) {
            return 'user.orders';
          }

          if (_permissions.print) {
            return 'print';
          }

          if (_permissions.dtftranscription) {
             return 'dtftranscription';
          }

          if (_permissions.printoriginal) {
            return 'printoriginal';
          }

          if (_permissions.storage) {
            return 'storage';
          }

          if (_permissions.kenpin) {
            return 'kenpin';
          }

          if (_permissions.kenpini) {
            return 'kenpini';
          }

            if (_permissions.kenpinuv) {
                return 'kenpinuv';
            }

            if (_permissions.random) {
                return 'random';
            }

            if (_permissions.newrandom) {
                return 'newrandom';
            }

            if (_permissions.preprocess) {
                return 'preprocess';
            }

            if (_permissions.andonkai) {
                return 'andonkai';
            }

            if (_permissions.andonwr) {
                return 'andonwr';
            }

            if (_permissions.andonsf) {
                return 'andonsf';
            }

            if (_permissions.seirenrandom) {
                return 'seirenrandom';
            }

            if (_permissions.totalrandom) {
                return 'totalrandom';
            }

            if (_permissions.randomuv) {
                return 'randomuv';
            }

          if (_permissions.reference) {
            return 'reference';
          }

           if (_permissions.delivery) {
             return 'delivery';
           }

          if (_permissions.seirendelivery) {
             return 'seirendelivery';
          }

          if (_permissions.sfdelivery) {
             return 'sfdelivery';
          }

            if (_permissions.image) {
                return 'image';
            }

            if (_permissions.import) {
                return 'import';
            }

            if (_permissions.quality) {
                return 'import';
            }

            if (_permissions.seirenquality) {
                return 'seirenquality';
            }

            if (_permissions.repair) {
                return 'repair';
            }

            if(_permissions.business) {
                return 'business';
            }

            if(_permissions.barcode) {
                return 'barcode';
            }

            if(_permissions.bulkshipping) {
                return 'shipping.bulk';
            }

            if(_permissions.factoryorder) {
                return 'maker';
            }
        }

      };

      return service;

      function setSession(token) {

        _session = token;

        if (isElectron) { // store token in local storage if electron
          localStorage.setItem('printty-app', encode(token));
        } else { // store token in cookies if web app
          $cookies.putObject('printty-app', encode(token));
        }

      }

      function getSession() {

        if (isElectron) {

          if (!_session && !localStorage.getItem('printty-app')) {
            return null;
          }

          return _session || decode(localStorage.getItem('printty-app'));

        } else {

          if (!_session && !$cookies.getObject('printty-app')) {
            return null;
          }

          return _session || decode($cookies.getObject('printty-app'));

        }

      }

      function destroySession() {
        _session = null;

        if (isElectron) {
          localStorage.removeItem('printty-app');
        } else {
          $cookies.remove('printty-app');
        }

      }

      function contains(mode_sub,word){
          var found = mode_sub.some(function(item) {
              return item.title === word;
          });
          return found;
      }

      function isAuthenticated() {
        return !!getSession();
      }

      function isAuthorized(permission) {
        return _permissions[permission];
      }

      function signin(credentials) {
        var deferred = $q.defer();

        var data  = {
          Account: credentials
        };

        ApiService.post('/signin', data, {
          disableErrorHandle: true
        }).then(
          function(data) {
            setSession(data.session);
            deferred.resolve(data);
          },
          function(error) {
              console.log('error',error);
            if (error.data.message && (error.data.message === 'Account.NotFound' || error.data.message === 'Account.Password.NotMatch')) {
              alert('email or password does not match');
            } else {
              alert(error.data.message ? error.data.message : 'Unexpected error');
            }

            deferred.reject(error);
          });

        return deferred.promise;
      }

      function signup(credentials) {
        var deferred = $q.defer();

        var data  = {
          Account: credentials
        };

        ApiService.put('/signup', data).then(
          function(data) {
            setSession(data.session);
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      // update session
      function update() {
        var deferred = $q.defer();

        var data = {
          session: getSession(),
          App: {
            version: '1.0 (' + BUILD_VERSION + ')'
          },
          OS: {
            version: navigator.sayswho.osVersion,
            name: navigator.sayswho.os,
            language: navigator.sayswho.language
          }
        };

        ApiService.post('/session', data).then(
          function(data) {
            Store.data('Mode').update(data);
            is_franchise = data.is_franchise;
            mode = data.mode;
            mode_sub = data.mode_sub;
            mode_sub_tab = data.mode_sub_tab;
            _setPermissions(data.permissions);

            _session = getSession();

            fetchAccount();

            if (isAuthorized('settings')) {
              fetchSettings();
            }

            deferred.resolve(data);
          },
          function(error) {
            // delete session if not found
            if (error.message === 'AccountSession.NotFound') {
              destroySession();
              deferred.resolve();
            } else {
              deferred.reject(error);
            }
          });

        return deferred.promise;
      }

      function signout() {
        var deferred = $q.defer();

        destroySession();
        Store.data('account').update(null);
        Store.data('settings').update(null);
        _permissions = _.cloneDeep(_defaultPermissions);

        deferred.resolve();

        return deferred.promise;
      }

      function fetchAccount() {
        var deferred = $q.defer();

        var params = {
          session: getSession()
        };

        ApiService.get('/account', params).then(
          function(data) {
            Store.data('account').update(data);
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error.data);
          });

        return deferred.promise;
      }

      function updateAccount(accountData) {
        var deferred = $q.defer();

        var postData = angular.merge(
          { session: getSession() },
          accountData
        );

        ApiService.post('/account', postData).then(
          function(data) {

            accountData.Account = _.omit(accountData.Account, ['password', 'passwordConf']);

            Store.data('account').update(accountData);

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchSettings() {
        var deferred = $q.defer();

        var params = {
          session: getSession()
        };

        ApiService.get('/account/settings', params).then(
          function(data) {

            if (_.isArray(data.AccountSettings.api_callback_options)) {
              data.AccountSettings.api_callback_options = {};
            }

            Store.data('settings').update(data);
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error.data);
          });

        return deferred.promise;
      }

      function updateSettings(settingsData) {
        var deferred = $q.defer();

        var postData = angular.merge(
          { session: getSession() },
          settingsData
        );

        ApiService.post('/account/settings', postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      /***** Helpers ******* */

      function _setPermissions(permissions) {

        _.forEach(permissions, function (permission) {

          if (permission === 'account_full_access') {
            _permissions.account = true;
          }

          if (permission === 'settings_full_access') {
            _permissions.settings = true;
          }

          if (permission === 'orders_full_access') {
            _permissions.orders = true;
          }

          if (permission === 'print_full_access') {
            _permissions.print = true;
          }

          if (permission === 'dtftranscription_full_access') {
            _permissions.dtftranscription = true;
          }

            if (permission === 'printoriginal_full_access') {
                _permissions.printoriginal = true;
            }

          if (permission === 'storage_full_access') {
            _permissions.storage = true;
          }

          if (permission === 'kenpin_full_access') {
            _permissions.kenpin = true;
          }

          if (permission === 'kenpini_full_access') {
            _permissions.kenpini = true;
          }

            if (permission === 'kenpinuv_full_access') {
                _permissions.kenpinuv = true;
            }

            if (permission === 'random_full_access') {
                _permissions.random = true;
            }

            if (permission === 'newrandom_full_access') {
                _permissions.newrandom = true;
            }

            if (permission === 'preprocess_full_access') {
                _permissions.preprocess = true;
            }

            if (permission === 'andonkai_full_access') {
                _permissions.andonkai = true;
            }

            if (permission === 'warehouserefinement_full_access') {
                _permissions.andonwr = true;
            }

            if (permission === 'andonsecondfactory_full_access') {
                _permissions.andonsf = true;
            }

            if (permission === 'seirenrandom_full_access') {
                _permissions.seirenrandom = true;
            }

            if (permission === 'totalrandom_full_access') {
                _permissions.totalrandom = true;
            }

            if (permission === 'randomuv_full_access') {
                _permissions.randomuv = true;
            }

          if (permission === 'reference_full_access') {
            _permissions.reference = true;
          }

            if (permission === 'delivery_full_access') {
                _permissions.delivery = true;
            }

            if (permission === 'seirendelivery_full_access') {
                _permissions.seirendelivery = true;
            }

            if (permission === 'secondfactorydelivery_full_access') {
                _permissions.sfdelivery = true;
            }

          if (permission === 'users_full_access') {
            _permissions.users = true;
          }

          if (permission === 'production_full_access') {
            _permissions.production = true;
          }

          if (permission === 'customers_full_access') {
            _permissions.customers = true;
          }

            if (permission === 'image_full_access') {
                _permissions.image = true;
            }

            if (permission === 'import_full_access') {
                _permissions.import = true;
            }

            if (permission === 'quality_full_access') {
                _permissions.quality = true;
            }

            if (permission === 'seirenquality_full_access') {
                _permissions.seirenquality = true;
            }

            if (permission === 'repair_full_access') {
                _permissions.repair = true;
            }

            if (permission === 'business_full_access') {
                _permissions.business = true;
            }

            if (permission === 'barcode_full_access') {
                _permissions.barcode = true;
            }

            if (permission === 'bulkshipping_full_access') {
                _permissions.bulkshipping = true;
            }

            if (permission === 'factoryorder_full_access') {
                _permissions.factoryorder = true;
            }

        });
        
      }
      
      function encode(str) {
        return angular.isDefined($window.btoa) ? $window.btoa(str) : JSON.stringify(str);
      }

      function decode(str) {
        return angular.isDefined($window.atob) ? $window.atob(str) : JSON.parse(str);
      }
      
    });

})();