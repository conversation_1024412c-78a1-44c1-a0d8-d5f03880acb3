(function () {

    angular.module('printty')

        .factory('FactoryOrderApi', function (ApiService, $q, Auth) {

            "ngInject";

            var API_MAP = {
                factoryOrder: '/factory-order',
                makers: '/factory-order/makers',
                factories: '/factories/user',
                csv: '/factory-order/csv/download',
                vendor: '/factory-order/vendor',
                vendorDetail: '/factory-order/vendor/detail',
                productCodes: '/factory-order/product-code',
                productColors: '/factory-order/product-color',
                productSizes: '/factory-order/product-size',
            };

            return {
                fetchFactoryOrders: fetchFactoryOrders,
                fetchMakers: fetchMakers,
                fetchFactories: fetchFactories,

                fetchProductCodes: fetchProductCodes,
                fetchProductColors: fetchProductColors,
                fetchProductSizes: fetchProductSizes,
                fetchOrderBefore: fetchOrderBefore,
                //
                fetchVendor: fetchVendor,
                createVendor: createVendor,
                updateVendor: updateVendor,
                deleteVendor: deleteVendor,

                downloadCSV: downloadCSV,

            };

            function fetchFactoryOrders(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.factoryOrder, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchMakers() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.makers, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchFactories() {
                var deferred = $q.defer();

                var getParams = {
                    session: Auth.session.get()
                };

                ApiService.get(API_MAP.factories, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductCodes(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.productCodes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductColors(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.productColors, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchProductSizes(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.productSizes, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchOrderBefore(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.vendorDetail, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchVendor(orderItemId) {
                const deferred = $q.defer();

                const getData = {
                    session: Auth.session.get(),
                    vendor_order_item_id: orderItemId
                };

                ApiService.get(API_MAP.vendor, getData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createVendor(vendorData) {
                var deferred = $q.defer();

                var putData  = {
                    session: Auth.session.get(),
                    data: vendorData,
                };

                ApiService.put(API_MAP.vendor, putData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateVendor(vendorData) {
                var deferred = $q.defer();

                var postData  = {
                    session: Auth.session.get(),
                    vendor: _.cloneDeep(vendorData),
                };

                ApiService.post(API_MAP.vendor, postData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteVendor(vendorId) {
                var deferred = $q.defer();

                var deleteData  = {
                    session: Auth.session.get(),
                    vendor: {
                        id: vendorId
                    }
                };

                ApiService.delete(API_MAP.vendor, deleteData).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function downloadCSV(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    {session: Auth.session.get()},
                    params
                );

                ApiService.post(API_MAP.csv, getParams).then(
                    function(data) {

                        var name = ApiService.getFileNameFromUrl(data.url);

                        ApiService.download(data.url, {
                            name: name,
                            type: 'application/csv'
                        })
                            .then(function(data) {
                                deferred.resolve(data);
                            })
                            .catch(function(error) {
                                deferred.reject(error);
                            });

                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });

})();
