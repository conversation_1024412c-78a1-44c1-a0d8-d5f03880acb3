(function () {
    angular.module('printty')

        .factory('Supplier', function (ApiService, $q, Auth) {

            'ngInject';

            var API_MAP = {
                suppliers: '/suppliers',
                supplier: '/supplier'
            };

            return {
                fetchSuppliers: fetchSuppliers,
                fetchSupplier: fetchSupplier,
                createSupplier: createSupplier,
                updateSupplier: updateSupplier,
                deleteSupplier: deleteSupplier,
            };

            function fetchSuppliers(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.suppliers, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function fetchSupplier(params) {
                var deferred = $q.defer();

                var getParams = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.get(API_MAP.supplier, getParams).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function createSupplier(params) {
                var deferred = $q.defer();

                var param = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.post(API_MAP.supplier, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function updateSupplier(params) {
                var deferred = $q.defer();

                var param = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.put(API_MAP.supplier, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

            function deleteSupplier(params) {
                var deferred = $q.defer();

                var param = _.merge(
                    { session: Auth.session.get() },
                    params
                );

                ApiService.delete(API_MAP.supplier, param).then(
                    function(data) {
                        deferred.resolve(data);
                    },
                    function(error) {
                        deferred.reject(error);
                    });

                return deferred.promise;
            }

        });
})();