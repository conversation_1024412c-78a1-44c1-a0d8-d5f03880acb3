(function () {

  angular.module('printty')

    .factory('Place', function (ApiService, $q, Auth) {

      "ngInject";

      return {
        list: listPlaces,

        details: detailsPlace,
        create: createPlace,
        update: updatePlace,
        delete: deletePlace,
        
        typesAll: typesAll,
        types: types,
        factoryAll: factoryAll,
        uploadCSV: uploadCSV,
      };

      function listPlaces(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/places', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function detailsPlace(placeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          place_id: placeId
        };

        ApiService.get('/places/place', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function createPlace(placeData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          Place: placeData
        };

        ApiService.put('/places/place', postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function updatePlace(placeData) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Place: placeData
        };

        ApiService.post('/places/place', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function deletePlace(placeId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          Place: {
            id: placeId
          }
        };

        ApiService.delete('/places/place', data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function typesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/places/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function types() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/places/place/types', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function factoryAll() {
            var deferred = $q.defer();

            var params = {
                session: Auth.session.get()
            };

            ApiService.get('/places/place/factories', params).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function uploadCSV(file) {
            var deferred = $q.defer();

            var uploadData = {
                session: Auth.session.get(),
                csv: file
            };

            ApiService.upload('/places/upload/csv', uploadData).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
      