const fs = require('fs');
const os = require('os');
const path = require('path');
const remote = require('electron');
const {Menu, MenuItem, app, BrowserWindow, ipcMain, shell, dialog} = remote;
const PDFWindow = require('electron-pdf-window')

// var printer = require('printer');
var http = require('http');
var https = require('https');

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
var mainWindow;

const contextMenu = new Menu();

contextMenu.append(new MenuItem({ role: 'cut' }));
contextMenu.append(new MenuItem({ role: 'copy' }));
contextMenu.append(new MenuItem({ role: 'paste' }));

function createWindow () {
    // Create the browser window.
    mainWindow = new BrowserWindow({
        title: "Printty",
        webPreferences: {
            nodeIntegration: false,
            preload: path.join(__dirname, 'preload.js')
        }
    });

    mainWindow.setMinimumSize(1000, 600);

    mainWindow.setSize(1200, 700);

    // and load the index.html of the app.
    mainWindow.loadURL(`file://${__dirname}/index.html`);

    // Emitted when the window is closed.
    mainWindow.on('closed', function () {
        // Dereference the window object, usually you would store windows
        // in an array if your app supports multi windows, this is the time
        // when you should delete the corresponding element.
        mainWindow = null
    });

    // initialize window menu
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow);

// Quit when all windows are closed.
app.on('window-all-closed', function () {
    // On OS X it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', function () {
    // On OS X it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (mainWindow === null) {
        createWindow();
    }
});

// context menu
app.on('browser-window-created', function (event, win) {
    win.webContents.on('context-menu', function (e, params) {
        contextMenu.popup(win, params.x, params.y)
    })
});
ipcMain.on('show-context-menu', function (event) {
    const win = BrowserWindow.fromWebContents(event.sender);
    contextMenu.popup(win)
});



// template for windows menu
const template = [
    {
        label: 'Edit',
        submenu: [
            {
                role: 'undo'
            },
            {
                role: 'redo'
            },
            {
                type: 'separator'
            },
            {
                role: 'cut'
            },
            {
                role: 'copy'
            },
            {
                role: 'paste'
            },
            {
                role: 'delete'
            },
            {
                role: 'selectall'
            }
        ]
    },
    {
        label: 'View',
        submenu: [
            {
                label: 'Reload',
                accelerator: 'CmdOrCtrl+R',
                click(item, focusedWindow) {
                    if (focusedWindow) focusedWindow.reload();
                }
            },
            {
                role: 'togglefullscreen'
            },
            {
                label: 'Toggle Developer Tools',
                accelerator: process.platform === 'darwin' ? 'Alt+Command+I' : 'Ctrl+Shift+I',
                click(item, focusedWindow) {
                    if (focusedWindow)
                        focusedWindow.webContents.toggleDevTools();
                }
            }
        ]
    },
    {
        role: 'window',
        submenu: [
            {
                role: 'minimize'
            },
            {
                role: 'close'
            }
        ]
    }
];

if (process.platform === 'darwin') {

    const name = remote.app.getName();

    template.unshift({
        label: name,
        submenu: [
            {
                role: 'about'
            },
            {
                type: 'separator'
            },
            {
                role: 'hide'
            },
            {
                role: 'hideothers'
            },
            {
                role: 'unhide'
            },
            {
                type: 'separator'
            },
            {
                role: 'quit'
            }
        ]
    });

    // Window menu.
    template[3].submenu = [
        {
            label: 'Close',
            accelerator: 'CmdOrCtrl+W',
            role: 'close'
        },
        {
            label: 'Minimize',
            accelerator: 'CmdOrCtrl+M',
            role: 'minimize'
        },
        {
            label: 'Zoom',
            role: 'zoom'
        }
    ];

}

/**********************************
 ************ EVENTS **************
 **********************************/

ipcMain.on('grid-pdf-download', function (event, evData) {

    // console.log(data);

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');
    const pdfPath = path.join(os.tmpdir(), 'electron-grid.pdf');

    var styles;

    // load print css files
    fs.readFile(__dirname + '/css/electron/pdf-grid.css', (err, data) => {

        if (err) throw err;

    styles = data;

    var document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>grid</title><style>${styles}</style></head><body>${evData.contents}</body></html>`;

    // wriye
    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;
        printPdfFile();
    });

});

    function printPdfFile() {

        var win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            // print window contents to pdf
            win.webContents.printToPDF({
                printBackground: true
            }, function (error, pdfData) {
                if (error) throw error;

                // show save dialog
                dialog.showSaveDialog({
                    title: 'grid.pdf',
                    filters: [
                        {name: 'pdf', extensions: ['pdf']},
                    ]
                }, function (fileName) {

                    if (fileName === undefined) return;

                    // save file
                    fs.writeFile(fileName, pdfData, function (error) {
                        if (error) throw error;

                        mainWindow.webContents.send('grid-pdf-downloaded');

                    });

                });

            });

        });

    }

});

ipcMain.on('pdf-download', function (event, evData) {

    // const pdfPath = path.join(os.tmpdir(), 'electron-pdf.pdf');

    // var request;
    // var file = fs.createWriteStream(pdfPath);
    //
    // if ( isHttp(evData.url) ) {
    //
    //   request = http.get(evData.url, function(response) {
    //     response.pipe(file);
    //     file.on('finish', function() { print();} );
    //   });
    //
    // } else {
    //
    //   request = https.get(evData.url, function(response) {
    //     response.pipe(file);
    //     file.on('finish', function() { print();} );
    //   });
    //
    // }
    //
    // function print() {
    //
    //   printer.printDirect({
    //     data: fs.readFileSync(pdfPath),
    //     printer: process.env[3], // printer name, if missing then will print to default printer
    //     type: 'PDF',
    //     success: function(jobID) {
    //       mainWindow.webContents.send('pdf-downloaded');
    //     },
    //     error: function(err){
    //
    //     }
    //   });
    //
    // }
    //
    // function isHttp(string) {
    //   var pattern = /^(http:\/\/)/;
    //   return pattern.test(string);
    // }

    var win = new BrowserWindow({
        title: "Printty",
        show: true
    });

    PDFWindow.addSupport(win);

    win.loadURL(evData.url);

    win.webContents.on('did-finish-load', function () {
        mainWindow.webContents.send('pdf-downloaded');
    });

});

ipcMain.on('print-sheets', function (event, evData) {

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');
    const pdfPath = path.join(os.tmpdir(), 'electron-pdf-tmp.pdf');

    var document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>print</title><style>${evData.styles}</style></head><body>${evData.contents}</body></html>`;

    // wriye
    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;
        printPdfFile();
    });

    function printPdfFile() {

        var win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            win.webContents.print();

            // inform that printing was finished
            setTimeout(function() {
                mainWindow.webContents.send('print-sheets:printed');
            }, 100);
        });

    }

});

ipcMain.on('print-images', function (event, evData) {

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');

    var lastIndex = evData.urls.length - 1;
    // A4 paper
    var styles = '@page{margin:0}body{margin:0;padding:0}img{display: inline;margin:0.445cm 0.95cm ;width:4.99cm;height:3.2cm;}';
    if(evData.size == 2) {
        // custom paper : 50mm x 30mm
        styles = '@page{margin:0}body{margin:0;padding:0}img{display: block;width:4.99cm;height:2.99cm;}';
    }
    if(evData.size == 3) {
        // custom paper : 500mm x 350mm
        styles = '@page{margin:0}body{margin:0;padding:0;display:flex;justify-content:center;align-items:center;height:100vh;}img{display:block;width:8cm;height:5cm;transform:rotate(90deg);}';
    }
    if(evData.size == '30_100'){
        styles = '@page{margin:0}body{margin:0;padding:0}img{display: block;width:9.99cm;height:2.99cm;}';
    }else if(evData.size == 'a4_30_100'){
        styles = '@page{margin:0}body{margin:0;padding:0}img{display: inline;margin:0.445cm 0.95cm ;width:9.99cm;height:2.99cm;}';
    }
    var contents = '';

    evData.urls.forEach(function (url) {
        contents += '<img src="' + url + '">'
    });

    var document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>qrcode</title><style>${styles}</style></head><body>${contents}</body></html>`;
    if(evData.barcode == true){
        document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>barcode</title><style>${styles}</style></head><body>${contents}</body></html>`;
    }

    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;
        printFile();
    });

    function printFile() {

        var win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            win.webContents.print();

            setTimeout(function() {
                mainWindow.webContents.send('print-images:printed');
            }, 100);

        });

    }

});

ipcMain.on('print-guide-note', function (event, evData) {

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');

    // A4 paper
    var styles = '@page{margin:0}body{margin:0;padding:0}img{display: inline;margin:0.445cm 0.95cm ;}';
    if(evData.size == 2) {
        // custom paper : 50mm x 30mm
        styles = '@page{margin:0}body{margin:0;padding:0}img{display: block;width:4.99cm;height:2.99cm;}';
    }
    var contents = '';

    for(var i = 0; i < evData.quantity ; i++){
        contents += '<img src="' + evData.url + '">'
    }

    var document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>qrcode</title><style>${styles}</style></head><body>${contents}</body></html>`;

    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;
        printFile();
    });

    function printFile() {

        var win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            win.webContents.print();

            setTimeout(function() {
                mainWindow.webContents.send('print-images:printed');
            }, 100);

        });

    }

});

ipcMain.on('save-images', function (event, evData) {

    var dir = 'resources/app/img/temp/';
    // var dir = 'build/img/temp/';

    // mkdir
    if (!fs.existsSync(dir)){
        fs.mkdirSync(dir);
    }

    var url = evData.url;
    var isPrint = evData.isPrint;
    var request = require('request');
    var productCode = evData.product_code;
    var productColorCode = evData.product_color_code;
    var title = evData.title;
    var name = evData.name;

    // write image
    var download = function(uri, filename, callback) {
        request.head(uri, function(err, res, body){
            console.log('content-type:', res.headers['content-type']);
            console.log('content-length:', res.headers['content-length']);

            request(uri).pipe(fs.createWriteStream(filename)).on('close', callback);
        });
    };

    var imgName = url.substr(url.lastIndexOf('/') + 1);
    var tail = "";
    var ext = "";

    if (title && title == '表') {
        tail =  '_1_-1_30';
    }else{
        tail =  '_1_-1_0';
    }

    if (name.includes('全面')) {
        ext = '.jpg';
    }else{
        ext = '.png';
    }

    var specially_product = ['00085-CVT', '500101', '594201'];
    if (specially_product.includes(productCode))
    {
        productCode = productCode.split('-')[0] + '-' + productColorCode;
    }

    imgName = productCode + '_Standard pallet_'+ imgName.replace(/\.[^/.]+$/, "") + tail + ext;

    download(url, dir + imgName, function(){
        console.log('done');

        if(isPrint === true) {
            var short_dir = 'img/temp/';

            console.log(__dirname + "/" + short_dir + imgName);
            shell.openItem(__dirname + "/" + short_dir + imgName);
        }

        setTimeout(function() {
            mainWindow.webContents.send('save-image:saved');
        }, 100);
    });

});

ipcMain.on('save-imagesE', function (event, evData) {

    var dir = 'resources/app/img/temp/';
    // var dir = 'build/img/temp/';

    // mkdir
    if (!fs.existsSync(dir)){
        fs.mkdirSync(dir);
    }

    var url = evData.url;
    var isPrint = evData.isPrint;
    var request = require('request');
    var productCode = evData.product_code;
    var productColorCode = evData.product_color_code;
    var title = evData.title;
    var name = evData.name;

    // write image
    var download = function(uri, filename, callback) {
        request.head(uri, function(err, res, body){
            console.log('content-type:', res.headers['content-type']);
            console.log('content-length:', res.headers['content-length']);

            request(uri).pipe(fs.createWriteStream(filename)).on('close', callback);
        });
    };

    var imgName = url.substr(url.lastIndexOf('/') + 1);
    var tail = "";
    var ext = "";

    if (title && title == '表') {
        // tail =  '_1_-1_30';
    }else{
        tail =  '_1_-1_0';
    }

    if (name.includes('全面')) {
        ext = '.jpg';
    }else{
        ext = '.png';
    }

    var specially_product = ['00085-CVT', '500101', '594201'];
    if (specially_product.includes(productCode))
    {
        productCode = productCode.split('-')[0] + '-' + productColorCode;
    }

    imgName = imgName.replace(/\.[^/.]+$/, "") + tail + ext;

    download(url, dir + imgName, function(){
        console.log('done');

        if(isPrint === true) {
            var short_dir = 'img/temp/';

            console.log(__dirname + "/" + short_dir + imgName);
            shell.openItem(__dirname + "/" + short_dir + imgName);
        }

        setTimeout(function() {
            mainWindow.webContents.send('save-imageE:saved');
        }, 100);
    });

});

ipcMain.on('save-imagesB', function (event, evData) {

    var dir = 'resources/app/img/temp/';
    // var dir = 'build/img/temp/';

    // mkdir
    if (!fs.existsSync(dir)){
        fs.mkdirSync(dir);
    }

    var url = evData.url;
    var request = require('request');

    // write image
    var download = function(uri, filename, callback) {
        request.head(uri, function(err, res, body){
            console.log('content-type:', res.headers['content-type']);
            console.log('content-length:', res.headers['content-length']);

            request(uri).pipe(fs.createWriteStream(filename)).on('close', callback);
        });
    };

    var imgName = url.substr(url.lastIndexOf('/') + 1);

    download(url, dir + imgName, function(){
        console.log('done');

        setTimeout(function() {
            mainWindow.webContents.send('save-imageB:saved');
        }, 100);
    });

});

ipcMain.on('delete-images', function (event, evData) {

    var dir = 'resources/app/img/temp/';
    // var dir = 'build/img/temp/';

    var url = evData.url;
    var fileName = url.substr(url.lastIndexOf('/') + 1);

    fs.stat(dir + fileName, function (err, stats) {
        console.log(stats);//here we got all information of file in stats variable

        if (err) {
            setTimeout(function() {
                mainWindow.webContents.send('delete-image:deleted');
            }, 100);
            return console.error(err);
        }

        fs.unlink(dir + fileName,function(err){
            if(err) return console.log(err);
            console.log('file deleted successfully');

            setTimeout(function() {
                mainWindow.webContents.send('delete-image:deleted');
            }, 100);
        });
    });

});

ipcMain.on('simple:print-image', function (event, evData) {

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');

    var styles = '@page{margin:0}body{margin:0;paddidng:0}img{display: block}';
    var document = `<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8"><title>qrcode</title><style>${styles}</style></head><body><img src="${evData.url}"></body></html>`;

    let win = new BrowserWindow({
        title: "Printty",
        show: false
    });

    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;
        printFile();
    });

    function printFile() {

        var win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            win.webContents.print();

            setTimeout(function() {
                mainWindow.webContents.send('simple:print-image:printed');
            }, 100);

        });

    }

});

ipcMain.on('simple:print-document', function (event, evData) {

    const printTmp = path.join(os.tmpdir(), 'electron-print-tmp.html');

    let title = evData.title || 'print';

    let document = `<!DOCTYPE html><html><head><title>${title}</title></head><meta charset="utf-8"><style>${evData.styles}</style><body>${evData.contents}</body></html>`;

    fs.writeFile(printTmp, document, function (error) {
        if (error) throw error;

        printFile();
    });

    function printFile() {

        const pdfPath = path.join(os.tmpdir(), 'print-qrs.pdf');

        let win = new BrowserWindow({
            title: "Printty",
            show: false
        });

        // load tmp file containing print data
        win.loadURL(`file://` + printTmp);

        win.webContents.on('did-finish-load', function () {

            win.webContents.print();

            // inform that printing was finished
            setTimeout(function() {
                mainWindow.webContents.send('simple:print-document:printed');
            }, 100);

        });

    }

});

ipcMain.on('openConditionNote', function (event, evData) {
    shell.openExternal(evData.url);
});
