{"env": {"browser": true, "commonjs": false, "es6": false, "node": false}, "parserOptions": {"ecmaFeatures": {"jsx": false}, "sourceType": "script"}, "rules": {"no-const-assign": "off", "no-this-before-super": "off", "no-undef": "off", "no-unreachable": "warn", "no-unused-vars": "warn", "constructor-super": "off", "valid-typeof": "warn", "curly": "warn", "eqeqeq": "warn", "no-multi-spaces": "warn", "brace-style": "warn", "quotes": ["warn", "single"]}}