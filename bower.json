{"name": "printty", "description": "printty web app", "version": "1.0.0", "private": true, "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "dependencies": {"angular": "^1.5.8", "angular-material": "^1.1.1", "angular-ui-router": "^0.3.1", "jquery": "^3.1.1", "moment": "^2.15.1", "ngclipboard": "^1.1.1", "chart.js": "^2.3.0", "ng-file-upload": "^12.2.13", "angular-cookies": "1.5.8", "lodash": "^4.17.2", "ngInfiniteScroll": "^1.3.4", "angular-qrcode": "^6.2.1", "file-saver": "^1.3.3"}}