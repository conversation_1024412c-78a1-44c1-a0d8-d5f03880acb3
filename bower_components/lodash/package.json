{"name": "lodash", "version": "4.17.2", "license": "MIT", "private": true, "main": "lodash.js", "engines": {"node": ">=4.0.0"}, "scripts": {"build": "npm run build:main && npm run build:fp", "build:fp": "node lib/fp/build-dist.js", "build:fp-modules": "node lib/fp/build-modules.js", "build:main": "node lib/main/build-dist.js", "build:main-modules": "node lib/main/build-modules.js", "doc": "node lib/main/build-doc github && npm run test:doc", "doc:fp": "node lib/fp/build-doc", "doc:site": "node lib/main/build-doc site", "doc:sitehtml": "optional-dev-dependency marky-markdown@^9.0.1 && npm run doc:site && node lib/main/build-site", "pretest": "npm run build", "style": "npm run style:main && npm run style:fp && npm run style:perf && npm run style:test", "style:fp": "jscs fp/*.js lib/**/*.js", "style:main": "jscs lodash.js", "style:perf": "jscs perf/*.js perf/**/*.js", "style:test": "jscs test/*.js test/**/*.js", "test": "npm run test:main && npm run test:fp", "test:doc": "markdown-doctest doc/*.md", "test:fp": "node test/test-fp", "test:main": "node test/test", "validate": "npm run style && npm run test"}, "devDependencies": {"async": "^2.1.2", "benchmark": "^2.1.2", "chalk": "^1.1.3", "cheerio": "^0.22.0", "codecov.io": "~0.1.6", "coveralls": "^2.11.15", "curl-amd": "~0.8.12", "docdown": "~0.7.1", "dojo": "^1.11.2", "ecstatic": "^2.1.0", "fs-extra": "~1.0.0", "glob": "^7.1.1", "istanbul": "0.4.5", "jquery": "^3.1.1", "jscs": "^3.0.7", "lodash": "4.17.1", "lodash-doc-globals": "^0.1.1", "markdown-doctest": "^0.9.0", "optional-dev-dependency": "^2.0.0", "platform": "^1.3.3", "qunit-extras": "^3.0.0", "qunitjs": "^2.0.1", "request": "^2.78.0", "requirejs": "^2.3.2", "sauce-tunnel": "^2.5.0", "uglify-js": "2.7.4", "webpack": "^1.13.3"}, "greenkeeper": {"ignore": ["lodash"]}}