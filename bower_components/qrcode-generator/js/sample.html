<!doctype html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=Utf-8">
    <script type="text/javascript" src="qrcode.js"></script>
    <!-- SJIS Support (optional) -->
<!--
    <script type="text/javascript" src="qrcode_SJIS.js"></script>
-->
    <!-- Uncomment to encode string in qrcode as UTF8 (optional) -->
<!--
    <script type="text/javascript" src="qrcode_UTF8.js"></script>
-->
    <script type="text/javascript" src="sample.js"></script>
    <title>QR Code for JavaScript</title>
  </head>
  <body onload="update_qrcode()">
    <form name="qrForm">
      <span>TypeNumber:</span>
      <select name="t"></select>
      <span>ErrorCorrectLevel:</span>
      <select name="e">
        <option value="L">L(7%)</option>
        <option value="M" selected="selected">M(15%)</option>
        <option value="Q">Q(25%)</option>
        <option value="H">H(30%)</option>
      </select>
      <br/>
      <textarea name="msg" rows="10" cols="40">here comes qr!</textarea>
      <br/>
      <input type="button" value="update" onclick="update_qrcode()"/>
      <div id="qr"></div>
    </form>
    <script type="text/javascript">
!function() {
  var t = document.forms['qrForm'].elements['t'];
  for (var i = 1; i <= 40; i += 1) {
    var opt = document.createElement('option');
    opt.appendChild(document.createTextNode(''+ i) );
    opt.value = '' + i;
    t.appendChild(opt);
  }
  t.value = '4';
}();
    </script>
  </body>
</html>
