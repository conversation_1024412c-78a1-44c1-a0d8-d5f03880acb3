{"name": "ngclipboard", "version": "1.1.1", "description": "Angularjs directive for clipboard.js", "keywords": ["ngclipboard", "copy", "cut", "clipboard", "<PERSON><PERSON>s"], "homepage": "https://github.com/sachinchoolur/ngclipboard", "bugs": {"url": "https://github.com/sachinchoolur/ngclipboard/issues", "email": "<EMAIL>"}, "author": {"name": "Sachin", "email": "<EMAIL>", "url": "https://github.com/sachinchoolur"}, "repository": {"type": "git", "url": "**************:sachinchoolur/ngclipboard.git"}, "main": "dist/ngclipboard.js", "license": "MIT", "scripts": {"test": "grunt"}, "dependencies": {"clipboard": "~1.5.5", "angular": ">=1.2.0"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-qunit": "^0.5.1", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-watch": "^0.6.1", "jshint-stylish": "^1.0.0", "load-grunt-tasks": "^2.0.0", "time-grunt": "^1.0.0"}}