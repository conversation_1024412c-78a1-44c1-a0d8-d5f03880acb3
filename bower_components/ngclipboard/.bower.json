{"name": "ngclipboard", "version": "1.1.1", "homepage": "https://github.com/sachinchoolur/ngclipboard", "authors": ["Sachin N <<EMAIL>>"], "description": "Angularjs directive for clipboard.js", "main": "dist/ngclipboard.js", "keywords": ["ngclipboard", "copy", "cut", "clipboard", "<PERSON><PERSON>s"], "dependencies": {"clipboard": "~1.5.5", "angular": ">=1.2.0"}, "ignore": ["README.md", "demo"], "_release": "1.1.1", "_resolution": {"type": "version", "tag": "1.1.1", "commit": "4870a07486c42be7cb0976a9a64005ef1f4dee60"}, "_source": "https://github.com/sachinchoolur/ngclipboard.git", "_target": "^1.1.1", "_originalSource": "ngclipboard", "_direct": true}