{"name": "ngInfiniteScroll", "main": "build/ng-infinite-scroll.js", "license": "MIT", "dependencies": {"angular": ">=1.2.0"}, "homepage": "https://github.com/ng-infinite-scroll/ng-infinite-scroll-bower", "version": "1.3.4", "_release": "1.3.4", "_resolution": {"type": "version", "tag": "1.3.4", "commit": "e689fd22738e743091e58929061ec4a135325af1"}, "_source": "https://github.com/ng-infinite-scroll/ng-infinite-scroll-bower.git", "_target": "^1.3.4", "_originalSource": "ngInfiniteScroll", "_direct": true}