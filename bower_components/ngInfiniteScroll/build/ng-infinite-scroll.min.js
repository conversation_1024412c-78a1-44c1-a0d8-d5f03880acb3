/* ng-infinite-scroll - v1.3.0 - 2016-11-04 */
!function(a,b){if("function"==typeof define&&define.amd)define(["module","exports","angular"],b);else if("undefined"!=typeof exports)b(module,exports,require("angular"));else{var c={exports:{}};b(c,c.exports,a.angular),a.infiniteScroll=c.exports}}(this,function(a,b,c){"use strict";function d(a){return a&&a.__esModule?a:{default:a}}Object.defineProperty(b,"__esModule",{value:!0});var e=d(c),f="infinite-scroll";e.default.module(f,[]).value("THROTTLE_MILLISECONDS",null).directive("infiniteScroll",["$rootScope","$window","$interval","THROTTLE_MILLISECONDS",function(a,b,c,d){return{scope:{infiniteScroll:"&",infiniteScrollContainer:"=",infiniteScrollDistance:"=",infiniteScrollDisabled:"=",infiniteScrollUseDocumentBottom:"=",infiniteScrollListenForEvent:"@"},link:function(f,g,h){function i(a){var b=a[0]||a;return isNaN(b.offsetHeight)?b.document.documentElement.clientHeight:b.offsetHeight}function j(a){var b=a[0]||a;return isNaN(window.pageYOffset)?b.document.documentElement.scrollTop:b.ownerDocument.defaultView.pageYOffset}function k(a){if(a[0].getBoundingClientRect&&!a.css("none"))return a[0].getBoundingClientRect().top+j(a)}function l(){var b=void 0,d=void 0;if(y===u)b=i(y)+j(y[0].document.documentElement),d=k(g)+i(g);else{b=i(y);var e=0;void 0!==k(y)&&(e=k(y)),d=k(g)-e+i(g)}A&&(d=i((g[0].ownerDocument||g[0].document).documentElement));var h=d-b,l=h<=i(y)*v+1;l?(x=!0,w&&(f.$$phase||a.$$phase?f.infiniteScroll():f.$apply(f.infiniteScroll))):(C&&c.cancel(C),x=!1)}function m(a,b){function d(){return g=(new Date).getTime(),c.cancel(f),f=null,a.call()}function e(){var e=(new Date).getTime(),h=b-(e-g);h<=0?(c.cancel(f),f=null,g=e,a.call()):f||(f=c(d,h,1))}var f=null,g=0;return e}function n(){y.unbind("scroll",D),null!=B&&(B(),B=null),C&&c.cancel(C)}function o(a){v=parseFloat(a)||0}function p(a){w=!a,w&&x&&(x=!1,D())}function q(a){A=a}function r(a){null!=y&&y.unbind("scroll",D),y=a,null!=a&&y.bind("scroll",D)}function s(a){if(null!=a&&0!==a.length){var b=void 0;if(b=a.nodeType&&1===a.nodeType?e.default.element(a):"function"==typeof a.append?e.default.element(a[a.length-1]):"string"==typeof a?e.default.element(document.querySelector(a)):a,null==b)throw new Error("invalid infinite-scroll-container attribute.");r(b)}}function t(){return z&&D(),c.cancel(C)}var u=e.default.element(b),v=null,w=null,x=null,y=null,z=!0,A=!1,B=null,C=!1,D=null!=d?m(l,d):l;return f.$on("$destroy",n),f.$watch("infiniteScrollDistance",o),o(f.infiniteScrollDistance),f.$watch("infiniteScrollDisabled",p),p(f.infiniteScrollDisabled),f.$watch("infiniteScrollUseDocumentBottom",q),q(f.infiniteScrollUseDocumentBottom),r(u),f.infiniteScrollListenForEvent&&(B=a.$on(f.infiniteScrollListenForEvent,D)),f.$watch("infiniteScrollContainer",s),s(f.infiniteScrollContainer||[]),null!=h.infiniteScrollParent&&r(e.default.element(g.parent())),null!=h.infiniteScrollImmediateCheck&&(z=f.$eval(h.infiniteScrollImmediateCheck)),C=c(t)}}}]),b.default=f,a.exports=b.default});