{"name": "clipboard", "version": "1.5.13", "description": "Modern copy to clipboard. No Flash. Just 2kb", "license": "MIT", "main": "dist/clipboard.js", "ignore": ["/.*/", "/demo/", "/test/", "/.*", "/bower.json", "/karma.conf.js", "/src", "/lib"], "keywords": ["clipboard", "copy", "cut"], "homepage": "https://github.com/zenorocha/clipboard.js", "_release": "1.5.13", "_resolution": {"type": "version", "tag": "v1.5.13", "commit": "26a9e9d56ccb7241c3a60fc7003e0fd48d77dd44"}, "_source": "https://github.com/zenorocha/clipboard.js.git", "_target": "~1.5.5", "_originalSource": "clipboard"}