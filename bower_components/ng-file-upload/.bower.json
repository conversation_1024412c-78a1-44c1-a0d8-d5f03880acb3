{"name": "ng-file-upload", "main": "ng-file-upload.js", "homepage": "https://github.com/danialfarid/ng-file-upload", "dependencies": {"angular": ">1.2.0"}, "authors": ["danialf <<EMAIL>>"], "description": "Lightweight Angular JS directive to upload files. Support drag&drop, paste image, progress and abort", "ignore": [], "license": "MIT", "version": "12.2.13", "_release": "12.2.13", "_resolution": {"type": "version", "tag": "12.2.13", "commit": "4ba3e7eb34c3ced628699e3e20bfca32ea5be1f5"}, "_source": "https://github.com/danialfarid/angular-file-upload-bower.git", "_target": "^12.2.13", "_originalSource": "ng-file-upload", "_direct": true}