{"name": "chart.js", "homepage": "http://www.chartjs.org", "description": "Simple HTML5 charts using the canvas element.", "version": "2.3.0", "license": "MIT", "main": "src/chart.js", "repository": {"type": "git", "url": "https://github.com/chartjs/Chart.js.git"}, "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^0.2.1", "bundle-collapser": "^1.2.1", "coveralls": "^2.11.6", "gulp": "3.9.x", "gulp-concat": "~2.1.x", "gulp-connect": "~2.0.5", "gulp-eslint": "^2.0.0", "gulp-file": "^0.3.0", "gulp-html-validator": "^0.0.2", "gulp-insert": "~0.5.0", "gulp-karma": "0.0.4", "gulp-replace": "^0.5.4", "gulp-size": "~0.4.0", "gulp-streamify": "^1.0.2", "gulp-uglify": "~0.2.x", "gulp-util": "~2.2.x", "gulp-zip": "~3.2.0", "jasmine": "^2.3.2", "jasmine-core": "^2.3.4", "karma": "^0.12.37", "karma-browserify": "^5.0.1", "karma-chrome-launcher": "^0.2.0", "karma-coverage": "^0.5.1", "karma-firefox-launcher": "^0.1.6", "karma-jasmine": "^0.3.6", "karma-jasmine-html-reporter": "^0.1.8", "merge-stream": "^1.0.0", "vinyl-source-stream": "^1.1.0"}, "spm": {"main": "Chart.js"}, "dependencies": {"chartjs-color": "^2.0.0", "moment": "^2.10.6"}}