{"name": "angular-ui-router", "version": "0.3.1", "license": "MIT", "main": "./release/angular-ui-router.js", "dependencies": {"angular": "^1.0.8"}, "ignore": ["**/.*", "node_modules", "bower_components", "component.json", "package.json", "lib", "config", "sample", "test", "tests", "ngdoc_assets", "Gruntfile.js", "files.js"], "homepage": "https://github.com/angular-ui/angular-ui-router-bower", "_release": "0.3.1", "_resolution": {"type": "version", "tag": "0.3.1", "commit": "6ac1c61991121f5324f99089003314bba3bc6a95"}, "_source": "https://github.com/angular-ui/angular-ui-router-bower.git", "_target": "^0.3.1", "_originalSource": "angular-ui-router", "_direct": true}