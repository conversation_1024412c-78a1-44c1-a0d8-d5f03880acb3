{"name": "angular-material", "version": "1.1.1", "main": "index", "format": "cjs", "registry": "github", "peerDependencies": {"angular": ">=1.3 <1.6", "angular-animate": ">=1.3 <1.6", "angular-aria": ">=1.3 <1.6"}, "jspm": {"dependencies": {"angular": "github:angular/bower-angular@^1.5.3", "angular-animate": "github:angular/bower-angular-animate@^1.5.3", "angular-aria": "github:angular/bower-angular-aria@^1.5.3", "angular-messages": "github:angular/bower-angular-messages@^1.5.3", "css": "systemjs/plugin-css@^0.1.9"}, "shim": {"angular-material": {"deps": []}}, "peerDependencies": {}}, "homepage": "https://material.angularjs.org", "repository": {"type": "git", "url": "git://github.com/angular/material.git"}, "licenses": [{"type": "MIT", "url": "https://github.com/angular/material/blob/master/LICENSE"}], "keywords": ["angular", "material", "browser", "client-side"], "bugs": {"url": "https://github.com/angular/material/issues"}}