{"name": "file-saver", "main": "FileSaver.js", "version": "1.3.3", "homepage": "https://github.com/eligrey/FileSaver.js", "authors": ["<PERSON> <<EMAIL>>"], "description": "An HTML5 saveAs() FileSaver implementation", "keywords": ["filesaver", "saveas", "blob"], "license": "MIT", "ignore": ["*", "!FileSaver.*js", "!LICENSE.md"], "_release": "1.3.3", "_resolution": {"type": "version", "tag": "1.3.3", "commit": "5ed507ef8aa53d8ecfea96d96bc7214cd2476fd2"}, "_source": "https://github.com/eligrey/FileSaver.js.git", "_target": "^1.3.3", "_originalSource": "file-saver", "_direct": true}