section.block-wide(layout='column')
	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
			h2 注文情報
		div.layout-row.buttons(flex layout-align='end center')
			div.search-form(focusable-search): form()
				input.search-input(type='text')
				i.icons8-search
			button.simple-icon-btn(type='button' ng-if='!vm.viewMode'): i.icons8-save
			button.simple-icon-btn.btn-add(type='button' ng-if='!vm.viewMode'): i.icons8-plus
			button.simple-icon-btn(type='button' ng-if='!vm.viewMode'): i.icons8-delete
	div.body.block-wide.overflow-hidden(flex='100' layout='row')
		md-content.sidebar
			form
				div.form.default-form.bottom-margin(layout='row' layout-wrap)
					div.input-container(flex=50)
						md-input-container
							label 注文番号
							input(type='text' value='AE341Q92' disabled)
					div.input-container(flex=50)
						md-input-container
							label 注文日
							input(type='text' value='2016.12.31' disabled)
					div.input-container(flex=100)
						md-input-container
							label 完成希望日
							input(type='text' value='2017.01.23' disabled)

				div.form.default-form(layout='row' layout-wrap ng-class="{'bottom-margin': vm.viewMode}")
					h3(ng-show='vm.moreBilling') 郵送先
					div.input-container(flex=100)
						md-input-container
							label 会社名
							input(type='text' value='Fuji Media Holdings' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label 住所
							input(type='text' value='110-0004 東京都台東区下谷1-13-7' ng-disabled='vm.viewMode')
					div.input-container(flex=50)
						md-input-container
							label 名前
							input(type='text' value='川口太郎' ng-disabled='vm.viewMode')
					div.input-container(flex=50)
						md-input-container
							label お届け時間
							input(type='text' value='12時〜16時' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label 電話番号
							input(type='text' value='080-1234-5678' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label メールアドレス
							input(type='text' value='<EMAIL>' ng-disabled='vm.viewMode')

				div.bottom-margin(ng-if='!vm.viewMode') md-checkbox(ng-model='vm.moreBilling') 別の請求先

				div.form.default-form.extra-bottom-margin(layout='row' layout-wrap ng-show='vm.moreBilling' ng-if='!vm.viewMode')
					h3 請求先
					div.input-container(flex=100)
						md-input-container
							label 会社名
							input(type='text' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label 住所
							input(type='text' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label 名前
							input(type='text' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label 電話番号
							input(type='text' ng-disabled='vm.viewMode')
					div.input-container(flex=100)
						md-input-container
							label メールアドレス
							input(type='text' ng-disabled='vm.viewMode')

				div(ng-if='!vm.viewMode')
					div: md-checkbox 袋詰め
					div: md-checkbox Tシャツオリジナルタグ

				div.form.default-form(layout='row' layout-wrap)
					div.input-container(flex=100)
						md-input-container
							label 小計
							input(type='text' value='20 860円' disabled)
					div.input-container(flex=100)
						md-input-container
							label 袋詰め価格
							input(type='text' value='500円' disabled)
					div.input-container(flex=100)
						md-input-container
							label Tシャツオリジナルタグ価格
							input(type='text' value='920円' disabled)
					div.input-container(flex=100)
						md-input-container
							label 送料
							input(type='text' value='870円' disabled)
					div.input-container(flex=100)
						md-input-container
							label 特別割引
							input.accent-negative(type='text' value='-600円' disabled)
					div.input-container(flex=100)
						md-input-container
							label 消費税
							input(type='text' value='1 130円' disabled)
					div.input-container(flex=100)
						md-input-container
							label 合計金額
							input.accent-total(type='text' value='22 860円' disabled)

		md-content.block-wide.content.list-content(layout='column')

			.highlighted-column-container(flex layout='row'): .highlighted-column(flex=10 flex-offset=65)

			md-virtual-repeat-container.list-container(flex)

				div.heading(layout='row' layout-align='center center')
					div.cell(flex=35) 内容
					div.cell(flex=10) 商品値段
					div.cell(flex=10) 印刷値段
					div.cell.text-right(flex=10) 合計
					div.cell.text-center(flex=10) ステータス
					div.cell(flex)

				div.list-body

					div.default-row(layout='row' layout-align='center center' md-virtual-repeat='product in vm.products' ng-show='product')
						div.cell.cell-images(flex=35 layout='row' layout-align='center center')
							div.flex-none.image(ng-repeat='src in product.images'): img(ng-src='{{ src }}')
							div.flex {{ product.title }}
						div.cell(flex=10) {{ product.comm_price }}
						div.cell(flex=10) {{ product.print_price }}
						div.cell.text-right(flex=10) {{ product.total_price }}
						div.cell.text-center(flex=10) {{ product.status }}
						div.cell.cell-action(flex, layout='row', layout-align='end center')
							div.text-small {{ product.created_at }}
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewProductDetail()') 詳細
								md-menu(md-position-mode="target-right target")
									md-button.md-raised.md-primary.btn-sm.btn-more(md-menu-align-target aria-label='product more' ng-click='$mdOpenMenu($event)')
										i.icons8-more
									md-menu-content(width='3' md-menu-align-target)
										md-menu-item(ng-repeat='i in [1, 2, 3]')
											md-button
												span {{ 'field' + i }}

// sidenavs
order-product-detail-sidenav(ng-if='vm.isShown')