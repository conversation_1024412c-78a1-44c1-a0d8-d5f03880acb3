section.block-wide(layout='column')

	.component-loading(ng-class='vm.loadingType')
		md-progress-circular(md-diameter='64', md-mode='indeterminate')

	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
			h2 注文履歴
		div.layout-row.buttons(flex layout-align='end center')
			div.search-form(focusable-search): form()
				input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
				i.icons8-search
	
	div.body.block-wide.overflow-hidden(flex='100' layout='row')
		md-content.block-wide.content.list-content(layout='column')

			.highlighted-column-container(flex layout='row'): .highlighted-column(ng-hide='vm.pagination.empty' flex=10 flex-offset=45)

			md-virtual-repeat-container.list-container(flex)

				div.heading(layout='row' layout-align='center center')
					div.cell(flex=45)
					div.cell.text-center(flex=10) ステータス
					div.cell(flex) 完成希望日
					div.cell(flex='none')

				div.list-body

					div.default-row(layout='row' layout-align='center center' md-virtual-repeat='order in vm.infiniteOrders' md-on-demand ng-show='order')
						div.cell(flex=15) {{ order.Order.number }}
						div.cell(flex=15) {{ order.Order.date }}
						div.cell.text-right(flex=15) {{ order.Order.amount | number }}円
						div.cell.text-center(flex=10 layout='column' layout-align='center center')
							div {{ order.Order.status }}
						div.cell {{ order.Order.production_date_preferred }}
						div.cell.cell-action(flex, layout='row', layout-align='end center')
							div.text-small {{ order.Order.updated_at }}
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewOrderDetail(order)') 詳細
					
					div.pagination-loading(ng-show='vm.pagination.loading' style='top: 36px;')
						md-progress-circular(md-diameter='40' md-mode='indeterminate')
						
		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
			
customer-order-detail.order-detail-wrapper.block-wide(open-on='vm.viewOrderDetail' guid='order-list-customer')