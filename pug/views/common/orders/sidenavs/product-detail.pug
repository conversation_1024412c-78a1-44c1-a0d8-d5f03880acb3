md-sidenav.management-sidenav.md-sidenav-right(md-component-id="order-product-detail").sidenav-fixed: .block-wide
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 注文情報
			div.sidenav-actions(ng-if='!vm.viewMode')
				button.btn-edit.simple-btn(type='button' ng-click='vm.toggleEditMode()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button'): i.icons8-delete
		md-content(flex='grow')
			product-sides-images(sides='vm.sides')

			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label 商品
						input(type='text' value='Tシャツ' ng-disabled='vm.isShow')
				div.input-container(flex=50)
					md-input-container
						label カラー
						input(type='text' value='グレー' ng-disabled='vm.isShow')
				div.input-container(flex=50)
					md-input-container
						label サイズ
						input(type='text' value='M' ng-disabled='vm.isShow')

			div.form.default-form(layout='row' layout-wrap)
				div.input-container(flex=33)
					md-input-container
						label 枚数
						input(type='text' value='10' ng-disabled='vm.isShow')
				div.input-container(flex=33)
					md-input-container
						label 値段
						input(type='text' value='2 000円' ng-disabled='vm.isShow')
				div.input-container.last(flex=33)
					md-input-container
						label 合計
						input(type='text' value='20 000円' ng-disabled='vm.isShow')