md-sidenav.management-sidenav.md-sidenav-right.sidenav-details(md-component-id='orders-history').sidenav-fixed.sidenav-history: .block-wide(layout='column')
	header.section-header(flex='none')
		button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
		h2 履歴
	div.body.block-wide.overflow-hidden(flex='grow' layout='row')
		
		md-content.block-wide.content.list-content(layout='column')

			md-virtual-repeat-container.list-container(flex)

				div.heading(layout='row' layout-align='center center')
					div.cell(flex=30)
						div.search
							div.search-form(focusable-search): form()
								i.icons8-search
								input.search-input(type='text')
					div.cell(flex=15) 請求月
					div.cell(flex=15)
					div.cell.text-center(flex=15) ステータス
					div.cell.heading-btns(flex layout='row', layout-align='end center')
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
						md-button.md-raised.md-primary.btn-sm ダウンロード

				div.list-body

					// todo: why virtual repeat is not working here (?)
					//div.default-row(layout='row' layout-align='center center' md-virtual-repeat='order in vm.orders' ng-show='order')
					div.default-row(layout='row' layout-align='center center' ng-repeat='order in vm.orders' ng-show='::order')
						div.cell(flex=15 layout='row' layout-align='start center')
							div: md-checkbox(ng-model='order.selected' aria-label='{{ ::order.number }}')
							div {{ order.number }}
						div.cell(flex=15) {{ ::order.order_date }}
						div.cell(flex=15) {{ ::order.billing_month }}
						div.cell.text-right(flex=15) {{ ::order.price }}
						div.cell.text-center(flex=15 layout='column' layout-align='center center')
							div(status-emphasis='order.status') {{ ::order.status }}
						div.cell.cell-action(flex, layout='row', layout-align='end center')
							div.text-small {{ ::order.created_at }}
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewOrderDetail()') 詳細
								
	.highlighted-column-container(flex layout='row'): .highlighted-column(flex=15 flex-offset=60)
		
order-detail.order-detail-wrapper.block-wide(view-detail='vm.viewOrderDetail' view-mode='true')