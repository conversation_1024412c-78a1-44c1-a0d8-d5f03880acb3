div.block-wide(layout='column')

	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'オプション追加' : 'オプション情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block.bottom-border

			div.add-color
				form.form.default-form(layout='row' layout-wrap name='optionForm')
					div.input-container(flex=100)
						md-input-container
							label Key
							input(type='text', ng-model='vm.key', name='key', required, ng-readonly='vm.showMode')
					div.input-container.bottom-border(flex=100)
						md-input-container
							label Value
							input(type='text', ng-model='vm.value', name='value', required, ng-readonly='vm.showMode')

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存