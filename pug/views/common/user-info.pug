div.switch-app-block
	md-menu.select-role(md-position-mode="target-right target")
		a(href ng-click='$mdOpenMenu($event)') {{ vm.appBlocks.options[vm.appBlocks.current] }}
		md-menu-content(width='3' md-menu-align-target style='max-height: 600px;')
			md-menu-item(ng-repeat='(option, title) in vm.appBlocks.options')
				md-button(ng-click='vm.appBlocks.set(option,title)' ng-disabled="::vm.appBlocks.current === option ")
					span {{ title }}
div.user-name
	a(href ng-click='vm.editProfile()') {{ vm.account.Account.nickname || vm.account.Account.username || vm.account.Account.email }}
.divider

button.btn-search.simple-btn.btn-sm(ng-click="vm.showGlobalSearch()")
	i.icons8-search
	div

md-menu(md-position-mode="target-right target")
	button.simple-btn.btn-sm.btn-more(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
		i.icons8-more
	md-menu-content(md-menu-align-target)
		md-menu-item
			md-button(type='button' ng-click='vm.viewApi()' ng-show="vm.isAuthorized('settings')") 設定
		md-menu-item
			md-button(ng-click='vm.viewCalendar()') 営業カレンダー
		md-menu-item
			md-button(type='button' ng-click='vm.downloadItemInfoCSV()') アイテム情報ダウンロード
button.simple-btn(type='button' ng-click='vm.logout()')
	i.icons8-exit

// sidenavs
profile-edit(open-on='vm.editProfile')
profile-api(open-on='vm.viewApi')
admin-calendar(open-on='vm.viewCalendar')
admin-global-search.global-search-wrapper.block-wide(open-on='vm.showGlobalSearch')