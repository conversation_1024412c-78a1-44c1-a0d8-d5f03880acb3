md-sidenav.management-sidenav.md-sidenav-right(md-component-id='profile-edit').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 アカウント情報
		md-content(flex='grow'): form(name='editAccountForm')
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label ユーザー名
						input(type='text' ng-model='vm.account.Account.username' name='username' required, ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 名前
						input(type='text' ng-model='vm.account.Account.nickname' name='nickname' required, ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label メールアドレス
						input(type='email' ng-model='vm.account.Account.email' name='email' required, ng-readonly="!vm.isAuthorized('account')")

			div.form.default-form(layout='row' layout-wrap, ng-if="vm.isAuthorized('account')")
				div.input-container(flex=50)
					md-input-container
						label パスワード
						input(type='password' ng-model='vm.account.Account.password' name='password' md-maxlength="255" minlength="4" ng-pattern="/^[A-Za-z0-9]*$/")
				div.input-container(flex=50)
					md-input-container
						label パスワード確認
						input(type='password' ng-model='vm.account.Account.passwordConf' name='passwordConf' ng-required='vm.account.Account.password' ng-pattern='vm.account.Account.password')
						
		footer.text-right(flex='none', ng-if="vm.isAuthorized('account')")
			md-button.md-raised.md-accent(ng-click='vm.saveAccount()') 保存