md-sidenav.management-sidenav.md-sidenav-right(md-component-id='profile-api').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.optionBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 設定
		md-content(flex='grow'): form(name='editSettingsForm')
			
			div.form.default-form.bottom-margin.layout-wrap.layout-row
				div.input-container(flex=100)
					md-input-container
						label 法人名
						input(type='text' ng-model='vm.settings.AccountSettings.billing.company' name='company' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 法人名（カナ）
						input(type='text' ng-model='vm.settings.AccountSettings.billing.company_kana' name='company_kana' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 郵便番号
						input(type='text' ng-model='vm.settings.AccountSettings.billing.address.postcode' name='postcode' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 都道府県
						input(type='text' ng-model='vm.settings.AccountSettings.billing.address.state' name='state' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 市町村
						input(type='text' ng-model='vm.settings.AccountSettings.billing.address.city' name='city' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 番地
						input(type='text' ng-model='vm.settings.AccountSettings.billing.address.address1' name='address1' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 建物名
						input(type='text' ng-model='vm.settings.AccountSetting.billings.address.address2' name='address2' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container
						label 電話番号
						input(type='text' ng-model='vm.settings.AccountSettings.billing.phonenumber' name='phonenumber' ng-readonly="!vm.isAuthorized('account')")
				div.input-container(flex=100)
					md-input-container.with-clipboard
						label API Key
						input(type='text' ng-model='vm.settings.AccountSettings.api_key' readonly)
						button.clipboard-button(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ vm.settings.AccountSettings.api_key }}")
							i.icons8-copy
						button.btn-dummy
							md-tooltip(md-visible='vm.clipSuccessMsg') Copied!
				
			div.form.default-form(layout='row' layout-wrap)
				
				div.input-container(flex=100)
					md-input-container
						label API Callback
						input(type='text' ng-model='vm.settings.AccountSettings.api_callback' name='api_callback' ng-readonly="!vm.isAuthorized('account')")

				// product linked code block
				.sidenav-list-block(flex=100)

					h3 API Callback Options

					.sidenav-list
						div.heading.layout-row.layout-align-start-center
							div.cell.flex Key
							div.cell.flex-none Value
						div.default-row.layout-row.layout-align-start-center(ng-repeat='(key, value) in vm.settings.AccountSettings.api_callback_options')
							div.cell.flex {{ key }}
							div.cell.flex-none.layout-row.layout-align-end-center
								div {{ value }}
								md-menu.small(md-position-mode="target-right target")
									button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
									md-menu-content.menu-small(md-menu-align-target)
										md-menu-item: md-button(ng-click='vm.optionBlock.show(key, value)') 詳細

					div.text-center: md-button.simple-button.btn-sm(ng-click='vm.optionBlock.add()') オプションを追加
							
		footer.text-right(flex='none', ng-if="vm.isAuthorized('account')")
			md-button.md-raised.md-accent(ng-click='vm.saveSettings()') 保存
		
	callback-option(
		ng-show='vm.optionBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.optionBlock.type',
		active='vm.optionBlock.active',
		on-close='vm.optionBlock.onClose()',
		on-create='vm.optionBlock.onCreate(key, value)',
		on-update='vm.optionBlock.onUpdate(original, key, value)'
		on-delete='vm.optionBlock.onDelete(key)'
	)