div.h-full(ng-class="{'bg-pink' : vm.isForWomen()}")
	header(flex='none' layout='row' layout-align='center center')
		div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

	// show messages
	div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')")
		md-icon.qr-icon(md-svg-src='img/icons/qr-code.svg', aria-label='scan')

	div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('printing')"): div.message-inner
		md-icon.qr-icon(md-svg-src='img/icons/print.svg', aria-label='print')
		p.text-center 印刷中

	//div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('finish')")
	//	md-icon.finish-icon(md-svg-src='img/icons/checkmark.svg', aria-label='success')

	div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && !vm.isMultiple")
		div.layout-column.layout-align-center-center
			img(ng-click='vm.showLargeImage($event, vm.task.current_step.TaskStep.large_image_url)' ng-src='{{ vm.task.current_step.TaskStep.image_url }}' ng-style="{'cursor': 'pointer'}")
			p.text-center {{ vm.task.current_step.TaskStep.title }}
			div.remark(ng-if="!vm.task.current_step.detail.is_embroidery")
				div.input(ng-if="vm.task.current_step.detail.is_factory_memo_2")
					div.input(ng-if="vm.task.current_step.detail.is_memo_claim" ng-style="{'color' : 'red'}") {{ vm.task.current_step.detail.memo_claim }}
					div.input(ng-if="!vm.task.current_step.detail.is_memo_claim") {{ vm.task.current_step.detail.memo_claim }}
				div.input(ng-if="!vm.task.current_step.detail.is_factory_memo_2")
					div.input {{ vm.task.current_step.detail.memo }}
				div.input(ng-if="!vm.task.current_step.detail.silk_memo")
					div.input シルク版： {{ vm.task.current_step.detail.silk_memo }}
				div(ng-if="vm.task.current_step.print_location_space")
					div(ng-repeat="data in vm.task.current_step.print_location_space")
						div(style="padding: 8px 0")
							span(style="margin-right:10px") 印刷位置
							span(ng-repeat="(key, value) in data") {{key}}: {{value}}
								span(ng-if='key == "間隔"') cm
								span(ng-if="!$last") 、
			div.zoom-in-image(ng-if="vm.task.current_step.detail.is_embroidery")
				div.input(ng-if="vm.task.current_step.detail.is_factory_memo_2")
					div.input(ng-if="vm.task.current_step.detail.is_memo_claim" ng-style="{'color' : 'red'}") {{ vm.task.current_step.detail.memo_claim }}
					div.input(ng-if="!vm.task.current_step.detail.is_memo_claim") {{ vm.task.current_step.detail.memo_claim }}
				div.input(ng-if="!vm.task.current_step.detail.is_factory_memo_2")
					div.input {{ vm.task.current_step.detail.memo }}
				div.embroidery-image
					img(ng-click='vm.showLargeImage($event, vm.task.current_step.TaskStep.embroidery_url)', ng-src="{{ vm.task.current_step.TaskStep.embroidery_url }}" ng-style="{'cursor': 'pointer'}")
				div(ng-if="vm.task.current_step.print_location_space")
					div(ng-repeat="data in vm.task.current_step.print_location_space")
						div(style="padding: 8px 0")
							span(style="margin-right:10px") 印刷位置
							span(ng-repeat="(key, value) in data") {{key}}: {{value}}
								span(ng-if='key == "間隔"') cm
								span(ng-if="!$last") 、
	div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && vm.isMultiple")
		div.layout-column.layout-align-center-center
			img(ng-src='{{ vm.task.current_step.TaskGroupStep.image_url }}')
			//-p.text-center {{ vm.task.current_step.TaskStep.title }}