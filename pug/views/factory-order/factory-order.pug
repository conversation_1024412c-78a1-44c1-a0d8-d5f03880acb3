.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.sidebar(flex='none', layout='column')
            div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

            div.sidebar-secondary
                sidebar-secondary-menu(statuses='vm.makers')
            div(flex='none' ng-style="{'margin-left':'-9px'}"): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add' ng-click='vm.createItemVendor()'): i.icons8-plus

        div.layout-column.block-min-width
            header.with-subheader(flex="none")
                div.subheader-th(layout='row' layout-align="start center")

                    div(style="width:140px;")
                        div(pick-date-header)
                            div {{ vm.startDate | dateJapan : 'day' }}
                            md-datepicker(ng-model='vm.startDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                    | -
                    div(style="width:140px;margin-left:30px;")
                        div(pick-date-header)
                            div {{ vm.endDate | dateJapan : 'day' }}
                            md-datepicker(ng-model='vm.endDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                    div.flex-20
                        md-input-container
                            md-select(ng-model='vm.factorySelected' name='factory_id' ng-change='vm.onFactoryChange()')
                                md-option(ng-repeat='factory in vm.factories' ng-value='factory.id' ng-selected='factory.id == vm.factoryId') {{ factory.title }}
                    div(flex, layout='row', layout-align='end center').user-info
                        user-manage
                div(layout="row" layout-align="start center")
                    md-time-picker.startTime.pick-date-header(ng-model='vm.startTime', message='message', no-meridiem='', read-only="readonly")
                    span(style="margin: 0 10px;") -
                    md-time-picker.endTime.pick-date-header(ng-model='vm.endTime', message='message', no-meridiem='', read-only="readonly")
                    md-button.md-raised.md-primary.btn-sm.chart-btn(ng-click='vm.onDateChange()', style="width:140px;margin-left:30px;") 検索

            md-content(flex='grow' layout='column' ui-view)

            style(type="text/css").
                md-select .md-select-value {
                    border-bottom-style: none !important;
                }
