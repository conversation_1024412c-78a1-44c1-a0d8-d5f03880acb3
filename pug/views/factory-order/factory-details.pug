md-sidenav.management-sidenav.md-sidenav-right(md-component-id='factory-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none', ng-style='{ display: "flex", justifyContent: "flex-end", alignItems: "center" }')
            button.esc-button(type='button', ng-click='vm.close()')
                md-icon(md-svg-src='img/icons/esc.svg')
            h2.title-center 発注追加入力画面
            div(ng-show='vm.isAdd')
                md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete
        md-content(flex='grow')
            form.form.default-form.custom-form.bottom-margin(layout='row' layout-wrap name='factoryDetailForm')
                div.input-container(flex=100 layout='column' ng-if="vm.isAdd")
                    label.custom-label メーカー
                    md-input-container.m-top-0
                        md-select(ng-model='vm.vendor.VendorOrderItem.product_linked_source_id' name='maker_id' ng-disabled='!vm.isAdd' ng-change="vm.selectProduct(vm.vendor.VendorOrderItem.product_linked_source_id)" )
                            md-option(ng-repeat='maker in vm.makers' ng-value='maker.id' ng-if='maker.id !== 0 ') {{ maker.title }}
                    span.error-text(ng-if="vm.checkMaker") 【メーカー)】入力して下さい。
                div.input-container(flex=100 layout='column' ng-if="!vm.isAdd")
                    md-input-container
                        label.custom-label メーカー
                        input(type='text' ng-model='vm.vendor.VendorOrderItem.source' readonly)
                div.input-container(flex=100 layout='column' ng-if="vm.isAdd")
                    label.custom-label 品番
                    md-input-container.m-top-0
                        md-select(ng-model='vm.vendor.VendorOrderItem.product_id' name='product_id' ng-disabled='!vm.isAdd' ng-change="vm.selectProductColor(vm.vendor.VendorOrderItem.product_id)")
                            md-option(ng-repeat='productCode in vm.productCodes' ng-value='productCode.product_id') {{ productCode.name }}
                    span.error-text(ng-if="vm.checkProduct") 【品番】入力して下さい。
                div.input-container(flex=100 layout='column' ng-if="!vm.isAdd")
                    md-input-container
                        label.custom-label 品番
                        input(type='text' ng-model='vm.vendor.VendorOrderItem.product' readonly)
                div.input-container(flex=100 layout='column' ng-if="vm.isAdd")
                    label.custom-label カラー
                    md-input-container.m-top-0
                        md-select(ng-model='vm.vendor.VendorOrderItem.color_id' name='color_id' ng-disabled='!vm.isAdd' ng-change="vm.updateColorId(vm.vendor.VendorOrderItem.color_id)")
                            md-option(ng-repeat='productColor in vm.productColors' ng-value='productColor.color_id') {{ productColor.name }}
                    span.error-text(ng-if="vm.checkColor") 【カラー】入力して下さい。
                div.input-container(flex=100 layout='column' ng-if="!vm.isAdd")
                    md-input-container
                        label.custom-label カラー
                        input(type='text' ng-model='vm.vendor.VendorOrderItem.color' readonly)
                div.input-container(flex=100 layout='column' ng-if="vm.isAdd")
                    label.custom-label サイズ
                    md-input-container.m-top-0
                        md-select(ng-model='vm.vendor.VendorOrderItem.size_id' name='size_id' ng-disabled='!vm.isAdd' ng-change="vm.updateSizeId(vm.vendor.VendorOrderItem.size_id)")
                            md-option(ng-repeat='productSize in vm.productSizes' ng-value='productSize.size_id') {{ productSize.name }}
                    span.error-text(ng-if="vm.checkSize") 【サイズ】入力して下さい。
                div.input-container(flex=100 layout='column' ng-if="!vm.isAdd")
                    md-input-container
                        label.custom-label サイズ
                        input(type='text' ng-model='vm.vendor.VendorOrderItem.size' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 発注前枚数
                        input(type='text' ng-model='vm.vendor.VendorOrderItem.order_before_quantity' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label.custom-label 発注枚数
                        input(type='number' ng-model='vm.vendor.VendorOrderItem.order_quantity' name='quantity' ng-readonly='vm.showMode' string-to-number)
                    span.error-text(ng-if="vm.checkQuantity") 【発注枚数】入力して下さい。
                    span.error-text(ng-if="vm.checkMinQuantity") 【発注枚数】 は0より大きくなければなりません。

        footer.text-right(flex='none')
            div(ng-hide='vm.showMode')
                div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存