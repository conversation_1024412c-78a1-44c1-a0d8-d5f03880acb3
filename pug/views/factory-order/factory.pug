md-virtual-repeat-container.content(flex='grow')
    div.m-bottom-30(layout='row')
        div.cell.heading-btns(flex layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.downloadCSV()') CSV出力

    md-divider

    div.default-row(layout='row' layout-align='start center')
        div.cell.flex.col-250
            b 発注
        div.cell.flex.col-250
            b 注文番号
        div.cell.flex.col-250
            b 置き場
        div.cell.flex.col-200
            b 納期遅れ
        div.cell.flex.col-250
            b メーカー
        div.cell.flex.col-250
            b 品番
        div.cell.flex.col-250
            b 画像
        div.cell.flex.col-200
            b カラー
        div.cell.flex.col-200
            b サイズ
        div.cell.flex.col-200
            b 発注前枚数
        div.cell.flex.col-200
            b 発注枚数
        div.cell.cell-action(flex, layout='row', layout-align='end center')

    div.default-row(layout='row' layout-align='start center' md-virtual-repeat='item in vm.infiniteFactoryItems' md-on-demand ng-show='item' ng-style="{'padding-top':'20px'}")
        div.cell.flex.col-250
            div
                md-checkbox(ng-model='item.selected' aria-label='select {{ item.VendorOrderItem.id }}')
        div.cell.flex.col-250 {{ item.VendorOrderItem.customer_order_number }}
        div.cell.flex.col-250 {{ item.VendorOrderItem.place_title }}
        div.cell.flex.col-200 {{ item.VendorOrderItem.delivery_late }}
        div.cell.flex.col-250 {{ item.VendorOrderItem.maker }}
        div.cell.flex.col-250 {{ item.VendorOrderItem.item_code }}
        div.cell.flex.col-250
            img.col-40.row-40(ng-if="item.VendorOrderItem.image_url != null " ng-src='{{ item.VendorOrderItem.image_url }}')
        div.cell.flex.col-200 {{ item.VendorOrderItem.color_title }}
        div.cell.flex.col-200 {{ item.VendorOrderItem.size_title }}
        div.cell.flex.col-200 {{ item.VendorOrderItem.order_before_quantity }}
        div.cell.flex.col-200 {{ item.VendorOrderItem.order_quantity }}
        div.cell.cell-action(flex, layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetailsVendor(item)') 詳細

    div.pagination-loading(style='display: none;')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

factory-details(
    is-shown='vm.factoryDetails.isShown',
    active='vm.factoryDetails.active',
    type='vm.factoryDetails.type',
    on-close='vm.factoryDetails.onClose()',
    on-create='vm.factoryDetails.onCreate()',
    on-update='vm.onUpdate(vendor)',
    on-delete='vm.onDelete(vendorId)'
)