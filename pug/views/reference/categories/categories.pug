div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell(flex=10) ID
            div.cell(flex=35) カテゴリ
            div.cell(flex=15) 厚み
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='category in vm.categories')
                div.cell(flex=10) {{ category.ProductCategory.id }}
                div.cell(flex=35) {{ category.ProductCategory.title }}
                div.cell(flex=15) {{ category.ProductCategory.thickness }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(category)') 詳細

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-category-details(
    is-shown='vm.categoryDetails.isShown',
    active='vm.categoryDetails.active',
    type='vm.categoryDetails.type',
    on-close='vm.onCategoryClose()',
    on-create='vm.onCategoryCreate()'
    on-update='vm.onCategoryUpdate(category)',
    on-delete='vm.onCategoryDelete(categoryId)'
)