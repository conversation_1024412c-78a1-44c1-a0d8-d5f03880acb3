md-sidenav.management-sidenav.md-sidenav-right(md-component-id='add-printer').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 プリンター追加
		md-content(flex='grow')
			form.form.default-form.custom-form(layout='row' layout-wrap name='addPrinterForm')
				div.input-container(flex=50)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.printer.Printer.title' name='title' required)
				div.input-container(flex=50)
					md-input-container
						label 種類
						md-select(ng-model='vm.printer.Printer.type_id' name='type_id' required)
							md-option(ng-repeat='type in vm.types' ng-value='type.PrinterType.id') {{ type.PrinterType.title }}
				div.layout-row.flex-100
					div.input-container(flex=50)
						md-input-container
							label カラー
							md-select(ng-model='vm.printer.Printer.color_id' name='color_id' required)
								md-option(ng-repeat='color in vm.colors' ng-value='color.PrinterColor.id') {{ color.PrinterColor.title }}
					div.input-container(flex=50)
						md-input-container
							label 印刷数
							input(type='number' ng-model='vm.printer.Printer.capacity' name='capacity' string-to-number required)
				div.layout-row.flex-100
					div.input-container(flex=50)
						md-input-container.option-customers-container(style='margin:10px 0 -10px')
							div.option-label
								label 自動ソートフラグ
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_auto_sort' ng-click='vm.toggleSelect()' aria-label='label' name='seiren_id')
					div.input-container(flex=50)
						md-input-container
							label ソートパターン
							md-select(ng-model='vm.printer.Printer.planning_sort_pattern_id' name='planning_sort_pattern_id' ng-disabled='vm.showSelect')
								md-option(ng-repeat='remark in vm.remarks' ng-value='remark.GeneralPurpose.key') {{ remark.GeneralPurpose.remarks }}
				div.layout-row.flex-100
					div.input-container(flex=100)
						md-input-container
							label 外注先コード
							input(type='text' ng-model='vm.printer.Printer.subcontractor_code' name='subcontractor_code' ng-readonly='vm.showMode')
				div.layout-row.flex-100.checkbox-custom
					div.input-container(flex=33)
						md-input-container.option-customers-container
							div.option-label
								label Kornit専用
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_kornit' aria-label='label' name='kornit_id' ng-disabled='vm.showMode') Kornit専用
					div.input-container(flex=33)
						md-input-container.option-customers-container
							div.option-label
								label Brother専用
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_brother' aria-label='label' name='is_brother' ng-disabled='vm.showMode') Brother専用
					div.input-container(flex=33)
						md-input-container.option-customers-container
							div.option-label
								label Epson専用
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_epson' aria-label='label' name='is_epson' ng-disabled='vm.showMode') Epson専用
					div.error-text( ng-if="vm.checkPrinter" ) Kornit専用、Brother専用、Epson専用はどれか１つのみチェックをつけて下さい。
				div.layout-row.flex-100
					div.input-container(flex=50)
						label.custom-label 工場
						md-input-container.m-top-0
							md-select(ng-model='vm.printer.Printer.factory_id' name='factory_id')
								md-option(ng-repeat='factory in vm.factories' ng-click='vm.selectFactory(factory.id)' ng-value='factory.id') {{ factory.title }}
						span.error-text( ng-if="vm.checkFactory" ) 【工場】入力して下さい。
					div.input-container(flex=50)
						md-input-container.option-customers-container
							div.option-label
								label そでプリント
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_exclude_sleeve' aria-label='label' name='is_exclude_sleeve' ng-disabled='vm.showMode') 含まない
				div.layout-row.flex-100
					div.input-container(flex=50)
						md-input-container.option-customers-container
							div.option-label
								label 淡色専用
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_light_color' aria-label='label' name='is_light_color' ng-disabled='vm.showMode') 淡色専用
					div.input-container(flex=50)
						md-input-container.option-customers-container
							div.option-label
								label ワーキングウェア専用
							div.option-customers
								md-checkbox.custom-content(ng-model='vm.printer.Printer.is_working_wear' aria-label='label' name='is_working_wear' ng-disabled='vm.showMode') ワーキングウェア専用
				div.layout-row.flex-100
					div.input-container(flex=50)
						md-input-container.option-customers-container
							div.option-label
								label 優先
							div.option-customers
								md-checkbox(ng-model='vm.printer.Printer.is_priority' aria-label='label' name='is_light_color' ng-disabled='vm.showMode') 優先
					div.input-container(flex=50)
						label.custom-label 1時間当たり生産面数
						md-input-container(style="margin-top: 5px;")
							input(type='number' ng-model='vm.printer.Printer.capacity_per_hour' name='capacity_per_hour' string-to-number required ng-readonly='vm.showMode')
						span.error-text(ng-if="vm.checkCapacity") 【1時間当たり生産面数】1～9999を入力してください。
				div.layout-row.flex-100
					md-input-container.option-customers-container
						div.option-label
							label 連携会社名
						div.option-customers(ng-repeat='customer in vm.optionList.customers')
							md-checkbox(ng-model='customer.selected' aria-label='label' name='customer_id' ng-disabled='vm.showMode') {{customer.Customer.title}}
				div.layout-row.flex-100
					div.input-container(flex=100)
						label あんどん
						md-input-container.m-top-0
							md-select(ng-model='vm.printer.Printer.andon_id' name='andon_id' ng-disabled='vm.showMode')
								md-option(ng-click='vm.printer.Printer.andon_id = null')
								md-option(ng-repeat='andon in vm.optionList.andons track by andon.Andon.id' ng-value='andon.Andon.id') {{ andon.Andon.title }}
				div.layout-row.flex-100
					div.input-container(flex=50)
						md-input-container.option-scroll-container
							div.option-label
								label 連携品番
							div.option-scroll
								div
									md-checkbox(name='' ng-disabled='vm.showMode' ng-click='vm.selectAllCode()') 全選択
								div(ng-repeat='code in vm.optionList.codes')
									md-checkbox(ng-model='code.selected' aria-label='label' name='code_id' ng-disabled='vm.showMode' ng-if='code.ProductJoin.is_same_day == 1') {{code.ProductLinkedCode.code}}  【即日】
									md-checkbox(ng-model='code.selected' aria-label='label' name='code_id' ng-disabled='vm.showMode' ng-if='code.ProductJoin.is_same_day != 1') {{code.ProductLinkedCode.code}}
					div.input-container(flex=50)
						md-input-container.option-scroll-container
							div.option-label
								label 連携カラー
							div.option-scroll
								div
									md-checkbox(name='' ng-disabled='vm.showMode' ng-click='vm.selectAllColor()') 全選択
								div(ng-repeat='color in vm.optionList.colors' )
									md-checkbox(ng-model='color.selected' aria-label='label' name='color_id' ng-disabled='vm.showMode') {{color.ProductColor.title}}
				div.layout-row.flex-100
					md-input-container.option-customers-container
						div.option-label
							label 連携サイズ
						div.option-scroll
							div
								md-checkbox(name='' ng-disabled='vm.showMode' ng-click='vm.selectAllSize()') 全選択
							div.option-customers(ng-repeat='size in vm.optionList.sizes')
								md-checkbox(ng-model='size.selected' aria-label='label' name='size_id' ng-disabled='vm.showMode') {{size.ProductSize.title}}
		footer.text-right(flex='none')
			md-button.md-raised.md-accent(ng-click='vm.save()') 保存

style(type="text/css").
	.default-form .flex-33 {
		border-right: none !important;
		max-width: 100%;
	}

	.custom-form .checkbox-custom {
		display: grid;
		grid-template-columns: repeat(3, minmax(0, 1fr));
		padding-bottom: 10px;
	}

	.custom-form .checkbox-custom .error-text {
		width: max-content;
	}

	.custom-content {
		display: block;
		width: max-content;
	}
