md-sidenav.management-sidenav.md-sidenav-right(md-component-id='uv-frame-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? 'UV追加' : 'UV情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='uvFrameForm')
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label ID
						input(type='text' ng-model='vm.uv.id' readonly)
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.uv.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label コード
						input(type='text' ng-model='vm.uv.code' name='code' ng-readonly='true' required)
				div.input-container(flex=100)
					md-input-container
						label フレーム幅
						input(type='number' ng-model='vm.uv.frame_width' name='frame_width' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label フレーム縦
						input(type='number' ng-model='vm.uv.frame_height' name='frame_height' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label frame_and_cell_top_offset
						input(type='number' ng-model='vm.uv.frame_and_cell_top_offset' name='frame_and_cell_top_offset' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label frame_and_cell_left_offset
						input(type='number' ng-model='vm.uv.frame_and_cell_left_offset' name='frame_and_cell_left_offset' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label cell_to_cell_horizontal_merge
						input(type='number' ng-model='vm.uv.cell_to_cell_horizontal_merge' name='cell_to_cell_horizontal_merge' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label cell_to_cell_vertical_merge
						input(type='number' ng-model='vm.uv.cell_to_cell_vertical_merge' name='cell_to_cell_vertical_merge' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label セル横
						input(type='number' ng-model='vm.uv.cell_width' name='cell_width' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label セル縦
						input(type='number' ng-model='vm.uv.cell_height' name='cell_height' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label 行数
						input(type='number' ng-model='vm.uv.rows' name='rows' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label 列数
						input(type='number' ng-model='vm.uv.columns' name='columns' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label 回転
						input(type='number' ng-model='vm.uv.rotation' name='rotation' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						md-input-container
						label 印刷時高さ設定
						input(type='number' ng-model='vm.uv.height_print' name='height_print' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100)
					md-input-container
						label 治具置場の場所
						input(type='text' ng-model='vm.uv.storage_location' name='storage_location' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					label.custom-label 指示書の用紙サイズ
					md-input-container.m-top-0
						md-select(ng-model='vm.uv.instruction_paper_size_id' name='instruction_paper_size_id' ng-disabled='vm.showMode')
							md-option(ng-repeat='paper in vm.listPaper' ng-value='paper.key') {{ paper.value }}
					div.error-text(style="color:red;margin-left:-4px" ng-if="vm.check_instruction_paper_size_id") 【指示書の用紙サイズ】入力して下さい。
				div.input-container(flex=100)
						label アイテム情報
						div.flex-100(layout='row' ng-style='{"margin-top" : "10px","height" : "27px", "border-top" : "1px solid #f5f5f5"}' ng-repeat='product in vm.Products')
							div {{ product.product_title }}

				div.layout-row.flex-100(ng-style='{"border-top" : "1px solid #c8c8c8"}')
					md-input-container.option-customers-container
						div.option-label
							div.checkbox-container(ng-style= '{"border-top" : "none"}'): md-checkbox(ng-model='vm.uv.is_die_cut' ng-disabled='vm.showMode') ダイカットベース
							div.checkbox-container(flex=100)
								label 画像の反転
								md-checkbox.m-left-20(ng-model='vm.uv.flip_horizontal' ng-disabled='vm.showMode' ) flip_horizontal
								md-checkbox(ng-model='vm.uv.flip_vertical' ng-disabled='vm.showMode' ) flip_vertical
				div.input-container(flex=100 style="border-top:none;margin-top:-40px" ng-if='vm.uv.is_die_cut'): md-input-container
					label フレーム幅
					input(type='number' ng-model='vm.uv.horizontal_width' name='horizontal_width' ng-readonly='vm.showMode' required string-to-number)
				div.input-container(flex=100 ng-if='vm.uv.is_die_cut'): md-input-container
					label フレーム縦
					input(type='number' ng-model='vm.uv.vertical_height' name='vertical_height' ng-readonly='vm.showMode' required string-to-number)

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存