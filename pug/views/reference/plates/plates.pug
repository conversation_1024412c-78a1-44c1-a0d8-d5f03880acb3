div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex) タイトル
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='plate in vm.plates')
				div.cell(flex) {{ plate.Plate.title }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small {{  plate.Plate.updated_at | userTime }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(plate)') 詳細

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
			
reference-plate-details(
	is-shown='vm.plateDetails.isShown',
	active='vm.plateDetails.active',
	type='vm.plateDetails.type',
	on-close='vm.onPlateClose()',
	on-create='vm.onPlateCreate()'
	on-update='vm.onPlateUpdate(plate)',
	on-delete='vm.onPlateDelete(plateId)'
)