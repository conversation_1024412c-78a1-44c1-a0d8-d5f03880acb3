md-sidenav.management-sidenav.md-sidenav-right(md-component-id='option-item-detail').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? 'オプションアイテム追加' : 'オプションアイテム情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

        md-content(flex='grow')
            form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='optionItemForm')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label ID
                        input(type='text' ng-model='vm.option_item.id' readonly)
                div.input-container(flex=100)
                    label.custom-label タイトル
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.option_item.title'  ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.hasError && vm.error.title" ) {{ vm.error.title }}
                div.input-container(flex=100)
                    label.custom-label コード
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.option_item.code'  ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.hasError && vm.error.code" ) {{ vm.error.code }}
                div.input-container(flex=100)
                    label.custom-label サイズ
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.option_item.size_title'  ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.hasError && vm.error.size_title" ) {{ vm.error.size_title }}
                div.input-container(flex=100)
                    label.custom-label カラー
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.option_item.color_title'  ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.hasError && vm.error.color_title" ) {{ vm.error.color_title }}
                div.input-container(flex=100)
                    md-input-container
                        label 素材
                        input(type='text' ng-model='vm.option_item.material' ng-readonly='vm.showMode')
                    span.error-text( ng-if="vm.hasError && vm.error.material" ) {{ vm.error.material }}
                div.input-container(flex=100)
                    md-input-container
                        label 説明
                        textarea(md-no-resize max-rows=7 ng-model='vm.option_item.description' ng-readonly='vm.showMode')
                    span.error-text( ng-if="vm.hasError && vm.error.description" ) {{ vm.error.description }}
                div.input-container(flex=100 ng-class="{'bottom-border': vm.isAdd}")
                    label.custom-label 単価
                    md-input-container.m-top-0
                        input(type='number' ng-model='vm.option_item.unit_price' ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.hasError && vm.error.unit_price" ) {{ vm.error.unit_price }}
                div.input-container.bottom-border(flex=100 ng-hide='vm.isAdd')
                    md-input-container
                        label 画像
                        button.btn-edit.simple-btn.side-img-btn(type='button' ng-click='vm.editImage(null)'): md-icon(md-svg-src='img/icons/upload.svg')
                        input#imgInput.ng-hide(type='file' ng-model='vm.uploadImg' ng-change='vm.editImage(this)')
                        div.side-img.text-center
                            img(ng-show='vm.option_item.image_url', ng-src='{{ vm.option_item.image_url }}')
        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存