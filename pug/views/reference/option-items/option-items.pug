div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell(flex)
            div(flex='none', layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='option_item in vm.option_items')
                div.cell(flex=10) {{ option_item.id }}
                div.cell(flex) {{ option_item.title }}
                div.cell(flex) {{ option_item.code }}
                div.cell(flex) {{ option_item.size_title }}
                div.cell(flex) {{ option_item.color_title }}
                div.cell(flex) {{ option_item.updated_at }}
                div.cell.cell-action(flex='none', layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(option_item)') 詳細


            div.pagination-loading(ng-show='vm.pagination.loading')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-option-items-details(
    is-shown='vm.optionItemsDetail.isShown',
    active='vm.optionItemsDetail.active',
    type='vm.optionItemsDetail.type',
    on-close='vm.onOptionItemClose()',
    on-create='vm.onOptionItemCreate()',
    on-update='vm.onOptionItemUpdate(option_item)',
    on-delete='vm.onOptionItemDelete(optionItemId)'
)