div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='andon in vm.infiniteAndons' md-on-demand ng-show='andon')
                div.cell(flex=15) {{ andon.id }}
                div.cell(flex=15) {{ andon.title }}
                div.cell(flex=15) {{ andon.factory_type }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(andon)') 詳細

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-andon-details(
    is-shown='vm.andonDetails.isShown',
    active='vm.andonDetails.active',
    type='vm.andonDetails.type',
    on-close='vm.onAndonClose()',
    on-create='vm.onAndonCreate()',
    on-update='vm.onAndonUpdate(andon)',
    on-delete='vm.onAndonDelete(andonId)'
)