md-sidenav.management-sidenav.md-sidenav-right(md-component-id='reference-andon-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? 'あんどん追加' : 'あんどん情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.delete($event)'): i.icons8-delete

        md-content(flex='grow')
            form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='andonFrameForm')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label ID
                        input(type='text' ng-model='vm.andon.id' readonly)
                div.input-container(flex=100)
                    label.custom-label 名前
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.andon.title' name='title' ng-readonly='vm.showMode')
                    span.error-text( ng-if="vm.checkAndon" ) 【名前】入力して下さい。
                div.input-container(flex=100)
                    label.custom-label 工場
                    md-input-container.m-top-0
                        md-select(ng-model='vm.andon.factory_id' name='factory_type' ng-disabled='vm.showMode')
                            md-option(ng-repeat='factoryType in vm.factoryTypes' ng-value='factoryType.Factory.id') {{ factoryType.Factory.title  }}
                    span.error-text( ng-if="vm.checkfactoryType" ) 【工場】入力して下さい。
        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
