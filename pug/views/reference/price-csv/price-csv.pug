div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-virtual-repeat-container.content(flex='grow')
        div.default-row(layout='row' layout-align='start center')
            div.cell.flex.col-150
                b 発注者
            div.cell.flex.col-400
                b URL
        div.default-row(layout='row' layout-align='start center' md-virtual-repeat='url in vm.infiniteUrls' md-on-demand ng-show='url')
            div.cell.flex.col-150 {{ url.PriceCsv.customer }}
            div.cell.flex.col-400
                a.link(href='{{ url.PriceCsv.url_archive  }}', target='_blank') {{url.PriceCsv.url_name}}
            div.cell.flex.col-250.cell-action(layout='row', layout-align='end center')
                div.text-small {{  url.PriceCsv.created_at }}


        div.pagination-loading(style='display: none;')
            md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした
