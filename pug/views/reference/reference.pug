.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('プリンター')")
						div(flex='grow'): a(ui-sref='reference.printers') プリンター
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add printer' ng-click='vm.addPinter()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('商品')")
						div(flex='grow'): a(ui-sref='reference.products') 商品
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add product' ng-click='vm.addProduct()' ng-if="vm.showAddProduct"): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('プラテン')")
						div(flex='grow'): a(ui-sref='reference.plates') プラテン
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add plate' ng-click='vm.addPlate()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('置き場')")
						div(flex='grow'): a(ui-sref='reference.places') 置き場
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add place' ng-click='vm.addPlace()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('ジグ')")
						div(flex='grow'): a(ui-sref='reference.printing-bases') ジグ
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add printing base' ng-click='vm.addPrintingBase()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('発注メーカー')")
						div(flex='grow'): a(ui-sref='reference.sources') 発注メーカー
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add printing base' ng-click='vm.addSource()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('UVフレーム')")
						div(flex='grow'): a(ui-sref='reference.uv-frames') UVフレーム
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add uv frame' ng-click='vm.addUvFrame()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('失敗理由')")
						div(flex='grow'): a(ui-sref='reference.reasons') 失敗理由
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addReason()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('EPSONお客様指定')")
						div(flex='grow'): a(ui-sref='reference.clients') EPSONお客様指定
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addClient()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('Kornitお客様指定')")
						div(flex='grow'): a(ui-sref='reference.clients-kornit') Kornitお客様指定
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addClientKornit()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('トリミング除外品番')")
						div(flex='grow'): a(ui-sref='reference.trims') トリミング除外品番
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('倉庫')")
						div(flex='grow'): a(ui-sref='reference.warehouses') 倉庫
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addWarehouse()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('カテゴリ')")
						div(flex='grow'): a(ui-sref='reference.categories') カテゴリ
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addCategory()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('画像ネーム')")
						div(flex='grow'): a(ui-sref='reference.image-name') 画像ネーム
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('特定品番仕分け')")
						div(flex='grow'): a(ui-sref='reference.order-product-code') 特定品番仕分け
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('値段設定のCSV')")
						div(flex='grow'): a(ui-sref='reference.price-csv') 値段設定のCSV
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('生産セル')")
						div(flex='grow'): a(ui-sref='reference.product-cell') 生産セル
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addProductCell()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('工場')")
						div(flex='grow'): a(ui-sref='reference.factories') 工場
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add factory' ng-click='vm.addFactory()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('あんどん')")
						div(flex='grow'): a(ui-sref='reference.andon') あんどん
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addAndon()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('CPフレーム')")
						div(flex='grow'): a(ui-sref='reference.cp-frames') CPフレーム
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add cp frame' ng-click='vm.addCpFrame()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('オプションアイテム')")
						div(flex='grow'): a(ui-sref='reference.optionitems') オプションアイテム
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(ng-click='vm.addOptionItem()'): i.icons8-plus
			div.sidebar-secondary
				sidebar-secondary-menu(statuses='vm.statuses' on-change='vm.onStatusChange')
						
		md-content.main-body.flex-100(ui-view)
		
		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')

	add-printer(open='vm.addPinter' on-create='vm.onPrinterCreate' ng-if="vm.state.is('reference.printers')")
	add-place(open='vm.addPlace' on-create='vm.onPlaceCreate' ng-if="vm.state.is('reference.places')")
	add-reason(open='vm.addReason' on-create='vm.onReasonCreate' ng-if="vm.state.is('reference.reasons')")
	add-warehouse(open='vm.addWarehouse' on-create='vm.onWarehouseCreate' ng-if="vm.state.is('reference.warehouses')")