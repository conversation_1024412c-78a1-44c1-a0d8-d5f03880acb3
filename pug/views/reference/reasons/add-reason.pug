md-sidenav.management-sidenav.md-sidenav-right(md-component-id='add-reason').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 失敗理由
		md-content(flex='grow')
			form.form.default-form.bottom-margin(layout='row' layout-wrap name='addReasonForm')
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.reason.KenpinStatus.title' name='title' required)
				div.input-container(flex=100)
					md-input-container
						label 種類
						md-select(ng-model='vm.reason.KenpinStatus.type' name='type' required)
							md-option(ng-repeat='type in vm.types' ng-value='type.id') {{ type.title }}

		footer.text-right(flex='none')
			md-button.md-raised.md-accent(ng-click='vm.save()') 保存