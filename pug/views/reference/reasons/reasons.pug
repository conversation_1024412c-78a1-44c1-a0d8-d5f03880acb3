div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell ID
			div.cell タイトル
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column' style='padding: 0 47px 0 60px;')
		div.search(layout='row')
			div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
				i.icons8-search
				input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
				button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
		md-divider

		ul(ui-sortable="vm.sortableOptions", ng-model="vm.reasons")
			li(ng-repeat="reason in vm.reasons")
				div.default-row(layout='row' layout-align='start center')
					div.cell {{ ($index + 1) }}
					div.cell.text-center {{ reason.KenpinStatus.title }}
					div.cell.cell-action(flex layout='row', layout-align='end center')
						div.buttons
							md-button.md-raised.md-primary.btn-sm(ng-click='vm.reasonDetails(reason)') 詳細

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

edit-reason(open='vm.reasonDetails' on-update='vm.onReasonUpdate')