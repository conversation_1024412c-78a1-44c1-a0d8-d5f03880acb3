div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=10) 表裏トリミングしない
			div.cell(flex=10) 袖トリミングしない
			div.cell(flex=35) 内容
			div.cell.text-center(flex=10) 種類
			div.cell(flex=10) サイズ数
			div.cell(flex) カラー数
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column')

		md-virtual-repeat-container(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.heading-btns(flex layout='row', layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.saveTrim(vm.products)') 保存
			md-divider

			div.default-row.text-align-center(layout='row' layout-align='start center' md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
				div(flex=10)
					md-checkbox.box-not-margin-bottom(ng-model='product.Product.not_trim', aria-label='label')
				div(flex=10)
					md-checkbox.box-not-margin-bottom(ng-model='product.Product.not_trim_sleeve', aria-label='label')
				div.cell(flex=35 layout='row' layout-align='start center')
					div.data-rowed.rowed-inline
						div.addition {{ product.Product.id }}
						.divider.addition(ng-show='product')
						div {{ product.Product.title }}

				div.cell.text-center(flex=10) {{ product.Product.type }}
				div.cell(flex=10) {{ product.Product.sizes_count }}
				div.cell(flex) {{ product.Product.colors_count }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center' ng-if='product')
					div.text-small {{ product.Product.updated_at | userTime }}

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='44' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした