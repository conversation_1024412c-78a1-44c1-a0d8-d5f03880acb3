div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=10)
			div.cell(flex=15) 名前
			div.cell(flex=15) 電話番号
			div.cell(flex) メールアドレス
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='client in vm.clients')
				div.cell(flex=10) {{ client.Client.id }}
				div.cell(flex=15) {{ client.Client.name }}
				div.cell(flex=15) {{ client.Client.phone_number }}
				div.cell(flex) {{ client.Client.email }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(client)') 詳細

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-client-details(
is-shown='vm.clientDetails.isShown',
active='vm.clientDetails.active',
type='vm.clientDetails.type',
on-close='vm.onClientClose()',
on-create='vm.onClientCreate()'
on-update='vm.onClientUpdate(client)',
on-delete='vm.onClientDelete(clientId)'
)