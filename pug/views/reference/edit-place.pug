md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-place').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 置場情報
			div.sidenav-actions
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.delete($event)'): i.icons8-delete
		md-content(flex='grow')
			form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='editPlaceForm')
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.place.Place.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					md-input-container
						label 種類
						md-select(ng-model='vm.place.Place.type_id' name='type_id' ng-disabled='vm.showMode' required)
							md-option(ng-repeat='type in vm.types' ng-value='type.PlaceType.id') {{ type.PlaceType.title }}
				div.input-container(flex=100)
					label.custom-label 工場
					md-input-container.m-top-0
						md-select(ng-model='vm.place.Place.factory_id' name='factory_id' ng-disabled='vm.showMode')
							md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
					span.error-text( ng-if="vm.checkFactory" ) 【工場】入力して下さい。

		footer.text-right(flex='none' ng-hide='vm.showMode')
			md-button.md-raised.md-accent(ng-click='vm.update()') 保存