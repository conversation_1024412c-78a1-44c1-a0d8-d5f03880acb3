div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell(flex=5) ID
            div.cell(flex=10) サイズ
            div.cell(flex=20) 商品
            div.cell(flex=15) コード
            div.cell(flex=20) 名前
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='image_name in vm.infiniteImageName' md-on-demand ng-show='image_name')
                div.cell(flex=5) {{ image_name.id }}
                div.cell(flex=10) {{ image_name.title }}
                div.cell(flex=20) {{ image_name.product_title }}
                div.cell(flex=15) {{ image_name.code }}
                div.cell(flex=20) {{ image_name.name_image }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(image_name)') 詳細
            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

reference-image-name-details(
    is-shown='vm.imageNameDetails.isShown',
    active='vm.imageNameDetails.active',
    type='vm.imageNameDetails.type',
    on-close='vm.onImageNameClose()',
    on-create='vm.onImageNameCreate()',
    on-update='vm.onImageNameUpdate(image_name)',
    on-delete='vm.onImageNameDelete(imageNameId)'
)