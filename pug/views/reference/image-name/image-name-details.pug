md-sidenav.management-sidenav.md-sidenav-right(md-component-id='reference-image-name-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 画像情報
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(ng-if='!vm.image_name.is_soko' type='button' ng-click='vm.edit()'): i.icons8-edit

        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='imageNameFrameForm')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label ID
                        input(type='text' ng-model='vm.image_name.id' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label サイズ
                        input(type='text' ng-model='vm.image_name.title' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 商品
                        input(type='text' ng-model='vm.image_name.product_title' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label コード
                        input(type='text' ng-model='vm.image_name.product_code' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 名前
                        input(type='text' ng-model='vm.image_name.name_image' name='name_image' ng-readonly='vm.showMode')

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存