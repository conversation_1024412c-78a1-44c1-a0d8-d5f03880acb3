div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=25) 内容
			div.cell.text-center(flex=10) 種類
			div.cell(flex=10) ソース
			div.cell(flex=10) コード
			div.cell(flex=10) サイズ数
			div.cell(flex=10) カラー数
			div.cell(flex) ミックス商品ID
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column')

		md-virtual-repeat-container(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
				div.cell(flex=25 layout='row' layout-align='start center')
					div.images(layout='row' layout-align='start center')
						div.image(ng-repeat='side in product.sides'): img(ng-src='{{ side.ProductColorSide.image_url }}')
					div.data-rowed.rowed-inline
						div {{ product.Product.title }}
						.divider.addition(ng-show='product')
						div.addition {{ product.Product.category }}
				div.cell.text-center(flex=10) {{ product.Product.type }}
				div.cell(flex=10) {{ product.Product.source }}
				div.cell(flex=10) {{ product.Product.code }}
				div.cell(flex=10) {{ product.Product.sizes_count }}
				div.cell(flex=10) {{ product.Product.colors_count }}
				div.cell(flex) {{ product.Product.product_id_mix }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center' ng-if='product')
					div.text-small {{ product.Product.updated_at}}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(product)') 詳細

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')
				
		.highlighted-column-container(ng-hide='vm.pagination.searchFailed' flex layout='row' style='overflow-y: initial;'): .highlighted-column(flex=10 flex-offset=25)

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

reference-product-details(
	is-shown='vm.isDetailsShown',
	active='vm.activeProduct',
	type='vm.detailsType',
	on-close='vm.onDetailsClose()',
	on-create='vm.onProductCreate()'
	on-update='vm.onProductUpdate(product)',
	on-delete='vm.onProductDelete(productId)'
)