div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=45) 内容
			div.cell(flex=10) カラ
			div.cell(flex) 印刷数
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.cell.data-rowed.rowed-inline(flex=45)
					div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
						i.icons8-search
						input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
						button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.cell-colors.layout-row(flex=10)
				div.cell(flex=20)
				div.cell.text-center(flex ng-style={"padding-top": "25px"})
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeFactory.Factory.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target style='width:175px;')
							md-menu-item(ng-repeat='factory in vm.factoryFilter')
								md-checkbox(ng-model='factory.selected', ng-change='vm.filterFactory(factory.Factory.id)') {{ factory.Factory.title }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')

			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='printer in vm.infinitePrinters' md-on-demand ng-show='printer')
				div.cell.data-rowed.rowed-inline(flex=45)
					div {{ printer.Printer.title }}
					.divider.addition
					div.addition {{ printer.Printer.type }}
				div.cell.cell-colors.layout-row(flex=10)
					div.color-dot(ng-repeat='color in printer.Printer.colorsArr' ng-class='color')
				div.cell(flex=20) {{ printer.Printer.capacity }}
				div.cell(flex) {{ printer.Printer.factory }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small {{  printer.Printer.updated_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.printerDetails(printer)') 詳細

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした
			
edit-printer(open='vm.printerDetails' on-delete='vm.onPrinterDelete')