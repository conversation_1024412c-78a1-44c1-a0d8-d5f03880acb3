md-sidenav.management-sidenav.md-sidenav-right(md-component-id='order-product-code-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 画像情報
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit

        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='productCodeFrameForm')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label アイテム名
                        input(type='text' ng-model='vm.product_code.Product.title' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label コード
                        input(type='text' ng-model='vm.product_code.ProductLinkedCodeJoin.code' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 色
                        input(type='text' ng-model='vm.product_code.ProductColorJoin.title' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label サイズ
                        input(type='text' ng-model='vm.product_code.ProductSizeJoin.title' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 指定
                        input(type='text' ng-model='vm.product_code.ProductColorSizeSkuJoin.sku' name='sku' ng-readonly='vm.showMode')

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存