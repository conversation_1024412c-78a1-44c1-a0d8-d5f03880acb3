div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell(flex=15) アイテム名
            div.cell(flex=15) コード
            div.cell(flex=15) 色
            div.cell(flex=10) サイズ
            div.cell(flex=15) 指定
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product_code in vm.infiniteProductCode' md-on-demand ng-show='product_code')
                div.cell(flex=15) {{ product_code.Product.title }}
                div.cell(flex=15) {{ product_code.ProductLinkedCodeJoin.code }}
                div.cell(flex=15) {{ product_code.ProductColorJoin.title }}
                div.cell(flex=10) {{ product_code.ProductSizeJoin.title }}
                div.cell(flex=15) {{ product_code.ProductColorSizeSkuJoin.sku }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(product_code)') 詳細

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

order-product-code-details(
    is-shown='vm.productCodeDetails.isShown',
    active='vm.productCodeDetails.active',
    type='vm.productCodeDetails.type',
    on-close='vm.onProductCodeClose()',
    on-create='vm.onProductCodeCreate()',
    on-update='vm.onProductCodeUpdate(product_code)',
    on-delete='vm.onProductCodeDelete(productCodeId)'
)