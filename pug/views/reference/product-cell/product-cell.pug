div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product_cell in vm.infiniteProductCells' md-on-demand ng-show='product_cell')
                div.cell(flex=15) {{ product_cell.id }}
                div.cell(flex=15) {{ product_cell.title }}
                div.cell(flex=15) {{ product_cell.factory_type }}
                div.cell(flex=10) {{ product_cell.product_cell_type }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(product_cell)') 詳細

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-product-cell-details(
    is-shown='vm.productCellDetails.isShown',
    active='vm.productCellDetails.active',
    type='vm.productCellDetails.type',
    on-close='vm.onProductCellClose()',
    on-create='vm.onProductCellCreate()',
    on-update='vm.onProductCellUpdate(productCell)',
    on-delete='vm.onProductCellDelete(productCellId)'
)