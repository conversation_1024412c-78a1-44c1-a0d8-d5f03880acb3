md-sidenav.management-sidenav.md-sidenav-right(md-component-id='factory-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? '工場追加' : '工場情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

        md-content(flex='grow')
            form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='factoryForm')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label ID
                        input(type='text' ng-model='vm.factory.id' readonly)
                div.input-container(flex=100)
                    label.custom-label 名前
                    md-input-container.m-top-0
                        input(type='text' ng-model='vm.factory.title' name='title' ng-readonly='vm.showMode' required)
                    span.error-text( ng-if="vm.checkFactory" ) 【名前】入力して下さい。
                div.checkbox-container(flex=100)
                    md-input-container.option-customers-container(style='margin:10px 0 -10px')
                        div.option-label
                            label フランチャイズ
                        div.option-customers
                            md-checkbox(ng-model='vm.factory.is_franchise' aria-label='label' name='is_franchise' ng-disabled='vm.showMode')

                div.checkbox-container(flex=100)
                    md-input-container.option-customers-container(style='margin:10px 0 -10px')
                        div.option-label
                            label プランニング
                        div.option-customers
                            md-checkbox(ng-model='vm.factory.is_planning' aria-label='label' name='is_planning' ng-disabled='vm.showMode')
                div.checkbox-container(flex=100)
                    md-input-container.option-customers-container(style='margin:10px 0 -10px')
                        div.option-label
                            label 外部工場
                        div.option-customers
                            md-checkbox(ng-model='vm.factory.is_external' aria-label='label' name='is_external' ng-disabled='vm.showMode')

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存