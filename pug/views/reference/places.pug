div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=45) 内容
			div.cell.text-center(flex=10) 種類
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.cell.data-rowed.rowed-inline(flex=45)
					div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
						i.icons8-search
						input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
						button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.cell-colors.layout-row(flex=10)
				div.cell.text-center(flex=20 ng-style={"padding-top": "25px"})
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeFactory.Factory.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target style='width:175px;')
							md-menu-item(ng-repeat='factory in vm.factoryFilter')
								md-checkbox(ng-model='factory.selected', ng-change='vm.filterFactory(factory.Factory.id)') {{ factory.Factory.title }}
				div.cell.heading-btns(flex layout='row', layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click="vm.CSVupload(null)" ) CSVアップロード
					input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='place in vm.infinitePlaces' md-on-demand ng-show='place')
				div.cell(flex=45) {{ place.Place.title }}
				div.cell.text-center(flex=10) {{ place.Place.type }}
				div.cell.text-center(flex=20) {{ place.Place.factory }}
				div.cell.cell-action(flex layout='row', layout-align='end center')
					div.text-small {{ place.Place.updated_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.placeDetails(place)') 詳細

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした
			
edit-place(open='vm.placeDetails' on-delete='vm.onPlaceDelete')