div.block-wide(layout='column')

	header(flex='none')
		button.go-back-button(type='button', ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? '連携設定追加' : '連携設定情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
			button.btn-edit.simple-btn(type='button', ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button', ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block

			div.add-color
				form.form.default-form(layout='row' layout-wrap, name='linkedCodeForm')
					div.input-container(flex=100)
						md-input-container
							label ソース
							input(type='text', ng-model='vm.code.code', name='code', required, ng-readonly='vm.showMode')
					div.input-container.bottom-border(flex=100)
						md-input-container
							label コード
							md-select(ng-model='vm.code.source_id' name='source_id' required, ng-disabled='vm.showMode')
								md-option(ng-repeat='source in vm.sources' ng-value='source.ProductLinkedSource.id') {{ source.ProductLinkedSource.title }}

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存