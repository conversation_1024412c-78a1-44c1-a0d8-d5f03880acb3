div.block-wide(layout='column', ng-hide='vm.sideBlock.isShown || vm.linkCodeBlock.isShown')

	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'カラー追加' : 'カラー情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block.bottom-border

			div.add-color
				form.form.default-form(layout='row' layout-wrap name='productColorForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.color.ProductColor.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text' ng-model='vm.color.ProductColor.title' name='color-title' ng-readonly='vm.showMode' required)
					div.input-container.bottom-border(flex=100)
						md-input-container(input-color='vm.color.ProductColor.hex')
							label HEX
							input(type='text' ng-model='vm.color.ProductColor.hex' name='color-hex' ng-readonly='vm.showMode' required)
							.color-preview-container: .color-preview(ng-repeat='color in colors' ng-style="{'background-color': color}")
					div.input-container(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.color.ProductColor.is_light_color' aria-label='label' ng-disabled='vm.showMode')
							label.text-black 淡色

		// sides list
		div.colors-list(ng-show='vm.showMode && !vm.isAdd')

			h3 サイド

			.printer-product-list
				div.heading.text(layout='row' layout-align='start center')
					div.cell(flex=50) 内容

				div.default-row(layout='row' layout-align='center center' ng-repeat='side in vm.sides')
					div.cell.flex
						div.data-rowed(layout='row' layout-align='start center')
							div {{ side.ProductColorSide.title }}
							div.addition(ng-show='side.ProductColorSide.is_main') メイン
					div.cell.flex-none.layout-row.layout-align-end-center
						md-menu(md-position-mode="target-right target")
							button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
							md-menu-content.menu-small(md-menu-align-target)
								md-menu-item
									md-button(ng-click="vm.sideBlock.show(side)") 詳細
								md-menu-item(ng-if="vm.showButton")
									md-button(ng-click="vm.sideBlock.setAsMain(side)" ng-disabled='side.ProductColorSide.is_main') メインにする

			div.text-center: md-button.save-color-btn.btn-sm(ng-click="vm.sideBlock.add()" ng-if="vm.showButton") サイドを追加
			
		// product linked code block
		.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd')

			h3 連携設定

			.sidenav-list
				div.heading.layout-row.layout-align-start-center
					div.cell.flex ソース
					div.cell.flex-none コード
				div.default-row.layout-row.layout-align-start-center(ng-repeat='code in vm.codes')
					div.cell.flex {{ code.ProductColorLinkedCode.source }}
					div.cell.flex-none.layout-row.layout-align-end-center 
						div {{ code.ProductColorLinkedCode.code }}
						md-menu.small(md-position-mode="target-right target")
							button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
							md-menu-content.menu-small(md-menu-align-target)
								md-menu-item: md-button(ng-click='vm.linkCodeBlock.show(code)') 詳細

			div.text-center: md-button.simple-button.btn-sm(ng-click='vm.linkCodeBlock.add()' ng-if="vm.showButton") 連携設定を追加
			
	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
		
// side form
product-color-side-details(
	ng-show='vm.sideBlock.isShown',
	loading-type='vm.loadingType'
	type='vm.sideBlock.type',
	active='vm.sideBlock.activeItem',
	on-create='vm.sideBlock.onCreate()',
	on-update='vm.sideBlock.onUpdate(side)',
	on-delete='vm.sideBlock.onDelete(sideId)',
	on-close='vm.sideBlock.onClose()',
	show-button='vm.showButton',
)

product-color-linked-code-details(
	ng-show='vm.linkCodeBlock.isShown',
	loading-type='vm.loadingType'
	type='vm.linkCodeBlock.type',
	active='vm.linkCodeBlock.activeItem',
	on-create='vm.linkCodeBlock.onCreate()',
	on-update='vm.linkCodeBlock.onUpdate(code)',
	on-delete='vm.linkCodeBlock.onDelete(codeId)',
	on-close='vm.linkCodeBlock.onClose()',
	show-button='vm.showButton',
)