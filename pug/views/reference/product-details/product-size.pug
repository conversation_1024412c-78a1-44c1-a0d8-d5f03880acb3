div.block-wide(layout='column', ng-hide='vm.linkCodeBlock.isShown')

	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'サイズ追加' : 'サイズ情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block.bottom-border

			div.add-color
				form.form.default-form(layout='row' layout-wrap name='productSizeForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.size.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text', ng-model='vm.size.title', name='size-title', required, ng-readonly='vm.showMode')
					div.input-container(flex=100)
						md-input-container
							label コード
							input(type='text', ng-model='vm.size.code', name='size-code', required, ng-readonly='vm.showMode')
					div.input-container(flex=100)
						md-input-container
							label 重量
							input(type='number', ng-model='vm.size.weight', name='size-code', ng-readonly='vm.showMode')
					div.input-container(flex=100)
						md-input-container
							label 襟開け（表）
							div(ng-style='{"position" : "relative"}')
								input(type='number', ng-model='vm.size.coller_open_front', name='coller_open_front', ng-readonly='vm.showMode')
								span(ng-style='{"position" : "absolute","top": "8px","right":"400px"}') cm
						span(ng-if="vm.check_front" ng-style='{"color" : "red"}') 襟開け（表）を0～100を入力してください。

					div.input-container(flex=100)
						md-input-container
							div(ng-style='{"position" : "relative"}')
								label 襟開け（裏）
								input(type='number', ng-model='vm.size.coller_open_back', name='coller_open_back', ng-readonly='vm.showMode')
								span(ng-style='{"position" : "absolute","top": "8px","right":"400px"}') cm
						span(ng-if="vm.check_back" ng-style='{"color" : "red"}') 襟開け（裏）を0～100を入力してください。

					div.input-container(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.size.is_dtf_image_change' aria-label='label' ng-disabled='vm.showMode')
							label(ng-style='{"font-size" : "12px"}') DTF画像サイズを変更する

					div.input-container.bottom-border(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.size.is_resize_84pct' aria-label='label' ng-disabled='vm.showMode')
							label(ng-style='{"font-size" : "12px"}') 印刷画像を84%にリサイズする
		// product linked code block
		.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd')

			h3 連携設定

			.sidenav-list
				div.heading.layout-row.layout-align-start-center
					div.cell.flex ソース
					div.cell.flex-none コード
				div.default-row.layout-row.layout-align-start-center(ng-repeat='code in vm.codes')
					div.cell.flex {{ code.ProductSizeLinkedCode.source }}
					div.cell.flex-none.layout-row.layout-align-end-center 
						div {{ code.ProductSizeLinkedCode.code }}
						md-menu.small(md-position-mode="target-right target")
							button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
							md-menu-content.menu-small(md-menu-align-target)
								md-menu-item: md-button(ng-click='vm.linkCodeBlock.show(code)') 詳細

			div.text-center: md-button.simple-button.btn-sm(ng-click='vm.linkCodeBlock.add()' ng-if="vm.showButton") 連携設定を追加

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
		
		
product-size-linked-code-details(
	ng-show='vm.linkCodeBlock.isShown',
	loading-type='vm.loadingType'
	type='vm.linkCodeBlock.type',
	active='vm.linkCodeBlock.activeItem',
	on-create='vm.linkCodeBlock.onCreate()',
	on-update='vm.linkCodeBlock.onUpdate(code)',
	on-delete='vm.linkCodeBlock.onDelete(codeId)',
	on-close='vm.linkCodeBlock.onClose()',
	show-button='vm.showButton',
)