div.block-wide(layout='column')

	header(flex='none')
		button.go-back-button(type='button', ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? '連携設定追加' : '連携設定情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
			button.btn-edit.simple-btn(type='button', ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button', ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block

			div.add-color
				form.form.default-form(layout='row' layout-wrap, name='plateForm')
					div.input-container(flex=100)
						md-input-container
							label サイズ
							md-select(ng-model='vm.plate.size_id' name='size_id' required, ng-disabled='vm.showMode')
								md-option(ng-repeat='size in vm.sizes' ng-value='size.ProductSize.id') {{ size.ProductSize.title }}
					div.input-container(flex=100)
						md-input-container
							label プラテン
							md-select(ng-model='vm.plate.plate_id' name='plate_id' required, ng-disabled='vm.showMode')
								md-option(ng-repeat='plate in vm.plates' ng-value='plate.Plate.id') {{ plate.Plate.title }}

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存