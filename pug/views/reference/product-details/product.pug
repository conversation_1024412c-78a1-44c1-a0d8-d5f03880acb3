md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-product').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.colorBlock.isShown || vm.sizeBlock.isShown || vm.linkCodeBlock.isShown || vm.feeBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? '商品追加' : '商品情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			product-sides-images.small(ng-if='!vm.isAdd && vm.sides.length' sides='vm.sides')
			form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='editProductForm')
				div.input-container(flex=100)
					div.h-full.flex.justify-between.align-item-center
						label.text-black 画像の回転
						md-checkbox.m-top-16(ng-model='vm.product.Product.rotate_right_90' aria-label='label' ng-disabled='vm.showMode || vm.product.Product.rotate_left_90 || vm.product.Product.rotate_180') 右90°
						md-checkbox.m-top-16(ng-model='vm.product.Product.rotate_left_90' aria-label='label' ng-disabled='vm.showMode || vm.product.Product.rotate_right_90 || vm.product.Product.rotate_180') 左90°
						md-checkbox.m-top-16(ng-model='vm.product.Product.rotate_180' aria-label='label' ng-disabled='vm.showMode || vm.product.Product.rotate_right_90 || vm.product.Product.rotate_left_90') 180°
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label ID
						input(type='text' ng-model='vm.product.Product.id' readonly)
				div.input-container(flex=50)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.product.Product.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=50)
					md-input-container
						label カテゴリ
						md-select(ng-model='vm.product.Product.category_id' name='category_id' ng-disabled='vm.showMode' required)
							md-option(ng-value='category.ProductCategory.id' ng-repeat='category in vm.categories') {{ category.ProductCategory.title }}
				div.input-container.flex-50-first(flex=50)
					md-input-container
						label 種類
						md-select(ng-model='vm.product.Product.type_id' name='type_id' ng-disabled='vm.showMode' ng-change='vm.onTypeChange()' required)
							md-option(ng-value='type.ProductType.id' ng-repeat='type in vm.types') {{ type.ProductType.title }}
				div.input-container(flex=50)
					div.h-full.flex.justify-between.align-item-center
						label.text-black レディース
						md-checkbox.m-0(ng-model='vm.product.Product.for_women' aria-label='label' ng-disabled='vm.showMode')
				div.input-container.flex-50-first(flex=50)
					label.custom-label 単価（淡色）
					md-input-container(style="margin-top: 5px;")
						input(type='number' ng-model='vm.product.Product.unit_price_light_color' string-to-number required ng-readonly='vm.showMode')
					span.error-text(ng-if="vm.validationErrors.unit_price_light_color") 単価（淡色）を0～9999999を入力してください。
				div.input-container.flex-50(flex=50)
					label.custom-label 単価（濃色）
					md-input-container(style="margin-top: 5px;")
						input(type='number' ng-model='vm.product.Product.unit_price_dark_color' string-to-number required ng-readonly='vm.showMode')
					span.error-text(ng-if="vm.validationErrors.unit_price_dark_color") 単価（濃色）を0～9999999を入力してください。
				div.input-container.flex-50-first(flex=50)
					label.custom-label プリント工賃（淡色）
					md-input-container(style="margin-top: 5px;")
						input(type='number' ng-model='vm.product.Product.design_fee_light_color' string-to-number required ng-readonly='vm.showMode')
					span.error-text(ng-if="vm.validationErrors.design_fee_light_color") プリント工賃（淡色）を0～9999999を入力してください。
				div.input-container.flex-50(flex=50)
					label.custom-label プリント工賃（濃色）
					md-input-container(style="margin-top: 5px;")
						input(type='number' ng-model='vm.product.Product.design_fee_dark_color' string-to-number required ng-readonly='vm.showMode')
					md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(ng-if='!vm.isAdd && vm.product.Product.is_design_fee' style="margin-left: 200px" ng-click="vm.feeBlock.add()"): i.icons8-more
					span.error-text(ng-if="vm.validationErrors.design_fee_dark_color") プリント工賃（濃色）を0～9999999を入力してください。
				div.input-container(flex=100)
					md-input-container
						label 英タイトル
						input(type='text' ng-model='vm.product.Product.english_title' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label ミックス商品ID
						input(type='number' ng-model='vm.product.Product.product_id_mix' ng-readonly='vm.showMode' string-to-number)
				div.input-container(flex=100 ng-show="vm.product.Product.type_id == 1 || vm.product.Product.type_id == 2 || vm.product.Product.type_id == 4")
					div.h-full.flex.align-item-center
						md-checkbox.m-top-16.m-left-20(ng-model='vm.product.Product.type_id_2' aria-label='label' ng-disabled='vm.showMode')
						label.text-black シルク印刷が可能
				div.input-container(flex=100)
					md-input-container
						label 値段
						input(type='number' ng-model='vm.product.ProductPrice.price' ng-readonly='vm.showMode' string-to-number)
				div.input-container(flex=100)
					md-input-container
						label セット販売個数
						input(type='number' ng-model='vm.product.Product.set_sales_quantity' ng-readonly='vm.showMode' string-to-number)
				div.error-text(ng-style="{'color': 'red','margin-top':'10px'}" ng-if='vm.check_set_sales_quantity') セット販売個数を1～100を入力してください。
				div.input-container(flex=100)
					md-input-container
						label 条件書
						input(type='text' ng-model='vm.product.Product.condition_note' ng-disabled='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 説明書
						input(type='text' ng-model='vm.product.Product.guide_note_url' ng-readonly='vm.showMode' style='width:90%;')
						button.btn-edit.simple-btn.side-img-btn(type='button' ng-click='vm.editImage(null)' ng-show='!vm.showMode'): md-icon(md-svg-src='img/icons/upload.svg' ng-show='!vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 送り状印字アイテム
						md-select(ng-model='vm.product.Product.delivery_slip_item_id' name='delivery_slip_item_id' ng-disabled='vm.showMode')
							md-option()
							md-option(ng-value='delivery.id' ng-repeat='delivery in vm.deliveries') {{ delivery.title }}
				div.input-container(flex=100)
					div.h-full.flex.align-item-center
						md-checkbox.m-top-16.m-left-20(ng-model='vm.product.Product.is_dry' aria-label='label' ng-disabled='vm.showMode')
						label.text-black ドライ
				div.input-container(flex=100)
					div.h-full.flex.align-item-center
						md-checkbox.m-top-16.m-left-20(ng-model='vm.product.Product.is_same_day' aria-label='label' ng-disabled='vm.showMode')
						label.text-black 即日
				div.input-container(flex=100)
					div.h-full.flex.align-item-center
						md-checkbox.m-top-16.m-left-20(ng-model='vm.product.Product.is_instruction_textcolor_change' aria-label='label' ng-disabled='vm.showMode')
						label.text-black 指示書の文字色を変更
				div.input-container(flex=100)
					label.custom-label 昇華画像の左右線サイズ (mm)
					md-input-container(style="margin-top: 5px;")
						input(type='number' ng-model='vm.product.Product.sublimation_image_borderline_size' string-to-number required ng-readonly='vm.showMode')
					span.error-text(ng-if="vm.checkBorderLine") 昇華画像の左右線サイズを0～999を入力してください。
				div.input-container.bottom-border(flex=100 ng-if="vm.product.Product.type_id && vm.product.Product.type_id === vm.uvTypeId")
					md-input-container
						label UVフレーム
						md-select(ng-model='vm.product.Product.uv_frame' name='uvFrame' ng-disabled='vm.showMode')
							md-option(ng-value='uv.UVFrame.id' ng-repeat='uv in vm.uvFrames') {{ uv.UVFrame.title }}
				div.input-container.bottom-border(flex=100 ng-if="vm.product.Product.type_id && vm.product.Product.type_id === vm.vakuumTypeId")
					md-input-container
						label ジグ
						md-select(ng-model='vm.product.Product.printing_base_id' name='printingBase' ng-disabled='vm.showMode')
							md-option(ng-value='base.PrintingBase.id' ng-repeat='base in vm.printingBases') {{ base.PrintingBase.title }}
				input#imgInput.ng-hide(type='file' ng-model='vm.uploadImg' ng-change='vm.editImage(this)')
				input.ng-hide(type='text' ng-model='vm.product.Product.guide_note_filename')

			// sizes list
			.sidenav-list-block.bottom-margin(ng-show='vm.showMode && !vm.isAdd')

				h3 サイズ

				.sidenav-list
					div.heading.text(layout='row' layout-align='start center')
						div.cell(flex=50) 内容

					div.default-row(layout='row' layout-align='center center' ng-repeat='size in vm.sizes')
						div.cell(flex=50): div.data-rowed(layout='row' layout-align='start center')
							div {{ size.ProductSize.title }}
							div.addition(ng-show='size.ProductSize.is_main') メイン
						div.cell.flex.layout-row.layout-align-end-center
							md-menu.small(md-position-mode="target-right target")
								button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
								md-menu-content.menu-small(md-menu-align-target)
									md-menu-item
										md-button(ng-click="vm.sizeBlock.show(size)") 詳細
									md-menu-item(ng-if="vm.showButton")
										md-button(ng-click="vm.sizeBlock.setAsMain(size)" ng-disabled='size.ProductSize.is_main') メインにする

				div.text-center: md-button.simple-button.btn-sm(ng-click="vm.sizeBlock.add()" ng-if="vm.showButton") サイズを追加

			// colors list
			.colors-list.bottom-border(ng-show='vm.showMode && !vm.isAdd')

				h3 カラー

				.printer-product-list
					div.heading.text(layout='row' layout-align='start center')
						div.cell(flex=50) プレビュー
						div.cell.text-right(flex=50) 内容

					div.default-row(layout='row' layout-align='center center' ng-repeat='color in vm.colors')
						div.cell.flex-none(layout='row' layout-align='start center')
							div.image.flex-none(ng-repeat='side in color.sides'): img(ng-src='{{ side.ProductColorSide.image_url }}')
						div.cell.flex.data-rowed.rowed-inline.text-right
							div.addition.reversed.have-margin(ng-show='color.ProductColor.is_main') メイン
							div {{ color.ProductColor.title }}
							div.divider
							div {{ color.ProductColor.hex }}
							div.color-preview-container(layout='row' layout-align='end center'): div.color-preview(ng-repeat='color in vm.makeColorArr(color.ProductColor.hex)' ng-style="{'background-color': color}")
							md-menu(md-position-mode="target-right target")
								button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
								md-menu-content.menu-small(md-menu-align-target)
									md-menu-item
										md-button(ng-click="vm.colorBlock.show(color)") 詳細
									md-menu-item(ng-if="vm.showButton")
										md-button(ng-click="vm.colorBlock.setAsMain(color)" ng-disabled='color.ProductColor.is_main') メインにする

				div.text-center: md-button.save-color-btn.btn-sm(ng-click="vm.colorBlock.add()" ng-if="vm.showButton") カラーを追加

			// product linked code block
			.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd')

				h3 連携設定

				.sidenav-list
					div.heading.layout-row.layout-align-start-center
						div.cell.flex ソース
						div.cell.flex-none コード
					div.default-row.layout-row.layout-align-start-center(ng-repeat='code in vm.codes')
						div.cell.flex {{ code.ProductLinkedCode.source }}
						div.cell.flex-none.layout-row.layout-align-end-center
							div {{ code.ProductLinkedCode.code }}
							md-menu.small(md-position-mode="target-right target")
								button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
								md-menu-content.menu-small(md-menu-align-target)
									md-menu-item: md-button(ng-click='vm.linkCodeBlock.show(code)') 詳細

				div.text-center: md-button.simple-button.btn-sm(ng-click='vm.linkCodeBlock.add()' ng-if="vm.showButton") 連携設定を追加

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存

	// color form
	product-color-details(
		ng-show='vm.colorBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.colorBlock.type',
		active='vm.colorBlock.activeItem',
		on-create='vm.colorBlock.onCreate()',
		on-update='vm.colorBlock.onUpdate(color, updateList)',
		on-delete='vm.colorBlock.onDelete(color)',
		on-close='vm.colorBlock.onClose()',
		show-button='vm.showButton',
	)
	// size form
	product-size-details(
		ng-show='vm.sizeBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.sizeBlock.type',
		active='vm.sizeBlock.activeItem',
		on-create='vm.sizeBlock.onCreate()',
		on-update='vm.sizeBlock.onUpdate(size)',
		on-delete='vm.sizeBlock.onDelete(sizeId)',
		on-close='vm.sizeBlock.onClose()',
		show-button='vm.showButton',
	)
	// product linked code form
	product-linked-code-details(
		ng-show='vm.linkCodeBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.linkCodeBlock.type',
		active='vm.linkCodeBlock.activeItem',
		on-create='vm.linkCodeBlock.onCreate()',
		on-update='vm.linkCodeBlock.onUpdate(code)',
		on-delete='vm.linkCodeBlock.onDelete(codeId)',
		on-close='vm.linkCodeBlock.onClose()',
		show-button='vm.showButton',
	)
	product-fee-design(
		ng-show='vm.feeBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.feeBlock.type',
		active='vm.feeBlock.activeItem',
		on-close='vm.feeBlock.onClose()',
		show-button='vm.showButton',
	)