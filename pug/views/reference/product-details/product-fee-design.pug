div.block-wide(layout='column', ng-hide='vm.linkCodeBlock.isShown')

    header(flex='none')
        button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
        h2 工賃登録
    md-content(flex='grow')
        .color-block.bottom-border
            div.add-color
                form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap)
                    div.input-container(flex=100)
                        md-input-container
                            label ID
                            input(type='text' ng-model='vm.product.id' readonly)
                    div.input-container(flex=50 style="border-bottom: 1px solid #c8c8c8;")
                        md-input-container
                            label タイトル
                            input(type='text' ng-model='vm.product.title' readonly)
                    div.input-container(flex=50 style="border-bottom: 1px solid #c8c8c8;")
                        md-input-container
                            label 種類
                            input(type='text' ng-model='vm.product_type.title' readonly)
                    div.layout-row.flex-100
                        md-input-container.option-customers-container(style="height:200px;overflow:auto")
                            div.option-label
                                label 発注会社名
                            div.option-customers(ng-repeat='customer in vm.customers')
                                md-checkbox(ng-model='customer.Customer.selected' aria-label='label' name='customer_id') {{customer.Customer.title}}

                div(style="width:100%")
                    div.custom-head-parents
                        div.item 数量
                        div.item 工賃（淡色）
                        div.item 工賃（濃色）
                    div
                        div(ng-repeat='(key,item) in vm.product_design_fees')
                            div.custom-tables
                                div.input-container.input-container-2
                                    div.item
                                        md-input-container
                                            input(type='number' ng-model="item.min_quantity")
                                        div.error-text()
                                            span {{ vm.errors[key].min_quantity }}
                                    div.item
                                        span.item-txt ~
                                    div.item
                                        div.input-container
                                            md-input-container
                                                input(type='number' ng-model="item.max_quantity")
                                            div.error-text()
                                                span {{ vm.errors[key].max_quantity }}
                                    div.item
                                        div.input-container
                                            md-input-container
                                                input(type='number' ng-model="item.special_design_fee_light_color")
                                            div.error-text()
                                                span {{ vm.errors[key].special_design_fee_light_color }}
                                            div.error-text.text-center(ng-if="vm.errors[key].design_fee_dark_light_color" style="width:216px")
                                                span {{ vm.errors[key].design_fee_dark_light_color }}
                                    div.item
                                        div.input-container
                                            md-input-container
                                                input(type='number' ng-model="item.special_design_fee_dark_color")
                                            div.error-text()
                                                span {{ vm.errors[key].special_design_fee_dark_color }}
                                    div.item(ng-if="key != 0")
                                        button.btn-delete.simple-btn(type='button' ng-click="vm.deleteDesignFee(item)"): i.icons8-delete


                        div(ng-if="vm.product_design_fees.length === 0")
                            div.footer-custom データがありません。

                md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(ng-click="vm.addDesignFee()"): i.icons8-plus


    footer.text-right(flex='none' ng-hide='vm.showMode')
        div(): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
style.
    .error-text {
        color: red;
        font-size: 12px;
    }
    .custom-tables .input-container-2 {
        display: flex;
        align-items: start;
        box-sizing: border-box;
        margin-left: -8px;
        margin-right: -8px;
    }
    .custom-tables .input-container .item {
        padding-right: 8px;
        padding-left: 8px;
        box-sizing: border-box;
        max-width: 113px;
    }
    .custom-tables .input-container .item .item-txt {
        height: 46px;
        line-height: 1;
        font-size: 24px;
        display: flex;
        align-items: end;
    }
    .custom-tables .simple-btn {
        height: 90px;
    }
    .custom-head-parents {
        display: flex;
        align-items: center;
    }
    .custom-head-parents .item:first-child {
        width: 240px;
        text-align: center;
    }
    .custom-head-parents .item{
        font-weight: bold;
    }

    .custom-head-parents .item:nth-child(2) {
        width: 130px;
        text-align: center;
        margin-right: 14px;
    }
    .footer-custom{
        text-align: center;
        margin-top: 30px;
    }

