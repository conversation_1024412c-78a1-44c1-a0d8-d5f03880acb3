div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=35) タイトル
			div.cell(flex) 数量
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column')

		md-virtual-repeat-container(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form()
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='base in vm.infiniteBases' md-on-demand ng-show='base')
				div.cell(flex=35) {{ base.PrintingBase.title }}
				div.cell(flex) {{ base.PrintingBase.quantity }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small {{ base.PrintingBase.updated_at | userTime }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(base)') 詳細

			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

printing-base-details(
	is-shown='vm.baseDetails.isShown',
	active='vm.baseDetails.active',
	type='vm.baseDetails.type',
	on-close='vm.onBaseClose()',
	on-create='vm.onBaseCreate()',
	on-update='vm.onBaseUpdate(base)',
	on-delete='vm.onBaseDelete(baseId)'
)