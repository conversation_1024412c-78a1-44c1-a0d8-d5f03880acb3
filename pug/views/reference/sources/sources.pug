div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex) タイトル
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='source in vm.sources')
				div.cell(flex) {{ source.ProductLinkedSource.title }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small {{  source.ProductLinkedSource.updated_at | userTime }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(source)') 詳細


			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

source-details(
is-shown='vm.sourceDetails.isShown',
active='vm.sourceDetails.active',
type='vm.sourceDetails.type',
on-close='vm.onSourceClose()',
on-create='vm.onSourceCreate()',
on-update='vm.onSourceUpdate(source)',
on-delete='vm.onSourceDelete(sourceId)'
)