div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell ID
			div.cell(flex=25) タイトル
			div.cell(flex=15) セル横
			div.cell(flex=15) セル縦
			div.cell(flex=15) 行数
			div.cell(flex) 列数
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search style="margin-left:35px"): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			ul(ui-sortable="vm.sortableOptions", ng-model="vm.cps" style="list-style: none;cursor: move;")
				li(ng-repeat="cp in vm.cps")
					div.default-row(layout='row' layout-align='start center')
						div.cell {{ ($index + 1) }}
						div.cell(flex=25) {{ cp.CPFrame.title }}
						div.cell(flex=15) {{ cp.CPFrame.cell_width }}
						div.cell(flex=15) {{ cp.CPFrame.cell_height }}
						div.cell(flex=15) {{ cp.CPFrame.rows }}
						div.cell(flex) {{ cp.CPFrame.columns }}
						div.cell.cell-action(flex='none', layout='row', layout-align='end center')
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(cp)') 詳細

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

cp-frame-details(
is-shown='vm.cpDetails.isShown',
active='vm.cpDetails.active',
type='vm.cpDetails.type',
on-close='vm.onCpFrameClose()',
on-create='vm.onCpFrameCreate()'
on-update='vm.onCpFrameUpdate(cp)',
on-delete='vm.onCpFrameDelete(cpId)'
)
style(type="text/css").
	.management .content .md-virtual-repeat-offsetter{
		padding:0 47px 0 19px;
	}
