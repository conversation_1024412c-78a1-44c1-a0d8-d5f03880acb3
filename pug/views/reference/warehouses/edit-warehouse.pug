md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-warehouse').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 失敗理由
			div.sidenav-actions
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			form.form.default-form.bottom-margin(layout='row' layout-wrap name='editPlaceForm')
				div.input-container(flex=100)
					md-input-container
						label ID
						input(type='text' ng-model='vm.warehouse.id' name='id' ng-readonly='true')
				div.input-container(flex=100)
					md-input-container
						label 種類
						input(type='text' ng-model='vm.warehouse.type' name='type' ng-readonly='true')
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.warehouse.title' name='title' ng-readonly='vm.showMode' required)

		footer.text-right(flex='none' ng-hide='vm.showMode')
			md-button.md-raised.md-accent(ng-click='vm.update()') 保存