div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=10) ID
			div.cell(flex=25) タイトル
			div.cell(flex=25) 種類
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column' style='padding: 0 47px 0 60px;')
		div.search(layout='row')
			div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
				i.icons8-search
				input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
				button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
		md-divider

		ul(ui-sortable="vm.sortableOptions", ng-model="vm.warehouses")
			li(ng-repeat="warehouse in vm.warehouses")
				div.default-row(layout='row' layout-align='start center')
					div.cell(flex=10) {{ warehouse.StorageActivityContent.id }}
					div.cell(flex=25) {{ warehouse.StorageActivityContent.title }}
					div.cell(flex=25) {{ warehouse.StorageActivityContent.type }}
					div.cell.cell-action(flex layout='row', layout-align='end center')
						div.buttons
							md-button.md-raised.md-primary.btn-sm(ng-click='vm.warehouseDetails(warehouse.StorageActivityContent)') 詳細

		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

edit-warehouse(open='vm.warehouseDetails' on-update='vm.onWarehouseUpdate')