.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.main-body.flex-100.random-content(ui-view)
            div.layout-column.block-min-width
                header.with-subheader(flex="none")
                    div.subheader-th(layout='row' style="justify-content: space-between; align-items:center")
                        div.layout-row(style='align-items:center;')
                            div(ng-class="flex-60")
                                div(pick-date-header)
                                    div {{ rc.date | dateJapan : 'day' }}
                                    md-datepicker(ng-model='rc.date' ng-change='rc.onChange()' md-placeholder='Enter date' md-open-on-focus)
                            md-select(ng-model='rc.factory' name='factory' aria-label='Select Factory' ng-change='rc.onChange()' style="margin-left:{{rc.styleHeader}}")
                                md-option(ng-repeat='factory in rc.factories' ng-value='factory.id') {{ factory.title }}
                        div.layout-row(style="margin-left:-{{rc.styleHeader}}") {{ rc.clock | date:'yyyy/M/d HH:mm'}}
                        div.user-info
                            user-manage

                md-content.main-body.flex-100(ui-view)
                    div(layout='row' ng-show="!rc.loading")
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap style="width: 66%")
                            div.layout-row.layout-align-center.record-title 終了時刻、現在進捗率、面数（枚数）
                            div(ng-if="rc.data.data")
                                div.header-top
                                    div.record-title.detail(ng-mouseover="rc.hover('texRealTime')" ng-mouseleave="rc.removeHover('texRealTime')") 実績＋予測
                                        div.record-title.font-size-70 {{rc.data.data.real_time}}
                                        div.tooltip-content(ng-if="rc.texRealTime") {{rc.data.data.real_time_hover}} 実績
                                    div.record-title.detail(style="padding: 10px 50px;")
                                        div.record-title.font-size-70 /
                                    div.record-title.detail(ng-mouseover="rc.hover('textEstimate')" ng-mouseleave="rc.removeHover('textEstimate')") 予定
                                        div.record-title.font-size-70 {{rc.data.data.time_estimate}}
                                        div.tooltip-content(ng-if="rc.textEstimate") {{rc.data.data.estimate_time_hover}} 予定
                                    div.record-title.detail(style="margin-left: 200px") 現在進捗率
                                        div.record-title.font-size-70 {{rc.data.data.progress}} %
                                div.header-top
                                    div.record-title.detail 残り
                                        div.record-title.font-size-70 {{rc.data.data.item_remain_task_count}} ({{rc.data.data.item_remain_quantity}})
                                    div.record-title.detail
                                        div.record-title.font-size-70(style="padding: 6px 29px;") /
                                    div.record-title.detail 予定
                                        div.record-title.font-size-70 {{rc.data.data.item_total_task_count}} ({{rc.data.data.item_total_quantity}})
                                    div.record-title.font-size-70
                                div.layout-row.layout-align-center.record-title
                                    table
                                        tr(ng-repeat='item in rc.data.data_detail')
                                            th.th-detail {{item.printer_type_title}}
                                            th.th-detail {{item.item_remain_task_count}} ({{item.item_remain_quantity}})
                                            th.th-detail /
                                            th.th-detail {{item.item_total_task_count}} ({{item.item_total_quantity}})

                            div.chart-container(style="display: block")
                                canvas#chartItem
                            div(style="margin-top:20px")
                                div.layout-row.record-title 面数詳細
                                table.table-andon
                                    tr
                                        th.text-left 号機
                                        th.text-left 面数（残り/予定）
                                        th.text-left 所要時間（実績＋予測/予定）
                                        th.text-left 終了時刻（実績＋予測/予定）
                                    tr(ng-repeat="value in rc.data.data_andon" ng-class="{'warning': (value.item_remain_task_count > 0 && rc.compareDate(value.real_time_detail)) }")
                                        th.text-left {{value.printer_andon_title}}
                                        th.text-left {{value.item_remain_task_count}} / {{value.item_total_task_count}}
                                        th.text-left {{value.real_time_text}} / {{value.estimate_time_text}}
                                        th.text-left {{value.real_time}} / {{value.estimate_time}}
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div.layout-row.layout-align-center.record-title 配送準備済
                            div.layout-row.layout-align-center.record-total(style="font-size:140px") {{ rc.data.data_delivery.total_delivery_type_remain_count }}/{{ rc.data.data_delivery.total_delivery_type_count }}
                            div(style="display:flex;flex-wrap: wrap")
                                div.item-delivery(ng-repeat='item in rc.data.data_delivery.data')
                                    div {{item.dt.delivery_type_title}} : {{item.sub12.delivery_type_remain_count}} / {{item.sub12.delivery_type_count}}
                            div.chart-container
                                canvas#chartDelivery
                    div.pagination-loading(ng-show="rc.loading")
                        md-progress-circular(md-diameter='40' md-mode='indeterminate')
style.
    .detail {
        display: flex;
        flex-direction: column;
        align-items: center
    }
    .text-left{
        text-align: left;
    }
    .header-top{
        display:flex;
        margin-top:10px;
        align-items: center;
    }
    .font-size-70{
        font-size: 70px !important;
    }
    .th-detail{
        padding: 0 30px;
        text-align: left;
        font-size: 22px;
    }
    .item-delivery{
        padding: 10px 20px;
        font-size: 22px;
    }

    .tooltip-content {
        position: absolute;
        background-color: #333;
        color: #fff;
        padding: 5px 10px;
        border-radius: 5px;
        box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.2);
        font-size: 14px;
        z-index: 1000;
        white-space: nowrap;
        transition: opacity 0.3s ease-in-out;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
    }
    .warning{
        background: #ffffbc;
        border: 1px solid yellow;
    }
    .table-andon {
        width: 90%;
        margin-left:100px;
        border-collapse: collapse;
    }