.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.main-body.flex-100.random-content(ui-view)
            div.layout-column.block-min-width
                header.with-subheader(flex="none")
                    div.subheader-th(layout='row' layout-align="start center")

                        div(ng-class="flex-60")
                            div(pick-date-header)
                                div {{ rc.date | dateJapan : 'day' }}
                                md-datepicker(ng-model='rc.date' ng-change='rc.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                        div.layout-row.flex.layout-align-end {{ rc.clock | date:'yyyy/M/d HH:mm'}}
                        div(flex, layout='row', layout-align='end center').user-info
                            user-manage

                md-content.main-body.flex-100(ui-view)
                    div(layout='row')
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div
                                div.layout-row.layout-align-center.record-title 生産面数
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.printed_side }}
                            div.middle
                                div.layout-row.layout-align-center.record-title 受注面数
                                div.layout-row.layout-align-center.record-total.total-garment {{ rc.data.customers.total_garment_printers }}
                            div.middle
                                div.layout-row.layout-align-center.record-title.inspector 生産面数内訳
                                div.default-row.record-detail.inspector(layout='row' ng-repeat='(name,num) in rc.data.customers.user_print')
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num }}
                            div
                                div.layout-row.layout-align-center.record-title 面数詳細
                                div.layout-row.record-detail(layout='row' ng-repeat='printer in rc.data.customers.printers')
                                    div.flex-80  {{ printer.Printer.title }}
                                    div.flex-20.layout-row.layout-align-end  {{ printer.Printer.total_garment_printer }}
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div
                                div.layout-row.layout-align-center.record-title 検品面数
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.total_production }}
                            div.middle
                                div.layout-row.layout-align-center.record-title.inspector 検品面数内訳
                                div.default-row.record-detail.inspector(layout='row' ng-repeat='(name,num) in rc.data.customers.user_check')
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num }}
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div
                                div.layout-row.layout-align-center.record-title D品率
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.d_percent }}%
                            div.middle
                                div.layout-row.layout-align-center.record-title D品詳細
                                div.default-row.record-detail(layout='row' ng-repeat='(name,num) in rc.data.customers.d_detail')
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num }}