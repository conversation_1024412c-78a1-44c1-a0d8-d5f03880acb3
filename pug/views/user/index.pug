.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center', ng-if="vm.isAuthorized('orders') && vm.showNav('注文')")
						div(flex='grow'): a(ui-sref='user.orders') 注文
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add' ng-click='vm.newOrder.add()', ng-if="vm.isAuthorized('orders')"): i.icons8-plus
					//- li(ui-sref-active='active' layout='row' layout-align='center center', ng-if="vm.isAuthorized('other')")
						div(flex='grow'): a(ui-sref='user.history') 請求
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('製造状況CSVダウンロード')")
						div(flex='grow'): a(href ng-click='vm.downloadCSV()') 製造状況CSVダウンロード
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('請求明細サマリ画面')")
						div(flex='grow'): a(ui-sref='user.billing') 請求明細サマリ画面

			div.sidebar-secondary
				sidebar-secondary-menu(statuses='vm.statuses')

		md-content.main-body.flex-100(ui-view)
		
		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')