div.layout-column.block-min-width
    header.with-subheader(flex="none")
        div.subheader-th(layout='row' layout-align="start center")

            div(style="width:140px;")
                div(pick-date-header)
                    div.billing-date {{ vm.startDate | dateJapan : 'year/month/day' }}
                    md-datepicker(ng-model='vm.startDate' md-placeholder='Enter date' md-open-on-focus)
            span.m-left-50 -
            div(style="width:140px;margin-left:30px;")
                div(pick-date-header)
                    div.billing-date {{ vm.endDate | dateJapan : 'year/month/day' }}
                    md-datepicker(ng-model='vm.endDate'  md-placeholder='Enter date' md-open-on-focus)
            div
                md-button.md-raised.md-primary.btn-sm.chart-btn(ng-click='vm.onDateChange()', style="width:140px;margin-left:30px;") 検索
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')
        div.content.no-virtual-repeat(flex='grow' )
            md-divider
            div.cell.m-top-5-rem(flex layout='row', layout-align='start center')
                md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownloadBilling()') 請求明細CSVダウンロード


            div(style='clear:both;')

            form.form.default-form.billing-form(layout='row' layout-wrap)
                div.input-container.text-center(flex=50)
                    md-input-container.form-billing-container
                        label.form-billing-label 該当月請求額（税抜：円）
                div.input-container.text-center(flex=50)
                    md-input-container.form-billing-container
                        input(type='text' ng-value='vm.billing.result.total_amount' readonly)
                div.layout-row.flex-100.text-center
                    div.input-container(flex=50)
                        md-input-container.form-billing-container
                            label.form-billing-label 注文数
                    div.input-container(flex=50)
                        md-input-container.form-billing-container
                            input(type='text' ng-value='vm.billing.result.total_order' readonly)
                div.layout-row.flex-100.text-center
                    div.input-container(flex=50)
                        md-input-container.form-billing-container
                            label.form-billing-label 商品点数
                    div.input-container(flex=50)
                        md-input-container.form-billing-container
                            input(type='text' ng-value='vm.billing.result.total_product' readonly)

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='62' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした
