div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell.progress-container(flex=35 layout='row' layout-align='start center' ng-style="{'visibility': vm.limit ? 'visible' : 'hidden'}")
				div(flex='none') 今月利用可能
				div.progress-bar(flex)
					div.number {{ vm.limit.usage | number }}円
					div.progress
						div.progress-current(style='width: {{ vm.getLimitPercent() }}%;')
					div.date-common.date-order 注文日
				div(flex='none') {{ vm.limit.limit | number }}円
				
			div.cell.text-center(flex=10) ステータス
			div.cell.text-center.date-end(flex=10) 完成希望日
			div.cell.text-center.date-end(flex=10) ボディ発注日
			div.cell.text-center.date-end(flex=10) 発送日
			div.cell.text-center.date-end(flex=8 style="padding-left:30px") 発注者
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')
		md-virtual-repeat-container.content(flex='grow' )

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div(flex, layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.downloadDataStockCSV()') 在庫情報のダウンロード
			md-divider
	
			div.default-row(layout='row' layout-align='center center' md-virtual-repeat='order in vm.infiniteOrders' md-on-demand ng-show='order')
				div.cell(flex=15) {{ order.Order.number }}
				div.cell.date-no-wrap(flex=10) {{ order.Order.date }}
				div.cell.text-right(flex=10) {{ order.Order.amount | number }}円
				div.cell.text-center(flex=10 layout='column' layout-align='center center')
					div {{ order.Order.status }}
				div.cell.text-center.date-no-wrap(flex=10) {{ order.Order.production_date_preferred }}
				div.cell.text-center.date-no-wrap(flex=10) {{ order.Order.body_ordered_at }}
				div.cell.text-center.date-no-wrap(flex=10) {{ order.Order.delivery_date }}
				div.cell.text-center.date-no-wrap(flex=8) {{ order.Order.user_order }}
				div.cell.cell-action(flex, layout='row', layout-align='end center')
					div.text-small {{ order.Order.updated_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewOrderDetail(order)') 詳細
	
			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='62' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る
	
		.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした
		
user-order-detail.order-detail-wrapper.block-wide(open-on='vm.viewOrderDetail', on-delete='vm.onOrderDelete(orderId)', on-item-update='vm.onOrderItemUpdate()')
new-order(open='vm.newOrder.add')