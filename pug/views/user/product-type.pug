md-sidenav.management-sidenav.md-sidenav-right.sidenav-details(md-component-id='product-type-select').sidenav-fixed: .block-wide(layout='column')
	header.section-header(flex='none')
		button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
		h2 商品
	div.body.block-wide.overflow-hidden(flex='100' layout='row')
		md-content.sidebar.flex-none
			div.search-form(focusable-search ng-hide='vm.searchFailedTypes'): form
				i.icons8-search
				input.search-input(type='text' ng-model='vm.search.searchQuery' ng-change="vm.search.search()")
			section.type-block(ng-hide='vm.searchFailedTypes')
				header.layout-row.layout-align-space-between-center(ng-class="{active: vm.isActive()}")
					a.label(href ng-click='vm.selectType()') すべて
			section.type-block(ng-repeat='type in vm.types')
				header.layout-row.layout-align-space-between-center(ng-class="{active: vm.isActive(type)}")
					div.label {{ type.ProductType.title }}
					a.select-all(href ng-click='vm.selectType(type.ProductType.id)') すべて
				div.types
					a(href ng-repeat='category in type.category' ng-click='vm.selectType(type.ProductType.id, category.ProductCategory.id)' ng-class="{active: vm.isActive(type, category)}") {{ category.ProductCategory.title }}
			
			div.search-failed(ng-show='vm.searchFailedTypes') 商品はありません
		
		md-content.content.types-content.flex-grow: div
			div.product-type-block.layout-row(ng-repeat='product in vm.products')
				div.sides.flex-50
					product-sides-images.small(sides='product.sides')
					div.text-center: md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectProduct(product)') 選択
				div.info.flex-50
					.default-form(layout='row' layout-wrap)
						div.input-container(flex=66)
							md-input-container
								label タイトル
								input(type='text' ng-value='product.Product.title' readonly)
						div.input-container.last(flex=33)
							md-input-container
								label 値段
								input(type='text' value='{{ product.price }}円' readonly)
						div.input-container(flex=100)
							md-input-container
								label サイズ
								input(type='text' ng-value='product.sizes' readonly)
						div.input-container(flex=100)
							md-input-container
								label カラー
								textarea(md-no-resize ng-model='product.colors' readonly)
								
			div.search-failed(ng-show='vm.searchFailed') 商品はありません