md-sidenav.management-sidenav.md-sidenav-right.sidenav-details(md-component-id='user-history-info').sidenav-fixed: .block-wide(layout='column')
	header.section-header(flex='none')
		button.esc-button(type='button' ng-click='hi.close()'): md-icon(md-svg-src='img/icons/esc.svg')
		h2 注文情報
	div.body.block-wide.overflow-hidden(flex='grow' layout='row')
		md-content.sidebar
			form
				div.form.default-form.bottom-margin(layout='row' layout-wrap)
					div.input-container(flex=50)
						md-input-container
							label 請求番号
							input(type='text' value='BL98389A' disabled)
					div.input-container(flex=50)
						md-input-container
							label 請求日
							input(type='text' value='2016.09.30' disabled)
					div.input-container(flex=100)
						md-input-container
							label 請求月
							input(type='text' value='2016年9月' disabled)
					div.input-container(flex=100)
						md-input-container
							label 金額
							input(type='text' value='83 500円' disabled)

				div.form.default-form(layout='row' layout-wrap)
					div.input-container(flex=100)
						md-input-container
							label ステータス
							input(type='text' value='予定' disabled)
							
		md-content.block-wide.content.list-content(layout='column')

			.highlighted-column-container(flex layout='row'): .highlighted-column(flex=20 flex-offset=60)

			md-virtual-repeat-container.list-container(flex)

				div.heading(layout='row' layout-align='center center')
					div.cell(flex=20) 内容
					div.cell(flex=20)
					div.cell.text-right(flex=20) 金額
					div.cell.text-center(flex=20) ステータス
					div.cell(flex)

				div.list-body
					
					// todo: why virtual repeat is not working here (?)
					//div.default-row(layout='row' layout-align='center center' md-virtual-repeat='order in hi.orders' ng-show='order')
					div.default-row(layout='row' layout-align='center center' ng-repeat='order in hi.orders' ng-show='order')
						div.cell(flex=20) {{ order.number }}
						div.cell(flex=20) {{ order.order_date }}
						div.cell.text-right(flex=20) {{ order.price }}
						div.cell.text-center(flex=20) {{ order.status }}
						div.cell.cell-action(flex=20, layout='row', layout-align='end center')
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='hi.viewOrderDetail()') 詳細
								
user-order-detail.order-detail-wrapper.block-wide(open-on='hi.viewOrderDetail' view-mode='true')