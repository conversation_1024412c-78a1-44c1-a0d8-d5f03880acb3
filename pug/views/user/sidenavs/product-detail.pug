md-sidenav.management-sidenav.md-sidenav-right(md-component-id='user-order-product-detail').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 注文情報
			div.sidenav-actions(ng-if='!vm.viewMode')
				//- button.btn-edit.simple-btn(type='button' ng-click='vm.toggleEditMode()'): i.icons8-edit
				md-button.md-raised.md-primary.btn-sm(type='button' ng-click='vm.BodyProcurementFail()') ボディ調達失敗
				button.btn-delete.simple-btn(type='button', ng-click='vm.confirmDelete($event)'): i.icons8-delete
		md-content(flex='grow')
			product-sides-images(sides='vm.sides', ng-if='vm.sides.length')
			
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label {{ vm.item.OrderItem.sub_id ? 'お客様注文番号' : '注文アイテムID' }}
						input(type='text' ng-value='vm.item.OrderItem.id' ng-readonly='vm.isShow')
				div.input-container(flex=100)
					md-input-container
						label 商品
						input(type='text' ng-value='vm.item.Product.title' ng-readonly='vm.isShow')
				div.input-container(flex=50)
					md-input-container
						label カラー
						input(type='text' ng-value='vm.item.ProductColor.title' ng-readonly='vm.isShow')
				div.input-container(flex=50)
					md-input-container
						label サイズ
						input(type='text' ng-value='vm.item.ProductSize.title' ng-readonly='vm.isShow')
				div.input-container(flex=100)
					md-input-container
						label 枚数
						input(type='text' ng-value='vm.item.OrderItem.quantity' ng-readonly='vm.isShow')
				div.input-container(flex=100)
					label オプション
					div.py-1(flex=100 ng-if='vm.item.Key.item_memo !== undefined')
						label.text-black 発注者注文番号 : {{vm.item.Key.item_memo}}
					div.py-1(ng-if='vm.item.Key.Other !== undefined' ng-repeat="(key, value) in vm.item.Key.Other")
						label.text-black {{key}} : {{value}}

			div.form.default-form(layout='row' layout-wrap)
				div.input-container(flex=33)
					md-input-container
						label 商品値段
						input(type='text' value='{{ vm.item.OrderItem.amounts.product_fee | number }}円' ng-readonly='vm.isShow')
				div.input-container(flex=33)
					md-input-container
						label 印刷値段
						input(type='text' value='{{ vm.item.OrderItem.amounts.printing_fee | number }}円' ng-readonly='vm.isShow')
				div.input-container.last(flex=33)
					md-input-container
						label 合計
						input(type='text' value='{{ vm.item.OrderItem.amounts.total | number }}円' ng-readonly='vm.isShow')