div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=30)
			div.cell(flex=15) 請求月
			div.cell.text-center(flex=10 flex-offset=15) ステータス
			div(flex, layout='row', layout-align='end center').user-info
				user-manage
	
	md-content(flex='grow' layout='column')
		md-virtual-repeat-container.content(flex='grow' )
	
			div.search(layout='row')
				div.search-form.layout-row.layout-align-start-center(flex=50 focusable-search): form()
					i.icons8-search
					input.search-input(type='text')
				div.cell.heading-btns(flex=50 layout='row', layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
					md-button.md-raised.md-primary.btn-sm ダウンロード
					
			md-divider
	
			div.default-row(layout='row' layout-align='center center' md-virtual-repeat='order in vm.orders' ng-show='order')
				div.cell(flex=15 layout='row' layout-align='start center') 
					div: md-checkbox(ng-model='order.selected' aria-label='{{ order.number }}')
					div {{ order.number }}
				div.cell(flex=15) {{ order.order_date }}
				div.cell(flex=15) {{ order.billing_month }}
				div.cell.text-right(flex=15) {{ order.price }}
				div.cell.text-center(flex=10 layout='column' layout-align='center center')
					div(status-emphasis='order.status') {{ order.status }}
				div.cell.cell-action(flex, layout='row', layout-align='end center')
					div.text-small {{ order.created_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.showHistoryInfo($index)') 詳細
	
			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='62' md-mode='indeterminate')
	
		.highlighted-column-container(layout='row'): .highlighted-column(flex=10 flex-offset=60)
	
		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る
	
		.nothing-found-msg(ng-if='false'): div 見つかりません
	
		.loading-list(flex-offset=15)
			md-progress-circular(md-diameter='130' md-mode='indeterminate')
		
user-history-info(show-func='vm.showHistoryInfo')