section.block-wide(layout='column')
	md-button.md-raised.md-primary.btn-sm.btn-toggle-edit(ng-click='vm.toggleShowMode()') Edit mode
	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
			div.select-buttons
				md-button.md-raised.md-primary.btn-sm.btn-select(ng-click='vm.selectType()')
					span {{ vm.product.Product ? vm.product.Product.title : '商品未選択' }}
				md-menu.md-menu-color-menu(md-position-mode="target-left target" ng-show='vm.active.color')
					md-button.md-raised.md-primary.btn-sm.btn-select(md-menu-align-target ng-click='$mdOpenMenu($event)')
						span {{ vm.active.color.ProductColor.title }}
						.color-preview(ng-repeat='colorHex in vm.makeColorArr(vm.active.color.ProductColor.hex)' ng-style="{'background-color': colorHex}")
					md-menu-content.md-menu-color-menu(width='3' md-menu-align-target)
						md-menu-item(ng-repeat='color in vm.colors')
							md-button(ng-click='vm.active.setColor(color)')
								span {{ color.ProductColor.title }}
								.color-preview(ng-repeat='colorHex in vm.makeColorArr(color.ProductColor.hex)' ng-style="{'background-color': colorHex}")
				md-menu(md-position-mode="target-left target" ng-show='vm.active.size')
					md-button.md-raised.md-primary.btn-sm.btn-select(md-menu-align-target ng-click='$mdOpenMenu($event)')
						span {{ vm.active.size.ProductSize.title }}
					md-menu-content(width='3' md-menu-align-target)
						md-menu-item(ng-repeat='size in vm.product.sizes')
							md-button(ng-click='vm.active.setSize(size)')
								span {{ size.ProductSize.title }}
		div.layout-row.sizes(flex='none' layout='row')
			div.size.flex-25.layout-column.layout-align-center-start
				div.size-label Coordinates (X)
				div.size-value.layout-row.layout-align-center-center
					input(type='text' maxlength='7' ng-model='vm.coordinates.x' ng-keydown="vm.coordinates.update($event, 'x')")
					span mm
			div.size.flex-25.layout-column.layout-align-center-start
				div.size-label Coordinates (Y)
				div.size-value.layout-row.layout-align-center-center
					input(type='text' maxlength='7' ng-model='vm.coordinates.y' ng-keydown="vm.coordinates.update($event, 'y')")
					span mm
			div.size.flex-25.layout-column.layout-align-center-start
				div.size-label Size (X)
				div.size-value.layout-row.layout-align-center-center
					input(type='text' maxlength='6' ng-model='vm.coordinates.size_x' ng-keydown="vm.coordinates.update($event, 'size_x')")
					span mm
			div.size.flex-25.layout-column.layout-align-center-start
				div.size-label Size (Y)
				div.size-value.layout-row.layout-align-center-center
					input(type='text' maxlength='6' ng-model='vm.coordinates.size_y' ng-keydown="vm.coordinates.update($event, 'size_y')")
					span mm
		
		div.layout-row.buttons(flex layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.toggleShowMode()') プレビュー
			md-button.md-raised.md-accent.btn-sm 保存
	
	div.body.block-wide(flex='100' layout='row')
		div.controls(flex='none' layout='column')
			md-button.md-fab.md-accent(aria-label='image file' ngf-select='vm.addImage($files, $file)' ng-model='vm.imageFile')
				i.icons8-image-file
			md-button.md-fab.md-primary.move-layer(aria-label='move' ng-class='vm.move.class' ng-click='vm.move.toggle()')
				i.icons8-resize-four-directions.icon-layer
				md-icon.icon-canvas(md-svg-src='img/icons/hand-cursor.svg')
			md-button.md-fab.md-primary(aria-label='rotate left' ng-click='vm.rotateLeft()')
				i.icons8-rotate-left
			md-button.md-fab.md-primary(aria-label='rotate right' ng-click='vm.rotateRight()')
				i.icons8-rotate-right
			md-button.md-fab.md-primary(aria-label='layers top' ng-click="vm.bringTo('top')")
				md-icon(md-svg-src='img/icons/layers.svg')
			md-button.md-fab.md-primary(aria-label='layers bottom' ng-click="vm.bringTo('back')")
				md-icon(md-svg-src='img/icons/layers2.svg')
			md-button.md-fab.md-primary(aria-label='align bottom' ng-click="vm.align('toBottom')")
				md-icon(md-svg-src='img/icons/align_bottom.svg')
			md-button.md-fab.md-primary(aria-label='align horizontal center' ng-click="vm.align('toHCenter')")
				md-icon(md-svg-src='img/icons/align_hor_center.svg')
			md-button.md-fab.md-primary(aria-label='align left' ng-click="vm.align('toLeft')")
				md-icon(md-svg-src='img/icons/align_left.svg')
			md-button.md-fab.md-primary(aria-label='align right' ng-click="vm.align('toRight')")
				md-icon(md-svg-src='img/icons/align_right.svg')
			md-button.md-fab.md-primary(aria-label='align top' ng-click="vm.align('toTop')")
				md-icon(md-svg-src='img/icons/align_up.svg')
			md-button.md-fab.md-primary(aria-label='align vertical center' ng-click="vm.align('toVCenter')")
				md-icon(md-svg-src='img/icons/align_ver_center.svg')
			md-button.md-fab.md-primary(aria-label='delete layer' ng-click="vm.deleteLayer()")
				i.icons8-delete
		
		div.order-edit-body.flex-grow: div
			.drawtool-container
			.drawtool-preview
		div.sides-container.flex-none
			div.side(ng-repeat='side in vm.active.color.sides' ng-class="{active: side.active}" ng-click='vm.active.setSide(side)')
				div.image(layout layout-align='center center')
					img(ng-src='{{ side.image_url }}')
				div.caption {{ side.title }}
					
product-type-select(open='vm.selectType' on-select='vm.productSelected')