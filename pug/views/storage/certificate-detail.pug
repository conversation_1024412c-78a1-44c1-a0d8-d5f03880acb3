md-sidenav.management-sidenav.md-sidenav-right(md-component-id='certificate-detail').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? 'カテゴリ追加' : 'カテゴリ情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.delete($event)'): i.icons8-delete
        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='certificateFrameForm')
                div.input-container(flex=100)
                    md-input-container
                        label 証紙コード
                        input(type='text' ng-model='vm.stamp.CertificateStamp.title' name='title' ng-readonly='vm.showMode' required)
                div.input-container(flex=100)
                    md-input-container
                        label 数量
                        input(type='number' string-to-number ng-model='vm.stamp.CertificateStamp.balance' name='balance' ng-readonly='vm.showMode' required)
                div.input-container(flex=100)
                    md-input-container
                        div.image(layout layout-align='center center' style='width:30%')
                            img(ng-src='{{ vm.stamp.CertificateStamp.image_url }}' alt='image_certificate' )
                    md-input-container
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.uploadCertificateDetailImage(null)' ng-hide='vm.showMode' required): span Upload Image
                        input#imgInputUpdate.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.uploadCertificateDetailImage(this)')
                        span#imageNameUpdate

                div.input-container(flex=100)
                    md-input-container
                        label コメント
                        textarea(type='text' ng-model='vm.stamp.CertificateStamp.comment' name='comment' ng-readonly='vm.showMode' required)

                div.input-container(flex=100) プリント履歴
                    div.wrap-product-side-history.flex(ng-repeat='(key, value) in vm.stamp.CertificateStamp.history' flex=100 )
                        div.flex-30 {{value.input <0 ? "手動出" : "手動入 +"}} {{value.input}}
                        div.flex-30 {{value.original}} -> {{value.output}}
                        div.flex-40 {{value.time}}

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存