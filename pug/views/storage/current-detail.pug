md-sidenav.management-sidenav.md-sidenav-right(md-component-id="storage-current-detail").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none').with-buttons
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 商品情報
			div.sidenav-actions
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
		md-content(flex='grow' layout='column')
			product-sides-images(flex='none' sides='vm.sides' mode='min' ng-if='vm.sides.length')

			form.form.default-form.bottom-margin.custom-form(flex='none' layout='row' layout-wrap name='updateStorageForm')
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-value='vm.product.Product.title' readonly)
				div(flex=100 layout='row')
					div.input-container(flex=50)
						md-input-container
							label 種類
							input(type='text' ng-value='vm.product.Product.type' readonly)
					div.input-container(flex=50)
						md-input-container
							label カテゴリ
							input(type='text' ng-value='vm.product.Product.category' readonly)
				div(flex=100 layout='row')
					div.input-container(flex=50)
						md-input-container
							label カラー
							input(type='text' ng-value='vm.product.ProductColor.title' readonly)
					div.input-container(flex=50)
						md-input-container
							label サイズ
							input(type='text' ng-value='vm.product.ProductSize.title' readonly)
				div.input-container(flex=100)
					label.custom-label 安全在庫
					md-input-container
						input(type='number' min='1' ng-model='vm.product.Storage.safty_stock' ng-value='vm.product.Storage.safty_stock' name='safty_stock' ng-readonly='vm.showMode')
					span.error-text( ng-if="vm.checkSaftyStock" ) 【安全在庫】1以上を入力して下さい。
				div.input-container(flex=100)
					label.custom-label 発注点
					md-input-container
						input(type='number' min='1' ng-model='vm.product.Storage.vendor_order_point' ng-value='vm.product.Storage.vendor_order_point' name='vendor_order_point' ng-readonly='vm.showMode')
					span.error-text( ng-if="vm.checkVendorOrderPoint" ) 【発注点】1以上を入力して下さい。
				div.input-container(flex=100)
					label.custom-label 発注調整数
					md-input-container
						input(type='number' min='1' ng-model='vm.product.Storage.adjust_vendor_order' ng-value='vm.product.Storage.adjust_vendor_order' name='adjust_vendor_order' ng-readonly='vm.showMode')
					span.error-text( ng-if="vm.checkAdjustVendorOrder" ) 【発注調整数】1以上を入力して下さい。
				div.checkbox-container(flex=100)
					div: md-checkbox(ng-model='vm.product.Storage.is_stock_alert_notifice' ng-disabled=' vm.showMode') 在庫アラート通知する
				div.input-container(flex=100)
					md-input-container
						label 販売停止数量
						input(type='number' ng-model='vm.product.Storage.min_quantity' ng-value='vm.product.Storage.min_quantity' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label ピッキングモード
						input(type='number' ng-model='vm.product.Storage.picking_mode' ng-value='vm.product.Storage.picking_mode' ng-readonly='vm.showMode')
				div.input-container(flex=100 ng-if="vm.product.list_picking && vm.product.list_picking.length")
					md-input-container
						label 自動ピッキング連携（アイテム）
						md-select(ng-model='vm.product.Storage.product_picking' name='product_picking' ng-disabled='vm.showMode' ng-change='vm.changeProductPicking(vm.product.Storage.product_picking)')
							md-option(ng-click='vm.product.Storage.product_picking = null')
							md-option(ng-value='picking.Product.id' ng-repeat='picking in vm.product.list_picking') {{picking.Product.title}}
				div.input-container(flex=100 ng-if="vm.product.list_picking && vm.product.list_picking.length")
					md-input-container
						label 自動ピッキング連携（カラー／サイズ）
						md-select(ng-model='vm.product.Storage.size_color' name='size_color' ng-disabled='vm.showMode')
							md-option(ng-value='size_color[0].color_size_id' ng-repeat='size_color in vm.product.list_size_color') {{size_color[0].color_size_title}}
					span.error-text(ng-if="vm.checkColorSize") 【自動ピッキング連携（カラー・サイズ）】未入力です。
				div.input-container(flex=100)
					label.custom-label ピッキング単位数
					md-input-container
						input(type='number' min='1' ng-model='vm.product.Storage.picking_unit_quantity' ng-value='vm.product.Storage.picking_unit_quantity' name='picking_unit_quantity' ng-readonly='vm.showMode')
					span.error-text( ng-if="vm.checkPickingQuantity" ) 【ピッキング単位数】1以上を入力して下さい。
				div.input-container(flex=100)
					md-input-container
						label 発注先
						md-select(ng-model='vm.product.Storage.supplier_id' name='supplier_id' ng-disabled='vm.showMode')
							md-option(ng-value='supplier.Supplier.id' ng-repeat='supplier in vm.suppliers') {{ supplier.Supplier.title }}

			div.form.default-form.bottom-margin(flex='none' ng-show='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 枚数
						input(type='text' ng-value='vm.product.Storage.quantity' readonly)
			
			md-divider(ng-show='vm.showMode')
			
			form.form.form-buttons.bottom-margin(flex='none' layout='row' layout-wrap name='addRemoveStorageForm' ng-show='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label.md-no-required 枚数
						input(type='number' ng-model='vm.movement' min=1 name='movement' required)
				div.buttons-container(flex=50 layout='row' layout-align='end center')
					md-menu
						md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							span 出庫
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='(id,title) in vm.storageContent.out')
								md-button(ng-click='vm.removeFromStorage(id)'): span {{title}}
					md-menu
						md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							span 入庫
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='(id,title) in vm.storageContent.in')
								md-button(ng-click='vm.addToStorage(id)'): span {{title}}

			div(ng-show='vm.showMode') 変更履歴
			storage-product-history(flex='shrink' product='vm.product' update='vm.updateHistory' ng-show='vm.showMode')

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div: md-button.md-raised.md-accent(ng-click='vm.update()') 保存

		style(type="text/css").
			.custom-form .checkbox-container {
				padding: 14px 0 0 0 !important;
			}