md-sidenav.management-sidenav.md-sidenav-right(md-component-id='csv-upload').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 アップロード確認
		md-content(flex='grow')
			div.printer-product-list

				div.heading.text(layout='row' layout-align='start center')
					div.cell(flex=50) 内容
					div.cell.text-right(flex=50) 枚数

				div.default-row(layout='row' layout-align='center center' ng-repeat='product in vm.products')
					div a
		footer.text-right.flex-none
			md-button.md-raised.md-accent(ng-click='vm.close()') 保存