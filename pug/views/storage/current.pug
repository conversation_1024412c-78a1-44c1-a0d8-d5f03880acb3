div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell.text-center(flex=15 ng-style={"padding-top": "25px"})
				md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 260px;margin-top:24px")
					md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
			div(style="width:140px;")
				div(pick-date-header)
					div {{ vm.startDate | dateJapan : 'day' }}
					md-datepicker(ng-model='vm.startDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
			| -
			div(style="width:140px;margin-left:30px;")
				div(pick-date-header)
					div {{ vm.endDate | dateJapan : 'day' }}
					md-datepicker(ng-model='vm.endDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
			div(style="width:140px;margin-left:30px;")
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.shipmentCsvDownload()') 出荷数CSV

			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')
		
		md-virtual-repeat-container.content(flex='grow' )

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.heading-btns(flex layout='row', layout-align='end center')
					div.total-garment
						div.garment-total(ng-repeat='(printer,quantity) in vm.total_garment')
							span.garment-total-info
								b {{ printer }} : {{ quantity }}
					div.button-list
						div(pick-date-header)
							div {{ vm.date | dateJapan : 'day' }}
							md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
						md-menu(md-position-mode="target-right target")
							md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
								span 在庫表CSV
							md-menu-content.menu-small(md-menu-align-target)
								md-menu-item
									md-button(ng-click='vm.CSVdownload()'): span CSVダウンロード
								md-menu-item
									md-button(ng-click='vm.CSVupload(null)'): span CSVアップロード
									input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.storagePicking(vm.pagination.product_type_id)') ピッキング
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.storageCertificate()') 証紙入力
			md-divider

			div.default-row.font-sm(layout='row' layout-align='start center')
				div.cell.cell-data(flex=20 layout='row' layout-align='start center'): b アイテム名
				div.cell(flex=5)
					md-menu(md-position-mode="target bottom")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): b {{ vm.activeSize.title || 'サイズ' }}
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='size in vm.storageFilter.sizes')
								md-button(ng-click='vm.setActiveSize(size)') {{ size.ProductSize.title }}
				div.cell(flex=10)
					md-menu(md-position-mode="target bottom")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): b {{ vm.activeColor.title || 'カラー' }}
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='color in vm.storageFilter.colors')
								md-button(ng-click='vm.setActiveColor(color)') {{ color.ProductColor.title }}
				div.cell(flex=10): b  総在庫数
				div.cell(flex=10): b  発注先
				div.cell(flex=5): b  安全在庫
				div.cell(flex=5): b  発注点
				div.cell(flex=5): b  発注調整数
				div.cell(flex=10): b  ピッキング表示順番
				div.cell(flex=10).storage-status
				div.cell.cell-action(flex layout='row', layout-align='end center')

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
				div.cell.cell-data(flex=20 layout='row' layout-align='start center')
					div.checkbox: md-checkbox(ng-model='product.selected' aria-label='label')
					div.images(layout='row' layout-align='start center')
						div.image(ng-repeat='side in product.sides'): img(ng-src='{{ side.ProductColorSide.image_url }}')
					div.data-rowed.rowed-inline
						div {{ product.Product.title }}
						.divider.addition
						div.addition {{ product.Product.type }}
						.divider.addition
						div.addition {{ product.Product.category }}
				div.cell(flex=5) {{ product.ProductSize.title }}
				div.cell(flex=10) {{ product.ProductColor.title }}
				div.cell(flex=10)  {{ product.Storage.quantity }}
				div.cell(flex=10)  {{ product.Supplier.title }}
				div.cell(flex=5)  {{ product.Storage.safty_stock }}
				div.cell(flex=5)  {{ product.Storage.vendor_order_point }}
				div.cell(flex=5)  {{ product.Storage.adjust_vendor_order }}
				div.cell.text-center(flex=10)  {{ product.Storage.picking_mode }}
				div.cell(flex=10).storage-status
					div.circle-yellow(ng-if="product.Storage.status === vm.STOCK_STATUS_WARNING")
					div.circle-red(ng-if="product.Storage.status === vm.STOCK_STATUS_OUT_STOCK")
					div.circle-orange(ng-if="product.Storage.status === vm.STOCK_STATUS_OUT_BALANCE")
					div.circle-two(ng-if="product.Storage.status === vm.STOCK_STATUS_OUT_BALANCE_AND_WARNING")
						div.circle-orange.circle-small
						div.circle-yellow.circle-small
					div.circle-two(ng-if="product.Storage.status === vm.STOCK_STATUS_OUT_BALANCE_AND_OUT_STOCK")
						div.circle-orange.circle-small
						div.circle-red.circle-small
				div.cell.cell-action(flex layout='row', layout-align='end center')
					div.text-small {{ product.Storage.updated_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.productDetails(product)') 詳細

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')
				
		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.searchFailed && vm.pagination.noPermission == false', style="margin: 122px 0;"): div 何も見つかりませんでした
		.nothing-found-msg(ng-if='vm.pagination.noPermission == true', style="margin: 122px 0;"): div ガーメントTOMSの在庫を見る権限がありません。
storage-current-detail(open='vm.productDetails' update-original='true' storage-content='vm.storageContent' factory="vm.factory")
storage-picking(open='vm.storagePicking' on-picking='vm.onPicking(products)')
storage-certificate(open='vm.storageCertificate' on-certificate='vm.onCertificate()')
storage-csv-upload(open='vm.openConfirm')
storage-picking-vakuum

style(type="text/css").
	.management header .cell {
		font-size: 14px;
	}

	md-select .md-select-value {
		border-bottom-style: none !important;
	}

	.total-garment {
		display: flex;
		flex-wrap: wrap;
	}

	storage-current .garment-total {
		width: max-content;
	}

	.button-list {
		display: flex;
		padding-right: 10px;
	}

	storage-current .garment-total .garment-total-info {
		white-space: nowrap;
		padding: 0px 16px;
		margin-bottom: 5px;
		display: inline-block;
	}