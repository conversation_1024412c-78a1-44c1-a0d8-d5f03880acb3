div.order-history(
		ng-show='vm.activity.length'
		infinite-scroll="vm.pagination.loadMore()"
		infinite-scroll-parent='.order-history-wrapper'
		infinite-scroll-distance='0.2',
		infinite-scroll-disabled='vm.pagination.busy || vm.pagination.end'
	)
	div.layout-row(ng-repeat='history in vm.activity')
		div.flex-30 {{ history.StorageActivity.type }}
		div.flex-30 {{ history.StorageActivity.quantity | printtyIntegers }}
		div.flex-35.layout-align-center {{ history.StorageActivity.change }}
		div.flex {{ history.StorageActivity.updated_at }}