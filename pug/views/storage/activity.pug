div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell.text-center(flex ng-style={"padding-top": "25px"})
				md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 260px;margin-top:24px")
					md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')
		md-virtual-repeat-container.content(flex='grow' )

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.heading-btns(flex layout='row', layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.deleteActivity()') 削除
			md-divider

			div.default-row(md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
				
				div.block-wide(layout='row' layout-align='start center' ng-if="product.mode === 'product'")
					div.cell.cell-data(flex=45 layout='row' layout-align='start center')
						md-checkbox(ng-model='product.selected' aria-label='select {{ product.Product.id }}')
						div.images(layout='row' layout-align='start center')
							div.image(ng-repeat='side in product.sides'): img(ng-src='{{ side.ProductColorSide.image_url }}')
						div.data-rowed.rowed-inline
							div {{ product.Product.title }}
							.divider.addition
							div.addition {{ product.Product.type }}
							.divider.addition
							div.addition {{ product.Product.category }}
					div.cell(flex=10) {{ product.ProductSize.title }}
					div.cell(flex=10) {{ product.ProductColor.title }}
					div.cell(flex=10 layout='row' layout-align='end center') 
						div {{ product.StorageActivity.format_quantity }}枚
						div.trend(ng-class='product.StorageActivity.trend'): md-icon(md-svg-src='img/icons/circled-up.svg')
					div.cell.text-right(flex=10) {{ product.StorageActivity.time }}
					div.cell.cell-action(flex layout='row', layout-align='end center')
						div.buttons
							md-button.md-raised.md-primary.btn-sm(ng-click='vm.productDetails(product)') 詳細
				
				div.block-wide(layout='row' layout-align='start center' ng-if="product.mode === 'date'")
					div.cell {{ product.date }}

			div.pagination-loading(style='display: none;')
				md-progress-circular(md-diameter='62' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-show='vm.pagination.searchFailed'): div 何も見つかりませんでした
			
storage-current-detail(open='vm.productDetails' on-storage-update='vm.onStorageUpdate' storage-content='vm.storageContent' factory="vm.factory")

style(type="text/css").
	.management header .cell {
		font-size: 14px;
	}

	md-select .md-select-value {
		border-bottom-style: none !important;
	}
