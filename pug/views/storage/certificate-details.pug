md-sidenav.management-sidenav.md-sidenav-right(md-component-id='certificate-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide.form-padding(layout='column')
        header(flex='none')
            button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg')
            h2 {{ vm.isAdd ? '証紙追加' : '値段情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.delete($event)'): i.icons8-delete
        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='certificateFrameForm')
                div.input-container(flex=100)
                    md-input-container
                        label 証紙コード
                        input(type='text' ng-model='vm.stamp.CertificateStamp.title' name='title' ng-readonly='vm.showMode' required)
                div.input-container(flex=100)
                    md-input-container
                        label 数量
                        input(type='number' ng-model='vm.stamp.CertificateStamp.quantity' name='quantity' ng-readonly='vm.showMode' required)
                div.input-container(flex=100)
                    md-input-container
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.uploadCertificateImage(null)'): span Upload Image
                        input#imgInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.uploadCertificateImage(this)')
                        span#imageName
                div.input-container(flex=100)
                    md-input-container
                        label コメント
                        textarea(type='text' ng-model='vm.stamp.CertificateStamp.comment' name='comment' ng-readonly='vm.showMode' required)

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存