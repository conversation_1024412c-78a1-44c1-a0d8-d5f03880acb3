md-sidenav.management-sidenav.md-sidenav-right(md-component-id='activity-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide.form-padding(layout='column')
        header(flex='none')
            button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg')
            h2 出荷履歴
        md-content(flex='grow' layout='column')
            div(
                infinite-scroll="vm.loadMoreItems()",
                infinite-scroll-parent='md-content',
                infinite-scroll-distance='0.2',
                infinite-scroll-disabled='vm.pagination.disabled'
            )
                div.block-wide.content-price(layout='row' layout-align="start center")
                    div.cell.text-center(flex=30) 証紙
                    div.cell.text-center(flex=20) 出荷数量
                    div.cell.text-center(flex=20) 残り数量
                    div.cell.text-center(flex=20) 出荷日
                div.default-row.price-defaut-row-after(layout='row' layout-align='start center' ng-repeat='activity in vm.activities' ng-show='activity')
                    div.cell.text-center(flex=30) {{activity.CertificateStamp.title}}
                    div.cell.text-center(flex=20) {{activity.CertificateStampActivity.change_quantity}}
                    div.cell.text-center(flex=20) {{activity.CertificateStampActivity.last_balance}}
                    div.cell.text-center(flex=20) {{activity.CertificateStampActivity.output_date}}
                div.pagination-loading(style='display: none;')
                    md-progress-circular(md-diameter='40' md-mode='indeterminate')

                .nothing-found-msg(ng-if='vm.empty'): div 何も見つかりませんでした
