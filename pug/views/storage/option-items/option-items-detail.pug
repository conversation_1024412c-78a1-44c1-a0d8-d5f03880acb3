md-sidenav.management-sidenav.md-sidenav-right(md-component-id="storage-option-items-detail").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none').with-buttons
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 オプションアイテム情報
		md-content(flex='grow' layout='column')
			div.product-sides-images
				div(layout='row' layout-align='center-center'): div.view-image.flex.justify-center.align-item-center
					img(ng-src='{{ vm.product.OptionItem.image_url }}')

			form.form.default-form.custom-form(flex='none' layout='row' layout-wrap name='updateStorageForm')
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-value='vm.product.OptionItem.title' readonly)
				div(flex=100 layout='row')
					div.input-container(flex=50)
						md-input-container
							label カラー
							input(type='text' ng-value='vm.product.OptionItem.size_title' readonly)
					div.input-container(flex=50)
						md-input-container
							label サイズ
							input(type='text' ng-value='vm.product.OptionItem.color_title' readonly)
				div.input-container(flex=100)
					md-input-container
						label 枚数
						input(type='text' ng-value='vm.product.StorageOptionJoin.balance' readonly)

			md-divider(ng-show='vm.showMode')
			
			form.form.form-buttons.bottom-margin(flex='none' layout='row' layout-wrap name='addRemoveStorageForm' ng-show='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label.md-no-required 枚数
						input(type='number' ng-model='vm.movement' min=1 name='movement' required)
				div.buttons-container(flex=50 layout='row' layout-align='end center')
					md-menu
						md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							span 出庫
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='(id,title) in vm.storageContent.out')
								md-button(ng-click='vm.removeFromStorage(id)'): span {{title}}
					md-menu
						md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							span 入庫
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='(id,title) in vm.storageContent.in')
								md-button(ng-click='vm.addToStorage(id)'): span {{title}}

			div(ng-show='vm.showMode') 変更履歴
			storage-option-items-history(flex='shrink' product='vm.product' update='vm.updateHistory' factory='vm.factory' ng-show='vm.showMode')

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div: md-button.md-raised.md-accent(ng-click='vm.update()') 保存

		style(type="text/css").
			.custom-form .checkbox-container {
				padding: 14px 0 0 0 !important;
			}