div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell.text-center(flex=15 ng-style={"padding-top": "25px"})
                md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 260px;margin-top:24px")
                    md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
            div(style="width:140px;")
                div(pick-date-header)
                    div {{ vm.startDate | dateJapan : 'day' }}
                    md-datepicker(ng-model='vm.startDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
            | -
            div(style="width:140px;margin-left:30px;")
                div(pick-date-header)
                    div {{ vm.endDate | dateJapan : 'day' }}
                    md-datepicker(ng-model='vm.endDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
            div(style="width:140px;margin-left:30px;")
                md-button.md-raised.md-primary.btn-sm(ng-click='vm.shipmentCsvDownload()') 出荷数CSV

            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')

        md-virtual-repeat-container.content(flex='grow' )

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
                    button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
                div.cell.heading-btns(flex layout='row', layout-align='end center')
                    div.button-list
                        div(pick-date-header)
                            div {{ vm.date | dateJapan : 'day' }}
                            md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
                        md-menu(md-position-mode="target-right target")
                            md-button.md-raised.md-primary.btn-sm(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                                span 在庫表CSV
                            md-menu-content.menu-small(md-menu-align-target)
                                md-menu-item
                                    md-button(ng-click='vm.CSVdownload()'): span CSVダウンロード
                                md-menu-item
                                    md-button(ng-click='vm.CSVupload(null)'): span CSVアップロード
                                    input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.storagePicking(vm.pagination.product_type_id)') ピッキング
            md-divider

            div.default-row.font-sm(layout='row' layout-align='start center')
                div.cell.cell-data(flex=20 layout='row' layout-align='start center'): b アイテム名
                div.cell.cell-data(flex=10 layout='row' layout-align='start center'): b サイズ
                div.cell.cell-data(flex=10 layout='row' layout-align='start center'): b カラー
                div.cell.cell-data(flex=10 layout='row' layout-align='start center'): b 総在庫数
                div.cell.cell-action(flex layout='row', layout-align='end center')

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
                div.cell.cell-data(flex=20 layout='row' layout-align='start center')
                    div.checkbox: md-checkbox(ng-model='product.selected' aria-label='label')
                    div.data-rowed.rowed-inline
                        div {{ product.OptionItem.title }}
                div.cell(flex=10) {{ product.OptionItem.size_title }}
                div.cell(flex=10) {{ product.OptionItem.color_title }}
                div.cell(flex=10) {{ product.StorageOption.balance }}
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.text-small {{ product.StorageOption.updated_at }}
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.productDetails(product)') 詳細

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed', style="margin: 122px 0;"): div 何も見つかりませんでした

storage-option-items-detail(open='vm.productDetails' update-original='true' storage-content='vm.storageContent' factory="vm.factory")
storage-option-items-picking(open='vm.storagePicking' on-picking='vm.onPicking(products)' factory="vm.factory")
storage-csv-upload(open='vm.openConfirm')

style(type="text/css").
    .management header .cell {
        font-size: 14px;
    }

    md-select .md-select-value {
        border-bottom-style: none !important;
    }

    .button-list {
        display: flex;
        padding-right: 10px;
    }