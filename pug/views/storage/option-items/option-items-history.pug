div.order-history(
		ng-show='vm.activity.length'
		infinite-scroll="vm.pagination.loadMore()"
		infinite-scroll-parent='.order-history-wrapper'
		infinite-scroll-distance='0.2',
		infinite-scroll-disabled='vm.pagination.busy || vm.pagination.end'
	)
	div.layout-row(ng-repeat='history in vm.activity')
		div.flex-30 {{ history.StorageOptionActivity.type }}
		div.flex-30 {{ history.StorageOptionActivity.quantity | printtyIntegers }}
		div.flex-35.layout-align-center {{ history.StorageOptionActivity.change }}
		div.flex {{ history.StorageOptionActivity.updated_at }}