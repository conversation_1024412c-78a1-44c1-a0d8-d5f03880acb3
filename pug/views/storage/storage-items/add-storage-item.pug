md-sidenav.management-sidenav.md-sidenav-right(md-component-id='add-storage-item').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 在庫アイテム管理追加
        md-content(flex='grow')
            form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='addStorageItemForm')
                div.layout-row.flex-100
                    div.input-container(flex=100)
                        label.custom-label 種類
                        md-input-container.m-top-0
                            md-select(ng-model='vm.storageItem.product_type_id' name='product_type_id' )
                                md-option(ng-repeat='product_type in vm.products_types track by product_type.ProductType.id' ng-click='vm.selectProductType(product_type.ProductType.id)' ng-value='product_type.ProductType.id') {{ product_type.ProductType.title }}
                        span.error-text( ng-if="vm.checkProductType" ) 【種類】入力して下さい。
                div.layout-row.flex-100
                    div.input-container(flex=100)
                        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search)
                            i.icons8-search
                            input.search-input(type='text' ng-model='vm.searchQuery' ng-change="vm.liveSearch()")
                            button.reset(type='button', ng-click="vm.clearSearch()"): i.icons8-delete-2
                div.layout-row.flex-100
                    div.input-container(flex=100)
                        label.custom-label アイテム
                        md-input-container.m-top-0
                            md-select(ng-model='vm.storageItem.product_id' name='product_id')
                                md-option(ng-repeat='product in vm.products track by product.Product.id' ng-click='vm.selectProduct(product.Product.id)' ng-value='product.Product.id') {{ product.Product.title }}
                        span.error-text( ng-if="vm.checkProduct" ) 【アイテム】入力して下さい。
                div.layout-row.flex-100(ng-style='{"flex-direction" : "column"}')
                    div.input-container(flex=100)
                        label.custom-label カラー／サイズ
                        span.error-text( ng-if="vm.checkColorSize" ) 【カラー／サイズ】1件以上選択して下さい。
                        md-input-container.option-customers-container(ng-if="vm.product_id != null")
                            div.option-scroll(ng-style='{"height":"630px"}')
                                div
                                    md-checkbox(name='' ng-click='vm.selectAllColorSize()') 全選択
                                div.option-customers(ng-repeat='color_size in vm.colors_sizes')
                                    md-checkbox(ng-model='color_size.is_check' aria-label='label' name='color_size_id') {{color_size.color_size_title}}
        footer.text-right(flex='none')
            md-button.md-raised.md-accent(ng-click='vm.save()') 保存

style(type="text/css").
    .management .search-form .with-reset i {
        display: block;
        line-height: 1;
        font-size: 15px;
        color: #888;
        margin: 0;
        padding: 0;
    }
