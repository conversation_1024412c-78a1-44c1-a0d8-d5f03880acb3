md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-storage-item').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 在庫アイテム管理情報
            div.sidenav-actions
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

        md-content(flex='grow')
            form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='editStorageItemForm')
                div.layout-row.flex-100
                    div.input-container(flex=100)
                        label.custom-label 種類
                        md-input-container.m-top-0
                            input(type='text' ng-model='vm.storageItem.ProductTypeJoin.title' name='product_type_title' ng-readonly='true')
                div.layout-row.flex-100
                    div.input-container(flex=100)
                        label.custom-label(ng-model="vm.storageItem.ProductJoin.id") アイテム
                        md-input-container.m-top-0
                            input(type='text' ng-model='vm.storageItem.ProductJoin.title' name='product_title' ng-readonly='true')
                div.layout-row.flex-100(ng-style='{"flex-direction" : "column"}')
                    div.input-container(flex=100)
                        label.custom-label カラー／サイズ
                        span.error-text( ng-if="vm.checkColorSize" ) 【カラー／サイズ】1件以上選択して下さい。
                        md-input-container.option-customers-container
                            div.option-scroll(ng-style='{"height":"650px"}')
                                div
                                    md-checkbox(name='' ng-disabled='vm.showMode' ng-click='vm.selectAllColorSize()') 全選択
                                div.option-customers(ng-repeat='color_size in vm.colors_sizes')
                                    md-checkbox(ng-model='color_size.is_check' aria-label='label' name='color_size_id' ng-disabled='vm.showMode') {{color_size.color_size_title}}

        footer.text-right(flex='none' ng-hide='vm.showMode')
            md-button.md-raised.md-accent(ng-click='vm.update()') 保存
