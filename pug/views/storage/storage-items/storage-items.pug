div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell.text-center(flex ng-style={"padding-top": "25px"})
                md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 260px;margin-top:24px")
                    md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content(flex='grow' layout='column')
        md-virtual-repeat-container.content(flex='grow' )

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.pagination.liveSearch()")
                    button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center')
                div.cell.flex.col-100
                    b ID
                div.cell.flex
                    b タイトル
                div.cell.cell-action(flex='none', layout='row', layout-align='end center')

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='storageItem in vm.infiniteStorageItems' md-on-demand ng-show='storageItem')
                div.cell.flex.col-100 {{ storageItem.StorageItemManagement.product_id }}
                div.cell.flex {{ storageItem.ProductJoin.title }}
                div.cell.cell-action(flex='none', layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.storageItemDetails(storageItem)') 詳細


            div.pagination-loading(ng-show='vm.pagination.loading')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-show='vm.pagination.searchFailed'): div 何も見つかりませんでした

edit-storage-item(open='vm.storageItemDetails' on-update='vm.onStorageItemUpdate')

style(type="text/css").
    .management header .cell {
        font-size: 14px;
    }

    md-select .md-select-value {
        border-bottom-style: none !important;
    }
