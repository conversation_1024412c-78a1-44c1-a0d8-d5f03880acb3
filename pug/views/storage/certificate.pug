md-sidenav.management-sidenav.md-sidenav-right(md-component-id="storage-certificate").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none').with-buttons
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 証紙
            div.sidenav-actions
                md-button.md-raised.md-primary.btn-xs(ng-click='vm.viewCertificateCreate()') 入荷設定
                md-button.md-raised.md-primary.btn-xs(ng-click='vm.viewActivityDetails()') 出荷履歴
        md-content(flex='grow' layout='column')
            div(
                infinite-scroll="vm.loadMoreItems()",
                infinite-scroll-parent='md-content',
                infinite-scroll-distance='0.2',
                infinite-scroll-disabled='vm.pagination.disabled'
            )
            div.block-wide.content-price(layout='row' layout-align="start center")
                div.cell.text-center(flex=20) 証紙コード
                div.cell.text-center(flex=20) 数量
                div.cell.text-center(flex=20) 残り数量
                div.cell.text-center(flex=20) 画像
            div.default-row.price-defaut-row-after(layout='row' layout-align='start center' ng-repeat='stamp in vm.stamps' ng-show='stamp')
                div.cell.text-center(flex=20) {{stamp.CertificateStamp.title}}
                div.cell.text-center(flex=20) {{stamp.CertificateStamp.quantity}}
                div.cell.text-center(flex=20) {{stamp.CertificateStamp.balance}}
                div.cell.text-center(flex=20)
                    div.image(layout layout-align='center center')
                        img(ng-src='{{ stamp.CertificateStamp.image_url }}')
                div.cell.cell-action(flex layout='row', layout-align='end center')
                    div.buttons
                        md-button.md-primary.md-raised.btn-sm(ng-click='vm.inputCertificate(stamp)') 入荷
                    div.buttons
                        md-button.md-primary.md-raised.btn-sm(ng-click='vm.viewDetail(stamp)') 詳細
            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

            .nothing-found-msg(ng-if='vm.empty'): div 何も見つかりませんでした

certificate-details(
    is-shown='vm.certificateDetails.isShown',
    active='vm.certificateDetails.active',
    type='vm.certificateDetails.type',
    on-close='vm.onCertificateClose()',
    on-create='vm.onCertificateCreate()'
    on-update='vm.onCertificateUpdate(stamp)',
    on-delete='vm.onCertificateDelete(stampId)'
)

activity-details(
    is-shown='vm.activityDetails.isShown',
    active='vm.activityDetails.active',
    type='vm.activityDetails.type',
    on-close='vm.onActivityClose()',
    on-create='vm.onActivityCreate()'
)

certificate-detail(
    is-shown='vm.certificateDetail.isShown',
    active='vm.certificateDetail.active',
    type='vm.certificateDetail.type',
    on-close='vm.onCertificateDetailClose()',
    on-create='vm.onCertificateDetailCreate()'
    on-update='vm.onCertificateDetailUpdate(stamp)',
    on-delete='vm.onCertificateDetailDelete(stampId)'
)