md-sidenav.management-sidenav.md-sidenav-left(md-component-id="storage-picking-vakuum" position='start').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	paging.vakuum-paging(
	page='vm.page'
	page-size='1'
	total="vm.pages"
	show-prev-next='true'
	show-first-last='true'
	ng-if='vm.taskGroups'
	text-title-page="{page}ページ"
	paging-action="vm.DoCtrlPagingAct(page)"
	text-title-first="最初"
	text-title-last="最後"
	text-title-next="進む"
	text-title-prev="戻る"
	)
	.page(ng-if='vm.taskGroups')
		#qrcode
		.page-part-two
			.qr-code
				.qr-code-content
					img(ng-src='https://api.qrserver.com/v1/create-qr-code/?size=120x120&data={{vm.taskGroup.code}}')
				p.title {{vm.taskGroup.title }}
		.page-part-one(data-rows="2", data-columns="4")
			.head {{vm.taskGroup.number }}
			.table
				.cell(ng-repeat='task in vm.taskGroup.previews')
					p.title
						| {{ task.short_title }}
					img.img(ng-click='vm.showLargeImage($event, task)' ng-src='{{task.image_url}}' ng-style="{'cursor': 'pointer'}")
					.number
						| {{ task.short_description }}
	.nothing-found-msg(ng-if='!vm.taskGroup'): div 何も見つかりませんでした