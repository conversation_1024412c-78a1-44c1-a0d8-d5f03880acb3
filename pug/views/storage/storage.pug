.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('在庫')")
						div(flex='grow'): a(ui-sref='storage.current') 在庫
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('アクティビティ')")
						div(flex='grow'): a(ui-sref='storage.activity') アクティビティ
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('発注先情報')")
						div(flex='grow'): a(ui-sref='storage.supplier') 発注先情報
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add supplier' ng-click='vm.addSupplier()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('在庫アイテム管理')")
						div(flex='grow'): a(ui-sref='storage.item') 在庫アイテム管理
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add reason' ng-click='vm.addStorageItem()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('在庫（オプションアイテム）')")
						div(flex='grow'): a(ui-sref='storage.optionitems') 在庫（オプションアイテム）
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('アクティビティ（オプションアイテム）')")
						div(flex='grow'): a(ui-sref='storage.optionitems_activity') アクティビティ（オプションアイテム）
			div.sidebar-secondary
				sidebar-secondary-menu(statuses='vm.statuses')

			div.sidebar-secondary.image-sidebar(ng-if='vm.types.length')
				sidebar-secondary-menu-type(types='vm.types')

		md-content.main-body.flex-100.storage-content(ui-view)
		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')

	storage-csv-upload(open='vm.openConfirm' ng-if="vm.state.is('storage.current')")
	storage-matrix.block-window(open='vm.matrix.show')
	add-storage-item(open='vm.addStorageItem' on-create='vm.onStorageItemCreate' ng-if="vm.state.is('storage.item')")