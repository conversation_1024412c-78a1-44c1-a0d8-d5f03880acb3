section.block-wide.matrix-container(layout='column')
	header.section-header(flex='none' layout='row')
		div.layout-row(flex='none' layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
		div.flex.layout-row.matrix-selects
			div.select-type.layout-row.layout-align-center-center
				.image.layout-align-center-center.layout-row: img(src='img/tmp/items/shirt7.png')
				md-input-container
					label Type
					md-select(ng-model='selectedVegetables', md-on-close='clearSearchTerm()', data-md-container-class='selectdemoSelectHeader', multiple)
						md-select-header.demo-select-header
							input.demo-header-searchbox.md-text(ng-model='searchTerm', type='search', placeholder='Search..')
						md-optgroup(label='vegetables')
							md-option(ng-value='vegetable', ng-repeat='vegetable in vegetables | filter:searchTerm') {{ vegetable }}
			div.simple-select.layout-row.layout-align-center-center
				md-input-container
					label 左
					md-select(ng-model='vm.selected.left')
						md-option(value='1')
							| カラー
						md-option(value='2')
							| カラー
			div.simple-select.layout-row.layout-align-center-center
				md-input-container
					label 上
					md-select(ng-model='vm.selected.up')
						md-option(value='1')
							| サイズ
						md-option(value='2')
							| サイズ
	div.body.block-wide(flex='100' layout='row')
		table
			thead
				tr
					th
					th S
					th M
					th L
					th X
					th XL
					th XXL
					th
			tbody
				tr(ng-repeat='item in vm.matrix')
					td.label {{ item.name }}
					td.text-center(ng-repeat='size in item.sizes track by $index') {{ size }}
					td.label.text-right {{ item.name }}