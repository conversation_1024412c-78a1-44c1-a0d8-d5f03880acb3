md-sidenav.management-sidenav.md-sidenav-right(md-component-id="storage-picking").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none').with-buttons
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 ピッキング
			div(pick-date-header)
				div {{ vm.date | dateJapan : 'day' }}
				md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
			div.sidenav-actions
				md-button.md-raised.md-primary.btn-sm(type='button' ng-click='vm.notifyOutOfStock()') 欠品連絡
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.pick()') ピッキング完了
		md-content(flex='grow' layout='column')
			div.layout-row.record-detail.header(layout='row')
				div.flex-80
					md-checkbox(ng-model='vm.allSelected' ng-change='vm.selectAll()' aria-label='label' style="margin-left: 5px;")
					b アイテム、カラー、サイズ
				div.flex-20.layout-row.layout-align-end
					b 数量
				div.flex-10.layout-row.layout-align-end
					b 済
			div
				div.layout-row.record-detail.data(layout='row' ng-repeat='task in vm.tasks track by $index')
					div.checkbox: md-checkbox.picking-checkbox(ng-model='task.selected' aria-label='label' ng-class="{true: 'change', false: ''}[task.change]")
					div.flex-80  {{ task.title }}
					div.flex-20.layout-row.layout-align-end.quantity(ng-class="{true: 'change', false: ''}[task.change]")  {{ task.quantity }}
					div.flex-10.layout-row.layout-align-end  {{ task.data.done }}

			.nothing-found-msg(ng-if='vm.empty'): div 何も見つかりませんでした