md-sidenav.management-sidenav.md-sidenav-right(md-component-id='supplier-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? '発注先追加' : '発注先情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow'): form(name='supplierForm')
			div.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.supplier.Supplier.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					md-input-container
						label 法人名
						input(type='text' ng-model='vm.supplier.Supplier.company' name='company' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 法人名（カナ）
						input(type='text' ng-model='vm.supplier.Supplier.company_kana' name='company_kana' ng-readonly='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label 郵便番号
						input(type='text' ng-model='vm.supplier.Supplier.postcode' name='postcode' ng-readonly='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label 都道府県
						input(type='text' ng-model='vm.supplier.Supplier.state' name='state' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 市町村
						input(type='text' ng-model='vm.supplier.Supplier.city' name='city' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 番地
						input(type='text' ng-model='vm.supplier.Supplier.address1' name='address1' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 建物名
						input(type='text' ng-model='vm.supplier.Supplier.address2' name='address2' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 電話番号
						input(type='text' ng-model='vm.supplier.Supplier.phone_number' name='phone_number' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label URL
						button.btn-edit.simple-btn.side-img-btn(type='button' ng-click='vm.openLink(vm.supplier.Supplier.url)' style='font-size: 100%' ng-show='vm.showMode'): md-icon(md-svg-src='img/icons/copy.svg')
						input(type='text' ng-model='vm.supplier.Supplier.url' name='url' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label メールアドレス
						button.btn-edit.simple-btn.side-img-btn(type='button' ng-click='vm.openMail(vm.supplier.Supplier.email)' style='font-size: 100%' ng-show='vm.showMode'): md-icon(md-svg-src='img/icons/copy.svg')
						input(type='text' ng-model='vm.supplier.Supplier.email' name='email' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 担当者
						input(type='text' ng-model='vm.supplier.Supplier.person_in_charge' name='person_in_charge' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label.custom-label 納期（日数）
						input(type='number' min='1' ng-model='vm.supplier.Supplier.delivery_time' name='delivery_time' ng-readonly='vm.showMode' string-to-number )
						span.error-text( ng-if="vm.checkDeliveryTime" ) 【納期（日数）】入力して下さい。
						span.error-text( ng-if="vm.deliveryTimeLessOne" ) 【納期（日数）】1以上を入力して下さい。

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存