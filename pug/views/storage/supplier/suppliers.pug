div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div(flex, layout='row', layout-align='end center').user-info
				user-manage

	md-content(flex='grow' layout='column')

		md-virtual-repeat-container.content(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2

			md-divider


			div.default-row(layout='row' layout-align='start center')
				div.cell.flex.col-100
					b ID
				div.cell.flex
					b タイトル
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='supplier in vm.suppliers')
				div.cell.flex.col-100 {{ supplier.Supplier.id }}
				div.cell.flex {{ supplier.Supplier.title }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small {{ supplier.Supplier.updated_at }}
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(supplier)') 詳細


			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

supplier-details(
is-shown='vm.supplierDetails.isShown',
active='vm.supplierDetails.active',
type='vm.supplierDetails.type',
on-close='vm.onSupplierClose()',
on-create='vm.onSupplierCreate()',
on-update='vm.onSupplierUpdate(supplier)',
on-delete='vm.onSupplierDelete(supplierId)'
)