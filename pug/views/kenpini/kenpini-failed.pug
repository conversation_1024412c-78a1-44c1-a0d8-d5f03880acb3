md-sidenav.management-sidenav.md-sidenav-right(md-component-id='add-kenpinNG').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2(ng-style={"margin": "20px"}).float-left 検品NG理由入力
			footer.text-right(flex='none')
				div: md-button.md-raised.md-accent(ng-click='vm.save()') 保存

		md-content(flex='grow')
			form.form.default-form.bottom-margin.custom-form(layout='row' layout-wrap name='dataForm')
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label 日付
						input(type='text' ng-model='vm.data.datetime' readonly)
				div.input-container(flex=100)
					md-input-container
						label 検品者
						input(type='text' ng-model='vm.data.username' name='username' readonly)
				div.input-container(flex=100)
					md-input-container
						label メーカー
						input(type='text' ng-model='vm.data.products_linked_source' name='products_linked_source' readonly)
				div.input-container(flex=100 layout='column')
					label.custom-label 生産セル
					md-input-container.m-top-0
						md-select(ng-model='vm.data.product_cell_id' name='product_cell_id' ng-disabled='!vm.isAdd' )
							md-option(ng-repeat='product_cell in vm.product_cells' ng-click='vm.selectProductCell(product_cell.ProductCell.id)' ng-value='product_cell.ProductCell.id') {{ product_cell.ProductCell.title }}
					span.error-text( ng-if="vm.checkProductCell" ) 【生産セル】入力して下さい。
				div.input-container(flex=100)
					md-input-container
						label 品番
						input(type='text' ng-model='vm.data.products_linked_code' name='products_linked_code' readonly)
				div.input-container(flex=100)
					md-input-container
						label サイズ
						input(type='text' ng-model='vm.data.products_size' name='products_size' readonly)
				div.input-container(flex=100)
					md-input-container
						label カラー
						input(type='text' ng-model='vm.data.products_color' name='products_color' readonly)
				div.input-container(flex=100)
					md-input-container
						label 合計アイテム数
						input(type='text' ng-model='vm.data.total_items' name='total_items' readonly)
				div.input-container(flex=100)
					md-input-container
						label.custom-label 枚数
						input(type='number' ng-model='vm.data.defect_quantity' name='defect_quantity' string-to-number )
						span.error-text( ng-if="vm.checkDefectQuantity" ) 【枚数】入力して下さい。
						span.error-text( ng-if="vm.checkMinDefectQuantity" ) 【枚数】 は0より大きくなければなりません。
				div.input-container(flex=100)
					md-input-container
						label 面数
						input(type='text' ng-model='vm.data.task_step' name='task_step' readonly)
				div.input-container(flex=100)
					md-input-container
						label 案件
						input(type='text' ng-model='vm.data.customer' name='customer' readonly)
				div.input-container(flex=100)
					md-input-container
						label 置き場
						input(type='text' ng-model='vm.data.place' name='place' readonly)
				div.input-container(flex=100)
					md-input-container
						label 注文番号
						input(type='text' ng-model='vm.data.order_number' name='order_number' readonly)
				div.input-container(flex=100)
					md-input-container
						label お客様名
						input(type='text' ng-model='vm.data.shipping_person' name='shipping_person' readonly)
				div.input-container(flex=100 layout='column')
					label.custom-label 欠点内容（大)
					md-input-container.m-top-0
						md-select(ng-model='vm.data.defect_content_category_id' name='defect_content_category_id' ng-disabled='!vm.isAdd' )
							md-option(ng-repeat='defectContentCategory in vm.defectContentCategories track by defectContentCategory.category_id' ng-click='vm.selectDefectContent(defectContentCategory.category_id)' ng-value='defectContentCategory.category_id') {{ defectContentCategory.title }}
					span.error-text( ng-if="vm.checkDefectCategory" ) 【欠点内容（大)】入力して下さい。
				div.input-container(flex=100 layout='column')
					label.custom-label 欠点内容（小）
					md-input-container.m-top-0
						md-select(ng-model='vm.data.defect_content_id' name='defect_content_id' ng-disabled='!vm.isAdd')
							md-option(ng-repeat='defectContent in vm.defectContents track by defectContent.DefectContent.id' ng-value='defectContent.DefectContent.id') {{ defectContent.DefectContent.title }}
					span.error-text( ng-if="vm.checkDefectContent" ) 【欠点内容（小)】入力して下さい。
					div(ng-if='vm.data.defect_content_id' ng-init='vm.selectDefectContent(vm.data.defect_content_category_id)')
				div.checkbox-container(flex=100 layout='row')
					div.item-box
						label.custom-label ガーメント発注
						md-radio-group(ng-model='vm.data.is_vendor_order' name='is_vendor_order' required)
							md-radio-button.m-left-20.m-bottom-0(ng-model='vm.kenpinTaskNG.is_vendor_order' ng-value='1' ng-click='vm.selectVendorOrder(1)' ng-style={"display": "inline-block"} ng-disabled='vm.showMode') 必要
							md-radio-button.m-left-20.m-bottom-0(ng-model='vm.kenpinTaskNG.is_vendor_order' ng-value='0' ng-click='vm.selectVendorOrder(0)' ng-style={"display": "inline-block"} ng-disabled='vm.showMode') 不要
					div(style="margin-top: 15px; margin-bottom: -10px;")
						span.error-text(ng-if="vm.checkVendor") 【ガーメント発注】入力して下さい。
				div.checkbox-container(flex=100)
					label 納期遅れ
					md-checkbox(ng-model='vm.data.delivery_late'  aria-label='label' ng-change='vm.selectDelivery(vm.data.delivery_late)' ng-style={"margin-left": "82px"})
				div.input-container(flex=100)
					md-input-container
						label 追記事項
						input(type='text' ng-model='vm.data.remarks' name='remarks' ng-readonly='vm.showMode')
