div.management.kenpini-header
    header.section-header(flex='none' layout='row')
        div.layout-row(flex layout-align='start center')
            button.simple-icon-btn.close-button(type='button' ng-click='vm.closeKenpinI()'): i.icons8-delete-2
            h2 検品モード
        div.layout-row.buttons(layout-align='end center')
            div: md-button.md-raised.md-accent(ng-disabled="isDisabled" ng-click='vm.save()') 検査完了

        div.layout-row.buttons(layout-align='end center')
            div: md-button.md-primary(ng-click='vm.confirmDelete($event)') キャンセル
md-content.block-wide.content.list-content(layout='column')
    div.kenpini-info
        div
            table.task-info
                tr
                    td.center(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
                        h4(ng-style="{'float' : 'left'}") 検品回数 : {{vm.numberCheck}}
                        span.task-info-header.text-red {{ vm.task.detail.storage }}
                        span.task-info-header.text-red(ng-if="vm.task.detail.delivery_type == 'ネコポス'" ng-style="{'float' : 'right', 'padding' : '20px 0px'}")
                            | {{ vm.task.detail.delivery_type }}
                        span.task-info-header(ng-if="vm.task.detail.delivery_type != 'ネコポス'" ng-style="{'float' : 'right', 'padding' : '20px 0px'}")
                            | {{ vm.task.detail.delivery_type }}
                    td(colspan=3 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
                        span.task-info-header 品番 ：{{ vm.task.detail.product_code }}
                        span.task-info-header カラー ：{{ vm.task.detail.product_color_title }}
                        span.task-info-header サイズ ：{{ vm.task.detail.product_size_title }}
                    td(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
                        span.task-info-header 納期日 ：{{ vm.task.detail.delivery_date }}
                    td(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
                        span.task-info-header アイテム名 ：{{ vm.task.detail.product_title }}
                tr
                    td(ng-repeat='(key, item) in vm.task' ng-if='key != "detail" && item.TaskStep && item.TaskStep.index')
                        div(style="width:400px;display:flex;justify-content: space-between;margin-bottom: -10px;align-items: center;")
                            div.oit-div {{item.TaskStep.title}}
                            span(style="font-size: 25px") {{item.TaskStep.index}}
                tr
                    td(ng-repeat='(key, item) in vm.task' ng-if='key != "detail" && key != "embroidery_info" && key != "countdown" ' ng-style="{'text-align' : 'center'}")
                        div(ng-if='item' style="width:400px;padding:0 5px")
                            p(style="margin-top:-18px") {{item.TaskStep.type_product}}
                            img(ng-click='vm.showLargeImage($event, item)', ng-src="{{ item.TaskStep.image_url }}" ng-style="{'cursor': 'pointer', 'border': '2px solid ' + item.TaskStep.border_color}" )
                            br
                            div.wrap-check-box
                                div.flex.wrap-ok-box
                                    md-checkbox(ng-model='item.okSelected', id="OK-{{key}}", aria-label='label', ng-click='vm.clickChange(item,key)', ng-change='vm.updateSubscribe(item)' ,ng-disabled='item.selected').good 検査OK
                                    span(ng-if="vm.task.detail.certificate_stamp" ng-style="{'color' : 'red','line-height' : '18px'}") 証紙OK (証紙: {{ vm.task.detail.certificate_stamp }})
                                div.flex
                                    md-checkbox(ng-model='item.selected' , id="NG-{{key}}", aria-label='label', ng-click='vm.clickChange(item,key)', ng-change='vm.viewReasion(item)' ,ng-disabled='item.okSelected') 検査NG
                                div.flex
                                    md-checkbox(ng-if="vm.task.detail.is_include" ng-model='item.check_include', aria-label='label', ng-change='vm.showIncludeOption($event, item)') 同梱オプション
                            br
                            img(ng-if="vm.task.detail.certificate_image" ng-src="{{ vm.task.detail.certificate_image }}" ng-style="{'width': '60px','margin-left':'10px'}")
            br
            div.layout-row(layout-align='center' ng-if='vm.checked == true || vm.task.detail.is_check_all == true')
                span.kenpini-check-all 全数チェック済
            div.layout-row(layout-align='center')
                div(ng-if="vm.task.embroidery_info"): md-button.md-raised.md-accent(ng-style="{'text-align' : 'left'}" ng-click='vm.embroideryInfo()') {{vm.is_embroidery ? 'ししゅう情報非表示' : 'ししゅう情報表示'}}
                span(ng-repeat='side in vm.task.detail.sides')
                    div: md-button.md-raised.md-accent(ng-style="{'text-align' : 'left'}" ng-click='vm.selectOKAll(side)') {{side}}OK全選択
                div(ng-if='vm.task.detail.is_include'): md-button.md-raised.md-accent(ng-style="{'text-align' : 'left'}" ng-click='vm.selectOKOptionAll()') 同梱全選択
                div: md-button.md-raised.md-accent(ng-style="{'text-align' : 'left'}" ng-click='vm.printQR($event)') QRシール印刷
            div.table
                table.step-info(ng-if='!vm.is_embroidery')
                    tr
                        td
                            div.label メモ
                            div.input(ng-if="vm.task.detail.is_factory_memo_2")
                                div.input(ng-if="vm.task.detail.is_memo_claim" ng-style="{'color' : 'red'}") {{ vm.task.detail.memo_claim }}
                                div.input(ng-if="!vm.task.detail.is_memo_claim") {{ vm.task.detail.memo_claim }}
                            div.input(ng-if="!vm.task.detail.is_factory_memo_2")
                                div.input {{ vm.task.detail.memo }}
                        td
                            div.label 納期日
                            div.input {{ vm.task.detail.delivery_date }}
                    tr(ng-if="vm.print_positions.length > 0")
                        td
                            div.input(ng-repeat='print_position in vm.print_positions')
                                div.input {{ print_position }}
                        td
                    tr
                        td
                            div.label 注文No
                            div.input {{ vm.task.detail.order_number }}
                        td
                            div.label カラー
                            div.input {{ vm.task.detail.product_color_title }}
                    tr
                        td
                            div.label 型式
                            div.input {{ vm.task.detail.category_title }}
                        td
                            div.label サイズ
                            div.input {{ vm.task.detail.product_size_title }}
                    tr
                        td
                            div.label 個人名
                            div.input {{ vm.task.detail.shipping_person }}
                        td
                            div.label 注文数
                            div.input {{ vm.task.detail.quantity }}
                    tr
                        td
                            div.label ステータス
                            div.input {{ vm.task.detail.order_status }}
                        td
                            div.label 発送区分
                            div.input {{ vm.task.detail.delivery_type }}
                    tr
                        td
                            div.label 証紙
                            div.input {{ vm.task.detail.certificate_stamp }}
                        td
                            div.label Eメール
                            div.input {{ vm.task.detail.shipping_email }}
                div#count-down

            div.kenpini-embroidery-info(ng-if='vm.is_embroidery')
                div.block-wide.common-info(layout='row' layout-align="start center")
                    div.cell(flex=40) 注文番号　{{vm.task.embroidery_info.order_number}}
                    div.cell(flex=20) 置場は　{{vm.task.embroidery_info.place}}
                    div.cell(flex=25) 発送日は　{{vm.task.embroidery_info.delivery_date}}
                    div.cell(flex=15) {{vm.task.embroidery_info.quantity}}
                div.detail-info(ng-if='vm.task.embroidery_info.note')
                    div.block-wide.detail-header-info(layout='row' layout-align="start center")
                        div.cell(flex=25) 入稿デザイン
                        div.cell(flex=10)
                        div.cell(flex=15) 回転
                        div.cell(flex=25) 反転
                        div.cell(flex=20) 使用フォント
                        div.cell(flex=20) 糸の色
                    div.detail-value-info
                        div.default-row.m-bottom-10(layout='row' layout-align='start center' ng-repeat='detail in vm.task.embroidery_info.note')
                            div.cell(flex=25) {{ detail.text }}
                            div.cell(flex=10)
                                button.btn-copy-text(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ detail.text }}")
                                    i.icons8-copy
                                button.btn-after-copy
                                    md-tooltip(md-visible='vm.clipSuccessMsg === detail.text') Copied!
                            div.cell(flex=15).rotate-degree {{ detail.rotate }}
                            div.cell(flex=25) {{ detail.flip }}
                            div.cell(flex=20) {{ detail.fontFamily }}
                            div.cell(flex=20)
                                div.square-color(ng-style='{"background" : detail.fill}')

kenpini-failed(
    is-shown='vm.isShowReasion',
    on-close='vm.onDetailsClose()',
)

style(type="text/css").
    .table {
        position: relative;
    }

    .table #count-down {
        font-size: 50px;
        font-weight: 700;
        text-align: right;
        position: absolute;
        right: 125px;
        top: 0;
    }