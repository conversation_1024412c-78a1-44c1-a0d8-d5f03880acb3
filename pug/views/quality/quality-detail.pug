md-sidenav.management-sidenav.md-sidenav-right(md-component-id='quality-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.colorBlock.isShown || vm.sizeBlock.isShown || vm.linkCodeBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2.float-left D 品情報
			md-checkbox.float-right(ng-model='vm.delivery_late' aria-label='label' name='delivery_late') 納期遅れ
			div.sidenav-actions
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			div(ng-repeat='item in vm.categories' flex='none' layout='row')
				div(flex=30 ng-if="item.KenpinStatus.category_title")
					md-checkbox(ng-model='item.selected' aria-label='label' name='status_id') {{item.KenpinStatus.category_title}}
				div(flex=50 ng-if="item.KenpinStatus.category_title")
					span {{item.KenpinStatus.title}}
				div(flex=80 ng-if="!item.KenpinStatus.category_title")
					md-checkbox(ng-model='item.selected' aria-label='label' name='status_id') {{item.KenpinStatus.title}}
				div(flex=20)
					input(type='number' ng-model='item.KenpinStatus.quantity' min=0 name='quantity' required style='width:50px')

		footer.text-right(flex='none')
			div: md-button.md-raised.md-accent(ng-click='vm.save()') 保存
