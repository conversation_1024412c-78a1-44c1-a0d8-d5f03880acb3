div.layout-column.block-min-width
	header.with-subheader(flex="none")
		div.subheader-th(layout='row' layout-align="start center")

			div(style="width:140px;")
				div(pick-date-header)
					div {{ vm.startDate | dateJapan : 'day' }}
					md-datepicker(ng-model='vm.startDate' md-placeholder='Enter date' md-open-on-focus)
			| -
			div(style="width:140px;margin-left:30px;")
				div(pick-date-header)
					div {{ vm.endDate | dateJapan : 'day' }}
					md-datepicker(ng-model='vm.endDate'  md-placeholder='Enter date' md-open-on-focus)
			div(style="padding:0 20px")
				md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory')
					md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
			div(style="margin-left:40px;")
				md-button.md-raised.md-primary.btn-sm.chart-btn(ng-click='vm.showPieChart()') {{ vm.chartPie ? 'D品内訳非表示' : 'D品内訳表示' }}
				md-button.md-raised.md-primary.btn-sm.chart-btn(ng-click='vm.showBarChart()') {{ vm.chartBar ? '機種別非表示' : '機種別表示' }}

			div(flex, layout='row', layout-align='end center').user-info
				user-manage
		div
			md-time-picker.startTime.pick-date-header(ng-model='vm.startTime', message='message', no-meridiem='', read-only="readonly")
			| -
			md-time-picker.endTime.pick-date-header(ng-model='vm.endTime', message='message', no-meridiem='', read-only="readonly")
			md-button.md-raised.md-primary.btn-sm.chart-btn(ng-click='vm.onDateChange()', style="width:140px;margin-left:30px;") 検索
		div(ng-style="{'padding-left': '40px'}")
			div.garment-total(ng-repeat='(printer,quantity) in vm.total' ng-style="{'padding': '16px 5px'}")
				span.garment-total-info
					b {{ printer }} : {{ quantity }}
		div.garment-total
			span.garment-total-info
				b 納期遅れ : {{ vm.total.late }}
	md-content(flex='grow' layout='column')
		div.content.no-virtual-repeat(
		flex='grow',
		infinite-scroll="vm.loadMore()"
		infinite-scroll-distance='0',
		infinite-scroll-disabled='vm.pagination.disabled'
		)

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button', ng-click="vm.pagination.clearSearch()"): i.icons8-delete-2
				div.cell.heading-btns(flex layout='row', layout-align='end center')
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.showTaskLate()') {{vm.pagination.only_late ? 'すべて表示' : '納期遅れを表示'}}
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
					md-menu(md-position-mode="target-right target")
						md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							i.icons8-more
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.qualityPicking()'): span D品ピッキング
							md-menu-item
								md-button(ng-click='vm.CSVdownload()'): span リストCSVダウンロード
							md-menu-item
								md-button(ng-click='vm.CSVdownloadWithSource("TOMS")'): span TOMS発注CSVダウンロード
							md-menu-item
								md-button(ng-click='vm.CSVdownloadWithSource("CAB")'): span CAB発注CSVダウンロード
			md-divider
			div#chart-legends-bar(ng-show='vm.chartBar')
			div#chart-legends-pie(ng-show='vm.chartPie')
			div.chart.ng-hide(ng-show='vm.chartPie || vm.chartBar' layout='row' ng-if="vm.chart")
				quality-pie-chart.chart-component(
					ng-show='vm.chartPie',
					start-date='vm.startDate',
					end-date='vm.endDate',
					type-id='vm.pagination.type_id',
					click-search='vm.clickSearch',
					start-time='vm.startTime',
					end-time='vm.endTime',
					only-late='vm.pagination.only_late',
					factory-id='vm.factory',
				)
				quality-bar-chart.chart-component(
					ng-show='vm.chartBar',
					start-date='vm.startDate',
					end-date='vm.endDate',
					type-id='vm.pagination.type_id',
					click-search='vm.clickSearch',
					start-time='vm.startTime',
					end-time='vm.endTime',
					only-late='vm.pagination.only_late',
					factory-id='vm.factory',
				)
			div(style='clear:both;')
			div.default-row(layout='row' layout-align='start center' ng-repeat='task in vm.tasks')
				.cell.cell-data(flex='100', layout='row', layout-align='start center')
					.checkbox
						md-checkbox(ng-model='task.selected', aria-label='label' ng-if="task")
					.data-rowed.rowed-inline
						div {{ task.order_number }}
						.divider(ng-if="task")
						div {{ task.index }}
						.divider(ng-if="task")
						div {{ task.product_code }}
						.divider(ng-if="task")
						div {{ task.product_name }}
						.divider(ng-if="task")
						div {{ task.place_title }}
						.divider(ng-if="task")
						div {{ task.product_color }}
						.divider(ng-if="task")
						div {{ task.product_size }}
						.divider(ng-if="task")
						div {{ task.customer }}
						.divider(ng-if="task")
						div {{ task.error_title }}
						.divider(ng-if="task")
						div {{ task.error_quantity }}
						.divider(ng-if="task")
						div {{ task.checker }}
						.divider(ng-if="task")
						div {{ task.date }}
						.divider(ng-if="task")
						div {{ task.time }}
						.divider(ng-if="task && task.delivery_late")
						div(ng-if="task.delivery_late" style='color: red;') {{ task.delivery_late }}
				.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(task)') 詳細
			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty' ng-show='!vm.chartPie && !vm.chartBar'): div 何も見つかりませんでした

quality-details(
is-shown='vm.isShown',
active='vm.taskDetails.active',
task='vm.taskDetails',
product-type-code='vm.taskDetails.product_type_code',
task-step-id='vm.taskDetails.task_step_id',
on-close='vm.onTaskClose()',
on-create='vm.onTaskCreate()',
on-update='vm.onTaskUpdate(task)',
on-delete='vm.onTaskDelete(taskId)'
)