.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center')

			div.sidebar-secondary
				sidebar-secondary-menu(statuses='vm.statuses')

		md-content.main-body.flex-100.storage-content(ui-view)
		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')