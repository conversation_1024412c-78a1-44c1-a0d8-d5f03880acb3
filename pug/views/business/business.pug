.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.sidebar(flex='none', layout='column')
            div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')
        div.layout-column.block-min-width
            header.with-subheader(flex="none")
                div.subheader-th(layout='row' layout-align="start center")
                    div(style="width:25%;")
                        div(pick-date-header)
                            div {{ vm.date | dateJapan : 'day' }}
                            md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)

                    div(flex, layout='row', layout-align='end center').user-info
                        user-manage

                div.layout-row(flex='50' layout-align='start center' style="float: left")
                    h2 生産設定
                    button.md-raised.md-accent.btn-print.md-button.md-ink-ripple.auto-plan(type='button' ng-click='vm.autoPlan()') 自動プランニング
                    button.md-raised.md-accent.btn-print.md-button.md-ink-ripple.auto-plan(type='button' ng-click='vm.autoPlanKornit()') Kornitプランニング
                div.layout-row.buttons(flex='50' layout-align='end center' style="float: right")
                    div.search-form(focusable-search style="margin-right: 20px"): form()
                        input.search-input(type='text', ng-model='vm.searchQuery', ng-change="vm.liveSearch()")
                        i.icons8-search
                    div.cell.heading-btns(flex layout='row', layout-align='end center')
                        md-button.md-raised.md-primary.btn-sm(ng-click='vm.importCSV()'): span Import CSV

            section.block-wide(layout='column')

                //.component-loading(ng-class='vm.loadingType')
                //    md-progress-circular(md-diameter='64', md-mode='indeterminate')


                div.body.block-wide.overflow-hidden.create-plan-content(flex='100' layout='row')
                    md-content.flex-50.products-list
                        div.header-row.layout-row.layout-align-center-center
                            .cell.cell-data.flex-35.layout-row.layout-align-start-center
                                .checkbox: md-checkbox(ng-model='vm.allSelected' ng-change='vm.toggleAll()' aria-label='label')
                                div 商品
                            .cell.flex-20.layout-row.layout-align-end-center
                                div.buttons
                                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.setEmailClient()') {{vm.emailClient ? 'すべて表示' : 'お客様指定'}}

                            .cell.flex-20.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') ソート
                                    md-menu-content.menu-small(md-menu-align-target)
                                        md-menu-item(ng-repeat='sort in vm.plannedSorts')
                                            md-button(ng-click='vm.setActiveSort(sort.key)' ng-disabled='sort.key === vm.activeSort') {{ sort.value }}

                            .cell.flex-20.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeType.ProductionType.title }}
                                    md-menu-content.menu-small(md-menu-align-target)
                                        md-menu-item(ng-repeat='type in vm.unplannedTypes')
                                            md-button(ng-click='vm.setActiveType(type)' ng-disabled='type.ProductionType.code === vm.activeType.ProductionType.code') {{ type.ProductionType.title }}

                            .cell.flex-30.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeColor.Color.title }}
                                    md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
                                        md-menu-item(ng-repeat='color in vm.unplannedFilter.Color')
                                            md-checkbox(ng-model='color.selected', ng-change='vm.reloadFilter(color , color.Color.title , "Color")') {{ color.Color.title }}
                            .cell.flex-30.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeCustomer.Customer.title }}
                                    md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
                                        md-menu-item(ng-repeat='customer in vm.unplannedFilter.Customer')
                                            md-checkbox(ng-model='customer.selected', ng-change='vm.reloadFilter(customer , customer.Customer.title , "Customer")') {{ customer.Customer.title }}
                            .cell.flex-30.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeProductCode.ProductCode.title }}
                                    md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
                                        md-menu-item(ng-repeat='pcode in vm.unplannedFilter.ProductCode')
                                            md-checkbox(ng-model='pcode.selected', ng-change='vm.reloadFilter(pcode , pcode.ProductCode.title , "ProductCode")') {{ pcode.ProductCode.title }}
                            .cell.flex-30.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeSize.Size.title }}
                                    md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
                                        md-menu-item(ng-repeat='size in vm.unplannedFilter.Size')
                                            md-checkbox(ng-model='size.selected', ng-change='vm.reloadFilter(size , size.Size.title , "Size")') {{ size.Size.title }}
                            .cell.flex-30.layout-row.layout-align-end-center
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeDate.Date.title }}
                                    md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
                                        md-menu-item(ng-repeat='date in vm.unplannedFilter.Date')
                                            md-checkbox(ng-model='date.selected', ng-change='vm.reloadFilter(date , date.Date.title , "Date")') {{ date.Date.title }}

                        div(
                            infinite-scroll="vm.loadMoreItems()"
                            infinite-scroll-parent='md-content'
                            infinite-scroll-distance='0.2',
                            infinite-scroll-disabled='vm.paginationItems.disabled'
                        )
                            div.default-row(ng-repeat='item in vm.unplanned', layout='row', layout-align='start center', ng-class="{'row-active': item.selected}")
                                .cell.cell-data.flex(layout='row', layout-align='start center')
                                    .checkbox
                                        md-checkbox(ng-model='item.selected', aria-label='label', ng-change='vm.onItemSelect()')
                                    .images(layout='row', layout-align='start center')
                                        .image(ng-repeat='task in item.tasks')
                                            img(ng-src='{{ task.OrderItemTask.image_url }}')
                                    .data-rowed(ng-class='(item.Order.email_client || vm.emailClient) ?"text-blue" :""')
                                        span {{ item.Order.number }}
                                        span.divider
                                        span {{ item.Product.title }}
                                        span.divider
                                        span {{ item.Product.code }}
                                        span.divider
                                        span {{ item.ProductColor.title }}
                                        span.divider
                                        span {{ item.ProductSize.title }}
                                        span.divider
                                        span {{ item.Customer.title }}
                                        span.divider
                                        span {{ item.OrderItem.quantity }}枚
                                        span(ng-if='item.Order.memo').divider
                                        span(ng-style='{"color" : "red"}') {{ item.Order.memo }}
                                .cell.text-right.flex-none {{ item.Order.production_date_preferred }}

                        .nothing-found-msg(layout-fill, ng-show='vm.paginationItems.empty'): div アイテムはありません


                    md-content.flex-50.printers-list
                        div.header.layout-row.layout-align-center-center
                            div.flex-20
                                md-menu(md-position-mode="target-right target")
                                    div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') プリンター
                                    md-menu-content.menu-small(md-menu-align-target)
                                        md-menu-item
                                            md-button(ng-click='vm.setFilterPrinter(0)' ng-disabled='0 === vm.activeFilterPrinter') すべて
                                        md-menu-item
                                            md-button(ng-click='vm.setFilterPrinter(101)' ng-disabled='101 === vm.activeFilterPrinter') ガーメント（Up-T・Budgets）
                                        md-menu-item
                                            md-button(ng-click='vm.setFilterPrinter(102)' ng-disabled='102 === vm.activeFilterPrinter') ガーメント（TMIX）
                                        md-menu-item
                                            md-button(ng-click='vm.setFilterPrinter(103)' ng-disabled='103 === vm.activeFilterPrinter') ガーメント（その他）
                            div.flex-80
                                div(pick-date-header)
                                    div {{ vm.date | dateJapan : 'day' }}
                                    md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()', md-placeholder='Enter date' md-open-on-focus)

                        div.suggested(ng-show='vm.recommendations.length')
                            div.heading.layout-row.layout-align-start-center
                                div 自動プランニング
                                md-button.md-raised.md-primary.btn-sm.btn-accept(aria-label='accept', ng-click='vm.processRecommendations()'): md-icon(md-svg-src='img/icons/checkmark.svg')

                            div.printer-row.layout-row.layout-align-start-center(ng-repeat='printer in vm.recommendations')
                                div.printer-name {{ printer.Printer.title }}
                                div.printer-progress(flex layout='row' layout-align='start center' layout-wrap)
                                    div.progress-text.layout-row.layout-align-start-center
                                        span {{ printer.Printer.workload }}
                                        span.divider
                                        span {{ printer.Printer.capacity }}
                                    div.progress
                                        div.progress-current(style='width: {{ vm.getPercent(printer.Printer) }}%;')

                        div.other(
                            ng-show='vm.printers.length',
                            infinite-scroll="vm.loadMorePrinters()"
                            infinite-scroll-parent='md-content'
                            infinite-scroll-distance='0.2',
                            infinite-scroll-disabled='vm.paginationPrinters.disabled'
                        )
                            div.heading
                                div 手動プランニング

                            div.printer-row.layout-row.layout-align-start-center(ng-repeat='printer in vm.printers')
                                div.printer-name {{ printer.Printer.title }}
                                div.printer-progress(flex layout='row' layout-align='start center' layout-wrap)
                                    div.progress-text.layout-row.layout-align-start-center
                                        span {{ printer.Printer.workload }}
                                        span.divider
                                        span {{ printer.Printer.capacity }}
                                    div.progress(ng-class="{'has-note': printer.note}")
                                        div.progress-current(style='width: {{ vm.getPercent(printer.Printer) }}%;')
                                    div.progress-note(ng-show='printer.note'): span
                                div.printer-action(ng-show='vm.selectedItems.length')
                                    md-button.md-raised.md-primary.btn-sm.btn-accept(aria-label='accept', ng-click='vm.processPrinter(printer)'): md-icon(md-svg-src='img/icons/checkmark.svg')

                        .nothing-found-msg(layout-fill, ng-show='vm.paginationPrinters.empty && !vm.recommendations.length'): div 利用可能なプリンターはありません

                    div.pagination-loading(style='display: none;')
                        md-progress-circular(md-diameter='40' md-mode='indeterminate')

                button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

                .nothing-found-msg(ng-if='vm.pagination.searchFailed', style="margin: 122px 0;"): div 何も見つかりませんでした