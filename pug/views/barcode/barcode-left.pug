div.h-full(ng-class="{'bg-pink' : vm.isForWomen()}")
    header(flex='none' layout='row' layout-align='center center')
        div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

    // show messages
    div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')")
        md-icon.qr-icon(md-svg-src='img/icons/qr-code.svg', aria-label='scan')

    div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('printing')"): div.message-inner
        md-icon.qr-icon(md-svg-src='img/icons/print.svg', aria-label='print')
        p.text-center 印刷中

    div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish')")
        div.layout-column.layout-align-center-center
            img(ng-src='{{ vm.task.tasks.TaskStep.image_url }}')