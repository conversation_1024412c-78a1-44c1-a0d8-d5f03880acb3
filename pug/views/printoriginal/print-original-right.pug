header(flex='none' layout='row' layout-align='start center')
	div.flex-none
		md-button.md-raised.md-primary.btn-back(ng-show="vm.state.is('manual', 'product')" ng-click="vm.state.back()")
			md-icon(md-svg-src='img/icons/left.svg', aria-label='go back')
			span 戻る
	div(flex layout='row', layout-align='end center').user-info
		user-manage

md-content.content(flex layout='column' layout-align='center center')
	
	div.scan-qr.text-center(ng-if="vm.state.is('scan')")
		h2 QRコートをスキャンしてください
		p.error-text.scanner-error(ng-if='vm.errorText') {{ vm.errorText }}
		md-button.md-raised.md-primary.btn-print(ng-click="vm.state.set('manual')") 手動入力
		input#scanner-field(type='text' ng-model='vm.scannerInput' ng-change='vm.findQrScan()' ng-blur='vm.focusScanField()')
	
	div.enter-qr.text-center(ng-if="vm.state.is('manual')")
		form(name='findQr' ng-submit='vm.findQr()')
			h2 QRコードを入力してください
			p.error-text(ng-if='vm.errorText') {{ vm.errorText }}
			.form-group
				input.simple-input(type='text' ng-model='vm.qrCodeInput' placeholder='例えば：Н4676446')
				button.reset(type='reset' ng-show='vm.qrCodeInput' ng-click="vm.clearInput()")
					i.icons8-delete-2
			md-button.md-raised.md-accent.btn-print(type='submit') 次へ
			
	div.enter-qr.text-center(ng-if="vm.state.is('printing')")
		h2 しばらくお待ちください
		md-button.md-raised.md-primary.btn-print(ng-click="vm.state.set('product', true)") キャンセル
		
	div.enter-qr.text-center(ng-if="vm.state.is('finish')")
		h2 印刷はいかがでしたか？
		div.buttons
			md-button.md-raised.md-accent.btn-print(ng-click="vm.printSuccess()") 終了
			md-button.md-raised.md-primary.btn-print(ng-click="vm.printFail()") 失敗
			md-button.md-raised.md-primary.btn-print(ng-click="vm.retryPrint()") 再印刷
			
	div.product-info(ng-if="vm.state.is('fail')")
		div.info-form
			
			//- items in multiple task
			section.item-list(ng-if='vm.task.current_step.items.length')
				div
					div.heading
						div.cell 商品
						div.cell
						div.cell.text-right 値段
					div.default-row(ng-repeat='item in vm.task.current_step.items')
						div.cell
							md-checkbox(ng-model='item.selected' aria-label='select {{ item.TaskStepItem.title }}' md-no-ink)
						div.cell
							div.layout-row.layout-align-start-center
								div.image.flex-none: img(ng-src='{{ item.TaskStepItem.image_url }}')
								div.flex {{ item.TaskStepItem.title }}
						div.cell
							div {{ item.TaskStepItem.shipping }}


			//- print buttons if electron application
			div.buttons.buttons-fail
				md-button.md-raised.md-accent.btn-print(ng-click="vm.failMultiple()") 失敗タスク登録
				md-button.md-raised.md-primary.btn-print(ng-click="vm.cancelFail()") キャンセル
			
	div.product-info(ng-if="vm.state.is('product')" layout='column')
		div.info-form.text-red.m-10
			span ❇ 入稿されたオリジナル画像を使用するモードです。
		div.info-form.default-row(ng-if='!vm.isMultiple && vm.task.current_step.TaskStep.title_error' layout='row' layout-align='center center' style={'font-size': '15px','color':'red'})
			div
				span このアイテムは
				span(ng-repeat='title_error in vm.task.current_step.TaskStep.title_error') {{title_error}}、
				span {{vm.task.current_step.TaskStep.multi_title_error == true ? '...でNGになっています' : 'でNGになっています' }}
		div.info-form

			//- info for single task
			table.step-info(ng-if='!vm.isMultiple && !vm.state.embroidery_info')
				tr
					td
						div.label 注文番号
						div.input {{ vm.task.current_step.detail.order_number }}
					td
						div.label プリンター
						div.input {{ vm.task.current_step.detail.printer_title }}
				tr
					td(colspan=2)
						div.label 商品
						div.input {{ vm.task.current_step.detail.product_title }} &ensp; {{ vm.task.product_code }}
				tr
					td
						div.label カラー
						div.input {{ vm.task.current_step.detail.product_color_title }}
					td
						div.label サイズ
						div.input {{ vm.task.current_step.detail.product_size_title }}
				tr
					td(colspan=2)
						div.label 郵送先
						div.input {{ vm.task.current_step.detail.order_shipping }}

			div.embroidery-info(ng-if='vm.state.embroidery_info')
				div.block-wide.common-info(layout='row' layout-align="start center")
					div.cell(flex=40) 注文番号　{{vm.task.embroidery_info.order_number}}
					div.cell(flex=20) 置場は　{{vm.task.embroidery_info.place}}
					div.cell(flex=25) 発送日は　{{vm.task.embroidery_info.delivery_date}}
					div.cell(flex=15) {{vm.task.embroidery_info.quantity}}
				div.detail-info(ng-if='vm.task.embroidery_info.note')
					div.block-wide.detail-header-info(layout='row' layout-align="start center")
						div.cell(flex=25) 入稿デザイン
						div.cell(flex=10)
						div.cell(flex=10) 回転
						div.cell(flex=25) 反転
						div.cell(flex=20) 使用フォント
						div.cell(flex=20) 糸の色
					div.detail-value-info
						div.default-row.m-bottom-10(layout='row' layout-align='start center' ng-repeat='detail in vm.task.embroidery_info.note')
							div.cell(flex=25) {{ detail.text }}
							div.cell(flex=10)
								button.btn-copy-text(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ detail.text }}")
									i.icons8-copy
								button.btn-after-copy
									md-tooltip(md-visible='vm.clipSuccessMsg === detail.text') Copied!
							div.cell(flex=10).rotate-degree {{ detail.rotate }}
							div.cell(flex=25) {{ detail.flip }}
							div.cell(flex=20) {{ detail.fontFamily }}
							div.cell(flex=20)
								div.square-color(ng-style='{"background" : detail.fill}')
				div.block-wide.size-detail-info(layout='row' layout-align="start center")
					div.cell(flex=30) 刺繍サイズ
					div.cell(flex=30) タテ　{{vm.task.embroidery_info.image_height}}
					div.cell(flex=30) ヨコ　{{vm.task.embroidery_info.image_width}}

			//- info for multiple task
			table.step-info(ng-if='vm.isMultiple')
				tr
					td
						div.label プリンター
						div.input {{ vm.task.current_step.detail.printer_title }}
					td
						div.label サイズ
						div.input {{ vm.task.current_step.detail.product_size_group }}
			
			//- items in multiple task
			section.item-list(ng-if='vm.isMultiple && vm.task.current_step.items.length')
				div
					div.heading
						div.flex
							div.cell.w-1-2 商品
							div.cell.w-1-2.text-right 値段
					div.default-row(ng-repeat='item in vm.task.current_step.items')
						div.flex
							div.cell.w-1-2
								div.layout-row.layout-align-start-center
									div.image.flex-none: img(ng-src='{{ item.TaskStepItem.image_url }}')
									div.flex {{ item.TaskStepItem.title }}
							div.cell.w-1-2
								div.layout-row.layout-align-end-center
									div.flex {{ item.TaskStepItem.shipping }}
									div.flex-none(ng-if='vm.isElectron')
										md-menu(md-position-mode="target-right target")
											md-button.md-raised.md-primary.btn-sm.btn-more(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
												i.icons8-more
											md-menu-content.menu-small(md-menu-align-target)
												md-menu-item
													md-button(ng-click='vm.downloadItemImage(item)'): span 印刷データをダウンロード
												md-menu-item
													md-button(ng-click="vm.openConditionNote(item.TaskStepItem.product_condition_note)" ng-disabled="item.TaskStepItem.product_condition_note == undefined") 条件書
						div.text-error(ng-if='item.TaskStepItem.title_error')
							span このアイテムは
							span(ng-repeat='title_error in item.TaskStepItem.title_error') {{title_error}}、
							span {{item.TaskStepItem.multi_title_error == true ? '...でNGになっています' : 'でNGになっています'}}
			//- print buttons if electron application
			div.buttons-row.buttons-actions.layout-row.layout-align-center-center.layout-wrap(ng-if='!vm.isMultiple && vm.isElectron && !vm.state.embroidery_info')
				md-button.md-raised.md-accent.btn-print(ng-click="vm.print()") 印刷する
				md-button.md-raised.md-primary.btn-print(ng-click="vm.download()") ダウンロード
			div.buttons-row.buttons-actions.layout-row.layout-align-center-center.layout-wrap(ng-if='!vm.isMultiple && vm.isElectron')
				md-button.md-raised.btn-print.btn-embroidery-info(ng-if="vm.task.current_step.detail.is_embroidery" ng-click="vm.embroideryInfo()") {{vm.state.embroidery_info ? 'ししゅう情報非表示' : 'ししゅう情報表示'}}
				md-button.md-raised.md-accent.btn-print(ng-if="!vm.state.embroidery_info" ng-click="vm.openConditionNote(vm.task.current_step.detail.product_condition_note)" ng-disabled="vm.task.current_step.detail.product_condition_note == undefined") 条件書
			
			//- dummy button if was open in web
			div.buttons-row.buttons-actions.layout-row.layout-align-center-center.layout-wrap(ng-if='!vm.isMultiple && !vm.isElectron')
				md-button.md-raised.md-primary.btn-print(disabled) アプリ外で印刷不可能となります
		

.steps-container.flex-none(ng-if="vm.state.is('product')" ng-show='vm.task')

	//- print buttons if electron application
	div.buttons-row.buttons-actions.layout-row.layout-align-center-center.layout-wrap(ng-if='vm.isMultiple && vm.isElectron')
		md-button.md-raised.md-accent.btn-print(ng-click="vm.print()" ng-show='vm.isPrintStep') 印刷する
		md-button.md-raised.md-primary.btn-print(ng-click="vm.download()" ng-show='vm.isPrintStep') ダウンロード
		md-button.md-raised.md-primary.btn-print(ng-click="vm.press()" ng-show='!vm.isPrintStep') プレス
		
	//- dummy button if was open in web
	div.buttons-row.buttons-actions.layout-row.layout-align-center-center.layout-wrap(ng-if='vm.isMultiple && !vm.isElectron')
		md-button.md-raised.md-primary.btn-print(disabled) アプリ外で印刷不可能となります
		
	.steps(ng-if='!vm.isMultiple')
		div.layout-row.layout-align-start-center
		
			//- previous steps
			div.flex-none.step.out(ng-repeat='step in vm.task.previous_steps')
				div.image.layout-row.layout-align-center-center
					img(ng-src='{{ step.TaskStep.image_url }}')
				div.name {{ step.TaskStep.title }}
				div.status {{ step.TaskStep.status }}
			
			//- current steps
			div.flex-none.step.current
				div.image.layout-row.layout-align-center-center
					img(ng-src='{{ vm.task.current_step.TaskStep.image_url }}')
				div.name {{ vm.task.current_step.TaskStep.title }}
				div.status {{ vm.task.current_step.TaskStep.status }}
			
			//- next steps
			div.flex-none.step(ng-repeat='step in vm.task.next_steps')
				div.image.layout-row.layout-align-center-center
					img(ng-src='{{ step.TaskStep.image_url }}')
				div.name {{ step.TaskStep.title }}
				div.status {{ step.TaskStep.status }}
					
	.steps(ng-if='vm.isMultiple')
		div.layout-row.layout-align-start-center

			//- previous steps
			div.flex-none.step.out(ng-repeat='step in vm.task.previous_steps')
				div.name {{ step.TaskGroupStep.title }}
				div.status {{ step.TaskGroupStep.status }}

			//- current steps
			div.flex-none.step.current
				div.name {{ vm.task.current_step.TaskGroupStep.title }}
				div.status {{ vm.task.current_step.TaskGroupStep.status }}

			//- next steps
			div.flex-none.step(ng-repeat='step in vm.task.next_steps')
				div.name {{ step.TaskGroupStep.title }}
				div.status {{ step.TaskGroupStep.status }}
					
print-original-download.ng-hide(ng-if="vm.isElectron && !vm.state.is('manual', 'scan')" task='vm.task' task-info='vm.taskInfo' download='vm.download' print='vm.print' on-download='vm.onDownload' on-print='vm.onPrint()' source='vm.source')