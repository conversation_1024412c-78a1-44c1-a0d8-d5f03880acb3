header(flex='none' layout='row' layout-align='center center')
    div.flex-45
        div.repair-date-picker
            div(pick-date-header)
                div {{ vm.state.date | dateJapan : 'day' }}
                md-datepicker(ng-model='vm.state.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
    div.logo.flex: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

div.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')")
    h2 本日の補修数量
    h2.repair-count {{ vm.state.count }}

div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('printing')"): div.message-inner
    md-icon.qr-icon(md-svg-src='img/icons/print.svg', aria-label='print')
    p.text-center 印刷中

div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && !vm.isMultiple")
    div.layout-column.layout-align-center-center
        img(ng-src='{{ vm.task.current_step.TaskStep.image_url }}')
        p.text-center {{ vm.task.current_step.TaskStep.title }}

div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && vm.isMultiple")
    div.layout-column.layout-align-center-center
        img(ng-src='{{ vm.task.current_step.TaskGroupStep.image_url }}')
        //-p.text-center {{ vm.task.current_step.TaskStep.title }}