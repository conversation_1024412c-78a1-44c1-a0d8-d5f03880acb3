section.login-block
	header
		h1 ログイン
	.body.form-body

		form(ng-submit='vm.login()')
			md-input-container.input-special
				label メールアドレス
				input(type='email' ng-model='vm.loginData.email')
			md-input-container.input-special
				label パスワード
				input(type='password' ng-model='vm.loginData.password')
			div.submit-container 
				md-button.md-raised.md-accent.btn-block.btn-lg.login-btn(type='submit' ng-disabled='!vm.loginData.email || !vm.loginData.password') ログイン
		
		.bottom-buttons.text-center
			a.md-button(ui-sref='auth.signup') 新規登録
			a.md-button(ui-sref='auth.forgot-pass') パスワードを忘れた方こちらへ