section.login-block
	header
		h1 新規登録
	.body.form-body

		form(ng-submit='vm.signup()')
			md-input-container.input-special
				label メールアドレス
				input(type='email' ng-model='vm.loginData.email')
			md-input-container.input-special
				label パスワード
				input(type='password' ng-model='vm.loginData.password' minlength="4")
			div.submit-container
				md-button.md-raised.md-accent.btn-block.btn-lg.login-btn(type='submit' ng-disabled='!vm.loginData.email || !vm.loginData.password') 新規登録

		.bottom-buttons.text-center
			a.md-button(ui-sref='auth.signin') ログイン