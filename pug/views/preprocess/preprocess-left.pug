div.h-full(ng-class="{'bg-pink' : vm.isForWomen()}")
    header(flex='none' layout='row' layout-align='center center')
        div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

    // show messages
    div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')")
        md-icon.qr-icon(md-svg-src='img/icons/qr-code.svg', aria-label='scan')

    div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('printing')"): div.message-inner
        md-icon.qr-icon(md-svg-src='img/icons/print.svg', aria-label='print')
        p.text-center 印刷中

    //div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('finish')")
    //	md-icon.finish-icon(md-svg-src='img/icons/checkmark.svg', aria-label='success')

    div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && !vm.isMultiple")
        div.layout-column.layout-align-center-center
            img(ng-click='vm.showLargeImage($event, vm.task.current_step.TaskStep.large_image_url)' ng-src='{{ vm.task.current_step.TaskStep.image_url }}' ng-style="{'cursor': 'pointer'}")
            p.text-center {{ vm.task.current_step.TaskStep.title }}
            div.remark(ng-if="!vm.task.current_step.detail.is_embroidery") {{ vm.task.current_step.detail.memo }}
            div.zoom-in-image(ng-if="vm.task.current_step.detail.is_embroidery")
                div.remark2 {{ vm.task.current_step.detail.memo }}
                div.embroidery-image
                    img(ng-click='vm.showLargeImage($event, vm.task.current_step.TaskStep.embroidery_url)', ng-src="{{ vm.task.current_step.TaskStep.embroidery_url }}" ng-style="{'cursor': 'pointer'}")

    div.product-image.block-wide.flex(ng-if="vm.state.is('product', 'finish') && vm.isMultiple")
        div.layout-column.layout-align-center-center
            img(ng-src='{{ vm.task.current_step.TaskGroupStep.image_url }}')
            //-p.text-center {{ vm.task.current_step.TaskStep.title }}