div.layout-column.block-min-width
	header.with-subheader(flex="none")
		div.subheader-th(layout='row' layout-align="start center")

			div(flex, layout='row', layout-align='end center').user-info
				user-manage

		div.subheader-nav
			md-nav-bar(nav-bar-aria-label='navigation links' md-no-ink md-selected-nav-item="::vm.initialState")
				md-nav-item(md-nav-sref='import.item.product' name='import.item.product' ng-if="vm.showSubModeTab('PRODUCT')") PRODUCT
				md-nav-item(md-nav-sref='import.item.source' name='import.item.source' ng-if="vm.showSubModeTab('SOURCE')") SOURCE
				md-nav-item(md-nav-sref='import.item.uvframe' name='import.item.uvframe' ng-if="vm.showSubModeTab('UV FRAME')") UV FRAME
				md-nav-item(md-nav-sref='import.item.cpframe' name='import.item.cpframe' ng-if="vm.showSubModeTab('CP FRAME')") CP FRAME
				md-nav-item(md-nav-sref='import.item.printingbase' name='import.item.printingbase' ng-if="vm.showSubModeTab('PRINTING BASE')") PRINTING BASE

	md-content(flex='grow' layout='column' ui-view)