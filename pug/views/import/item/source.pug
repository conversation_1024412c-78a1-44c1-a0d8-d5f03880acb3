md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownloadTemplate()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex
			b タイトル
	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='source in vm.sources')
		div.cell.flex.col-100 {{ source.ProductLinkedSource.id }}
		div.cell.flex {{ source.ProductLinkedSource.title }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  source.ProductLinkedSource.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(source)') 詳細


	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

source-details(
is-shown='vm.sourceDetails.isShown',
active='vm.sourceDetails.active',
type='vm.sourceDetails.type',
on-close='vm.onSourceClose()',
on-create='vm.onSourceCreate()',
on-update='vm.onSourceUpdate(source)',
on-delete='vm.onSourceDelete(sourceId)'
)