md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownloadBatch()') 一括ダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownloadTemplate()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-500
			b PRODUCT NAME
		div.cell.flex.col-200
			b TYPE
		div.cell.flex
			b CATEGORY
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='product in vm.infiniteProducts' md-on-demand ng-show='product')
		div.cell.flex.col-100 {{ product.Product.id }}
		div.cell.flex.col-500 {{ product.Product.title }}
		div.cell.flex.col-200 {{ product.ProductType.title }}
		div.cell.flex {{ product.ProductCategory.title }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  product.Product.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(product)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

product-details(
is-shown='vm.productDetails.isShown',
active='vm.productDetails.active',
type='vm.productDetails.type',
on-close='vm.onProductClose()',
on-create='vm.onProductCreate()',
on-update='vm.onProductUpdate(product)',
on-delete='vm.onProductDelete(productId)'
)