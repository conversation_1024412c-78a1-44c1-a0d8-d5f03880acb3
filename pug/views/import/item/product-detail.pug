md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-product').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.colorBlock.isShown || vm.sizeBlock.isShown || vm.linkCodeBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? '商品追加' : '商品情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			form.form.default-form.bottom-margin(layout='row' layout-wrap name='editProductForm')
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label ID
						input(type='text' ng-model='vm.product.Product.id' readonly)
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.product.Product.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					md-input-container
						label カテゴリ
						md-select(ng-model='vm.product.Product.category_id' name='category_id' ng-disabled='vm.showMode' required)
							md-option(ng-value='category.ProductCategory.id' ng-repeat='category in vm.categories') {{ category.ProductCategory.title }}
				div.input-container(flex=100)
					md-input-container
						label 種類
						md-select(ng-model='vm.product.Product.type_id' name='type_id' ng-disabled='vm.showMode' ng-change='vm.onTypeChange()' required)
							md-option(ng-value='type.ProductType.id' ng-repeat='type in vm.types') {{ type.ProductType.title }}
				div.input-container.bottom-border(flex=100 ng-if="vm.product.Product.type_id == 3 || vm.product.Product.type_id == 8")
					md-input-container
						label UVフレーム
						md-select(ng-model='vm.product.Product.uv_frame' name='uvFrame' ng-disabled='vm.showMode' ng-change="vm.onFrameChange('uv')")
							md-option(ng-value='uv.UVFrame.id' ng-repeat='uv in vm.uvFrames') {{ uv.UVFrame.title }}
				div.input-container.bottom-border(flex=100 ng-if="vm.product.Product.type_id == 5")
					md-input-container
						label ジグ
						md-select(ng-model='vm.product.Product.printing_base_id' name='printingBase' ng-disabled='vm.showMode')
							md-option(ng-value='base.PrintingBase.id' ng-repeat='base in vm.printingBases') {{ base.PrintingBase.title }}
				div.input-container.bottom-border(flex="100" ng-if="vm.product.Product.type_id == 12 || vm.product.Product.type_id == 8")
					md-input-container
						label CPフレーム
						md-select(ng-model='vm.product.Product.cp_frame' name='cp_frame' ng-disabled='vm.showMode' ng-change="vm.onFrameChange('cp')")
							md-option(ng-value='cp.CPFrame.id' ng-repeat='cp in vm.cpFrames') {{ cp.CPFrame.title }}
				div(ng-if="vm.error" style="color:red;margin-top:10px") {{vm.text}}

			// product linked code block
			.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd')

				h3 連携設定

				.sidenav-list
					div.heading.layout-row.layout-align-start-center
						div.cell.flex ソース
						div.cell.flex-none コード
					div.default-row.layout-row.layout-align-start-center(ng-repeat='code in vm.codes')
						div.cell.flex {{ code.ProductLinkedCode.source }}
						div.cell.flex-none.layout-row.layout-align-end-center
							div {{ code.ProductLinkedCode.code }}
							md-menu.small(md-position-mode="target-right target")
								button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
								md-menu-content.menu-small(md-menu-align-target)
									md-menu-item: md-button(ng-click='vm.linkCodeBlock.show(code)') 詳細

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存

	// product linked code form
	product-linked-code-details(
	ng-show='vm.linkCodeBlock.isShown',
	loading-type='vm.loadingType'
	type='vm.linkCodeBlock.type',
	active='vm.linkCodeBlock.activeItem',
	on-create='vm.linkCodeBlock.onCreate()',
	on-update='vm.linkCodeBlock.onUpdate(code)',
	on-delete='vm.linkCodeBlock.onDelete(codeId)',
	on-close='vm.linkCodeBlock.onClose()',
	show-button='vm.showButton',
	)