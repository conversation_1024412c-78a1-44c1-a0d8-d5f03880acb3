md-sidenav.management-sidenav.md-sidenav-right(md-component-id='base-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? 'ジグ追加' : 'ジグ情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			form.form.default-form.bottom-margin(layout='row' layout-wrap name='baseForm')
				div.input-container(flex=100 ng-if='!vm.isAdd')
					md-input-container
						label ID
						input(type='text' ng-model='vm.base.id' readonly)
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.base.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					md-input-container
						label 数量
						input(type='number' ng-model='vm.base.quantity' name='quantity' ng-readonly='vm.showMode' required string-to-number)

		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存