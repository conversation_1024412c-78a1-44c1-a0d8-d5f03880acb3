md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownloadTemplate()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-600
			b タイトル
		div.cell.flex
			b 数量
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' ng-repeat='base in vm.bases')
		div.cell.flex.col-100 {{ base.PrintingBase.id }}
		div.cell.flex.col-600 {{ base.PrintingBase.title }}
		div.cell.flex {{ base.PrintingBase.quantity }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(base)') 詳細


	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

import-item-base-details(
is-shown='vm.printingbaseDetails.isShown',
active='vm.printingbaseDetails.active',
type='vm.printingbaseDetails.type',
on-close='vm.onBaseClose()',
on-create='vm.onBaseCreate()',
on-update='vm.onBaseUpdate(base)',
on-delete='vm.onBaseDelete(baseId)'
)