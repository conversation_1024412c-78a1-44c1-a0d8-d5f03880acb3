md-sidenav.management-sidenav.md-sidenav-right(md-component-id='vakuum-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'Vakuumオプション追加' : 'Vakuumオプション追加情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.vakuum-block.bottom-border

			div.add-vakuum
				form.form.default-form(layout='row' layout-wrap name='vakuumForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.vakuum.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label product_id
							input(type='number' ng-model='vm.vakuum.product_id' name='vakuum-product-id' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label customer_id
							input(type='number' ng-model='vm.vakuum.customer_id' name='vakuum-customer-id' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_original_width
							input(type='number' ng-model='vm.vakuum.image_original_width' name='vakuum-image-original-width' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_original_height
							input(type='number' ng-model='vm.vakuum.image_original_height' name='vakuum-image-original-height' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_trimmed_width
							input(type='number' ng-model='vm.vakuum.image_trimmed_width' name='vakuum-image-trimmed-width' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_trimmed_height
							input(type='number' ng-model='vm.vakuum.image_trimmed_height' name='vakuum-image-trimmed-height' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_round_coder
							input(type='number' ng-model='vm.vakuum.image_round_corder' name='image-round-corder' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label flip_horizontal
							input(type='number' ng-model='vm.vakuum.flip_horizontal' name='vakuum-flip-horizontal' ng-readonly='vm.showMode' required string-to-number)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label flip_vertical
							input(type='number' ng-model='vm.vakuum.flip_vertical' name='vakuum-flip-vertical' ng-readonly='vm.showMode' required string-to-number)

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存