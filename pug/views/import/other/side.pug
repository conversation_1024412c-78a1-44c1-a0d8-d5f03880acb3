md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-100
			b TITLE
		div.cell.flex.col-100
			b IMAGE
		div.cell.flex.col-500
			b PRODUCT NAME
		div.cell.flex
			b COLOR NAME
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='side in vm.infiniteSides' md-on-demand ng-show='side')
		div.cell.flex.col-100 {{ side.ProductColorSide.id }}
		div.cell.flex.col-100 {{ side.ProductColorSide.title }}
		div.cell.flex.col-100
			img.col-40.row-40(ng-src='{{ side.ProductColorSide.medium_image_url }}')
		div.cell.flex.col-500 {{ side.ProductColor.Product.title }}
		div.cell.flex {{ side.ProductColor.title }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  side.ProductColorSide.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(side)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

side-details(
is-shown='vm.sideDetails.isShown',
active='vm.sideDetails.active',
type='vm.sideDetails.type',
on-close='vm.onSideClose()',
on-create='vm.onSideCreate()',
on-update='vm.onSideUpdate(side)',
on-delete='vm.onSideDelete(sideId)'
)