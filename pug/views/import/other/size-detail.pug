md-sidenav.management-sidenav.md-sidenav-right(md-component-id='size-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'サイズ追加' : 'サイズ情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.size-block.bottom-border

			div.add-size
				form.form.default-form(layout='row' layout-wrap name='sizeForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.size.id' readonly)
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ソース
							input(type='text' ng-model='vm.size.source' readonly)
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label コート
							input(type='text' ng-model='vm.size.product_code' readonly)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text' ng-model='vm.size.title' name='size-title' ng-readonly='vm.showMode' required)
					div.input-container(flex=100)
						md-input-container(input-size='vm.size.code')
							label CODE
							input(type='text' ng-model='vm.size.code' name='size-code' ng-readonly='vm.showMode' required)
					div.input-container(flex=100)
						md-input-container
							label メイン
							input(type='number' ng-model='vm.size.is_main' name='size-main' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label 襟開け（表）
							div(ng-style='{"position" : "relative"}')
								input(type='number', ng-model='vm.size.coller_open_front', name='coller_open_front', ng-readonly='vm.showMode')
								span(ng-style='{"position" : "absolute","top": "8px","right":"400px"}') cm
						span(ng-if="vm.check_front" ng-style='{"color" : "red"}') 襟開け（表）を0～100を入力してください。

					div.input-container(flex=100)
						md-input-container
							div(ng-style='{"position" : "relative"}')
								label 襟開け（裏）
								input(type='number', ng-model='vm.size.coller_open_back', name='coller_open_back', ng-readonly='vm.showMode')
								span(ng-style='{"position" : "absolute","top": "8px","right":"400px"}') cm
						span(ng-if="vm.check_back" ng-style='{"color" : "red"}') 襟開け（裏）を0～100を入力してください。

					div.input-container(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.size.is_dtf_image_change' aria-label='label' ng-disabled='vm.showMode')
							label(ng-style='{"font-size" : "12px"}') DTF画像サイズを変更する

					div.input-container.bottom-border(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.size.is_resize_84pct' aria-label='label' ng-disabled='vm.showMode')
							label(ng-style='{"font-size" : "12px"}') 印刷画像を84%にリサイズする
	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存