md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-100
			b PID
		div.cell.flex.col-500
			b タイトル
		div.cell.flex
			b HEX
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='color in vm.infiniteColors' md-on-demand ng-show='color')
		div.cell.flex.col-100 {{ color.ProductColor.id }}
		div.cell.flex.col-100 {{ color.ProductColor.product_id }}
		div.cell.flex.col-500 {{ color.ProductColor.title }}
		div.cell.flex {{ color.ProductColor.hex }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  color.ProductColor.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(color)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

color-details(
is-shown='vm.colorDetails.isShown',
active='vm.colorDetails.active',
type='vm.colorDetails.type',
on-close='vm.onColorClose()',
on-create='vm.onColorCreate()',
on-update='vm.onColorUpdate(color)',
on-delete='vm.onColorDelete(colorId)'
)