md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-100
			b PID
		div.cell.flex.col-100
			b CID
		div.cell.flex.col-100
			b I_O_W
		div.cell.flex.col-100
			b I_O_H
		div.cell.flex.col-100
			b I_R_W
		div.cell.flex.col-100
			b I_R_H
		div.cell.flex.col-100
			b I_T_W
		div.cell.flex.col-100
			b I_T_H
		div.cell.flex.col-100
			b C_A_I_T_O
		div.cell.flex.col-100
			b C_A_I_L_O
		div.cell.flex.col-100
			b I_R_C
		div.cell.flex.col-100
			b F_H
		div.cell.flex
			b F_V
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='uv in vm.infiniteUvs' md-on-demand ng-show='uv')
		div.cell.flex.col-100 {{ uv.ProductUVOption.id }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.product_id }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.customer_id }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_original_width }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_original_height }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_resize_width }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_resize_height }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_trimmed_width }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_trimmed_height }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.cell_and_image_top_offset }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.cell_and_image_left_offset }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.image_round_corder }}
		div.cell.flex.col-100 {{ uv.ProductUVOption.flip_horizontal }}
		div.cell.flex {{ uv.ProductUVOption.flip_vertical }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(uv)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

uv-details(
is-shown='vm.uvDetails.isShown',
active='vm.uvDetails.active',
type='vm.uvDetails.type',
on-close='vm.onUvClose()',
on-create='vm.onUvCreate()',
on-update='vm.onUvUpdate(uv)',
on-delete='vm.onUvDelete(uvId)'
)