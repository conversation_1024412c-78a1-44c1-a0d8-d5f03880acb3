md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-130
			b ID
		div.cell.flex.col-130
			b PID
		div.cell.flex.col-130
			b CID
		div.cell.flex.col-130
			b I_O_W
		div.cell.flex.col-130
			b I_O_H
		div.cell.flex.col-130
			b I_T_W
		div.cell.flex.col-130
			b I_T_H
		div.cell.flex.col-130
			b I_R_C
		div.cell.flex.col-130
			b F_H
		div.cell.flex
			b F_V
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='vakuum in vm.infiniteVakuums' md-on-demand ng-show='vakuum')
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.id }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.product_id }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.customer_id }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.image_original_width }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.image_original_height }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.image_trimmed_width }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.image_trimmed_height }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.image_round_corder }}
		div.cell.flex.col-130 {{ vakuum.ProductVakuumOption.flip_horizontal }}
		div.cell.flex {{ vakuum.ProductVakuumOption.flip_vertical }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(vakuum)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

vakuum-details(
is-shown='vm.vakuumDetails.isShown',
active='vm.vakuumDetails.active',
type='vm.vakuumDetails.type',
on-close='vm.onVakuumClose()',
on-create='vm.onVakuumCreate()',
on-update='vm.onVakuumUpdate(vakuum)',
on-delete='vm.onVakuumDelete(vakuumId)'
)