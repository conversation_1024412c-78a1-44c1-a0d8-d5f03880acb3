md-sidenav.management-sidenav.md-sidenav-right(md-component-id='color-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'カラー追加' : 'カラー情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block.bottom-border

			div.add-color
				form.form.default-form(layout='row' layout-wrap name='colorForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.color.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text' ng-model='vm.color.title' name='color-title' ng-readonly='vm.showMode' required)
					div.input-container(flex=100)
						md-input-container(input-color='vm.color.hex')
							label HEX
							input(type='text' ng-model='vm.color.hex' name='color-hex' ng-readonly='vm.showMode' required)
							.color-preview-container: .color-preview(ng-repeat='color in colors' ng-style="{'background-color': color}")
					div.input-container(flex=100)
						div.h-full.flex.align-item-center
							md-checkbox.m-top-16.m-left-20(ng-model='vm.color.is_light_color' aria-label='label' ng-disabled='vm.showMode')
							label.text-black 淡色
					div.input-container(flex=100)
						md-input-container
							label is_pdf
							input(type='number' ng-model='vm.color.is_pdf' name='color-pdf' ng-readonly='vm.showMode' required string-to-number)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label メイン
							input(type='number' ng-model='vm.color.is_main' name='color-main' ng-readonly='vm.showMode' required string-to-number)

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存