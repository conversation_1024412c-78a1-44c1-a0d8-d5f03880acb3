md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-500
			b タイトル
		div.cell.flex.col-150
			b CODE
		div.cell.flex.col-150
			b サイズ
		div.cell.flex
			b カラー
		div.cell.cell-action(flex='none', layout='row', layout-align='end center' style="padding-right: 138px;")
			b 更新日時

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='item in vm.infiniteData' md-on-demand ng-show='item')
		div.cell.flex.col-100 {{ item.id }}
		div.cell.flex.col-500 {{ item.title }}
		div.cell.flex.col-150 {{ item.code }}
		div.cell.flex.col-150 {{ item.size_title }}
		div.cell.flex {{ item.color_title }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{ item.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(item)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

reference-option-items-details(
	is-shown='vm.optionItemsDetail.isShown',
	active='vm.optionItemsDetail.active',
	type='vm.optionItemsDetail.type',
	on-close='vm.onOptionItemClose()',
	on-update='vm.onOptionItemUpdate(option_item)',
	on-delete='vm.onOptionItemDelete(optionItemId)'
)