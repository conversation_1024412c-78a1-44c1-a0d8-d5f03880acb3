md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b ID
		div.cell.flex.col-100
			b PID
		div.cell.flex.col-200
			b タイトル
		div.cell.flex.col-200
			b CODE
		div.cell.flex.col-300
			b ソース
		div.cell.flex
			b コート
		div.cell.cell-action.col-200(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='size in vm.infiniteSizes' md-on-demand ng-show='size')
		div.cell.flex.col-100 {{ size.ProductSize.id }}
		div.cell.flex.col-100 {{ size.ProductSize.product_id }}
		div.cell.flex.col-200 {{ size.ProductSize.title }}
		div.cell.flex.col-200 {{ size.ProductSize.code }}
		div.cell.flex.col-300 {{ size.Product.ProductLinkedCode[0].ProductLinkedSource.title }}
		div.cell.flex {{ size.Product.ProductLinkedCode[0].code }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  size.ProductSize.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(size)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

size-details(
is-shown='vm.sizeDetails.isShown',
active='vm.sizeDetails.active',
type='vm.sizeDetails.type',
on-close='vm.onSizeClose()',
on-create='vm.onSizeCreate()',
on-update='vm.onSizeUpdate(size)',
on-delete='vm.onSizeDelete(sizeId)'
)