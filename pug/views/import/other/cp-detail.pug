md-sidenav.management-sidenav.md-sidenav-right(md-component-id='cp-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'CPオプション追加' : 'CPオプション追加情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.uv-block.bottom-border

			div.add-uv
				form.form.default-form(layout='row' layout-wrap name='cpForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.cp.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label product_id
							input(type='number' ng-model='vm.cp.product_id' name='cp-product-id' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label customer_id
							input(type='number' ng-model='vm.cp.customer_id' name='cp-customer-id' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_original_width
							input(type='number' ng-model='vm.cp.image_original_width' name='cp-image-original-width' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_original_height
							input(type='number' ng-model='vm.cp.image_original_height' name='cp-image-original-height' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_resize_width
							input(type='number' ng-model='vm.cp.image_resize_width' name='cp-image-resize-width' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_resize_height
							input(type='number' ng-model='vm.cp.image_resize_height' name='cp-image-resize-height' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_trimmed_width
							input(type='number' ng-model='vm.cp.image_trimmed_width' name='cp-image-trimmed-width' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_trimmed_height
							input(type='number' ng-model='vm.cp.image_trimmed_height' name='cp-image-trimmed-height' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label cell_and_image_top_offset
							input(type='number' ng-model='vm.cp.cell_and_image_top_offset' name='cp-cell-and-image-top-offset' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label cell_and_image_left_offset
							input(type='number' ng-model='vm.cp.cell_and_image_left_offset' name='cp-cell-and-image-left-offset' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label image_round_corder
							input(type='number' ng-model='vm.cp.image_round_corder' name='image-round-corder' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label flip_horizontal
							input(type='number' ng-model='vm.cp.flip_horizontal' name='cp-flip-horizontal' ng-readonly='vm.showMode' required string-to-number)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label flip_vertical
							input(type='number' ng-model='vm.cp.flip_vertical' name='cp-flip-vertical' ng-readonly='vm.showMode' required string-to-number)

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存