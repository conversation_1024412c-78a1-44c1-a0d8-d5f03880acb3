md-sidenav.management-sidenav.md-sidenav-right(md-component-id='side-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'SIDE追加' : 'SIDE情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.side-block.bottom-border

			div.add-side
				form.form.default-form(layout='row' layout-wrap name='sideForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.side.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label color id
							input(type='number' ng-model='vm.side.color_id' name='side-color-id' ng-readonly='vm.showMode' required string-to-number)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text' ng-model='vm.side.title' name='side-title' ng-readonly='vm.showMode' required)
					div.input-container(flex=100)
						md-input-container
							label JSON
							textarea(md-no-resize max-rows=7 ng-model='vm.side.content' name='side-json' ng-readonly='vm.showMode' required)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label IMAGE
							button.btn-edit.simple-btn.side-img-btn(type='button' ng-click='vm.editImage(null)'): md-icon(md-svg-src='img/icons/upload.svg')
							input#imgInput.ng-hide(type='file' ng-model='vm.uploadImg' ng-change='vm.editImage(this)')
							div.side-img
								img(ng-src='{{ vm.side.medium_image_url }}')

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存