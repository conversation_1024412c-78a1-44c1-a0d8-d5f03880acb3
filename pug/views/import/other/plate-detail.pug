md-sidenav.management-sidenav.md-sidenav-right(md-component-id='plate-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'PLATE追加' : 'PLATE情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd')
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.plate-block.bottom-border

			div.add-plate
				form.form.default-form(layout='row' layout-wrap name='plateForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label PID
							input(type='text' ng-model='vm.plate.size.product_id' readonly)
					div.input-container(flex=100)
						md-input-container
							label SIDE NAME
							input(type='text' ng-model='vm.plate.side.title' readonly)
					div.input-container(flex=100)
						md-input-container
							label SIZE NAME
							input(type='text' ng-model='vm.plate.size.title' readonly)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label PLATE
							md-select(ng-model='vm.plate.plate_id' name='plate-plate-id' ng-disabled='vm.showMode' required )
								md-option(ng-value='p.Plate.id' ng-repeat='p in vm.proPlates') {{ p.Plate.title }}

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存