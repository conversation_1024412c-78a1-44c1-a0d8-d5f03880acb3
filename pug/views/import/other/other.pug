div.layout-column.block-min-width
	header.with-subheader(flex="none")
		div.subheader-th(layout='row' layout-align="start center")

			div(flex, layout='row', layout-align='end center').user-info
				user-manage

		div.subheader-nav(style="width: 750px")
			md-nav-bar(nav-bar-aria-label='navigation links' md-no-ink md-selected-nav-item="::vm.initialState")
				md-nav-item(md-nav-sref='import.other.color' name='import.other.color' ng-if="vm.showSubModeTab('COLOR')") COLOR
				md-nav-item(md-nav-sref='import.other.size' name='import.other.size' ng-if="vm.showSubModeTab('SIZE')") SIZE
				md-nav-item(md-nav-sref='import.other.side' name='import.other.side' ng-if="vm.showSubModeTab('SIDE')") SIDE
				md-nav-item(md-nav-sref='import.other.uv' name='import.other.uv' ng-if="vm.showSubModeTab('UV OPTION')") UV OPTION
				md-nav-item(md-nav-sref='import.other.vakuum' name='import.other.vakuum' ng-if="vm.showSubModeTab('VAKUUM OPTION')") VAKUUM OPTION
				md-nav-item(md-nav-sref='import.other.cp' name='import.other.cp' ng-if="vm.showSubModeTab('CP OPTION')") CP OPTION
				md-nav-item(md-nav-sref='import.other.plate' name='import.other.plate' ng-if="vm.showSubModeTab('PRINT')") PRINT
				md-nav-item(md-nav-sref='import.other.optionitem' name='import.other.optionitem' ng-if="vm.showSubModeTab('OPTION ITEM')") OPTION ITEM

	md-content(flex='grow' layout='column' ui-view)