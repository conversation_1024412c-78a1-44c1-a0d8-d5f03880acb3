md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVTemplateDownload()') CSVテンプレートダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.CSVdownload()') CSVダウンロード
			md-button.md-raised.md-primary.btn-sm(ng-if='vm.isElectron' ngf-select="vm.CSVupload($file)" ) CSVアップロード
			md-button.md-raised.md-primary.btn-sm(ng-if='!vm.isElectron' ng-click="vm.CSVupload(null)" ) CSVアップロード
			input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.CSVupload(this)')

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-100
			b PID
		div.cell.flex.col-500
			b PRODUCT NAME
		div.cell.flex.col-150
			b SIDE NAME
		div.cell.flex.col-150
			b SIZE NAME
		div.cell.flex
			b PLATE NAME
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')

	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='plate in vm.infinitePlates' md-on-demand ng-show='plate')
		div.cell.flex.col-100 {{ plate.ProductJoin.id }}
		div.cell.flex.col-500 {{ plate.ProductJoin.title }}
		div.cell.flex.col-150 {{ plate.ProductColorSideJoin.title }}
		div.cell.flex.col-150 {{ plate.ProductSizeJoin.title }}
		div.cell.flex {{ plate.PlateJoin.title }}
		div.cell.cell-action(flex='none', layout='row', layout-align='end center')
			div.text-small {{  plate.ProductColorSideSizeLinkedPlate.updated_at }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(plate)') 詳細


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

plate-details(
is-shown='vm.plateDetails.isShown',
active='vm.plateDetails.active',
type='vm.plateDetails.type',
on-close='vm.onPlateClose()',
on-create='vm.onPlateCreate()',
on-update='vm.onPlateUpdate(plate)',
on-delete='vm.onPlateDelete(plateId)'
)