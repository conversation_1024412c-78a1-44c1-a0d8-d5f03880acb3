md-sidenav.management-sidenav.md-sidenav-right(md-component-id='edit-product').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.colorBlock.isShown || vm.sizeBlock.isShown || vm.linkCodeBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 検査がNGの理由を入力してくだい。

		md-content(flex='grow')
			div(ng-repeat='item in vm.categories' flex='none' layout='row')
				div(flex=70)
					md-checkbox(ng-model='item.selected' aria-label='label' name='status_id') {{item.KenpinStatus.title}}
				div(flex=30)
					input(type='number' ng-model='item.KenpinStatus.quantity' min=0 name='quantity' required style='width:50px')


		footer.text-right(flex='none')
			div: md-button.md-raised.md-accent(ng-click='vm.save()') 保存
