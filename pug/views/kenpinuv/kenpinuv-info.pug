div.management.kenpinuv-header
	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.closeKenpin()'): i.icons8-delete-2
			h2 検品モード
		div.layout-row.buttons(layout-align='end center')
			div: md-button.md-raised.md-accent(ng-disabled="isDisabled" ng-click='vm.save()') 検査完了

		div.layout-row.buttons(layout-align='end center')
			div: md-button.md-primary(ng-click='vm.confirmDelete($event)') キャンセル
md-content.block-wide.content.list-content(layout='column')
	div.kenpinuv-info
		div
			table.task-info
				tr
					td(colspan=4 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
						h4 検品回数 : {{vm.numberCheck}}
				tr
					td(ng-repeat='item in vm.task')
						div.oit-div
							div
								span {{item.title_index}}
								span.text-red {{item.title_place}}
							span.oit-str {{item.oit_str}}
						div.task-info-header(ng-if='item.title_index') {{ item.current_step.detail.product_title }} {{ item.current_step.detail.product_color_title }}
				tr
					td(ng-repeat='(key,item) in vm.task')
						div(ng-if='item.nashi') {{item.nashi}}
						div(ng-if='item.current_step')
							img(ng-click='vm.showLargeImage($event, item)', ng-src="{{ item.current_step.TaskStep.image_url }}" ng-style="{'cursor': 'pointer'}")
							br
							div.wrap-check-box
								div.flex.wrap-ok-box
									md-checkbox(ng-model='item.okSelected', id="OK-{{key}}", aria-label='label', ng-click='vm.clickChange(item,key)',  ng-change='vm.updateSubscribe(item)' ,ng-disabled='item.selected').good 検査OK
									span(ng-if="item.current_step.detail.certificate_stamp" ng-style="{'color' : 'red','line-height' : '18px'}") 証紙OK (証紙: {{ item.current_step.detail.certificate_stamp }})
									br
								div.flex
									md-checkbox(ng-model='item.selected', id="NG-{{key}}", aria-label='label' , ng-click='vm.clickChange(item,key)', ng-change='vm.viewReasion(item)' ,ng-disabled='item.okSelected') 検査NG
								div.flex
									md-checkbox(ng-if="item.current_step.detail.is_include" ng-model='item.check_include', aria-label='label', ng-change='vm.showIncludeOption($event, item)') 同梱オプション
							br
							img(ng-src='{{item.current_step.detail.certificate_stamp_image}}' ng-style="{'width': '60px','margin-left':'10px'}")

			br
			div.layout-row(layout-align='center' ng-if='vm.task.default.data.is_check_all == true')
				span.kenpinuv-check-all 全数チェック済
			div.layout-row(layout-align='center')
				span(ng-repeat='side in vm.task.sides')
					div: md-button.md-raised.md-accent(ng-click='vm.selectOKAll(side)') {{side}}OK全選択
				div: md-button.md-raised.md-accent(ng-if="vm.has_include" ng-click='vm.selectOKOptionAll()') 同梱全選択
			div.step-info-div
				div.table
					table.step-info
						tr(ng-repeat='(key,val) in vm.task.detail')
							td [{{key}}]{{val}}
					div#count-down
kenpinuv-failed(
is-shown='vm.isShowReasion',
on-close='vm.onDetailsClose()',
)

style(type="text/css").
	.table {
		position: relative;
	}

	.table #count-down {
		font-size: 50px;
		font-weight: 700;
		text-align: right;
		position: absolute;
		right: 125px;
		top: 0;
	}