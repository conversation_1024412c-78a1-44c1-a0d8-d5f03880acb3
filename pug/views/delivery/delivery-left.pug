header(flex='none' layout='row' layout-align='center center')
	div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

// show messages
div.image-messages.block-wide.justify-start(style='padding-top: 16px' flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')")
	div.key-value(ng-if="vm.printCtrl.placeData.countPlace")
		div.item-key-value
			div.box-key-value(ng-repeat="(key, value) in vm.printCtrl.placeData.countPlace")
				div.text
					span {{key}} :
				div.text
					span {{value}}
	table.place-detail-table(style="display:table-cell; border-spacing : 0 36px")
		tbody(style="display:table; width:100%")
			tr(ng-repeat='item in vm.printCtrl.placeData')
				td(style="width:20%")
					span(style="display:block").place-detail-title {{item.title}}
					span(style="display:block") 残り
				td.text-center(style="width:30%")

					span {{item.done}} / {{item.total}}
				td
					div.list-result
						div.place-detail-customer( style="width: max-content;min-width: 50%;" ng-repeat='(key, i) in item' ng-if="(i.done || i.total) && !i.type")
							| {{key}} {{i.done}} / {{i.total}}
					div.list-result
						div.place-detail-customer( style="width: max-content;min-width: 50%;" ng-repeat='(key, i) in item' ng-if="(i.done || i.total) && i.type")
							| {{key}} {{i.done}} / {{i.total}}

div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('fail')")
	i.finish-icon.icons8-delete-2

div.image-messages.block-wide(flex layout='column' layout-align='center center' ng-if="vm.state.is('finish')")
	md-icon.finish-icon(md-svg-src='img/icons/checkmark.svg', aria-label='success')

div.remark.block-wide(flex layout='column' ng-if="vm.state.is('fail','finish')")
	div(ng-if='vm.printCtrl.memo') {{ vm.printCtrl.memo}}
	div(ng-if='!vm.printCtrl.memo')
		div(ng-style={"font-weight": "600", "font-size": "14px"}) 置き場・注文番号・枚数・品番・カラー・サイズ・発注元・納期・状態
		div(ng-repeat="task in vm.printCtrl.tasks")
			p(ng-style={"font-size": "14px"}) {{ task.PlaceJoin.title }}・{{ task.OrderJoin.number }}・{{ task.ProductJoin.title }}・{{ task.ProductColorJoin.title }}・{{ task.ProductSizeJoin.title }}・{{ task.CustomerJoin.title }}・{{ task.OrderJoin.production_date_preferred }}・{{ task.Task.status }}

div.giftset.block-wide(flex layout='column' ng-if="vm.state.is('fail','finish') && (vm.printCtrl.giftset == 1) ") ギフトセット : あり