md-sidenav.management-sidenav.md-sidenav-right(md-component-id='box-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? '箱登録' : '箱情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete
        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='boxForm')
                div.input-container(flex=100)
                    md-input-container
                        label 発注者
                        md-select(ng-model='vm.box.Customer.id' name='customer_id' ng-disabled='!vm.isAdd')
                            md-option(ng-click='vm.box.Customer.id = null')
                            md-option(ng-repeat='customer in vm.customers' ng-value='customer.Customer.id') {{ customer.Customer.title }}
                div.input-container(flex=100)
                    md-input-container
                        label 箱No.
                        input(type='text' ng-model='vm.box.Box.number' name='box_number' ng-readonly='!vm.isAdd')
                div.input-container(flex=100)
                    md-input-container
                        label 最大数
                        input(type='number' ng-model='vm.box.Box.max_capacity' name='max_capacity' ng-readonly='vm.showMode' ng-required='!showMode')
                div.input-container(flex=100 ng-if='!vm.isAdd')
                    md-input-container
                        label 状態
                        md-select(ng-model='vm.box.BoxStatus.id' name='boxes_status_id' ng-disabled='!vm.isAdd')
                            md-option(ng-click='vm.box.BoxStatus.id = null')
                            md-option(ng-repeat='status in vm.statuses' ng-value='status.BoxStatus.id') {{ status.BoxStatus.title }}
                div.input-container.input-date-picker(ng-if='!vm.isAdd && vm.box.Box.boxes_status_id == 4' flex=100)
                    label 発送日
                    div(style="width:140px;")
                        div(pick-date-header)
                            div(ng-if='vm.date !== null').width-date-picker {{vm.date | dateJapan : 'year/month/day'}}
                            md-datepicker(ng-disabled='vm.showMode' ng-model='vm.date' md-placeholder='Enter date' md-open-on-focus)

        footer.text-right(flex='none')
            div(ng-hide='vm.showMode')
                div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
                div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存