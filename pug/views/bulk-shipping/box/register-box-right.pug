header(flex='none' layout='row' layout-align='start center')
    div(flex layout='row', layout-align='end center').user-info
        user-manage

md-content.content(flex layout='column' layout-align='center center')
    div.text-center.scan-barcode(ng-if="vm.state.is('scan')")
        md-icon.barcode-icon(md-svg-src='img/icons/barcode.svg', aria-label='scan')
        h2 商品バーコードを読み取ってください。
        p.text-red.scanner-error(ng-if='vm.state.errorText') {{ vm.state.errorText }}
            p.text-green(ng-if='vm.state.repairText') {{ vm.state.repairText }}
            p.text-orange(ng-if='vm.wait') {{ vm.wait }}
            md-button.md-raised.md-primary.btn-print(ng-click="vm.state.set('manual')") 手動入力
            input#scanner-field(type='text' ng-model='vm.scannerInput' ng-change='vm.findQrScan()' ng-blur='vm.focusScanField()')

    div.enter-qr.text-center(ng-if="vm.state.is('manual')")
        md-icon.barcode-icon(md-svg-src='img/icons/barcode.svg', aria-label='scan')
        form(name='findQr' ng-submit='vm.findQr()')
            h2 商品バーコードを読み取ってください。
            p.error-text(ng-if='vm.state.errorText') {{ vm.state.errorText }}
            .form-group
                input.simple-input(type='text' ng-model='vm.qrCodeInput' placeholder='例えば：Н4676446')
                button.reset(type='reset' ng-show='vm.qrCodeInput' ng-click="vm.clearInput()")
                    i.icons8-delete-2
            md-button.md-raised.md-accent.btn-print(type='submit') 次へ