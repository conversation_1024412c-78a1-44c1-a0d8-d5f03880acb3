div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div 発送日
            div(style="width:180px;")
                div(pick-date-header)
                    div {{ vm.startDate | dateJapan : 'year/month/day' }}
                    md-datepicker(ng-model='vm.startDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
            | -
            div(style="width:180px;margin-left:30px;")
                div(pick-date-header)
                    div {{ vm.endDate | dateJapan : 'year/month/day' }}
                    md-datepicker(ng-model='vm.endDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
            div(flex, layout='row', layout-align='end center').user-info
                user-manage

    md-content.content(flex='grow' layout='column')

        md-virtual-repeat-container(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form()
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()" placeholder='注文IDを検索')
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            div.m-bottom-30(layout='row')
                div.cell.heading-btns(flex layout='row', layout-align='end center')
                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.downloadBoxCSV()') 完了CSVダウンロード
                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.collectToShip()') 選択をまとめて発送

            md-divider

            div.default-row(layout='row' layout-align='start center')
                div.cell.flex.col-250
                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()')
                        b 全て
                div.cell.flex.col-250
                    b 箱No.
                div.cell.flex.col-250
                    b 内容
                div.cell.flex.col-250
                    b 状態
                div.cell.flex.col-200
                    b 発送日
                div.cell.cell-action(flex, layout='row', layout-align='end center')

            div.default-row(layout='row' layout-align='start center' md-virtual-repeat='box in vm.infiniteBoxes' md-on-demand ng-show='box')
                div.cell.flex.col-250
                    div.m-left-20
                        md-checkbox(ng-model='box.selected' aria-label='select {{ box.Box.id }}' ng-if='box.Box && box.Box.status_code != "shipment_completed"')
                div.cell.flex.col-250 {{ box.Box.number }}
                div.cell.flex.col-250 {{ box.Box.total_enclosed }} / {{ box.Box.max_capacity }}
                div.cell.flex.col-250 {{ box.Box.status_title }}
                div.cell.flex.col-200 {{ box.Box.status_code == "shipment_completed" ? box.Box.shipping_date : ''}}
                div.cell.cell-action(flex, layout='row', layout-align='end center')
                    md-button.md-raised.md-primary.btn-sm(aria-label='add' ng-click='vm.registerBoxes(box)') 封入
                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(box)') 詳細

            div.pagination-loading(style='display: none;')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

box-details(
    is-shown='vm.boxDetails.isShown',
    active='vm.boxDetails.active',
    type='vm.boxDetails.type',
    on-close='vm.boxDetails.onClose()',
    on-create='vm.boxDetails.onCreate()'
    on-update='vm.boxDetails.onUpdate(box)',
    on-delete='vm.boxDetails.onDelete(boxId)'
)

register-box(
    open-on='vm.registerBoxes',
    on-reload='vm.registerBoxDetail.onReload()'
)