header(flex='none' layout='row' layout-align='center center' style='background: none')
    div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

// show messages
div.block-wide.register-box-info(flex layout='column' layout-align='center center' ng-if="vm.state.is('scan', 'manual')" ng-show='vm.box')
    div.info-box-number
        div No. {{ vm.box.Box.number }}
        div {{ vm.state.enclosed }} / {{vm.box.Box.max_capacity}}

    div(flex)
        div.info-order-number
            span.font-size-24 注文ID
            div(layout='row' layout-align='start center' ng-repeat='order in vm.state.numbers')
                button.button-number(ng-click='vm.deleteOrderNumber(order.value)') {{ order.value }}
                span.m-left-20 {{ order.item_enclosed }} / {{order.total_item}}
        div.button-sort
            span ソート
            button(type='button', ng-click='vm.sortNumber()'): md-icon(md-svg-src='img/icons/sort.svg')