.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.sidebar(flex='none', layout='column')
            div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

            div.navbar
                nav: ul
                    li(ui-sref-active='active' layout='row' layout-align='center center')
                        div(flex='grow'): a(ui-sref='shipping.bulk') 封入
                        div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add' ng-click='vm.createBox()'): i.icons8-plus

            div.sidebar-secondary
                sidebar-secondary-menu(statuses='vm.statuses')

            div.sidebar-secondary.image-sidebar
                sidebar-secondary-menu-type(types='vm.customers')

        md-content.main-body.flex-100(ui-view)

        .loading-list
            md-progress-circular(md-diameter='80' md-mode='indeterminate')

