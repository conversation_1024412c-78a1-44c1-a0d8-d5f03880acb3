div.block-wide(layout='column')
	header(flex='none')
		button.go-back-button(type='button', ng-click='vm.back()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 値段設定
		div.sidenav-actions
			md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add printer' ng-click='vm.viewCreate()'): i.icons8-plus
			md-button.md-raised.md-primary.btn-xs(ng-click='vm.viewBodyPriceDetails()') ボディ設定
	md-content(flex='grow')

		div(
			infinite-scroll="vm.loadMoreItems()",
			infinite-scroll-parent='md-content',
			infinite-scroll-distance='0.2',
			infinite-scroll-disabled='vm.pagination.disabled'
		)
		div.block-wide.content-price(layout='row' layout-align="start center")
			div.cell.text-center(flex=20) 色
			div.cell.text-center(flex=20) サイズ
			div.cell.text-center(flex=15) プリント面
			div.cell.text-center(flex=20) プリント価格
			div.cell.text-center(flex=15) ボディ価格
		div.default-row.price-defaut-row-after(layout='row' layout-align='start center' ng-repeat='price in vm.prices' ng-show='price')
			div.cell.text-center(flex=20) {{ price.ProductPrice.color }}
			div.cell.text-center(flex=20) {{ price.ProductPrice.size }}
			div.cell.text-center(flex=15) {{ price.ProductPrice.side }}
			div.cell.text-center(flex=20) {{ price.ProductPrice.price }}
			div.cell.text-center(flex=15) {{ vm.original_body_price }}
			div.cell.cell-action(flex layout='row', layout-align='end center')
				div.buttons
					md-button.md-primary.md-raised.btn-sm(ng-click='vm.viewPriceCustomerDetails(price)') 詳細
		div.pagination-loading(style='display: none;')
			md-progress-circular(md-diameter='40' md-mode='indeterminate')
	button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る
	.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

price-customer-details(
	is-shown='vm.priceCustomerDetails.isShown',
	active='vm.priceCustomerDetails.active',
	type='vm.priceCustomerDetails.type',
	on-close='vm.onPriceCustomerClose()',
	on-create='vm.onPriceCustomerCreate()'
	on-update='vm.onPriceCustomerUpdate(price)',
	on-delete='vm.onPriceCustomerDelete(priceId)'
)