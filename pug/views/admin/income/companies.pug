md-virtual-repeat-container.content(flex='grow' )

	div.search(layout='row')
		div.search-form.flex-none.layout-row.layout-align-start-center(flex=50 focusable-search): form()
			i.icons8-search
			input.search-input(type='text')
		div.cell.heading-btns(flex=50 layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
			md-button.md-raised.md-primary.btn-sm 請求書
	md-divider

	div.default-row(layout='row' layout-align='center center' md-virtual-repeat='company in vm.companies' ng-show='company')
		div.cell(flex=25 layout='row' layout-align='start center')
			div: md-checkbox(ng-model='company.selected' aria-label='{{ ::company.name }}')
			div {{ ::company.name }}
		div.cell(flex=20) {{ ::company.amount }}
		div.cell.text-center(flex=10 layout='column' layout-align='center center')
			div(status-emphasis='::company.status') {{ ::company.status }}
		div.cell.cell-action(flex, layout='row', layout-align='end center')
			div.text-small {{ ::company.date }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.showOrders()') 詳細

	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='62' md-mode='indeterminate')
		
.highlighted-column-container(layout='row'): .highlighted-column(flex=10 flex-offset=45)

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='false'): div 見つかりません

.loading-list(flex-offset=15)
	md-progress-circular(md-diameter='130' md-mode='indeterminate')
			
orders-list(show-func='vm.showOrders')