md-sidenav.management-sidenav.md-sidenav-right(md-component-id='price-customer-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide.form-padding(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 {{ vm.isAdd ? '値段追加' : '値段情報' }}
            div.sidenav-actions(ng-hide='vm.isAdd')
                button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
                button.btn-delete.simple-btn(type='button' ng-click='vm.delete($event)'): i.icons8-delete
        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='priceCustomerFrameForm')
                div.input-container(flex=100)
                    md-input-container
                        label サイズ
                        md-select(ng-model='vm.price.ProductPrice.size_id' name='size_id' ng-disabled='vm.showMode' required)
                            md-option(ng-repeat='size in vm.details.sizes' ng-value='size.ProductSize.id') {{ size.ProductSize.title }}
                    md-input-container
                        label 色
                        md-select(ng-model='vm.price.ProductPrice.color_id' name='color_id' ng-disabled='vm.showMode' ng-change="vm.fetchSidesOfColor(vm.price.ProductPrice.color_id)" required)
                            md-option(ng-repeat='color in vm.details.colors' ng-value='color.ProductColor.id') {{ color.ProductColor.title }}
                    md-input-container
                        label プリント面
                        md-select(ng-model='vm.price.ProductPrice.side_id' name='side_id' ng-disabled='vm.showMode' required)
                            md-option(ng-repeat='side in vm.sides' ng-value='side.id') {{ side.title }}
                    md-input-container
                        label 設定
                        input(type='number' ng-model='vm.price.ProductPrice.price' name='price' ng-readonly='vm.showMode' required)

        footer.text-right(flex='none' ng-hide='vm.showMode')
            div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
            div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存