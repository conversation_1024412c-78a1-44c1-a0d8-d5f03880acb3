md-sidenav.management-sidenav.md-sidenav-right(md-component-id='vm.guid' eval-attr-as-expr="mdComponentId").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column' ng-hide='vm.optionBlock.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? '発注者追加' : '発注者情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete
		md-content(flex='grow'): form(name='customerForm')
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label カスタマーID
						input(type='text' ng-model='vm.customer.Customer.id' name='id' readonly)
				div.input-container(flex=100)
					md-input-container
						label タイトル
						input(type='text' ng-model='vm.customer.Customer.title' name='title' ng-readonly='vm.showMode' required)
				div.input-container(flex=100)
					md-input-container
						label 注文上限
						input(type='number' ng-model='vm.customer.Customer.limit' name='limit' ng-readonly='vm.showMode' string-to-number required)
				div.input-container(flex=100)
					md-input-container
						label 法人名
						input(type='text' ng-model='vm.customer.Customer.billing.company' name='company' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 法人名（カナ）
						input(type='text' ng-model='vm.customer.Customer.billing.company_kana' name='company_kana' ng-readonly='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label 郵便番号
						input(type='text' ng-model='vm.customer.Customer.billing.address.postcode' name='postcode' ng-disabled='vm.showMode')
				div.input-container(flex=50)
					md-input-container
						label 都道府県
						input(type='text' ng-model='vm.customer.Customer.billing.address.state' name='state' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 市町村
						input(type='text' ng-model='vm.customer.Customer.billing.address.city' name='city' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 番地
						input(type='text' ng-model='vm.customer.Customer.billing.address.address1' name='address1' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 建物名
						input(type='text' ng-model='vm.customer.Customer.billing.address.address2' name='address2' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 電話番号
						input(type='text' ng-model='vm.customer.Customer.billing.phonenumber' name='phonenumber' ng-readonly='vm.showMode')
				div.input-container(flex=100)
					md-input-container
						label 配達サービス
						md-select(ng-model='vm.customer.DeliveryService.id' name='service_id' ng-disabled='vm.showMode' ng-required='!showMode')
							md-option(ng-repeat='service in vm.deliveryServices' ng-value='service.DeliveryService.id') {{ service.DeliveryService.title }}
				div.input-container(flex=100)
					md-input-container
						label Printty注文番号プリフィックス
						input(type='text' ng-model='vm.customer.Customer.prefix' name='prefix' ng-readonly='vm.showMode')
				div.checkbox-container(flex=100): md-checkbox(ng-model='vm.customer.Customer.is_production_date_preferred_setting_available' ng-disabled='vm.showMode') 生産希望日設定可能
				div.checkbox-container(flex=100): md-checkbox(ng-model='vm.customer.Customer.is_bulk_shipping' ng-disabled='vm.showMode') まとめて発送
				div.checkbox-container(flex=100): md-checkbox(ng-model='vm.customer.Customer.is_bill_query_sales' ng-disabled='vm.showMode') 請求クエリー_営業用
				div.checkbox-container(flex=100): md-checkbox(ng-model='vm.customer.Customer.is_deliverynote_unnecessary' ng-disabled='vm.showMode') 納品書不要
				div.input-container(flex=100)
					md-input-container.with-clipboard
						label API Key
						input(type='text' ng-model='vm.customer.Customer.api_key' name='api_key' ng-readonly='vm.showMode')
						button.clipboard-button(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ vm.customer.Customer.api_key }}")
							i.icons8-copy
						button.btn-dummy
							md-tooltip(md-visible='vm.clipSuccessMsg') Copied!
			
			div.form.default-form(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label API Callback
						input(type='text' ng-model='vm.customer.Customer.api_callback' name='api_callback' ng-readonly='vm.showMode')

				div.input-container(flex=100)
					md-input-container
						label Procurement API Callback
						input(type='text' ng-model='vm.customer.Customer.procurement_api_callback' name='procurement_api_callback' ng-readonly='vm.showMode')
				
				// product linked code block
				.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd' flex=100)

					h3 API Callback Options

					.sidenav-list
						div.heading.layout-row.layout-align-start-center
							div.cell.flex Key
							div.cell.flex-none Value
						div.default-row.layout-row.layout-align-start-center(ng-repeat='(key, value) in vm.customer.Customer.api_callback_options')
							div.cell.flex {{ key }}
							div.cell.flex-none.layout-row.layout-align-end-center 
								div {{ value }}
								md-menu.small(md-position-mode="target-right target")
									button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
									md-menu-content.menu-small(md-menu-align-target)
										md-menu-item: md-button(ng-click='vm.optionBlock.show(key, value)') 詳細

					div.text-center: md-button.simple-button.btn-sm(ng-click='vm.optionBlock.add()') オプションを追加
						
		footer.text-right(flex='none' ng-hide='vm.showMode')
			div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
			div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
			
	callback-option(
		ng-show='vm.optionBlock.isShown',
		loading-type='vm.loadingType'
		type='vm.optionBlock.type',
		active='vm.optionBlock.active',
		on-close='vm.optionBlock.onClose()',
		on-create='vm.optionBlock.onCreate(key, value)',
		on-update='vm.optionBlock.onUpdate(original, key, value)'
		on-delete='vm.optionBlock.onDelete(key)'
	)