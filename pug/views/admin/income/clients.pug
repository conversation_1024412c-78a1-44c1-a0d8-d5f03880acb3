md-virtual-repeat-container.content(flex='grow')
	
	div.search.layout-row
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
	md-divider

	div.default-row(layout='row' layout-align='center center' md-virtual-repeat='customer in vm.infiniteCustomers' md-on-demand ng-show='customer')
		div.cell(flex) {{ customer.Customer.title }}
		div.cell.cell-action(flex, layout='row', layout-align='end center')
			div.text-small {{ customer.Customer.updated_at | userTime }}
			div.buttons
				md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(customer)') 詳細
				md-menu(md-position-mode="target-right target")
					md-button.md-raised.md-primary.btn-sm.btn-more(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
						i.icons8-more
					md-menu-content.menu-small(md-menu-align-target)
						md-menu-item
							md-button(ng-click='vm.showOrders(customer)') 注文履歴
						//- md-menu-item
							md-button(ng-click='vm.showHistory()') History
						md-menu-item
							md-button(ng-click='vm.showChangePrice(customer)') 値段設定

	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
	
orders-list.order-detail-wrapper.block-wide(open-on='vm.showOrders')
//- orders-history(open='vm.showHistory')
change-price(open='vm.showChangePrice')

client-details(
	is-shown='vm.customerDetails.isShown',
	active='vm.customerDetails.active',
	type='vm.customerDetails.type',
	on-close='vm.customerDetails.onClose()'
	on-create='vm.customerDetails.onCreate()'
	on-update='vm.customerDetails.onUpdate(customer)',
	on-delete='vm.customerDetails.onDelete(customerId)'
	guid='client-details-id'
)