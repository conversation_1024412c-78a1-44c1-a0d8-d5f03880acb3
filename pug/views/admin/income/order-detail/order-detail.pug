section.block-wide(layout='column')

	.component-loading(ng-class='vm.loadingType')
		md-progress-circular(md-diameter='64', md-mode='indeterminate')

	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
			h2 注文情報
		div.layout-row.buttons(flex layout-align='end center')
			div.search-form(focusable-search): form()
				input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
				i.icons8-search
	div.body.block-wide.overflow-hidden(flex='100' layout='row')
		md-content.sidebar
			form
				div.form.default-form.bottom-margin(layout='row' layout-wrap)
					div.input-container(flex=50)
						md-input-container
							label 注文番号
							input(type='text', ng-value='vm.order.number', readonly)
					div.input-container(flex=50)
						md-input-container
							label 注文日
							input(type='text' ng-value='vm.order.date' readonly)
					div.input-container(flex=100)
						md-input-container
							label 完成希望日
							input(type='text' ng-value='vm.order.production_date_preferred' readonly)

				div.form.default-form(layout='row' layout-wrap)
					h3(ng-show='vm.moreBilling') 郵送先
					div.input-container(flex=100)
						md-input-container
							label 法人名
							input(type='text' ng-model='vm.order.shipping.company' readonly)
					div.input-container(flex=50)
						md-input-container
							label 郵便番号
							input(type='text' ng-model='vm.order.shipping.address.postcode' readonly)
					div.input-container(flex=50)
						md-input-container
							label 都道府県
							input(type='text' ng-model='vm.order.shipping.address.state' readonly)
					div.input-container(flex=100)
						md-input-container
							label 市町村
							input(type='text' ng-model='vm.order.shipping.address.city' readonly)
					div.input-container(flex=100)
						md-input-container
							label 番地
							input(type='text' ng-model='vm.order.shipping.address.address1' readonly)
					div.input-container(flex=100)
						md-input-container
							label 建物名
							input(type='text' ng-model='vm.order.shipping.address.address2' readonly)
					div.input-container(flex=50)
						md-input-container
							label 個人名
							input(type='text' ng-model='vm.order.shipping.person' readonly)
					div.input-container(flex=50)
						md-input-container
							label お届け時間
							input(type='text' ng-value='vm.getDeliveryPeriodById(vm.order.billing.delivery_period_id)' readonly)
					div.input-container(flex=100)
						md-input-container
							label 電話番号
							input(type='text' ng-model='vm.order.shipping.phonenumber' readonly)
					div.input-container(flex=100)
						md-input-container
							label メールアドレス
							input(type='text' ng-model='vm.order.shipping.email' readonly)
					div.input-container(flex=50)
						md-input-container
							label 発送日
							input(type='text' ng-model='vm.order.delivery_date' readonly)
					div.input-container(flex=50)
						md-input-container
							label 配送業者
							input(type='text' ng-model='vm.deliveryService.title' readonly)
					div.input-container(flex=100)
						md-input-container
							label 伝票番号
							input(type='text' ng-model='vm.order.tracking_number' readonly)

				div.bottom-margin: md-checkbox(ng-model='vm.moreBilling' disabled) 別の請求先

				div.form.default-form(layout='row' layout-wrap ng-show='vm.moreBilling')
					h3 請求先
					div.input-container(flex=100)
						md-input-container
							label 法人名
							input(type='text' ng-model='vm.order.billing.company' readonly)
					div.input-container(flex=50)
						md-input-container
							label 郵便番号
							input(type='text' ng-model='vm.order.billing.address.postcode' readonly)
					div.input-container(flex=50)
						md-input-container
							label 都道府県
							input(type='text' ng-model='vm.order.billing.address.state' readonly)
					div.input-container(flex=100)
						md-input-container
							label 市町村
							input(type='text' ng-model='vm.order.billing.address.city' readonly)
					div.input-container(flex=100)
						md-input-container
							label 番地
							input(type='text' ng-model='vm.order.billing.address.address1' readonly)
					div.input-container(flex=100)
						md-input-container
							label 建物名
							input(type='text' ng-model='vm.order.billing.address.address2' readonly)
					div.input-container(flex=50)
						md-input-container
							label 個人名
							input(type='text' ng-model='vm.order.billing.person' readonly)
					div.input-container(flex=50)
						md-input-container
							label お届け時間
							input(type='text' ng-value='vm.getDeliveryPeriodById(vm.order.billing.delivery_period_id)' readonly)
					div.input-container(flex=100)
						md-input-container
							label 電話番号
							input(type='text' ng-model='vm.order.billing.phonenumber' readonly)
					div.input-container(flex=100)
						md-input-container
							label メールアドレス
							input(type='text' ng-model='vm.order.billing.email' readonly)

				div.form.default-form.extra-bottom-margin(layout='row' layout-wrap)
					h3 オプション
					div.input-container.py-1(flex=100 ng-if='vm.key.customer_order_number !== undefined')
						label.text-black 発注者注文番号 : {{vm.key.customer_order_number}}
					div.py-1(flex=100 ng-if='vm.key.factory_memo !== undefined')
						label.text-black 工場メモ : {{vm.key.factory_memo}}
					div.py-1(flex=100 ng-if='vm.key.delivery_note_url !== undefined')
						label.text-black 納品書 : {{vm.key.delivery_note_url}}
					div.py-1(flex=100 ng-if='vm.key.customer_order_amount !== undefined')
						label.text-black 代引き : {{vm.key.customer_order_amount}}円
					div.py-1(ng-if='vm.key.Other !== undefined' ng-repeat="(key, value) in vm.key.Other")
						label.text-black {{key}} : {{value}}
				div
					div: md-checkbox(ng-model='vm.order.use_package' disabled) 袋詰め
					div: md-checkbox(ng-model='vm.order.use_original_tag' disabled) Tシャツオリジナルタグ

				div.form.default-form(layout='row' layout-wrap)
					div.input-container(flex=100)
						md-input-container
							label 小計
							input(type='text' value='{{ vm.order.amounts.subtotal | number }}円' readonly)
					div.input-container(flex=100, ng-show='vm.order.use_package')
						md-input-container
							label 袋詰め価格
							input(type='text' value='{{ vm.order.amounts.package_fee | number }}円' readonly)
					div.input-container(flex=100, ng-show='vm.order.use_original_tag')
						md-input-container
							label Tシャツオリジナルタグ価格
							input(type='text' value='{{ vm.order.amounts.original_tag_fee | number }}円' readonly)
					div.input-container(flex=100)
						md-input-container
							label 送料
							input(type='text' value='{{ vm.order.amounts.shipping_fee | number }}円' readonly)
					div.input-container(flex=100)
						md-input-container
							label 特別割引
							input.accent-negative(type='text' value='{{ vm.order.amounts.discount | number }}円' readonly)
					div.input-container(flex=100)
						md-input-container
							label 消費税
							input(type='text' value='{{ vm.order.amounts.tax_fee | number }}円' readonly)
					div.input-container(flex=100)
						md-input-container
							label 合計金額
							input.accent-total(type='text' value='{{ vm.order.amounts.total | number }}円' readonly)

		md-content.block-wide.content.list-content(layout='column', ng-if='vm.orderId')

			.highlighted-column-container(flex layout='row'): .highlighted-column(flex=10 flex-offset=70)

			md-virtual-repeat-container.list-container(flex)

				div.heading(layout='row' layout-align='center center')
					div.cell(ng-if="vm.items[0]['OrderItem']['sub_id']" flex=10) お客様注文番号
					div.cell(flex=30) 内容
					div.cell(flex=10) 商品値段
					div.cell(flex=10) 印刷値段
					div.cell.text-right(flex=10) 合計
					div.cell.text-center(flex=10) オプション
					div.cell.text-center(flex=10) ステータス
					div.cell(flex)

				div.list-body

					div.default-row(layout='row' layout-align='center center' md-virtual-repeat='item in vm.infiniteItems' md-on-demand ng-show='item')
						div.cell(flex=10) {{ item.OrderItem.sub_id ? item.OrderItem.sub_id : item.OrderItem.id}}
						div.cell.cell-images(flex=30 layout='row' layout-align='center center')
							div.flex-none.image(ng-repeat='task in item.tasks'): img(ng-src='{{ task.OrderItemTask.image_url }}')
							div.flex {{ item.Product.title }}•{{ item.ProductSize.title }}•{{ item.ProductColor.title }}•{{ item.OrderItem.quantity }}
						div.cell(flex=10) {{ item.OrderItem.amounts.product_fee | number }}円
						div.cell(flex=10) {{ item.OrderItem.amounts.printing_fee| number }}円
						div.cell.text-right(flex=10) {{ item.OrderItem.amounts.total| number }}円
						div.cell.text-center(flex=10) {{ item.OrderItem.has_key ? '有' : '' }}
						div.cell.text-center(flex=10) {{ item.OrderItem.status }}
						div.cell.cell-action(flex, layout='row', layout-align='end center')
							div.text-small {{ item.OrderItem.updated_at }}
							div.buttons
								md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewItemDetail(item)') 詳細
								
			.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした

// sidenavs
customer-order-product-detail-sidenav(guid='{{vm.guid}}')