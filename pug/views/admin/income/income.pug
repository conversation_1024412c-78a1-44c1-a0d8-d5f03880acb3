div.layout-column.block-min-width
	//- header.with-subheader(flex="none")
		div.subheader-th(layout='row' layout-align="start center")
			
			div.flex-25(ng-if="vm.state.is('admin.income.companies')")
				div(pick-date-header)
					div {{ vm.date | dateJapan : 'month' }}
					md-datepicker(ng-model='vm.date' md-mode='month' md-placeholder='Enter date' md-open-on-focus)
			div.flex-20.cell(ng-if="vm.state.is('admin.income.companies')") 金額
			div.flex-10.cell.text-center(ng-if="vm.state.is('admin.income.companies')") ステータス
			
			div(flex, layout='row', layout-align='end center').user-info
				user-manage
		
		div.subheader-nav
			md-nav-bar(nav-bar-aria-label='navigation links' md-no-ink md-selected-nav-item="::vm.initialState")
				md-nav-item(md-nav-sref='admin.income.clients' name='admin.income.clients') 発注者
				md-nav-item(md-nav-sref='admin.income.companies' name='admin.income.companies') 請求
				
	//- tmp
	header(flex="none")
		div.block-wide(layout='row' layout-align="end center")
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage
				
	md-content(flex='grow' layout='column' ui-view)