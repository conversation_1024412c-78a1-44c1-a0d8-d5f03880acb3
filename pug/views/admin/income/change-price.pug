md-sidenav.management-sidenav.md-sidenav-right(md-component-id='change-price').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column', ng-hide='vm.priceDetails.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 値段設定
		md-content(flex='grow')
			
			div.product-list-narrow(
				infinite-scroll="vm.loadMoreItems()",
				infinite-scroll-parent='md-content',
				infinite-scroll-distance='0.2',
				infinite-scroll-disabled='vm.pagination.disabled'
			)
				div.heading
					div.subheading.text(layout='row' layout-align='center center')
						div.cell(flex) 内容
						//div
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.downloadPriceCSV()') CSVダウンロード
						div
							md-button.md-raised.md-primary.btn-sm(ng-click='vm.uploadCSV(null)'): span CSVアップロード
							input#fileInput.ng-hide(type='file' ng-model='vm.uploadFile' ng-change='vm.uploadCSV(this)')
					div.subheading(layout='row' layout-align='center center')
						div.cell(flex)
							div.search
								div.search-form(focusable-search): form()
									i.icons8-search
									input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
				div.default-row(layout='row' layout-align='center center' ng-repeat='product in vm.products')
					div.cell(flex layout='row' layout-align='start center')
						div.images(layout='row' layout-align='start center')
							div.image(ng-repeat='side in product.sides'): img(ng-src='{{ side.ProductColorSide.image_url }}')
						div {{ product.Product.title }}
					div.cell.cell-action(flex='none', layout='row', layout-align='end center')
						div.buttons
							md-button.md-raised.md-primary.btn-xs(ng-click='vm.viewPriceDetails(product)') 詳細
						
						
	change-price-details(
		ng-show='vm.priceDetails.isShown'
		active='vm.priceDetails.active',
		loading-type='vm.loadingType',
		on-close='vm.priceDetails.onClose()'
	)