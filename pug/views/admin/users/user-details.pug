md-sidenav.management-sidenav.md-sidenav-right(md-component-id='user-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 {{ vm.isAdd ? 'ユーザー登録' : 'ユーザー情報' }}
			div.sidenav-actions(ng-hide='vm.isAdd')
				button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
				button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

		md-content(flex='grow')
			form.form.default-form.bottom-margin(layout='row' layout-wrap name='userForm')
				div.input-container(flex=100)
					md-input-container
						label ユーザー名
						input(type='text' ng-model='vm.user.User.username' name='username' ng-readonly='vm.showMode' ng-required='!showMode')
				div.input-container(flex=100)
					md-input-container
						label 名前
						input(type='text' ng-model='vm.user.User.nickname' name='nickname' ng-readonly='vm.showMode' ng-required='!showMode')
				div.input-container(flex=100)
					md-input-container
						label メールアドレス
						input(type='text' ng-model='vm.user.User.email' name='email' ng-readonly='vm.showMode' ng-required='!showMode')
				div.input-container(flex=100)
					md-input-container
						label 役割
						md-select(ng-model='vm.user.User.role_id' name='role_id' ng-disabled='vm.showMode' ng-required='!showMode')
							md-option(ng-repeat='role in vm.roles' ng-value='role.Role.id') {{ role.Role.title }}
				div.input-container(flex=100)
					md-input-container
						label 発注者
						md-select(ng-model='vm.user.Customer.id' name='customer_id' ng-disabled='vm.showMode')
							md-option(ng-click='vm.user.Customer.id = null') 
							md-option(ng-repeat='customer in vm.customers' ng-value='customer.Customer.id') {{ customer.Customer.title }}
				div.input-container(flex=100)
					md-input-container
						label メイン工場
						md-select(ng-model='vm.user.User.main_factory_id' name='main_factory_id' ng-disabled='vm.showMode' ng-required='vm.check_is_franchise')
							md-option(ng-if="!vm.check_is_franchise")
							md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
				div.input-container(flex=100)
					md-input-container
						label パスワード
						input(type='password' ng-model='vm.user.User.password' name='password' ng-readonly='vm.showMode' ng-required='vm.isAdd')
				div.checkbox-container(flex=100): md-checkbox(ng-model='vm.user.User.is_direct_export' ng-disabled='vm.showMode') ダイレクト出荷
				div.input-container(flex=100)
					md-input-container
						label 外注先コード
						input(type='text' ng-model='vm.user.User.subcontractor_code' name='sub_code' ng-readonly='vm.showMode')
				div.checkbox-container(flex=100)
					label 使用プリンター
					md-checkbox.m-left-20(ng-model='vm.user.User.is_kornit' ng-disabled='vm.user.User.is_epson || vm.showMode' ) Kornit
					md-checkbox(ng-model='vm.user.User.is_epson' ng-disabled='vm.user.User.is_kornit || vm.showMode' ) EPSON
				div.checkbox-container(flex=100)
					label 発払い・代引き
					md-checkbox.m-left-20(ng-model='vm.user.User.is_sagawa' ng-disabled='vm.user.User.is_japan_post || vm.showMode' ) 佐川
					md-checkbox(ng-model='vm.user.User.is_japan_post' ng-disabled='vm.user.User.is_sagawa || vm.showMode' ) 日本郵便



		footer.text-right(flex='none')
			div(ng-hide='vm.showMode')
				div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
				div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
			//- div(ng-show='!vm.isAdd && vm.showMode')
				md-button.md-raised.md-primary パスワードリセット