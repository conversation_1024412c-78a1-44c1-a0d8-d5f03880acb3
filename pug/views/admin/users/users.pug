div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="end center")
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column')

		md-virtual-repeat-container(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form()
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
				div.buttons(flex="100" flex-gt-md="100")
					div.cell.cell-action(flex='none', layout='row', layout-align='end end')
						md-button.md-raised.md-primary.btn-print(ng-click="vm.downloadDeliveryCompany()") ダウンロード

			md-divider

			div.default-row(layout='row' layout-align='start center' md-virtual-repeat='user in vm.infiniteUsers' md-on-demand ng-show='user')
				div.cell.cell-info.simple(flex, layout='row', layout-align='start center')
					md-icon.user-self-icon(md-svg-src="img/icons/user.svg", ng-if='user.User.is_self')
					div {{ user.User.nickname }}
					div.secondary {{ user.User.username }}
				div.cell.cell-action(flex='none', layout='row', layout-align='end center')
					div.text-small(ng-if='user.User.last_login') 最新利用日時: {{ user.User.last_login | userTime }}
					div.text-small(ng-if='!user.User.last_login') まだログインしたことはありません
					div.buttons
						md-menu.select-role(md-position-mode="target-right target")
							md-button.md-raised.md-primary.btn-sm(md-menu-align-target type='button' ng-click='$mdOpenMenu($event)' ng-disabled='user.User.is_self')
								span {{ user.User.role_title }}
								md-icon(md-svg-src="img/icons/expand-arrow.svg")
							md-menu-content(width='3' md-menu-align-target)
								md-menu-item(ng-repeat='role in vm.userRoles')
									md-button(ng-disabled='role.Role.id == user.User.role_id' ng-click='vm.setUserRole(user, role)')
										span {{ role.Role.title }}
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(user)') 詳細

			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')
				
		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
			
admin-user-details(
	is-shown='vm.userDetails.isShown',
	active='vm.userDetails.active',
	type='vm.userDetails.type',
	on-close='vm.userDetails.onClose()',
	on-create='vm.userDetails.onCreate()'
	on-update='vm.userDetails.onUpdate(user)',
	on-delete='vm.userDetails.onDelete(userId)'
)