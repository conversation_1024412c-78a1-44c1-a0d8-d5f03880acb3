div.layout-column.block-min-width
    header(flex="none")
        div.block-wide(layout='row' layout-align="start center")
            div.cell(flex=35)
            div.cell(flex=10)
            div.cell(flex)
            div(flex='none', layout='row', layout-align='end center').user-info
                user-manage

    md-content.content(flex='grow' layout='column')

        md-virtual-repeat-container(flex='grow')

            div.search(layout='row')
                div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form()
                    i.icons8-search
                    input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
                    button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
            md-divider

            div.default-row(layout='row' layout-align='start center')
                div.cell.flex.col-300
                    b 置き場
                div.cell.flex.col-300.m-w-210
                    b 注文番号
                div.cell.flex.col-300
                    b 顧客
                div.cell.flex.col-300
                    b 納期
                div.cell.flex.col-300
                    b 合計数量
                div.cell.flex.col-300
                    b 状態
                div.cell.flex.col-300
                    b プランニング日
                div.cell.flex.col-300(ng-if='vm.have_tasks == 0')
                div.cell.flex.cell-action(layout='row', layout-align='end center')

            div.box_1(md-virtual-repeat='orderDetail in vm.infiniteOrderStatusSearch' md-on-demand ng-show='orderDetail')
                div.default-row(layout='row' layout-align='start center' )
                    div.cell.flex.col-300 {{ orderDetail.Order.place_title }}
                    div.cell.flex.col-300.m-w-210 {{ orderDetail.Order.number }}
                    div.cell.flex.col-300 {{ orderDetail.Order.customer }}
                    div.cell.flex.col-300 {{ orderDetail.Order.production_date_preferred }}
                    div.cell.flex.col-300 {{ vm.total_count }}
                    div.cell.flex.col-300 {{ orderDetail.Order.order_status  }}
                    div.cell.flex.col-300 {{ orderDetail.Order.planning_date  }}
                    div.cell.flex.col-300(ng-if='vm.have_tasks == 0')
                        .buttons(ng-style="{'min-width':'20%'}")
                            md-menu(md-position-mode="target-right target")
                                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                                    i.icons8-more
                                md-menu-content.menu-small(md-menu-align-target)
                                    md-menu-item
                                        md-button(ng-click='vm.cancelOrder()') 注文キャンセル
                    div.cell.flex.cell-action(layout='row', layout-align='end center')

                div.box_2(ng-style = "{'padding-left':'52px'}")
                    div.default-row(layout='row' layout-align='start center')
                        div.cell.flex(ng-style = "{'min-width':'8%' , 'flex': '0'}")
                            b
                        div.cell.flex(ng-style = "{'min-width':'16%', 'flex': '0'}")
                            b 品番
                        div.cell.flex(ng-style = "{'min-width':'12%', 'flex': '0'}")
                            b カラー
                        div.cell.flex(ng-style = "{'min-width':'10%', 'flex': '0'}")
                            b サイズ
                        div.cell.flex(ng-style = "{'min-width':'10%', 'flex': '0'}")
                            b 数量
                        div.cell.flex(ng-style = "{'min-width':'10%', 'flex': '0'}")
                            b 面
                        div.cell.flex(ng-style = "{'min-width':'14%', 'flex': '0'}")
                            b 状態
                        div.cell.flex.cell-action(layout='row', layout-align='end center')

                    div(ng-repeat='orderItemDetail in orderDetail.OrderItems')
                        div(ng-if="orderItemDetail.OrderItem.is_planning"  ng-repeat='taskDetail in orderItemDetail.Tasks.CurrentTaskStep')
                            div.default-row(layout='row' layout-align='start center' )
                                div.cell.flex(ng-style = "{'min-width':'8%'}" ng-if="taskDetail.display" ng-repeat='step in taskDetail.TaskStepItem')
                                    .images(layout='row', layout-align='start center' ng-style = "{'width':'32px'}")
                                        img(ng-src='{{ step.small_preview_image_url }}')
                                div.cell.flex(ng-style = "{'min-width':'16%'}" ng-if="taskDetail.display") {{ orderItemDetail.OrderItem.product_title }}
                                div.cell.flex(ng-style = "{'min-width':'12%'}" ng-if="taskDetail.display") {{ orderItemDetail.OrderItem.product_color_title }}
                                div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="taskDetail.display") {{ orderItemDetail.OrderItem.product_size_title }}
                                div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="taskDetail.display") {{ taskDetail.Task.task_index }}
                                div.cell.flex(ng-style = "{'min-width':'8%'}" ng-if="!taskDetail.display" ng-repeat='step in taskDetail.TaskStepItem')
                                    .images(layout='row', layout-align='start center' ng-style = "{'width':'32px'}")
                                        img(ng-src='{{ step.small_preview_image_url }}')
                                div.cell.flex(ng-style = "{'min-width':'16%'}" ng-if="!taskDetail.display")
                                div.cell.flex(ng-style = "{'min-width':'12%'}" ng-if="!taskDetail.display")
                                div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="!taskDetail.display")
                                div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="!taskDetail.display")
                                div.cell.flex(ng-style = "{'min-width':'10%'}") {{ taskDetail.title }}
                                div.cell.flex(ng-style = "{'min-width':'14%'}") {{ taskDetail.Task.status }}
                                div.cell.flex.cell-action(layout='row', layout-align='end center')
                                .buttons(ng-style="{'min-width':'20%'}")
                                    md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(taskDetail)') 詳細
                                    md-menu(md-position-mode="target-right target")
                                        md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                                            i.icons8-more
                                        md-menu-content.menu-small(md-menu-align-target)
                                            md-menu-item
                                                md-menu(md-offset="-20 0")
                                                    a.sub-menu(ng-click='$mdOpenMenu($event)') ステータス変更
                                                    md-menu-content.menu-small(md-menu-align-target)
                                                        md-menu-item(ng-repeat='status in vm.statusChange')
                                                            md-button(ng-model='status.key' ng-click='vm.changeStatusTask(taskDetail, status.key)') {{ status.value }}

                        div(ng-if="!orderItemDetail.OrderItem.is_planning" ng-repeat='orderItemTask in orderItemDetail.OrderItem.OrderItemTask')
                                div.default-row(layout='row' layout-align='start center' )
                                    div.cell.flex(ng-style = "{'min-width':'8%'}" ng-if="orderItemTask.display")
                                        .images(layout='row', layout-align='start center' ng-style = "{'width':'32px'}")
                                            img(ng-src='{{ orderItemTask.small_preview_image_url }}')
                                    div.cell.flex(ng-style = "{'min-width':'16%'}" ng-if="orderItemTask.display") {{ orderItemDetail.OrderItem.product_title }}
                                    div.cell.flex(ng-style = "{'min-width':'12%'}" ng-if="orderItemTask.display") {{ orderItemDetail.OrderItem.product_color_title }}
                                    div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="orderItemTask.display") {{ orderItemDetail.OrderItem.product_size_title }}
                                    div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="orderItemTask.display") {{ orderItemDetail.OrderItem.quantity }}
                                    div.cell.flex(ng-style = "{'min-width':'8%'}" ng-if="!orderItemTask.display")
                                        .images(layout='row', layout-align='start center' ng-style = "{'width':'32px'}")
                                            img(ng-src='{{ orderItemTask.small_preview_image_url }}')
                                    div.cell.flex(ng-style = "{'min-width':'16%'}" ng-if="!orderItemTask.display")
                                    div.cell.flex(ng-style = "{'min-width':'12%'}" ng-if="!orderItemTask.display")
                                    div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="!orderItemTask.display")
                                    div.cell.flex(ng-style = "{'min-width':'10%'}" ng-if="!orderItemTask.display")
                                    div.cell.flex(ng-style = "{'min-width':'10%'}") {{ orderItemTask.product_color_side }}
                                    div.cell.flex(ng-style = "{'min-width':'14%'}") {{ orderItemDetail.OrderItem.status }}
                                    div.cell.flex.cell-action(layout='row', layout-align='end center' ng-style="{'min-width':'20%'}")

            div.pagination-loading(ng-show='vm.pagination.loading')
                md-progress-circular(md-diameter='40' md-mode='indeterminate')

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        .nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

admin-production-task-details(
    is-shown='vm.taskDetails.isShown',
    active='vm.taskDetails.active',
    on-close='vm.onTaskClose()',
    on-delete='vm.onTaskDelete(taskId)'
    guid ='tasks-id'
)