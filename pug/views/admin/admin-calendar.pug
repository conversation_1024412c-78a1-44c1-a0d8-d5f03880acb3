md-sidenav.management-sidenav.md-sidenav-right(md-component-id='admin-calendar').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide.admin-calendar
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 営業カレンダー
		.content-calendar
			md-content
				div.layout-align-center-center.layout-wrap(layout="row")
					div(flex="90")
						multiple-date-picker(ng-model="vm.selectedDays"
						show-days-of-surrounding-months="true"
						sunday-first-day="true"
						month-changed="vm.onMonthChanged"
						day-click="vm.dayClick"
						highlight-days="vm.highlightDays")
				br
				div.layout-align-center-center.layout-wrap(layout="row")
					md-input-container(flex="90" ng-class="{'md-input-has-value':vm.selectedDayOfWeek.length}")
						label 営業なし曜日
						md-select(ng-model="vm.selectedDayOfWeek" ng-change="vm.onSelectedWeekdaysChange()" multiple md-selected-text="vm.getSelectedText()" aria-label="曜日")
							md-optgroup(label="その他")
								md-option(ng-repeat="(key,day) in vm.typesDay" ng-value="day" ng-click="vm.addOrDeleteSpecial(day);vm.typeDaysChange(day);") {{::day.name}}
							md-optgroup(label="曜日")
								md-option(ng-repeat="(key,day) in vm.daysOfWeek" ng-value="day" ng-click="vm.addOrDelete(day);vm.weekDaysChange(day);") {{::day.name}}
			//div.layout-align-center-center.date-container(layout="row")
				div.form-content(flex="95")
					form(name="RuleForm" ng-submit="vm.addNewRule(RuleForm)")
						div(layout="row")
							md-input-container(flex="40")
								label
								input.date-rule.no-border-color(type="text" placeholder="Change day type" ng-model="vm.newRule.date" name="date" readonly)
							md-input-container(flex="5")
							md-input-container(flex="35" ng-class="{'md-input-has-value':vm.newRule.type}")
								p.no-border-color.type-day.md-input.text-right(type="text" ng-show="vm.newRule.type") {{vm.newRule.type=="include"?"This day is off":"This is work day"}}
							md-input-container(flex="10")
								md-button.btn-save-rule.btn-sm(type="submit" ng-class="{'is-active':vm.newRule.date}" class="md-primary" aria-label="add rule")
									md-icon.layout-align-center-center(layout="row") 保存
							md-input-container(flex="10")
								md-button.btn-close-rule.btn-sm(type="button" ng-show="vm.newRule.date" ng-click="vm.hideForm()" aria-label="add rule")
									md-icon.layout-align-center-center(layout="row")
										i.icons8-delete-2
			div.layout-align-center-center(layout="row")
				md-content.content-list(flex)
					md-list
						md-list-item(ng-repeat="rule in vm.rules|filter:{ProductionCalendarRule:{key:'date'}}|orderBy:' -ProductionCalendarRule.value'")
							p(flex="50") {{::rule.ProductionCalendarRule.value}}
							p(flex="40").text-right {{::rule.ProductionCalendarRule.type=="include"?"営業なし":"営業あり"}}
							md-button.btn-sm.close-btn(ng-click="vm.deleteRuleFromTable(rule)" ng-class="{'is-loading':rule.loading}" class="md-secondary" aria-label="delete rule")
								md-icon.layout-align-center-center(layout="row")
									i.icons8-delete-2
			//div.layout-align-center-center.layout-wrap(layout="row")
				br
				div(flex="80")
					md-divider
				br
				div(flex="80")
					md-checkbox(ng-model="vm.showRules") Show rules
				div(flex="80" ng-show="vm.showRules")
					md-tabs.table-rule
						md-tab(label="Days of week rule")
							md-content
								md-list
									md-list-item(ng-repeat="rule in vm.rules|filter:{ProductionCalendarRule:{key:'!date'}}")
										div(flex="40") {{::rule.ProductionCalendarRule.key}}
										div(flex="30") {{::rule.ProductionCalendarRule.type}}
										div(flex="30")
											md-button(ng-click="vm.deleteRuleFromTable(rule)")
												i.icons8-delete
						md-tab(label="Dates rule")

