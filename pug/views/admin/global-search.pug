section.block-wide.global-search-content(layout='column')


	header.section-header(flex='none' layout='row')
		div.layout-row.buttons(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
			h2 検索
		div.layout-row.buttons(flex layout-align='end center')
			div.search-form(focusable-search): form()
				input.search-input(id="globalSearch" type='text' ng-model='vm.searchQuery' ng-change="vm.globalSearch()")
				i.icons8-search


	md-content.content.no-virtual-repeat(flex='grow' layout='column')

		// customers
		section(ng-if="vm.results.customers.total_count>0")
			md-subheader.sticky-header
				div.layout-row.layout-align-center-center
					div.section-line(flex="100")
					div.layout-align-center-center(flex="10" layout="column")
						md-button.md-button.md-raised.section-text(slide-toggle="#customers" ng-click="vm.showCustomers=!vm.showCustomers;" layout="row")
							span お客様
							md-icon.collapse-btn( ng-class="{'close':!vm.showCustomers}" md-svg-src="img/icons/expand-arrow.svg")
					div.section-line(flex="100")

			div.slideable(id="customers" ng-style="{'height':(vm.results.customers.object.length*53+96)+'px'}")
				md-list
					md-list-item.default-row(ng-repeat='item in vm.results.customers.object')

						div.layout-row.layout-align-center-center(flex="100")
							div.cell.cell-data(flex='80' layout='row' layout-align='start center' layout-wrap)
								.data-rowed(layout='row', layout-align='start center')
									div {{ item.Customer.title }}

							div.cell.cell-action(flex=20, layout='row', layout-align='end center')
								div.buttons
									md-button.md-primary.md-raised.btn-sm(ng-click='::vm.viewDetailsCustomer(item)') 詳細
				br
				div(ng-if="vm.results.customers.object.length<vm.results.customers.total_count")
					div.layout-row.layout-align-center-center
						md-button.md-primary.md-raised.btn-sm(ng-click="vm.showMoreInSection('customers')") もっと
				br
				br

		// orders
		section(ng-if="vm.results.orders.total_count>0")
			md-subheader.sticky-header
				div.layout-row.layout-align-center-center
					div.section-line(flex="100")
					div.layout-align-center-center(flex="10" layout="column")
						md-button.md-button.md-raised.section-text(slide-toggle="#orders" ng-click="vm.showOrders=!vm.showOrders;" layout="row")
							span 注文
							md-icon.collapse-btn(ng-class="{'close':!vm.showOrders}" md-svg-src="img/icons/expand-arrow.svg")
					div.section-line(flex="100")
			div.slideable(id="orders" ng-style="{'height':(vm.results.orders.object.length*53+96)+'px'}")
				md-list
					md-list-item.default-row(ng-repeat='item in vm.results.orders.object')

						div.layout-row.layout-align-center-center(flex="100")
							div.cell.cell-data(flex=80 layout='row' layout-align='start center' layout-wrap)
								.data-rowed(layout='row', layout-align='start center')
									div {{ ::item.Order.number  }} [{{::item.Order.customer_order_number}}] {{ ::item.Customer.title }}

							div.cell.cell-action(flex=20, layout='row', layout-align='end center')
								div.buttons
									md-button.md-primary.md-raised.btn-sm(ng-click='vm.viewOrderDetail(item)') 詳細
				br
				div(ng-if="vm.results.orders.object.length<vm.results.orders.total_count")
					div.layout-row.layout-align-center-center
						md-button.md-primary.md-raised.btn-sm(ng-click="vm.showMoreInSection('orders')") もっと
				br
				br

		// tasks
		section(ng-if="vm.results.tasks.total_count>0")
			md-subheader.sticky-header
				div.layout-row.layout-align-center-center
					div.section-line(flex="100")
					div.layout-align-center-center(flex="10")
						md-button.md-button.md-raised.section-text(slide-toggle="#tasks" ng-click="vm.showTasks=!vm.showTasks;" layout="row")
							span タスク
							md-icon.collapse-btn(ng-class="{'close':!vm.showTasks}" md-svg-src="img/icons/expand-arrow.svg")
					div.section-line(flex="100")

			div.slideable(id="tasks" ng-style="{'height':(vm.results.tasks.object.length*53+96)+'px'}")
				md-list
					md-list-item.default-row(ng-repeat='item in vm.results.tasks.object')

						div.layout-row.layout-align-start-center(flex="100")
							div.cell.cell-data(flex=90 layout='row' layout-align='start center' layout-wrap)
								.data-rowed(layout='row', layout-align='start center')
									div 生産プラン {{::item.Task.date}} {{::item.Product.title}} 置き場 {{::item.Place.title}} 号機 {{::item.Printer.title}} 注文 {{::item.Order.number}} [{{::item.Order.customer_order_number}}] {{::item.Customer.title}}

							div.cell.cell-action(flex=10, layout='row', layout-align='end center')
								div.buttons
									md-button.md-primary.md-raised.btn-sm(ng-click='::vm.viewDetailsTask(item)') 詳細
				br
				div(ng-if="vm.results.tasks.object.length<vm.results.tasks.total_count")
					div.layout-row.layout-align-center-center
						md-button.md-primary.md-raised.btn-sm(ng-click="vm.showMoreInSection('tasks')") もっと
				br
				br

		// places
		section(ng-if="vm.results.places.total_count>0")
			md-subheader.sticky-header
				div.layout-row.layout-align-center-center
					div.section-line(flex="100")
					div.layout-align-center-center(flex="10")
						md-button.md-button.md-raised.section-text(slide-toggle="#places" ng-click="vm.showPlaces=!vm.showPlaces;" layout="row")
							span 置き場
							md-icon.collapse-btn(ng-class="{'close':!vm.showPlaces}" md-svg-src="img/icons/expand-arrow.svg")
					div.section-line(flex="100")
			div.slideable(id="places" ng-style="{'height':(vm.results.places.object.length*53+96)+'px'}")
				md-list
					md-list-item.default-row(ng-repeat='item in vm.results.places.object')

						div.layout-row.layout-align-start-center(flex="100")
							div.cell.cell-data(flex=100 layout='row' layout-align='start center' layout-wrap)
								.data-rowed(layout='row', layout-align='start center')
									div 生産プラン {{::item.Task.date}} {{::item.Product.title}} 置き場 {{::item.Place.title}} 注文 {{::item.Order.number}} [{{::item.Order. customer_order_number}}] {{::item.Customer.title}}

				br
				div(ng-if="vm.results.places.object.length<vm.results.places.total_count")
					div.layout-row.layout-align-center-center
						md-button.md-primary.md-raised.btn-sm(ng-click="vm.showMoreInSection('places')") もっと
				br
				br

		// printers
		section(ng-if="vm.results.printers.total_count>0")
			md-subheader.sticky-header
				div.layout-row.layout-align-center-center
					div.section-line(flex="100")
					div.layout-align-center-center(flex="10")
						md-button.md-button.md-raised.section-text(slide-toggle="#printers" ng-click="vm.showPrinters=!vm.showPrinters;" layout="row")
							span 号機
							md-icon.collapse-btn(ng-class="{'close':!vm.showPrinters}" md-svg-src="img/icons/expand-arrow.svg")
					div.section-line(flex="100")
			div.slideable(id="printers" ng-style="{'height':(vm.results.printers.object.length*53+96)+'px'}")
				md-list
					md-list-item.default-row(ng-repeat='item in vm.results.printers.object')

						div.layout-row.layout-align-start-center(flex="100")
							div.cell.cell-data(flex=100 layout='row' layout-align='start center' layout-wrap)
								.data-rowed(layout='row', layout-align='start center')
									div 生産プラン {{::item.Task.date}} {{::item.Product.title}} 号機 {{::item.Printer.title}} 注文 {{::item.Order.number}} [{{::item.Order. customer_order_number}}] {{::item.Customer.title}}

				br
				div(ng-if="vm.results.printers.object.length<vm.results.printers.total_count")
					div.layout-row.layout-align-center-center
						md-button.md-primary.md-raised.btn-sm(ng-click="vm.showMoreInSection('printers')") もっと
				br
				br

		section(ng-if="vm.pagination.empty")
			div.nothing-found-msg
				div.layout-row.layout-align-center-center 何も見つかりませんでした

	div.pagination-loading(ng-show="vm.pagination.loading")
		md-progress-circular(md-diameter='40' md-mode='indeterminate')


admin-production-task-details(
is-shown='vm.taskDetails.isShown',
active='vm.taskDetails.active',
on-close='vm.taskDetails.onTaskClose()',
on-delete='vm.taskDetails.onTaskDelete(taskId)'
guid ='task-global-search-id'
)

client-details(
is-shown='vm.customerDetails.isShown',
active='vm.customerDetails.active',
type='vm.customerDetails.type',
on-close='vm.customerDetails.onClose()'
on-update='vm.customerDetails.onUpdate(customer)',
on-delete='vm.customerDetails.onDelete(customerId)'
guid='client-global-search-id'
)

customer-order-detail.order-detail-wrapper.block-wide(open-on='vm.viewOrderDetail' guid='order-list-global-search')
