md-dialog.include-option(aria-label="large image" ng-style='{"background-color" : "rgb(250,250,250)", "width" :"auto", "height" : "auto"}')
    md-dialog-content(ng-style='{"padding" : "0 30px 30px"}')
        div.md-dialog-content(ng-style="{'margin': 'auto', 'max-width': '100%'}")
            md-content.content(flex='grow' layout='column')
                div.box-dialog
                    div.layout-row.flex-100
                        md-input-container.option-customers-container
                            div.check-box-alone
                                div.head-con(style='margin-top: 22px;') データ種類
                                div.list-checkbox.item-checkbox
                                    md-radio-group
                                        md-radio-button.m-left-20.m-bottom-0(ng-value='specialType' aria-label="{{export_data.selectedType.ModeSubTab.title}}"  ng-click='selectCSVType(export_data.selectedType.ModeSubTab.id)' ng-style={"display": "inline-block"} ng-disabled='vm.showMode' ng-if="showSubModeTab(export_data.selectedType.ModeSubTab.title)") {{export_data.selectedType.ModeSubTab.title}}
                                        md-radio-button.m-left-20.m-bottom-0(ng-repeat='csvType in export_data.csv_types' ng-value='csvType.ModeSubTab.id' ng-click='selectCSVType(csvType.ModeSubTab.id)' ng-style={"display": "inline-block"} ng-disabled='showMode' ng-if="showSubModeTab(csvType.ModeSubTab.title)") {{csvType.ModeSubTab.title}}
                    div.category-box
                        div.layout-row.flex-100.layout-padding
                            div.box-conditional
                                div.date-con
                                    div.time-d-flex
                                        div.head-con(style='margin-top: 10px; margin-right:42px') 日付
                                        div.start-time-area
                                            div.select-date
                                                div(pick-date-header)
                                                    div.border-order-date {{ startDate | dateJapan : 'year/month/day' }}
                                                    md-datepicker(ng-model='startDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                                        span.m-left-50.m-right-50.m-top-8 ~
                                        div.start-time-area
                                            div.select-date
                                                div(pick-date-header)
                                                    div.border-order-date {{ endDate| dateJapan : 'year/month/day' }}
                                                    md-datepicker(ng-model='endDate' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)
                                        div.radio-con-list(ng-if="csv_type == 8" style='margin-left: 36px;')
                                            md-radio-group
                                                md-radio-button.m-left-20.m-0.m-top-4(ng-value='specialValue' aria-label="注文日" ng-click='selectDateType(1)' ng-style={"display": "inline-block"}) 注文日
                                                md-radio-button.m-left-20.m-0.m-top-4(ng-value='2' ng-click='selectDateType(2)' ng-style={"display": "inline-block"}) 発送日
                                div.checkbox-con(ng-if="csv_type != 7 && csv_type != 8 && export_data.external == false")
                                    div.head-con(style='margin-top: 10px;') カテゴリ
                                    div.list-checkbox
                                        md-menu-item(ng-repeat='printerType in export_data.printer_types')
                                            md-checkbox(aria-label='select {{ printerType.PrinterType.id }}' ng-click='selectPrinterType(printerType.PrinterType.id)' name='printer_id' ng-disabled='showMode') {{ printerType.PrinterType.title }}
                                div.select-box(ng-if="csv_type != 7 && csv_type != 8 && csv_type != 21 && export_data.external == false ")
                                    div.head-con 工場
                                    div.select-box-list(style='margin-left: 6px;')
                                        md-input-container
                                            md-select(ng-model='vm.export_data.factory_id' name='factory_id')
                                                md-option(ng-repeat='factory in export_data.factories' ng-value='factory.id' ng-selected='factory.id == export_data.factorySelected' ng-click='selectFactoryType(factory.id)') {{ factory.title }}
                                div.checkbox-con(ng-if="csv_type == 8 && export_data.external == false")
                                    div.head-con(style='margin-top: 10px; margin-right: 48px;') 顧客
                                    div.list-checkbox
                                        md-menu-item(ng-repeat='customer in export_data.customers')
                                            md-checkbox(aria-label='select {{ customer.Customer.id }}' ng-click='selectCustomer(customer.Customer.id)' name='customer_id' ng-disabled='showMode') {{ customer.Customer.title }}
                                div.checkbox-con(ng-if="export_data.external == true")
                                    div.head-con(style='margin-top: 10px; margin-right: 48px;') 顧客
                                    div.list-checkbox
                                        md-radio-group(ng-repeat='customer in export_data.customers' ng-model='customer.Customer.id')
                                            md-radio-button.m-0.m-top-4(ng-value='customer.Customer.id' name="customer_id" ng-style={"display": "inline-block"}) {{customer.Customer.title}}
                                div.title 抽出条件

                        md-button.button-download.md-raised.md-accent.btn-print.md-button.md-ink-ripple(ng-click='downloadCSV()'): span CSV出力

                style(type="text/css").
                    .box-dialog  {
                        position: relative;
                    }
                    .box-dialog .category-box {
                        display: flex;
                        align-items: flex-end;
                    }
                    .button-download {
                      width: 30px;
                      height: 30px;
                    }
                    md-content {
                        min-width: 50px;
                    }

                    .layout-padding {
                        padding-left: 32px;
                    }
                    .box-conditional {
                        position: relative;
                        border: 1px solid #000;
                        padding: 35px;
                        width: 700px;
                        height: 232px;
                    }

                    .box-conditional .checkbox-con {
                        margin-bottom: 20px;
                        margin-top: 20px;
                    }

                    .box-conditional .checkbox-con, .box-conditional .select-box {
                        display: flex;
                    }

                    .box-conditional .list-checkbox {
                        grid-template-columns: repeat(3, minmax(0, 1fr));
                        display: flex;
                        flex-wrap: wrap;
                        max-width: 552px;
                    }

                    .box-conditional .list-checkbox md-menu-item {
                        width: 33.3333333333%;
                    }

                    .box-conditional .title {
                        position: absolute;
                        left: -40px;
                        top: -18px;
                        background-color: #fff;
                        padding: 15px 10px;
                    }

                    .box-conditional .head-con {
                        margin-right: 20px;
                    }

                    .box-conditional .select-box .head-con {
                        margin: 7px 20px;
                    }

                    .check-box-alone {
                        display: flex;
                        width: 100%;
                    }

                    .check-box-alone .item-checkbox {
                        border: 1px solid #000;
                        width: max-content;
                        padding: 15px 20px 0px 0px;
                        margin-left: 10px;
                    }
                    .m-top-8 {
                        margin-top: 8px;
                    }

                    .m-top-4 {
                        margin-top: 4px;
                    }

                    md-input-container {
                        padding: 0px;
                        margin: 0px;
                    }

                    md-select .md-select-value {
                        border-bottom-style: none !important;
                    }