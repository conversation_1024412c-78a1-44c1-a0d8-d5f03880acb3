div.layout-column.block-min-width.ct-ha
    header(flex="none")
        div.block-wide(layout='row' layout-align="end center")
            div(flex='none', layout='row', layout-align='end center').user-info
                user-manage

    md-content.content(flex='grow' layout='column')

        md-virtual-repeat-container(flex='grow')

            md-divider
            div.layout-row.flex-100.m-top-20
                div.option-customers-container
                    div.condition-title 注文日付
                    div.time-d-flex
                        div.start-time-area
                            div.select-date
                                div(pick-date-header)
                                    div.border-order-date {{ vm.startDate | dateJapan : 'year/month/day' }}
                                    md-datepicker(ng-model='vm.startDate' md-placeholder='Enter date' md-open-on-focus)
                            div.select-hour
                                md-select(ng-model='vm.startHour' name='start_hour' required)
                                    md-option(ng-repeat='hour in vm.body_order.hours' ng-value='hour') {{ hour }}
                                span 時
                            div.select-minute
                                md-select(ng-model='vm.startMinute' name='start_minute' required)
                                    md-option.width-option(ng-repeat='minute in vm.body_order.minutes' ng-value='minute') {{ minute }}
                                span 分
                        span.m-left-50.m-right-50.m-top-30 ~
                        div.start-time-area
                            div.select-date
                                div(pick-date-header)
                                    div.border-order-date {{ vm.endDate| dateJapan : 'year/month/day' }}
                                    md-datepicker(ng-model='vm.endDate'  md-placeholder='Enter date' md-open-on-focus)
                            div.select-hour
                                md-select(ng-model='vm.endHour' name='end_hour' required)
                                    md-option(ng-repeat='hour in vm.body_order.hours' ng-value='hour') {{ hour }}
                                span 時
                            div.select-minute
                                md-select(ng-model='vm.endMinute' name='end_minute' required)
                                    md-option(ng-repeat='minute in vm.body_order.minutes' ng-value='minute') {{ minute }}
                                span 分

            div.layout-row.flex-100
                md-input-container.option-customers-container
                    div.condition-title クライアント
                    div.option-customers(ng-repeat='customer in vm.body_order.customers')
                        md-checkbox(ng-model='customer.selectedCustomer' aria-label='select {{ customer.Customer.id }}' name='customer_id' ng-disabled='vm.showMode') {{customer.Customer.title}}

            div.layout-row.flex-100
                md-input-container.option-customers-container
                    div.condition-title メーカー
                    div.option-customers(ng-repeat='source in vm.body_order.sources')
                        md-checkbox(ng-model='source.selectedSource' aria-label='select {{ source.ProductLinkedSource.id }}' name='source_id' ng-disabled='vm.showMode') {{source.ProductLinkedSource.title}}

            div.layout-row.flex-100
                a.btn-download-csv.m-top-25.m-bottom-25(href ng-click='vm.downloadBodyOrderCSV()') 上記検索結果のガーメント発注CSVをダウンロード
            div.layout-row.flex-100
                a.btn-download-csv(href ng-click='vm.downloadItemPendingCSV()') 保留中アイテムリストのCSVファイルをダウンロードする

            div.layout-row.flex-100
                md-input-container.option-customers-container
                    div.condition-title 過去５件のDL履歴
                    table.history-table
                        thead
                            tr
                                th ダウンロード処理日
                                th ファイルダウンロード
                        tbody
                            tr(ng-repeat='history in vm.body_order.history' ng-show='history')
                                td {{history.HistoryBodyOrder.created_at}}
                                td
                                    a.link(href='{{ history.HistoryBodyOrder.url  }}', target='_blank') {{history.HistoryBodyOrder.url_name}}

        button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

        style(type="text/css").
            md-select-menu md-content {
                min-width: 50px;
            }