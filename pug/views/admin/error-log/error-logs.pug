div.layout-column.block-min-width
	header(flex="none")
		div.block-wide(layout='row' layout-align="start center")
			div.cell(flex=35) ファイル名
			div.cell(flex=10) 行
			div.cell(flex) エラー
			div(flex='none', layout='row', layout-align='end center').user-info
				user-manage

	md-content.content(flex='grow' layout='column')

		md-virtual-repeat-container(flex='grow')

			div.search(layout='row')
				div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form()
					i.icons8-search
					input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
					button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
			md-divider

			div(md-virtual-repeat='log in vm.infiniteLogs' md-on-demand ng-show='log')
				div.default-row.chart(layout='row' layout-align='start center' ng-if='log.ErrorLogUploadCsv.date')
					div.cell {{ log.ErrorLogUploadCsv.date }}
				div.default-row(layout='row' layout-align='start center')
					div.cell(flex=35) {{ log.ErrorLogUploadCsv.file_name }}
					div.cell(flex=10) {{ log.ErrorLogUploadCsv.row }}
					div.cell(flex) {{ log.ErrorLogUploadCsv.error }}
					div.cell.cell-action(flex='none', layout='row', layout-align='end center')
						div.text-small {{ log.ErrorLogUploadCsv.time }}

			div.pagination-loading(ng-show='vm.pagination.loading')
				md-progress-circular(md-diameter='40' md-mode='indeterminate')

		button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

		.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした