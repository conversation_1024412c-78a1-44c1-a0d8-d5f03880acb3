div.search(layout='row', style='position: fixed; background: #fafafa; z-index: 11; width: 89%; padding: 0 30px 0 57px;')
	div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search flex='15'): form
		i.icons8-search
		input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
		button.reset(type='button' ng-click="vm.clearSearch()" ng-style='{"left" : 200}'): i.icons8-delete-2
	div.cell.text-center(flex='30' style='margin-top:15px;')
		md-button.md-raised.md-primary.btn-sm(virtual-repeat-scroll-top-custom) トップに戻る
		md-button.md-raised.md-primary.btn-sm(virtual-repeat-scroll-bottom-custom) 最下部に移動
	div.cell.text-center(flex='20' ng-style={"padding-top": "25px"})
		md-menu(md-position-mode="target-right target")
			div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeCustomer.Customer.title }}
			md-menu-content.menu-small.custom-checkbox(md-menu-align-target style='width:175px;')
				md-menu-item(ng-repeat='customer in vm.customerFilter')
					md-checkbox(ng-model='customer.selected', ng-change='vm.filterCustomer(customer.Customer.id)') {{ customer.Customer.title }}

	div.cell.text-center(flex='15' style='padding-top: 25px;background: #f5f5f5; margin-left:79px')
		md-menu(md-position-mode="target-right target")
			div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeFilter.value }}
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item(ng-repeat='filter in vm.statusFilter')
					md-button(ng-click='vm.setActiveFilter(filter)' ng-disabled='filter.key === vm.activeFilter.key') {{ filter.value }}

	div.cell.heading-btns(flex layout='row', layout-align='end center')
		md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
		md-menu(md-position-mode="target-right target")
			md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
				i.icons8-more
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') ステータス変更
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='status in vm.statusChange')
								md-button(ng-model='status.key' ng-click='vm.changeStatusSelected(status.key)') {{ status.value }}
				md-menu-item
					md-button(ng-click='vm.printQrSelected($event)'): span QRコード印刷
				md-menu-item
					md-button(ng-click='vm.deleteTaskSelected()'): span タスク削除
				md-menu-item
					md-button(ng-click='vm.downloadBusinessCSV()'): span CSV抽出
md-virtual-repeat-container.content(flex='grow' )

	div.search(layout='row')

	md-divider

	div.default-row(md-virtual-repeat='task in vm.infiniteTasks' md-on-demand ng-show='task' ng-switch="task.rowType")

		//- single
		div.layout-row.layout-align-center-center(ng-switch-when="single")
			.cell.cell-data(flex='70', layout='row', layout-align='start center')
				.checkbox
					md-checkbox(ng-model='task.selected', aria-label='label')
				.images(layout='row', layout-align='start center')
					.image(ng-repeat='step in task.steps')
						img(ng-src='{{ step.TaskStep.image_url }}')
				.data-rowed.rowed-inline
					div {{ task.PlaceJoin.title }}
					.divider
					div {{ task.Order.number }}
					.divider
					div {{ task.Product.title }}
					.divider
					div {{ task.ProductColor.title }}
					.divider
					div {{ task.ProductSize.title }}
					.divider
					div {{ task.Customer.title }}
					.divider.addition
					.addition {{ task.Order.production_date_preferred }}
					.divider.addition
					.addition 合計数量: {{ task.Order.total_quantity }}
			.cell.text-center(flex='15') {{ task.Task.status }}
			.cell.cell-action(flex, layout='row', layout-align='end center')
				.buttons
					md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewDetails(task)') 詳細
					md-menu(md-position-mode="target-right target")
						md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							i.icons8-more
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') ステータス変更
									md-menu-content.menu-small(md-menu-align-target)
										md-menu-item(ng-repeat='status in vm.statusChange')
											md-button(ng-model='status.key' ng-click='vm.changeStatusTask(task, status.key)') {{ status.value }}
							md-menu-item
								md-button(ng-click='vm.printQr($event,task.Task.id)'): span QRコード印刷

		//- outer
		div.layout-row.layout-align-center-center(ng-switch-when="outer")
			.cell.cell-data(flex, layout='row', layout-align='start center')
				.checkbox
					md-checkbox(ng-model='task.selected', aria-label='label')
				.data-rowed(layout='row', layout-align='start center')
					div {{ order.uid }}

		//- inner
		div.layout-row.layout-align-center-center(ng-switch-when="inner")
			.cell.cell-data.indented(flex='60', layout='row', layout-align='start center')
				.checkbox
					md-checkbox(ng-model='task.selected', aria-label='label')
				.images(layout='row', layout-align='start center')
					.image(ng-repeat='src in order.images')
						img(ng-src='{{ src }}')
				.data-rowed(layout='row', layout-align='start center')
					div {{ order.type }}
					.divider
					div {{ order.size }}
					.divider
					div {{ order.quantity }}
					.divider.addition
					.addition {{ order.date }}
			.cell.text-center(flex='15') {{ order.status }}
			.cell.cell-action(flex='flex', layout='row', layout-align='end center')
				.text-small {{ order.created_at }}
				.buttons
					md-button.md-raised.md-primary.btn-sm 詳細

	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

.highlighted-column-container(flex layout='row' ng-hide='vm.pagination.empty'): .highlighted-column(flex=15 flex-offset=70)


.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 400px;margin-top:24px")
	md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}


admin-production-task-details(
	is-shown='vm.taskDetails.isShown',
	active='vm.taskDetails.active',
	on-close='vm.onTaskClose()',
	on-delete='vm.onTaskDelete(taskId)'
	guid ='tasks-id'
)