md-sidenav.management-sidenav.md-sidenav-right(md-component-id='vm.guid' eval-attr-as-expr="mdComponentId").sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 タスク情報
			div.sidenav-actions(ng-if='!vm.viewMode')
				button.btn-qr.simple-btn(type='button' ng-click='vm.toggleQr()'): md-icon(md-svg-src='img/icons/qr-code.svg', aria-label='qr code')
				button.btn-delete.simple-btn(type='button', ng-click='vm.confirmDelete($event)'): i.icons8-delete
		
			.qr-popup(ng-show='vm.qrShown')
				qrcode(version='6', error-correction-level='M', size='130', data='{{ vm.activeStep.code }}')
			.qr-popup-backdrop(ng-show='vm.qrShown', ng-click='vm.toggleQr()')
		
		md-content(flex='grow')
			product-sides-images(sides='vm.sides', ng-if='vm.sides.length', on-select='vm.onSelect(active)')
			div.flex(ng-if='vm.task.Task.is_3d' style="justify-content: flex-end;")
				md-button.md-raised.md-primary.btn-sm(ng-click="vm.download3D(vm.task.CurrentTaskStep.OrderItemTask.original_content_image_url)") 3D Tripoをダウンロードする
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label 注文番号
						input(type='text' ng-value='vm.task.Order.number' readonly)
				div.input-container(flex=100 ng-if='vm.task.Order.customer_email !== null')
					md-input-container
						label お客様のEmail
						input(type='text' ng-value='vm.task.Order.customer_email' readonly)
				div.input-container(flex=50)
					md-input-container
						label 完成希望日
						input(type='text' ng-value='vm.task.Order.production_date_preferred' readonly)
				div.input-container(flex=50)
					md-input-container
						label ステータス
						input(type='text' ng-value='vm.task.Task.status' readonly)
				div.input-container(flex=100)
					md-input-container
						label 発注者
						input(type='text' ng-value='vm.task.Customer.title' readonly)
				div.input-container(flex=100)
					md-input-container
						label 決済方法
						input(type='text' ng-value='vm.task.PaymentJoin.title' readonly)
				div.input-container(flex=100)
					md-input-container.with-clipboard
						label QR code
						input(type='text' ng-value='vm.activeStep.code' readonly)
						button.clipboard-button(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ vm.activeStep.code }}")
							i.icons8-copy
						button.btn-dummy
							md-tooltip(md-visible='vm.clipSuccessMsg') Copied!
			
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=100)
					md-input-container
						label 商品
						input(type='text' ng-value='vm.task.Product.title' readonly)
				div.input-container(flex=50)
					md-input-container
						label カラー
						input(type='text' ng-value='vm.task.ProductColor.title' readonly)
				div.input-container(flex=50)
					md-input-container
						label サイズ
						input(type='text' ng-value='vm.task.ProductSize.title' readonly)

			div.form.default-form.bottom-margin(layout='row' layout-wrap) QRシール発行履歴
				div.wrap-product-side-history(ng-repeat='task in vm.task.steps')
					product-sides-history(ng-if='task.TaskStep.user_qr_print !== null')
						div {{task.TaskStep.title}}
						div(ng-style={"width": "180px", "margin-left": "80px"}) {{task.TaskStep.user_qr_print}}
						div(ng-style={"width": "100px","margin-left": "58px"}) {{task.TaskStep.qr_print_date}}

			div.form.default-form.bottom-margin(layout='row' layout-wrap) プリント履歴
				div.wrap-product-side-history(ng-repeat='side in vm.sides')
					product-sides-history(ng-if='side.user_print !== null')
						div {{side.title}}
						div(ng-style={"width": "180px", "margin-left": "80px"}) {{side.user_print}}
						div(ng-style={"width": "100px","margin-left": "58px"}) {{side.printers_title}}

			div.form.default-form.bottom-margin(layout='row' layout-wrap) 検品履歴
				div.wrap-product-side-history(ng-repeat='task in vm.task.steps')
					product-sides-history(ng-if='task.TaskStep.user_checking !== null')
						div {{task.TaskStep.title}}
						div(ng-style={"width": "180px", "margin-left": "80px"}) {{task.TaskStep.user_checking}}
						div(ng-style={"width": "100px","margin-left": "58px"}) {{task.TaskStep.created_at}}
