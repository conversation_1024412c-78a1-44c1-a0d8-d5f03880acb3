div.layout-column.block-min-width
	header.with-subheader(flex="none")
		div.subheader-th(layout='row' layout-align="start center")

			div(ng-class="{'flex-60': vm.state.is('admin.production.tasks') || vm.state.is('admin.production.places') || vm.state.is('admin.production.places-seiren'), 'flex-25': vm.state.is('admin.production.printers') || vm.state.is('admin.production.waiting')}")
				div(pick-date-header)
					div {{ vm.date | dateJapan : 'day' }}
					md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)

			div(flex, layout='row', layout-align='end center').user-info
				user-manage

		div.subheader-nav
			md-nav-bar(nav-bar-aria-label='navigation links' md-no-ink md-selected-nav-item="vm.initialState")
				md-nav-item(md-nav-sref='admin.production.printers' name='admin.production.printers' ng-if="vm.showSubModeTab('号機')") 号機
				md-nav-item(md-nav-sref='admin.production.tasks' name='admin.production.tasks' ng-if="vm.showSubModeTab('状態')") 状態
				md-nav-item(md-nav-sref='admin.production.places' name='admin.production.places' ng-if="vm.showSubModeTab('置き場')") 置き場
				md-nav-item(md-nav-sref='admin.production.waiting' name='admin.production.waiting' ng-if="vm.showSubModeTab('生産待ち')") 生産待ち
		div.garment-total(ng-if="vm.subcontractor !== true" style="padding: 16px 0")
			span.garment-total-info(ng-repeat='item in vm.total_garment')
				b {{item.title}} : {{item.count }}


	md-content(flex='grow' layout='column' ui-view)
	
create-plan.block-window(
	is-shown='vm.isCreatePlanShown',
	on-close='vm.onCreatePlanClose()'
)