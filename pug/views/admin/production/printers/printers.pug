div.content.no-virtual-repeat(
flex='grow',
infinite-scroll="vm.loadMore()"
infinite-scroll-parent='admin-production-printers.component'
infinite-scroll-distance='0.2',
infinite-scroll-disabled='vm.pagination.disabled'
)

    div.search(layout='row', style='position: fixed; background: #fafafa; z-index: 11; width: 89%; margin-left: -60px; padding: 0 37px 0 57px;')
        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
            i.icons8-search
            input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
            button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
        div.cell.heading-btns(style='justify-content: center;margin-left:235px' flex layout='row', layout-align='end center')
                md-button.md-raised.md-primary.btn-sm(ng-click='vm.backToTop()') トップに戻る
                md-button.md-raised.md-primary.btn-sm(ng-click='vm.backToBottom()') 最下部に移動
        div.cell.heading-btns(flex layout='row' style='flex:2', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.showChart = !vm.showChart') {{ vm.showChart ? 'グラフ非表示' : 'グラフ表示' }}
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.prePrintQR($event)') QRコード印刷
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.printRemainingQR($event)') 残QRコード印刷
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.printQRGroups()') QRグループ印刷
            md-menu(md-position-mode="target-right target")
                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                    i.icons8-more
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item
                        md-button(ng-click='vm.downloadCSV($event)'): span タスクCSVダウンロード
                        md-datepicker.in-menu-hidden(ng-model='vm.csvDate' md-mode='month' md-placeholder='Enter date' md-open-on-focus ng-change='vm.onCsvDateChange()')
                    md-menu-item
                        md-button(ng-click='vm.downloadCSVTotalSide($event)'): span 面数集計CSVダウンロード
                        md-datepicker.in-menu-hidden(ng-model='vm.csvDateTotalSide' md-mode='month' md-placeholder='Enter date' md-open-on-focus ng-change='vm.onCsvTotalSideDateChange()')
                    md-menu-item
                        md-button(ng-click='vm.printSheet(null)'): span 材料表
                    md-menu-item
                        md-button(ng-click='vm.printSheet(true)'): span 残材料表
                    md-menu-item
                        md-button(ng-click='vm.deleteTasks($event)'): span タスク削除
    md-divider
    div.printer-item(ng-style="{'height': (vm.showChart ? '68px' : '75px')}")
    div.chart.ng-hide(ng-show='vm.showChart' ng-if="vm.factory")
        admin-production-printers-chart.chart-component(
        target-date='vm.pagination.date',
        type-id='vm.pagination.type_id',
        factory-id ='vm.factory',
        )

    div.printer-item.default-row(layout='row' layout-align='start center' ng-repeat='printer in vm.printers')
        div.cell(flex='none' layout='row' layout-align='start center')
            div: md-checkbox(ng-model='printer.selected' aria-label='select {{ printer.Printer.title }}')
            div {{ printer.Printer.title }}
        div.cell.cell-progress(flex layout='row' layout-align='start center' layout-wrap)
            div.progress-text.layout-row.layout-align-start-center
                span {{ printer.Printer.workload }}
                span.divider
                span {{ printer.Printer.capacity }}
            div.progress
                div.progress-current(style='width: {{ vm.getPercent(printer.Printer) }}%;')
            div.progress-text.layout-row.layout-align-start-center(style='margin-left: 20px;')
                span {{ printer.Printer.printedQRcodes }}
                span.divider
                span {{ printer.Printer.workload }}
        div.cell.cell-action(flex='none', layout='row', layout-align='end center')
            div.buttons
                md-button.md-raised.md-primary.btn-sm(ng-click='vm.viewPrinter(printer)') 詳細
                md-menu(md-position-mode="target-right target")
                    md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                        i.icons8-more
                    md-menu-content.menu-small(md-menu-align-target)
                        md-menu-item
                            md-button(ng-click='vm.printSheet(null, printer)'): span 材料表
                        md-menu-item
                            md-button(ng-click='vm.printSheet(true, printer)'): span 残材料表
                        md-menu-item
                            md-button(ng-click='vm.deleteTasks($event, printer)'): span タスク削除

    div.pagination-loading(ng-show='vm.pagination.loading')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした

printer-info(
is-shown='vm.printerDetails.isShown',
active='vm.printerDetails.active',
on-close='vm.onPrinterClose()'
)

md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 400px;margin-top:24px")
    md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}

printer-product-sheet.hide(ng-if='vm.isElectron')
printer-qr-groups.printer-qr-groups()