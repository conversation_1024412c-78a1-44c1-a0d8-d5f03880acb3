div.block-wide(layout='column')
	header(flex='none')
		button.go-back-button(type='button', ng-click='vm.back()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 タスク情報
		div.sidenav-actions
			button.btn-qr.simple-btn(type='button' ng-click='vm.toggleQr()'): md-icon(md-svg-src='img/icons/qr-code.svg', aria-label='qr code')
			button.btn-delete.simple-btn(type='button', ng-click='vm.confirmDelete($event)'): i.icons8-delete
			md-menu(md-position-mode="target-right target")
				button.btn-more.simple-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
				md-menu-content.menu-small(md-menu-align-target)
					md-menu-item
						md-button(ng-click='vm.printQr($event)'): span QRコード印刷
					md-menu-item
						md-button(ng-click='vm.replan($event)'): span 再プランニング 
			
		.qr-popup(ng-show='vm.qrShown')
			qrcode(version='6', error-correction-level='M', size='130', data='{{ vm.activeStep.code }}')
		.qr-popup-backdrop(ng-show='vm.qrShown', ng-click='vm.toggleQr()')
	
	md-content(flex='grow')
		product-sides-images(sides='vm.sides', ng-if='vm.sides.length', on-select='vm.onSelect(active)')

		div.form.default-form.bottom-margin(layout='row' layout-wrap)
			div.input-container(flex=100)
				md-input-container
					label 注文番号
					input(type='text' ng-value='vm.task.Order.number' readonly)
			div.input-container(flex=50)
				md-input-container
					label 完成希望日
					input(type='text' ng-value='vm.task.Order.production_date_preferred' readonly)
			div.input-container(flex=50)
				md-input-container
					label ステータス
					input(type='text' ng-value='vm.task.Task.status' readonly)
			div.input-container(flex=100)
				md-input-container
					label 発注者
					input(type='text' ng-value='vm.task.Customer.title' readonly)
			div.input-container(flex=100)
				md-input-container.with-clipboard
					label QR code
					input(type='text' ng-value='vm.activeStep.code' readonly)
					button.clipboard-button(ngclipboard ngclipboard-success="vm.onSuccessCopy(e);" data-clipboard-text="{{ vm.activeStep.code }}")
						i.icons8-copy
					button.btn-dummy
						md-tooltip(md-visible='vm.clipSuccessMsg') Copied!

		div.form.default-form.bottom-margin(layout='row' layout-wrap)
			div.input-container(flex=100)
				md-input-container
					label 商品
					input(type='text' ng-value='vm.task.Product.title' readonly)
			div.input-container(flex=50)
				md-input-container
					label カラー
					input(type='text' ng-value='vm.task.ProductColor.title' readonly)
			div.input-container(flex=50)
				md-input-container
					label サイズ
					input(type='text' ng-value='vm.task.ProductSize.title' readonly)