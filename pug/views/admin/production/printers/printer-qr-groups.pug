.page(ng-repeat='item in vm.groups' data-index="{{$index}}")
	.qr-codes
		.qr-code(ng-repeat="code in item.qrcodes")
			img(src='' width='{{item.qrSize}}', alt='qr-code')
			qrcode.ng-hide(version='6', error-correction-level='M', size='{{item.qrSize}}', data='{{ code.code }}' download)
			p.title {{ code.title }}
	.table
		.cell(ng-repeat='preview in item.previews')
			p.title(style="margin: 0; padding: 0;") {{ preview.title }}
			img(ng-src='{{ preview.image_url }}', alt='')
			p.detail(style="margin: 0; padding: 0;") {{ preview.detail }}
