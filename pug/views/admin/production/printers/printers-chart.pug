div.chart-header.float-left.w-2-5(layout='row' layout-align='start center' layout-wrap)
	div.buttons
		md-menu(md-position-mode="target-right target")
			md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{vm.type_description}}
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item
					md-button(ng-click='vm.setType("day")'): span 週
				md-menu-item
					md-button(ng-click='vm.setType("month")'): span 月
	div.chart-datepicker
		div(pick-date-header)
			md-datepicker(ng-model='vm.date' md-mode='month' md-placeholder='Enter date' md-open-on-focus, ng-change='vm.onDateSelect()', ng-show='vm.type == "month"')
			md-datepicker(ng-model='vm.date' md-mode='day' md-placeholder='Enter date' md-open-on-focus, md-date-filter='vm.isMonday', ng-change='vm.onDateSelect()', ng-show='vm.type == "day"')
		//md-button.md-raised.md-primary
			md-icon.md-datepicker-calendar-icon(aria-label='md-calendar' md-svg-src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM2gtMVYxaC0ydjJIOFYxSDZ2Mkg1Yy0xLjExIDAtMS45OS45LTEuOTkgMkwzIDE5YzAgMS4xLjg5IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bTAgMTZINVY4aDE0djExek03IDEwaDV2NUg3eiIvPjwvc3ZnPg==')
	div.chart-daterange.layout-row.layout-align-start-center
		md-button.md-raised.md-primary.go-prev(ng-click='vm.goPrevWeek()')
			md-icon(md-svg-src='img/icons/left.svg' aria-label='date prev')
		div.range {{ vm.dates.start.format('YYYY.MM.DD') }} - {{ vm.dates.end.format('YYYY.MM.DD') }}
		md-button.md-raised.md-primary.go-next(ng-click='vm.goNextWeek()')
			md-icon(md-svg-src='img/icons/left.svg' aria-label='date next')
	div.buttons
		md-menu(md-position-mode="target-right target")
			md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{vm.value_description}}
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item
					md-button(ng-click='vm.setValue(1)'): span 商品
				md-menu-item
					md-button(ng-click='vm.setValue(2)'): span プリンター
				md-menu-item
					md-button(ng-click='vm.setValue(3)'): span 生産面数内訳
				md-menu-item
					md-button(ng-click='vm.setValue(4)'): span 発注者
	md-button.md-raised.md-primary.btn-sm.btn-more.last(ng-click='vm.search()') 検索
	//- div.chart-menu
		md-menu(md-position-mode="target-right target")
			md-button.md-raised.md-primary.btn-sm.with-arrow(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') 号機毎
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item
					md-button() field1
				md-menu-item
					md-button() field2
	//- div.chart-compare
		md-checkbox(aria-label='compare')
		div 比べる
div.float-right.w-3-5(layout='row' layout-align='start center' layout-wrap)
	span.p-4(ng-if='vm.period_total !== ""' ng-repeat='(key, value) in vm.period_total')
		b {{value.title}} : {{value.data}}
	span.p-4(ng-if='vm.period_total === ""')
		b この期間には生産面数内訳がありません。

div.chart-body: canvas