md-sidenav.management-sidenav.md-sidenav-right(md-component-id='printer-info').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
	div.block-wide(layout='column', ng-hide='vm.taskDetails.isShown')
		header(flex='none')
			button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
			h2 プリンター生産情報
		md-content(flex='grow')
			div.form.default-form.bottom-margin(layout='row' layout-wrap)
				div.input-container(flex=50)
					md-input-container
						label プリンター
						input(type='text' ng-value='vm.printer.title' readonly)
				div.input-container(flex=50)
					md-input-container
						label 印刷数
						input(type='text' ng-value="vm.printer.workload + ' • ' + vm.printer.capacity" readonly)
				div.input-container.bottom-border(flex=100)
					md-input-container
						label 外注先コード
						input(type='text' ng-value='vm.printer.subcontractor_code' readonly)
			
			div.printer-product-list(
				infinite-scroll="vm.loadMoreTasks()"
				infinite-scroll-parent='md-content'
				infinite-scroll-distance='0.2'
				infinite-scroll-disabled='vm.pagination.disabled'
			)
				
				div.heading.text(layout='row' layout-align='start center')
					div.cell(flex=50) プレビュー
					div.cell.text-right(flex=50) 内容

				div.default-row(layout='row' layout-align='center center' ng-repeat='task in vm.tasks')
					div.cell(flex layout='row' layout-align='start center')
						div.image.flex-none(ng-repeat='step in task.steps'): img(ng-src='{{ step.TaskStep.image_url }}')
					div.cell.flex-none.layout-row.layout-align-end-center
						div {{ task.Order.number }}
						div.divider 
						div {{ task.Product.title }}
						.buttons
							md-button.md-raised.md-primary.btn-xs(ng-click='vm.viewTaskDetails(task)') 詳細
							
	admin-production-printer-task-details(
		ng-show='vm.taskDetails.isShown'
		active='vm.taskDetails.active',
		loading-type='vm.loadingType',
		on-close='vm.onTaskClose()',
		on-delete='vm.onTaskDelete(taskId)'
	)