section.block-wide(layout='column')

	.component-loading(ng-class='vm.loadingType')
		md-progress-circular(md-diameter='64', md-mode='indeterminate')

	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='space-between center')
			div(flex layout-align='start center')
				button.simple-icon-btn.close-button(type='button' ng-click='vm.close()'): i.icons8-delete-2
				h2 生産設定
				button.md-raised.md-accent.btn-print.md-button.md-ink-ripple.auto-plan(type='button' ng-click='vm.autoPlan()') 自動プランニング
				button.md-raised.md-accent.btn-print.md-button.md-ink-ripple.auto-plan(type='button' ng-click='vm.autoPlanKornit()') Kornitプランニング
			div.user-info
				h2 合計面数: {{vm.side_count}}
			div.user-info
				button.md-raised.md-accent.btn-print.md-button(type='button' ng-click='vm.filterBrother()') {{vm.isBattle ? 'すべて表示' : 'Brotherアイテム'}}
				button.md-raised.md-accent.btn-print.md-button(type='button' ng-click='vm.filterEmbroidery()') {{vm.is_emb ? 'すべて表示' : '刺しゅうアイテム'}}
		div.layout-row.buttons(flex layout-align='end center')
			div.search-form(focusable-search): form()
				input.search-input(type='text', ng-model='vm.searchQuery', ng-change="vm.liveSearch()")
				i.icons8-search
	
	div.body.block-wide.overflow-hidden.create-plan-content(flex='100' layout='row')
		md-content.flex-65.products-list
			
			div.header-row.layout-row.layout-align-center-center
				.cell.cell-data.layout-row.layout-align-start-center
					.checkbox(style="margin-right: 0"): md-checkbox(ng-model='vm.allSelected' ng-change='vm.toggleAll()' aria-label='label')
				.cell.flex-20.layout-row.layout-align-end-center
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.setEmailClient()') {{vm.emailClient ? 'すべて表示' : 'EPSON指定'}}
				.cell.flex-20.layout-row.layout-align-end-center
					div.buttons
						md-button.md-raised.md-primary.btn-sm(ng-click='vm.setEmailClientKornit()') {{vm.emailClientKornit ? 'すべて表示' : 'KORNIT指定'}}

				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') ソート
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='sort in vm.plannedSorts')
								md-button(ng-click='vm.setActiveSort(sort.key)' ng-disabled='sort.key === vm.activeSort') {{ sort.value }}

				.cell.flex-25.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeType.ProductType.title }}
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item(ng-repeat='type in vm.unplannedTypes')
								md-button(ng-click='vm.setActiveType(type)' ng-disabled='type.ProductType.code === vm.activeType.ProductType.code') {{ type.ProductType.title }}

				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeColor.Color.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='color in vm.unplannedFilter.Color')
								md-checkbox(ng-model='color.selected', ng-change='vm.reloadFilter(color , color.Color.title , "Color")') {{ color.Color.title }}
				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeCustomer.Customer.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='customer in vm.unplannedFilter.Customer')
								md-checkbox(ng-model='customer.selected', ng-change='vm.reloadFilter(customer , customer.Customer.title , "Customer")') {{ customer.Customer.title }}
				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeProductCode.ProductCode.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='pcode in vm.unplannedFilter.ProductCode')
								md-checkbox(ng-model='pcode.selected', ng-change='vm.reloadFilter(pcode , pcode.ProductCode.title , "ProductCode")') {{ pcode.ProductCode.title }}
				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeSize.Size.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='size in vm.unplannedFilter.Size')
								md-checkbox(ng-model='size.selected', ng-change='vm.reloadFilter(size , size.Size.title , "Size")') {{ size.Size.title }}
				.cell.flex-40.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeUploadDate.UploadDate.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='date in vm.unplannedFilter.UploadDate')
								md-checkbox(ng-model='date.selected', ng-change='vm.reloadFilter(date , date.UploadDate.title , "UploadDate")') {{ date.UploadDate.title }}
				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeDate.Date.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='date in vm.unplannedFilter.Date')
								md-checkbox(ng-model='date.selected', ng-change='vm.reloadFilter(date , date.Date.title , "Date")') {{ date.Date.title }}
				.cell.flex-30.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeSku.Sku.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='sku in vm.unplannedFilter.Sku')
								md-checkbox(ng-model='sku.selected', ng-change='vm.reloadFilter(sku , sku.Sku.title , "Sku")') {{ sku.Sku.title }}
				.cell.flex-40.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeMaker.Maker.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='maker in vm.unplannedFilter.Maker')
								md-checkbox(ng-model='maker.selected', ng-change='vm.reloadFilter(maker , maker.Maker.title , "Maker")') {{ maker.Maker.title }}
				.cell.flex-25.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeSameDay.SameDay.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='same in vm.unplannedFilter.SameDay')
								md-checkbox(ng-model='same.selected', ng-change='vm.reloadFilter(same , same.SameDay.code , "SameDay")') {{ same.SameDay.title }}
				.cell.flex-40.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeDeliveryType.DeliveryType.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='delivery in vm.unplannedFilter.DeliveryType')
								md-checkbox(ng-model='delivery.selected', ng-change='vm.reloadFilter(delivery , delivery.DeliveryType.code , "DeliveryType")') {{ delivery.DeliveryType.title }}
				.cell.flex-25.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeTotalQuantity.TotalQuantity.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='total in vm.unplannedFilter.TotalQuantity')
								md-checkbox(ng-model='total.selected', ng-change='vm.reloadFilter(total , total.TotalQuantity.code , "TotalQuantity")') {{ total.TotalQuantity.code }}
				.cell.flex-25.layout-row.layout-align-end-center
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeNumberItemTasks.NumberItemTask.title }}
						md-menu-content.menu-small.custom-checkbox(md-menu-align-target)
							md-menu-item(ng-repeat='number in vm.unplannedFilter.NumberItemTask')
								md-checkbox(ng-model='number.selected', ng-change='vm.reloadFilter(number , number.NumberItemTask.code , "NumberItemTask")') {{ number.NumberItemTask.title }}

			div(
				infinite-scroll="vm.loadMoreItems()"
				infinite-scroll-parent='md-content'
				infinite-scroll-distance='0.2',
				infinite-scroll-disabled='vm.paginationItems.disabled'
			)
				div.default-row(ng-repeat='item in vm.unplanned', layout='row', layout-align='start center', ng-class="{'row-active': item.selected}")
					.cell.cell-data.flex(layout='row', layout-align='start center')
						.checkbox
							md-checkbox(ng-model='item.selected', aria-label='label', ng-change='vm.onItemSelect()')
						.images(layout='row', layout-align='start center')
							.image(ng-repeat='task in item.tasks')
								img(ng-src='{{ task.OrderItemTask.image_url }}')
						.data-rowed(ng-class='!(item.Order.email_client || vm.emailClient || item.Order.email_client_kornit || vm.emailClientKornit || item.OrderItem.is_battle || vm.isBattle) ?"" :((item.OrderItem.is_battle || vm.isBattle) ?"text-purple" :(item.Order.email_client || vm.emailClient) ?"text-green" :"text-orange")')
							span {{ item.Order.number }}
							span.divider
							span {{ item.Product.title }}
							span.divider
							span {{ item.Product.code }}
							span.divider
							span {{ item.ProductColor.title }}
							span.divider
							span {{ item.ProductSize.title }}
							span.divider
							span {{ item.Customer.title }}
							span.divider
							span {{ item.OrderItem.quantity }}枚
							span.divider
							span 合計{{ item.OrderItem.sum_quantity }}
							span(ng-if='item.Order.memo').divider
							span(ng-style='{"color" : "red"}') {{ item.Order.memo }}
							span(ng-if='item.OrderItem.body_procurement_failure !== "" && item.OrderItem.body_procurement_failure !== null').divider
							span(ng-if='item.OrderItem.body_procurement_failure !== "" && item.OrderItem.body_procurement_failure !== null' ng-style='{"color" : "red"}') ボディ調達失敗で保留
							span(ng-repeat='task in item.tasks') {{' • '}} {{task.OrderItemTask.title}} {{ }}
							span(ng-if='item.OrderItem.print_device != null')
								span.divider
								span(style='color: rgba(0,0,0,0.87)') {{item.OrderItem.print_device}}
					.cell.text-right.flex-none {{ item.Order.production_date_preferred }}
				div.pagination-loading(ng-show='vm.paginationItems.loadingItem')
					md-progress-circular(md-diameter='40' md-mode='indeterminate')
			.nothing-found-msg(layout-fill, ng-show='vm.paginationItems.empty'): div アイテムはありません


		md-content.flex-35.printers-list
			div.header.layout-row.layout-align-center-center
				div.flex-20
					md-menu(md-position-mode="target-right target")
						div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') プリンター
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.setFilterPrinter(0)' ng-disabled='0 === vm.activeFilterPrinter') すべて
							md-menu-item
								md-button(ng-click='vm.setFilterPrinter(101)' ng-disabled='101 === vm.activeFilterPrinter') ガーメント（Up-T・Budgets）
							md-menu-item
								md-button(ng-click='vm.setFilterPrinter(102)' ng-disabled='102 === vm.activeFilterPrinter') ガーメント（TMIX）
							md-menu-item
								md-button(ng-click='vm.setFilterPrinter(103)' ng-disabled='103 === vm.activeFilterPrinter') ガーメント（その他）
				div.flex-20
					md-input-container
						md-select(ng-model='vm.factory_id' name='factory_id')
							md-option(ng-repeat='factory in vm.factoryFilter track by factory.id' ng-value='factory.id' ng-selected='factory.id == vm.factorySelected' ng-click='vm.onFactoryChange(factory.id)') {{ factory.title }}
				div.flex-60
					div(pick-date-header)
						div {{ vm.date | dateJapan : 'day' }}
						md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()', md-placeholder='Enter date' md-open-on-focus)
			
			div.suggested(ng-show='vm.recommendations.length')
				div.heading.layout-row.layout-align-start-center
					div 自動プランニング
					md-button.md-raised.md-primary.btn-sm.btn-accept(aria-label='accept', ng-click='vm.processRecommendations()'): md-icon(md-svg-src='img/icons/checkmark.svg')
				
				div.printer-row.layout-row.layout-align-start-center(ng-repeat='printer in vm.recommendations')
					div.printer-name {{ printer.Printer.title }}
					div.printer-progress(flex layout='row' layout-align='start center' layout-wrap)
						div.progress-text.layout-row.layout-align-start-center
							span {{ printer.Printer.workload }}
							span.divider
							span {{ printer.Printer.capacity }}
						div.progress
							div.progress-current(style='width: {{ vm.getPercent(printer.Printer) }}%;')

			div.other(
				ng-show='vm.printers.length',
				infinite-scroll="vm.loadMorePrinters()"
				infinite-scroll-parent='md-content'
				infinite-scroll-distance='0.2',
				infinite-scroll-disabled='vm.paginationPrinters.disabled'
			)
				div.heading
					div 手動プランニング
				
				div.printer-row.layout-row.layout-align-start-center(ng-repeat='printer in vm.printers')
					div.printer-name {{ printer.Printer.title }}
					div.printer-progress(flex layout='row' layout-align='start center' layout-wrap)
						div.progress-text.layout-row.layout-align-start-center
							span {{ printer.Printer.workload }}
							span.divider
							span {{ printer.Printer.capacity }}
						div.progress(ng-class="{'has-note': printer.note}")
							div.progress-current(style='width: {{ vm.getPercent(printer.Printer) }}%;')
						div.progress-note(ng-show='printer.note'): span
					div.printer-action(ng-show='vm.selectedItems.length')
						md-button.md-raised.md-primary.btn-sm.btn-accept(aria-label='accept', ng-click='vm.processPrinter(printer)'): md-icon(md-svg-src='img/icons/checkmark.svg')
						
			.nothing-found-msg(layout-fill, ng-show='vm.paginationPrinters.empty && !vm.recommendations.length'): div 利用可能なプリンターはありません

			style(type="text/css").
				md-select .md-select-value {
					border-bottom-style: none !important;
				}