md-virtual-repeat-container.content(flex='grow' )

	div.search(layout='row')
		div.with-reset.search-form.flex-none.layout-row.layout-align-start-center(focusable-search): form()
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2

		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-menu(md-position-mode="target-right target")
				md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
					i.icons8-more
				md-menu-content.menu-small(md-menu-align-target)
					md-menu-item
						md-button(ng-click='vm.CSVdownload()'): span 伝言メモcsvダウンロード
	md-divider

	div.default-row(md-virtual-repeat='item in vm.infiniteUnplanned' md-on-demand ng-show='item')
		.cell.cell-data(flex='flex', layout='row', layout-align='start center')
			.images(layout='row', layout-align='start center')
				.image(ng-repeat='task in item.tasks')
					img(ng-src='{{ task.OrderItemTask.image_url }}')
			.data-rowed.rowed-inline
				div {{ item.Order.number }}
				.divider
				div {{ item.Product.title }}
				.divider
				div {{ item.ProductColor.title }}
				.divider
				div {{ item.ProductSize.title }}
				.divider
				div {{ item.Customer.title }}
				.divider
				div {{ item.OrderItem.quantity }}枚
				.divider.addition(ng-show='!!item.Order.production_date_preferred')
				.addition {{ item.Order.production_date_preferred }}


	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='62' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした