md-sidenav.management-sidenav.md-sidenav-right(md-component-id='manual-shipping-complete-details').sidenav-fixed: .block-wide(component-loading='vm.loadingType')
    div.block-wide(layout='column')
        header(flex='none')
            button.esc-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/esc.svg')
            h2 注文情報

        md-content(flex='grow')
            form.form.default-form.bottom-margin(layout='row' layout-wrap name='completeShippingFrameForm')
                div.input-container(flex=100)
                    md-input-container
                        label 注文番号
                        input(type='text' ng-model='vm.order.Order.number' readonly)
                div.input-container(flex=100)
                    md-input-container
                        label 発送サービス
                        md-select(ng-model='vm.order.Order.delivery_service_id' name='delivery_service_id' required)
                            md-option(ng-repeat='service in vm.deliveryServices' ng-value='service.DeliveryService.id') {{ service.DeliveryService.title }}
                div.input-container(flex=100)
                    md-input-container
                        label 発送番号
                        input(type='text' ng-model='vm.order.Order.tracking_number' name='tracking_number')
                div.input-container(flex=100)
                    md-input-container
                        label メモ
                        input(type='text' ng-model='vm.order.Order.memo')

        footer.text-right(flex='none')
            div: md-button.md-raised.md-accent(ng-click='vm.updateTrackingNumber()') 保存