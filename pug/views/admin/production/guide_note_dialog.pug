md-dialog.guide_note
	md-dialog-content
		div.default-row(layout='row' layout-align='start center' ng-repeat='product in products')
			div.cell.cell-progress(flex layout='row' layout-align='start center' layout-wrap)
				div.progress-text.layout-row.layout-align-start-center
					span {{ product.Product.guide_note }}
			div.cell.cell-action(flex='none', layout='row', layout-align='end center')
				input(type='number' ng-model='product.Product.quantity' name='quantity')
				div.buttons
					md-button.md-raised.md-primary.btn-sm(ng-click='printGuideNote(product)') 発行
		.nothing-found-msg(ng-if='products.length == 0'): div 何も見つかりませんでした

	md-dialog-actions
		md-button.md-primary(ng-click='closeDialog()') キャンセル