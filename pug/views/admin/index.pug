.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('発注者')")
						div(flex='grow'): a(ui-sref='admin.income') 発注者
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-sidebar.btn-add(aria-label='add' ng-click='vm.createCustomer()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('生産')")
						div(flex='grow'): a(ui-sref='admin.production') 生産
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-sidebar.btn-settings(aria-label='add' ng-click='vm.createPlan()' ng-if="vm.showCreatePlan"): i.icons8-settings
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('ユーザー')")
						div(flex='grow'): a(ui-sref='admin.users') ユーザー
						div(flex='none'): md-button.md-raised.md-primary.btn-sm.btn-add.btn-sidebar(aria-label='add' ng-click='vm.createUser()'): i.icons8-plus
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('エラーログ')")
						div(flex='grow'): a(ui-sref='admin.error-log') エラーログ
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('ボディ発注')")
						div(flex='grow'): a(ui-sref='admin.body-order') ボディ発注
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('受注エラー')")
						div(flex='grow'): a(ui-sref='admin.image-error') 受注エラー
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('注文状態検索')")
						div(flex='grow'): a(ui-sref='admin.order-status-search') 注文状態検索
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('各種データ出力') && vm.showTab")
						div(flex='grow'): a(href ng-click='vm.downloadExportCSV($event)', ng-style="{'cursor': 'pointer'}") 各種データ出力



			div.sidebar-secondary
				sidebar-secondary-menu(statuses='vm.statuses')

		md-content.main-body.flex-100(ui-view)
		
		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')

