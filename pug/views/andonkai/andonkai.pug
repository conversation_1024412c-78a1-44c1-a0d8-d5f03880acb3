.block-wide.management(layout="column")
    .block-wide(layout='row' flex='100')
        md-content.main-body.flex-100.random-content(ui-view)
            div.layout-column.block-min-width
                header.with-subheader(flex="none")
                    div.subheader-th(layout='row' layout-align="start center")

                        div.layout-row
                            md-select(ng-model='rc.factory' name='factory' aria-label='Select Factory' ng-change='rc.onChange()')
                                md-option(ng-repeat='factory in rc.factories' ng-value='factory.id') {{ factory.title }}
                        div.layout-row.flex.layout-align-end {{ rc.clock | date:'yyyy/M/d HH:mm'}}
                        div(flex, layout='row', layout-align='end center').user-info
                            user-manage

                md-content.main-body.flex-100(ui-view)
                    div(layout='row')
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div(ng-class="flex-60")
                                div(pick-date-header)
                                    div {{ rc.date1 | dateJapan : 'day' }}
                                    md-datepicker(ng-model='rc.date1' ng-change='rc.onChange()' md-placeholder='Enter date' md-open-on-focus)
                            div
                                div.layout-row.layout-align-center.record-title 残り面数
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.garment_printers_remaining }}
                                div(style="display: flex;align-items: center;flex-wrap: wrap")
                                    div.default-row(layout='row' ng-repeat='(title,count) in rc.data.customers.printer_type.remaining' style="width:53%")
                                        div.layout-row.layout-align-center.record-title.inspector(style="font-size:30px;white-space: nowrap") {{title}} : {{count}}
                            div
                                div.layout-row.layout-align-center.record-title 受注面数
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.total_garment_printers }}
                                div(style="display: flex;align-items: center;flex-wrap: wrap")
                                    div.default-row(layout='row' ng-repeat='(title,count) in rc.data.customers.printer_type.total' style="width:53%")
                                        div.layout-row.layout-align-center.record-title.inspector(style="font-size:30px;white-space: nowrap") {{title}} : {{count}}
                            div.middle
                                div.layout-row.layout-align-center.record-title 残り面数内訳
                                div.default-row.record-detail.inspector(layout='row' ng-repeat='(name,num) in rc.data.customers.user_print')
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num }}
                            div
                                div.layout-row.layout-align-center.record-title 面数詳細
                                div.layout-row.record-detail(layout='row' ng-repeat='andon in rc.data.customers.andons')
                                    div.flex-80  {{ andon.Andon.title }}
                                    div.flex-20.layout-row.layout-align-end  {{ andon[0].total_task - andon[0].tasks_wait }}/{{ andon[0].total_task }}
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div(ng-class="flex-60")
                                div(pick-date-header)
                                    div {{ rc.date2 | dateJapan : 'day' }}
                                    md-datepicker(ng-model='rc.date2' ng-change='rc.onChange()' md-placeholder='Enter date' md-open-on-focus)
                            div
                                div.layout-row.layout-align-center.record-title 検品面数
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.total_production }}
                            div.middle(ng-if='!rc.data.hiddenInspected')
                                div.layout-row.layout-align-center.record-title 検品面数内訳
                                div.default-row.record-detail.inspector(layout='row' ng-repeat='(name,num) in rc.data.customers.user_check')
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num }}
                            div
                                div.layout-row.layout-align-center.record-title D品率
                                div.layout-row.layout-align-center.record-total(style="white-space:nowrap") {{ rc.data.customers.d_percent }} %
                                div.layout-row.layout-align-center.record-title D品詳細
                                div.layout-row.record-detail(layout='row' ng-repeat='(name,num) in rc.data.customers.d_detail')
                                    div.flex-80  {{ name }}
                                    div.flex-20.layout-row.layout-align-end  {{ num }}
                        div.cell(flex='none' layout='column' layout-align='start' layout-wrap)
                            div(ng-class="flex-60")
                                div(pick-date-header)
                                    div {{ rc.date3 | dateJapan : 'day' }}
                                    md-datepicker(ng-model='rc.date3' ng-change='rc.onChange()' md-placeholder='Enter date' md-open-on-focus)
                            div
                                div.layout-row.layout-align-center.record-title 配送準備済
                                div.layout-row.layout-align-center.record-total {{ rc.data.customers.customers_data.done }}/{{ rc.data.customers.customers_data.total }}
                            div.middle
                                div.default-row.inspector(layout='row' ng-repeat='(name,num) in rc.data.customers.customers_data.customers' style="margin:0 100px;height:35px;font-size:25px")
                                    div.flex-80 {{ name }}
                                    div.flex-20.layout-row.layout-align-end {{ num.done }}/{{ num.total }}
                                div(style="display: flex;align-items: center;flex-wrap: wrap;padding: 5px 0 10px 100px;")
                                    div.default-row(layout='row' ng-repeat='item in rc.data.customers.customers_data.deliveries' style="width:48%;padding:5px 0;")
                                        div.layout-row.layout-align-center(style="white-space: nowrap;font-size: 22px;") {{item.name}} : {{item.done}} / {{item.total}}
                            div
                                div.layout-row.layout-align-center.record-title 配送準備数内訳
                                div(style="padding:12px 110px;font-size:22px")
                                    div.layout-row(layout='row' style="padding: 5px 0")
                                        div.flex-40
                                        div.flex-25 数量
                                        div.flex-35 注文数
                                    div.layout-row(layout='row' style="padding: 5px 0" ng-repeat='item in rc.data.customers.customers_data.users')
                                        div.flex-40 {{item.order_total.delivery_prepare_user_username}}
                                        div.flex-25 {{item.task_total.task_cnt}} /
                                        div.flex-35 {{item.order_total.order_cnt}}
                            div.middle
                                div.layout-row.layout-align-center.record-title 前処理
                                div.layout-row.record-detail(layout='row' ng-repeat='preprocessData in rc.data.customers.preprocessDatas')
                                    div.flex-80  {{ preprocessData.username }}
                                    div.flex-20.layout-row.layout-align-end  {{ preprocessData.preprocessTasks }}