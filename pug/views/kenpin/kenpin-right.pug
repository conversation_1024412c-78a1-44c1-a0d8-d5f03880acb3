header(flex='none' layout='row' layout-align='start center')
	div.flex-none
		md-button.md-raised.md-primary.btn-back(ng-show="vm.state.is('manual', 'product')" ng-click="vm.state.back()")
			md-icon(md-svg-src='img/icons/left.svg', aria-label='go back')
			span 戻る
	div(flex layout='row', layout-align='end center').user-info
		user-manage

md-content.content(flex layout='column' layout-align='center center')
	
	div.scan-qr.text-center(ng-if="vm.state.is('scan')")
		h2 QRコートをスキャンしてください
		p.error-text.scanner-error(ng-if='vm.errorText') {{ vm.errorText }}
		md-button.md-raised.md-primary.btn-print(ng-click="vm.state.set('manual')") 手動入力
		input#scanner-field(type='text' ng-model='vm.scannerInput' ng-change='vm.findQrScan()' ng-blur='vm.focusScanField()')
	
	div.enter-qr.text-center(ng-if="vm.state.is('manual')")
		form(name='findQr' ng-submit='vm.findQr()')
			h2 QRコードを入力してください
			p.error-text(ng-if='vm.errorText') {{ vm.errorText }}
			.form-group
				input.simple-input(type='text' ng-model='vm.qrCodeInput' placeholder='例えば：Н4676446')
				button.reset(type='reset' ng-show='vm.qrCodeInput' ng-click="vm.clearInput()")
					i.icons8-delete-2
			md-button.md-raised.md-accent.btn-print(type='submit') 次へ
			
	div.enter-qr.text-center(ng-if="vm.state.is('printing')")
		h2 しばらくお待ちください
		md-button.md-raised.md-primary.btn-print(ng-click="vm.state.set('product', true)") キャンセル
		
	div.enter-qr.text-center(ng-if="vm.state.is('finish')")
		h2 印刷はいかがでしたか？
		div.buttons
			md-button.md-raised.md-accent.btn-print(ng-click="vm.printSuccess()") 終了
			md-button.md-raised.md-primary.btn-print(ng-click="vm.printFail()") 失敗
			md-button.md-raised.md-primary.btn-print(ng-click="vm.retryPrint()") 再印刷