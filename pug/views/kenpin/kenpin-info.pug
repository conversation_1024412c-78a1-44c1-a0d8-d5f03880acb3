div.management.kenpin-header
	header.section-header(flex='none' layout='row')
		div.layout-row(flex layout-align='start center')
			button.simple-icon-btn.close-button(type='button' ng-click='vm.closeKenpin()'): i.icons8-delete-2
			h2 検品モード
		div.layout-row.buttons(layout-align='end center')
			div: md-button.md-raised.md-accent(ng-disabled="isDisabled" ng-click='vm.save()') 検査完了

		div.layout-row.buttons(layout-align='end center')
			div: md-button.md-primary(ng-click='vm.confirmDelete($event)') キャンセル
md-content.block-wide.content.list-content(layout='column')
	div.kenpini-info
		div
			table.task-info
				tr
					td.align-item-center.justify-between(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px', 'display':'flex'}")
						div.d-inline-block
							h4 検品回数 : {{vm.numberCheck}}
							h4 在庫数 : {{vm.task['default'].data.current_step.detail.inventory_quantity !== null ? vm.task['default'].data.current_step.detail.inventory_quantity : 'なし'}}
						span.task-info-header.text-red {{ vm.task['default'].data.current_step.detail.storage }}
						span.task-info-header.text-red(ng-if="vm.task['default'].data.current_step.detail.delivery_type == 'ネコポス'" ng-style="{'float' : 'right', 'padding' : '20px 0px'}")
							| {{ vm.task['default'].data.current_step.detail.delivery_type }}
						span.task-info-header(ng-if="vm.task['default'].data.current_step.detail.delivery_type != 'ネコポス'" ng-style="{'float' : 'right', 'padding' : '20px 0px'}")
							| {{ vm.task['default'].data.current_step.detail.delivery_type }}
					td(colspan=3 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
						span.task-info-header 品番 ：{{ vm.task['default'].data.current_step.detail.product_code }}
						span.kenpin-inline-flex
							span.task-info-header カラー ：{{ vm.task['default'].data.current_step.detail.product_color_title }}
							span.task-info-header カラーのコード ：{{ vm.task['default'].data.current_step.detail.product_color_code }}
						span.task-info-header サイズ ：{{ vm.task['default'].data.current_step.detail.product_size_title }}
					td(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
						span.task-info-header 納期日 ：{{ vm.task['default'].data.current_step.detail.delivery_date }}
					td(colspan=1 ng-style="{'text-align' : 'left', 'padding-left' : '20px'}")
						span.task-info-header アイテム名 ：{{ vm.task['default'].data.current_step.detail.product_title }}
				tr
					td(ng-repeat='item in vm.task')
						div.oit-div {{item.title}}
				tr
					td(ng-repeat='(key,item) in vm.task')
						div(ng-if='item.nashi') {{item.nashi}}
						div(ng-if='item.current_step' ng-style="{'text-align' : 'center'}")
							p(style="margin:-18px 0 5px 0") {{item.current_step.TaskStep.type_product}}
							img(ng-click='vm.showLargeImage($event, item)', ng-src="{{ item.current_step.TaskStep.image_url }}" ng-style="{'cursor': 'pointer', 'border': '2px solid ' + item.current_step.TaskStep.border_color}" )
							br
							span {{ vm.task['default'].data.current_step.detail.index }}
							br
							div.wrap-check-box
								div.flex.align-item-center.wrap-ok-box
									md-checkbox(ng-if='item.box', ng-model='item.okSelected', id="OK-{{key}}", aria-label='label', ng-click='vm.clickChange(item,key)', ng-change='vm.updateSubscribe(item)' ,ng-disabled='item.selected', ng-style="{'margin-bottom' : '0'}").check-box.good 検査OK
									span(ng-if="vm.task['default'].data.current_step.detail.certificate_stamp" ng-style="{'color' : 'red','line-height' : '18px'}") 証紙OK (証紙: {{ vm.task['default'].data.current_step.detail.certificate_stamp }})
								div.flex.align-item-center.wrap-ok-box
									md-checkbox(ng-if='item.box', ng-model='item.selected', id="NG-{{key}}", aria-label='label',ng-click='vm.clickChange(item,key)', ng-change='vm.viewReasion(item)' ,ng-disabled='item.okSelected').check-box 検査NG
								div.flex.align-item-center(ng-if="vm.task['default'].data.current_step.detail.is_include")
									md-checkbox(aria-label='label' ng-model="item.check_include" ng-change='vm.showIncludeOption($event, item)') 同梱オプション
							br
							img(ng-if="vm.task['default'].data.current_step.detail.certificate_image" ng-src="{{vm.task['default'].data.current_step.detail.certificate_image}}" ng-style="{'width': '60px','margin-left':'10px'}")
							div.pb-12(ng-if='!item.box')
			div.text-center.text-red.text-xl {{ vm.task['default'].data.current_step.detail.late ? 'ほかのアイテムで既に納期遅れが発生しています' : ''}}
			div.table
				table.step-info
					tr
						td
							div.label メモ
							div.input(ng-if="vm.task['default'].data.current_step.detail.is_factory_memo_2")
								div.input(ng-if="vm.task['default'].data.current_step.detail.is_memo_claim" ng-style="{'color' : 'red'}") {{ vm.task['default'].data.current_step.detail.memo_claim }}
								div.input(ng-if="!vm.task['default'].data.current_step.detail.is_memo_claim") {{ vm.task['default'].data.current_step.detail.memo_claim }}
							div.input(ng-if="!vm.task['default'].data.current_step.detail.is_factory_memo_2")
								div.input {{ vm.task['default'].data.current_step.detail.memo }}
						td
							div.label 納期日
							div.input {{ vm.task['default'].data.current_step.detail.delivery_date }}
					tr(ng-if="vm.print_positions.length > 0")
						td
							div.input(ng-repeat='print_position in vm.print_positions')
								div.input {{ print_position }}
						td
					tr
						td
							div.label 注文No
							div.input {{ vm.task['default'].data.current_step.detail.order_number }}
						td
							div.label カラー
							div.input {{ vm.task['default'].data.current_step.detail.product_color_title }}
					tr
						td
							div.label 型式
							div.input {{ vm.task['default'].data.current_step.detail.category_title }}
						td
							div.label サイズ
							div.input {{ vm.task['default'].data.current_step.detail.product_size_title }}
					tr
						td
							div.label 個人名
							div.input {{ vm.task['default'].data.current_step.detail.shipping_person }}
						td
							div.label 注文数
							div.input {{ vm.task['default'].data.current_step.detail.quantity }}
					tr
						td
							div.label ステータス
							div.input {{ vm.task['default'].data.current_step.detail.order_status }}
						td
							div.label 発送区分
							div.input {{ vm.task['default'].data.current_step.detail.delivery_type }}
					tr(ng-if="vm.task['default'].data.current_step.detail.certificate_stamp")
						td
							div.label 証紙
							div.input {{ vm.task['default'].data.current_step.detail.certificate_stamp }}
					tr
						td
							div.label Eメール
							div.input {{ vm.task['default'].data.current_step.detail.shipping_email }}
				div#count-down
kenpin-failed(
is-shown='vm.isShowReasion',
on-close='vm.onDetailsClose()',
)

style(type="text/css").
	.table {
		position: relative;
	}

	.table #count-down {
		font-size: 50px;
		font-weight: 700;
		text-align: right;
		position: absolute;
		right: 125px;
		top: 0;
	}