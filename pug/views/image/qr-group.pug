md-virtual-repeat-container.content(flex='grow')

    div.search(layout='row')
        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
            i.icons8-search
            input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
            button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
        div.cell.heading-btns(flex layout='row', layout-align='start center')
            b 本日のエラータスクの合計: {{vm.total_error}}
        div.cell.heading-btns(flex layout='row', layout-align='start center')
            b 待機中のタスクの合計: {{vm.total_waiting}}
        div.cell.heading-btns(flex layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-menu(md-position-mode="target-right target")
                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                    i.icons8-more
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item
                        md-button(ng-click='vm.reloadQrgroup()') リロード
                    md-menu-item
                        md-button(ng-click='vm.deleteQrgroup()') 削除

    md-divider

    div.default-row(layout='row' layout-align='start center')
        div.cell.flex.col-40
        div.cell.flex.col-150
            b タスクID
        div.cell.flex.col-300
            b プランニング日
        div.cell.flex.col-150
            b ユーザー名
        div.cell.flex.col-150
            b 状態
        div.cell.flex.col-150
            b 進行度
        div.cell.flex.col-300
            b URL
        div.cell.flex.col-300
            b エラーメッセージ
        div.cell.flex.cell-action(layout='row', layout-align='end center')
    div.default-row(layout='row' layout-align='start center' md-virtual-repeat='qrgroup in vm.infiniteQrgroups' md-on-demand ng-show='qrgroup.TaskGroupQRSheetQueue')
        div.cell.flex.col-40
            md-checkbox(ng-model='qrgroup.selected' aria-label='select {{ qrgroup.TaskGroupQRSheetQueue.id }}' ng-if='qrgroup.TaskGroupQRSheetQueue')
        div.cell.flex.col-150 {{ qrgroup.TaskGroupQRSheetQueue.id }}
        div.cell.flex.col-300 {{ qrgroup.TaskGroupQRSheetQueue.date }}
        div.cell.flex.col-150 {{ qrgroup.TaskGroupQRSheetQueue.user_nickname }}
        div.cell.flex.col-150 {{ qrgroup.status }}
        div.cell.flex.col-150 {{ qrgroup.percent }}
        div.cell.flex.col-300: a.link(ng-if="qrgroup.TaskGroupQRSheetQueue.url_archive !== null" href='{{ qrgroup.TaskGroupQRSheetQueue.url_archive  }}', target='_blank') Link TaskGroupQRSheets
        div.cell.flex.col-300 {{ qrgroup.error }}
        div.cell.flex.cell-action(layout='row', layout-align='end center')
            div.text-small {{  qrgroup.TaskGroupQRSheetQueue.created }}


    div.pagination-loading(style='display: none;')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした