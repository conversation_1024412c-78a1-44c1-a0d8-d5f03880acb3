md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 400px;margin-top:22px")
    md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
md-virtual-repeat-container.content(flex='grow')

    div.search(layout='row')
        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
            i.icons8-search
            input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
            button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
        div.cell.heading-btns(flex layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-menu(md-position-mode="target-right target")
                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                    i.icons8-more
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item
                        md-button(ng-click='vm.getPrintPaper()') 印刷データ取得

    md-divider

    div.default-row(layout='row' layout-align='start center')
        div.cell.flex.col-40
        div.cell.flex.col-250(style="padding-left:40px")
            b 注文番号
    div.default-row(layout='row' layout-align='start center' md-virtual-repeat='paper_print in vm.infinitePaperPrint' md-on-demand)
        div.cell.col-40
            md-checkbox(ng-model='paper_print.selected' aria-label='select {{ paper_print.Order.id }}' ng-if='paper_print.Order')
        div.cell.flex.col-250(layout='column' ng-if='paper_print.Order')
            div {{ paper_print.Order.number }} ({{ paper_print.OrderOptionalKeyJoin.customer_order_number}})

    div.pagination-loading(style='display: none;')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした