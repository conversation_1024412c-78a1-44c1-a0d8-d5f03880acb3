.block-wide.management(layout="column")
	.block-wide(layout='row' flex='100')
		md-content.sidebar(flex='none', layout='column')
			div.logo: a(href): md-icon(md-svg-src='img/logo.svg', aria-label='Logo')

			div.navbar
				nav: ul
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('シングルタスク')")
						div(flex='grow'): a(ui-sref='image.single') シングルタスク
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('グループタスク')")
						div(flex='grow'): a(ui-sref='image.group') グループタスク
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('納品書のメール発行プロセスの監視')")
						div(flex='grow'): a(ui-sref='image.delivery') 納品書のメール発行プロセスの監視
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('指示書発行プロセス監視')")
						div(flex='grow'): a(ui-sref='image.qrgroup') 指示書発行プロセス監視
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('グループ印刷データのメール発行プロセスの監視')")
						div(flex='grow'): a(ui-sref='image.uv') グループ印刷データのメール発行プロセスの監視
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('昇華印刷画像の取得')")
						div(flex='grow'): a(ui-sref='image.sublimation') 昇華印刷画像の取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('DTF印刷画像の取得')")
						div(flex='grow'): a(ui-sref='image.dtf') DTF印刷画像の取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('資材印刷画像の取得')")
						div(flex='grow'): a(ui-sref='image.material') 資材印刷画像の取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('テキスタイル印刷画像の取得')")
						div(flex='grow'): a(ui-sref='image.textile') テキスタイル印刷画像の取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('紙印刷データの取得')")
						div(flex='grow'): a(ui-sref='image.paper_print') 紙印刷データの取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('3Dデータの取得')")
						div(flex='grow'): a(ui-sref='image.data_3d') 3Dデータの取得
					li(ui-sref-active='active' layout='row' layout-align='center center' ng-if="vm.showNav('外部工場印刷データの取得')")
						div(flex='grow'): a(ui-sref='image.external_factory_print_data') 外部工場印刷データの取得

			div.sidebar-secondary.image-sidebar
				sidebar-secondary-menu(statuses='vm.imageStatuses')

			div.sidebar-secondary.image-sidebar(ng-if='vm.imageCustomers.length')
				sidebar-secondary-menu-customer(customers='vm.imageCustomers')

			div.sidebar-secondary.image-sidebar(ng-if='vm.types.length')
				sidebar-secondary-menu-type(types='vm.types')

		div.layout-column.block-min-width
			header.with-subheader(flex="none")
				div.subheader-th(layout='row' layout-align="start center")

					div(style="width:11%;")
						div(pick-date-header)
							div {{ vm.date | dateJapan : 'day' }}
							md-datepicker(ng-model='vm.date' ng-change='vm.onDateChange()' md-placeholder='Enter date' md-open-on-focus)

					div(flex, layout='row', layout-align='end center').user-info
						user-manage

			md-content(flex='grow' layout='column' ui-view)

		.loading-list
			md-progress-circular(md-diameter='80' md-mode='indeterminate')

