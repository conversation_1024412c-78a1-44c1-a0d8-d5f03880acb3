md-virtual-repeat-container.content(flex='grow')

	div.search(layout='row')
		div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
			i.icons8-search
			input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
			button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
		div.cell.heading-btns(flex layout='row', layout-align='start center')
			b 本日のエラータスクの合計: {{vm.total_error}}
		div.cell.heading-btns(flex layout='row', layout-align='start center')
			b 待機中のタスクの合計: {{vm.total_waiting}}
		div.cell.heading-btns(flex layout='row', layout-align='end center')
			md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
			md-menu(md-position-mode="target-right target")
				md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
					i.icons8-more
				md-menu-content.menu-small(md-menu-align-target)
					md-menu-item
						md-button(ng-click='vm.reloadImage()') リロード
					md-menu-item
						md-button(ng-click='vm.getPrintImage()') 印刷画像取得

	md-divider

	div.default-row(layout='row' layout-align='start center')
		div.cell.flex.col-40
		div.cell.flex.col-150
			b タスクID
		div.cell.flex.col-300
			b 注文番号
		div.cell.flex.col-150
			b 画像
		div.cell.flex.col-150
			b 状態
		div.cell.flex.col-400
			b エラーメッセージ
		div.cell.flex.cell-action(layout='row', layout-align='end center')
	div.default-row(layout='row' layout-align='start center' md-virtual-repeat='image in vm.infiniteImages' md-on-demand ng-show='image.OrderItemTask')
		div.cell.flex.col-40
			md-checkbox(ng-model='image.selected' aria-label='select {{ image.OrderItemTask.id }}' ng-if='image.OrderItemTask')
		div.cell.flex.col-150 {{ image.OrderItemTask.id }}
		div.cell.flex.col-300 {{ image.OrderJoin.number }}
		div.cell.flex.col-150
			img.img-40(ng-src='{{ image.OrderItemTask.small_preview_image_url }}' ng-click='vm.showLargeImage($event, image)' ng-style="{'cursor': 'pointer'}")
		div.cell.flex.col-150 {{ image.status }}
		div.cell.flex.col-400 {{ image.error }}
		div.cell.flex.cell-action(layout='row', layout-align='end center')
			div.text-small {{  image.OrderItemTask.created_at }}


	div.pagination-loading(style='display: none;')
		md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした