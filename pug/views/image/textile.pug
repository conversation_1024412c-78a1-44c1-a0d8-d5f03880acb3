md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 400px;margin-top:22px")
    md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}
md-virtual-repeat-container.content(flex='grow')

    div.search(layout='row')
        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
            i.icons8-search
            input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
            button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
        div.cell.heading-btns(flex layout='row', layout-align='start center')
            b 本日のタスクの合計: {{vm.total_textile}}
        div.cell.heading-btns(ng-style="{'margin': 'auto','margin-left':'176px'}")
            md-menu(md-position-mode="target-right target")
                div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeFilter.value }}
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item(ng-repeat='filter in vm.statusFilter')
                        md-button(ng-click='vm.setActiveFilter(filter)' ng-disabled='filter.key === vm.activeFilter.key') {{ filter.value }}
        div.cell.heading-btns(flex layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-menu(md-position-mode="target-right target")
                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                    i.icons8-more
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item
                        md-button(ng-click='vm.getPrintTextile()') 印刷画像取得

    md-divider

    div.default-row.default-row-new(layout='row' layout-align='start center')
        div.cell.flex.col-40
        div.cell.flex.col-250
            b プリンター
        div.cell.flex.col-40
        div.cell.flex.col-150
            b タスクID
        div.cell.flex.col-500(style="min-width:400px; margin-left:15px")
            b 注文番号
        div.cell.flex.col-150
            b 画像
        div.cell.flex.col-250
            b ステータス
        div.cell.flex.col-150
            b ⅮL日時
        div.cell.flex.col-150
            b ⅮLユーザー
    div.default-row.default-row-new(layout='row' layout-align='start' ng-repeat='printer in vm.printers' md-on-demand ng-show='printer')
        div.cell.col-40
            md-checkbox(ng-model='printer.selected' aria-label='select {{ printer.id }}' ng-if='printer' ng-change="vm.selectAllByPrinter(printer)")
        div.cell.flex.col-250(layout='column')
            div {{ printer.title }}
        div.cell.flex.col-40(layout='column')
            md-checkbox.m-bottom-25(ng-repeat="textile in printer.data" ng-model='textile.selected' aria-label='select {{ textile.OrderItemTask.id }}' ng-if='textile.OrderItemTask' ng-change="vm.selectTask(textile)")
        div.cell.flex.col-150(layout='column')
            div.m-bottom-25.h-20-px(ng-repeat="textile in printer.data") {{ textile.OrderItemTask.id }}
        div.cell.flex.col-500(layout='column' style="min-width:400px")
            div.m-bottom-25.h-20-px(ng-repeat="textile in printer.data") {{ textile.OrderJoin.number }} ({{textile.OrderOptionalKeyJoin.value}})
        div.cell.flex.col-150(layout='column')
            div.m-bottom-25.h-20-px(ng-repeat="textile in printer.data")
                img.img-25(ng-src='{{ textile.OrderItemTask.small_preview_image_url }}' ng-click='vm.showLargeImage($event, textile)' ng-style="{'cursor': 'pointer'}")
        div.cell.flex.col-250(layout='column')
            div.m-bottom-25.h-20-px(ng-repeat="textile in printer.data") {{ textile.TaskStepStatusJoin.title }}
        div.cell.flex.col-150(layout='column')
            div.m-bottom-25.h-20-px.text-small(ng-repeat="textile in printer.data") {{  textile.OrderItemTask.download_time }}
        div.cell.flex.col-150(layout='column')
            div.m-bottom-25.h-20-px(ng-repeat="textile in printer.data") {{  textile.UserJoin.username }}

    div.pagination-loading(style='display: none;')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした