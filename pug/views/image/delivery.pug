md-virtual-repeat-container.content(flex='grow')

    div.search(layout='row')
        div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search): form
            i.icons8-search
            input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
            button.reset(type='button' ng-click="vm.clearSearch()"): i.icons8-delete-2
        div.cell.heading-btns(flex layout='row', layout-align='start center')
            b 本日のエラータスクの合計: {{vm.total_error}}
        div.cell.heading-btns(flex layout='row', layout-align='start center')
            b 待機中のタスクの合計: {{vm.total_waiting}}
        div.cell.heading-btns(flex layout='row', layout-align='end center')
            md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
            md-menu(md-position-mode="target-right target")
                md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
                    i.icons8-more
                md-menu-content.menu-small(md-menu-align-target)
                    md-menu-item
                        md-button(ng-click='vm.reloadDelivery()') リロード
                    md-menu-item
                        md-button(ng-click='vm.deleteDelivery()') 削除

    md-divider

    div.default-row(layout='row' layout-align='start center')
        div.cell.flex.col-40(ng-style='{"min-width" : "3%"}')
        div.cell.flex.col-100(ng-style='{"min-width" : "7%"}')
            b タスクID
        div.cell.flex.col-150(ng-style='{"min-width" : "9%"}')
            b プランニング日
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}')
            b ユーザー名
        div.cell.flex.col-100(ng-style='{"min-width" : "6.5%"}')
            b 状態
        div.cell.flex.col-100(ng-style='{"min-width" : "6.5%"}')
            b 進行度
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}')
            b URL
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}')
            b
        div.cell.flex.col-100(ng-style='{"min-width" : "10%"}')
            b
        div.cell.flex.col-200(ng-style='{"min-width" : "13%"}')
            b エラーメッセージ
        div.cell.flex.col-200(ng-style='{"min-width" : "13%"}')
            b
        div.cell.flex.cell-action(layout='row', layout-align='end center')
    div.default-row(layout='row' layout-align='start center' md-virtual-repeat='delivery in vm.infiniteDeliveries' md-on-demand ng-show='delivery.DeliveryNodesQueueItem')
        div.cell.flex.col-40(ng-style='{"min-width" : "3%"}')
            md-checkbox(ng-model='delivery.selected' aria-label='select {{ delivery.DeliveryNodesQueueItem.id }}' ng-if='delivery.DeliveryNodesQueueItem')
        div.cell.flex.col-100(ng-style='{"min-width" : "7%"}') {{ delivery.DeliveryNodesQueueItem.id }}
        div.cell.flex.col-150(ng-style='{"min-width" : "9%"}') {{ delivery.DeliveryNodesQueueItem.date }}
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}') {{ delivery.DeliveryNodesQueueItem.user_nickname }}
        div.cell.flex.col-100(ng-style='{"min-width" : "6.5%"}') {{ delivery.status }}
        div.cell.flex.col-100(ng-style='{"min-width" : "6.5%"}') {{ delivery.percent }}
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}'): a.link(ng-if="delivery.DeliveryNodesQueueItem.url_archive !== null" href='{{ delivery.DeliveryNodesQueueItem.url_archive  }}') Link Delivery
        div.cell.flex.col-100(ng-style='{"min-width" : "9%"}'): a.link(ng-if="delivery.DeliveryNodesQueueItem.url_archive !== null" ng-click='vm.openPopup(delivery.DeliveryNodesQueueItem.url_yupacket_archive)' href='{{ delivery.DeliveryNodesQueueItem.url_yupacket_archive  }}') ゆうパケット
        div.cell.flex.col-100(ng-style='{"min-width" : "10%"}'): a.link(ng-if="delivery.DeliveryNodesQueueItem.url_archive !== null" ng-click='vm.openPopup(delivery.DeliveryNodesQueueItem.url_not_yupacket_archive)' href='{{ delivery.DeliveryNodesQueueItem.url_not_yupacket_archive  }}') ゆうパケット以外
        div.cell.flex.col-200(ng-style='{"min-width" : "13%"}') {{ delivery.error }}
        div.cell.flex.cell-action(layout='row', layout-align='end center' ng-style='{"min-width" : "13%"}')
            div.text-small {{  delivery.DeliveryNodesQueueItem.created }}


    div.pagination-loading(style='display: none;')
        md-progress-circular(md-diameter='40' md-mode='indeterminate')

button.scroll-top-btn(type='button' virtual-repeat-scroll-top) トップに戻る

.nothing-found-msg(ng-if='vm.pagination.searchFailed'): div 何も見つかりませんでした