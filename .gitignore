### Sass template
.sass-cache/
*.css.map

### Node template
# Logs
logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules
jspm_packages

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

### JetBrains template
.idea/

## File-based project format:
*.iws

## Plugin-specific files:

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

### OSX template
*.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

### Manually added
bower_components/angular-material/layouts
bower_components/angular-material/modules
bower_components/angular-ui-router/src
bower_components/jquery/external
bower_components/jquery/src
bower_components/moment/src
bower_components/moment/templates
bower_components/moment/typing-tests
bower_components/moment/locale/*
!bower_components/moment/locale/ja.js
bower_components/ngclipboard/test
bower_components/ngclipboard/src
bower_components/ngclipboard/.jshintrc
bower_components/ngclipboard/.travis.yml
bower_components/ngclipboard/.jscsrc
bower_components/ngclipboard/Gruntfile.js
bower_components/ngclipboard/.editorconfig

bower_components/chart.js/.github
bower_components/chart.js/docs
bower_components/chart.js/samples
bower_components/chart.js/scripts
bower_components/chart.js/src
bower_components/chart.js/test
bower_components/chart.js/.bower.json
bower_components/chart.js/.codeclimate.yml
bower_components/chart.js/.editorconfig
bower_components/chart.js/.eslintignore
bower_components/chart.js/.eslintrc
bower_components/chart.js/.gitignore
bower_components/chart.js/.npmignore
bower_components/chart.js/.travis.yml
bower_components/chart.js/composer.json
bower_components/chart.js/config.jshintrc
bower_components/chart.js/CONTRIBUTING.md
bower_components/chart.js/karma.conf.ci.js
bower_components/chart.js/karma.conf.js
bower_components/chart.js/karma.coverage.conf.js
bower_components/chart.js/gulpfile.js
bower_components/chart.js/MAINTAINING.md

bower_components/ng-file-upload/*
!bower_components/ng-file-upload/ng-file-upload.js
!bower_components/ng-file-upload/ng-file-upload.min.js
!bower_components/ng-file-upload/README.md
!bower_components/ng-file-upload/.bower.json

bower_components/lodash/.travis.yml
bower_components/lodash/doc
bower_components/lodash/yarn.lock
bower_components/lodash/perf
bower_components/lodash/.gitattributes
bower_components/lodash/fp
bower_components/lodash/.markdown-doctest-setup.js
bower_components/lodash/test
bower_components/lodash/lib
bower_components/lodash/.editorconfig
bower_components/lodash/vendor
bower_components/lodash/.github
bower_components/lodash/.jscsrc
bower_components/lodash/.gitignore

bower_components/qrcode-generator/java
bower_components/qrcode-generator/as3
bower_components/qrcode-generator/php
bower_components/qrcode-generator/python
bower_components/qrcode-generator/ruby
bower_components/qrcode-generator/ts

/js/plugins/archive

/build
/css
/views
/app
/config
.awspublish*
index.html