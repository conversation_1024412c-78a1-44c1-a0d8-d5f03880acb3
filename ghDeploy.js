var fs = require('fs');
var path = require('path');
var ghpages = require('gh-pages');

var outputPath = 'build';

var logger = function (msg) {
  console.log(msg);
};

logger('Starting deployment to GitHub.');

var GIT_REMOTE_NAME = 'origin';
var COMMIT_MESSAGE = 'Updates';

const options = {
  logger: logger,
  remote: GIT_REMOTE_NAME,
  message: COMMIT_MESSAGE
};

ghpages.publish(outputPath, options, function(err) {
  if (err) {
    logger('GitHub deployment done. STATUS: ERROR.');
    throw err;
  } else {
    logger('GitHub deployment done. STATUS: SUCCESS.');
  }
});