var gulp = require('gulp'),
    connect = require('gulp-connect'),
    pug = require('gulp-pug'),
    sass = require('gulp-sass'),
    autoprefixer = require('gulp-autoprefixer'),
    merge = require('merge-stream'),
    changed = require('gulp-changed'),
    ngAnnotate = require('gulp-ng-annotate'),
    useref = require('gulp-useref'),
    gulpif = require('gulp-if'),
    gulpIgnore = require('gulp-ignore'),
    uglify = require('gulp-uglify'),
    cleanCss = require('gulp-clean-css'),
    awspublish = require('gulp-awspublish');

/******** Live Server *******/
gulp.task('connect', function() {
  connect.server({
    port: 7110,
    livereload: true
  });
});

gulp.task('connect-build', function() {
  connect.server({
    root: 'build',
    port: 7110,
    livereload: true
  });
});

/******* SCSS FILES *******/
gulp.task('scss', function() {
  return gulp.src('scss/**/*.scss')
    .pipe(sass({
      outputStyle: 'expanded',
      sourceComments: true
    }).on('error', sass.logError))
    .pipe(autoprefixer({
      browsers: ['last 2 versions', 'ie 10']
    }))
    .pipe(gulp.dest('css'))
    .pipe(connect.reload());
});

gulp.task('sсss-material', function() {
  return gulp.src('bower_components/angular-material/angular-material.scss')
    .pipe(sass({
      outputStyle: 'expanded',
      sourceComments: true
    }).on('error', sass.logError))
    .pipe(autoprefixer({
      browsers: ['last 2 versions', 'ie 10']
    }))
    .pipe(gulp.dest('css'))
    .pipe(connect.reload());
});

/********* PUG ********/
gulp.task('pug', function() {
  return merge(
    gulp.src('pug/index.pug')
      .pipe(pug({
        doctype: 'html',
        pretty: true,
        locals: {
          electron: false
        }
      }))
      .pipe(gulp.dest('')),
    gulp.src('pug/views/**/*.pug')
      .pipe(pug({
        doctype: 'html'
      }))
      .pipe(gulp.dest('views'))
  );
});

gulp.task('pug-electron', function() {
  gulp.src('pug/index.pug')
    .pipe(pug({
      doctype: 'html',
      pretty: true,
      locals: {
        electron: true
      }
    }))
    .pipe(gulp.dest(''));
});

/******** JS **********/
gulp.task('js', function () {
  gulp.src(['js/**/*.js'])
    .pipe(connect.reload());
});

/********* HTML ***************/
gulp.task('html', function () {

  return merge(
    gulp.src('*.html')
      .pipe(connect.reload()),
    gulp.src('views/**/*.html')
      .pipe(changed('views'))
      .pipe(connect.reload())
  );

});

/*********** BUILD ***************/

var buildFolder = 'build';

gulp.task('build-concat', function () {
  return gulp.src('index.html')
    .pipe(useref())
    .pipe(gulpif('js/api-config.js', gulp.dest(buildFolder)))
    .pipe(gulpIgnore.exclude('js/api-config.js'))
    .pipe(gulpif('local_config/chart.js', gulp.dest(buildFolder)))
    .pipe(gulpIgnore.exclude('local_config/chart.js'))
    .pipe(gulpif('*.js', ngAnnotate()))
    .pipe(gulpif('*.css', cleanCss()))
    .pipe(gulp.dest(buildFolder));
});

gulp.task('build-copy', function () {
  return merge(
    gulp.src('views/**/*.html')
      .pipe(gulp.dest(buildFolder + '/views')),
    gulp.src('img/**/*')
      .pipe(gulp.dest(buildFolder + '/img')),
    gulp.src('fonts/**/*')
      .pipe(gulp.dest(buildFolder + '/fonts')),
    gulp.src('local_config/**/*')
        .pipe(gulp.dest(buildFolder + '/local_config'))
  );
});

gulp.task('build', ['build-concat', 'build-copy'], function () {
  
  // minify js files (cannot be done through build-concat)
  var jsSrc = buildFolder + '/js/';
  
  gulp.src([jsSrc + 'app.min.js', jsSrc + 'vendor.min.js'])
    .pipe(uglify({mangle: true}))
    .pipe(gulp.dest(buildFolder + '/js'));
});

var localAwsConfig = {
  getAwsConf: function (environment) {
    var conf = require('./config/aws');
    if (!conf[environment]) {
      throw 'No aws conf for env: ' + environment;
    }
    if (!conf[environment + 'Headers']) {
      throw 'No aws headers for env: ' + environment;
    }
    return {keys: conf[environment], headers: conf[environment + 'Headers']};
  }
};

function publishAWS(type) {
  var awsConf = localAwsConfig.getAwsConf(type);
  var publisher = awspublish.create(awsConf.keys);
  return gulp.src(buildFolder+"/**/**")
    .pipe(awspublish.gzip({ext: ''}))
    .pipe(publisher.publish(awsConf.headers))
    .pipe(publisher.cache())
    .pipe(publisher.sync())
    .pipe(awspublish.reporter());
}

gulp.task('publish:aws',['build'], function () {
  return publishAWS('developer');
});
gulp.task('publish:aws:production',['build'], function () {
  return publishAWS('production');
});

/*********** Watch ************/
gulp.task('watch', ['sсss-material', 'scss', 'pug', 'connect'], function() {

  gulp.watch('scss/**/*.scss', ['scss']);
  gulp.watch('bower_components/angular-material/angular-material.scss', ['sсss-material']);
  gulp.watch('pug/**/*.pug', ['pug']);
  gulp.watch('*.html', ['html']);
  gulp.watch('js/**/*.js', ['js']);

});


gulp.task('default', ['watch']);