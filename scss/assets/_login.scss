.login-page {
  background: #ececec url('../img/background.jpg') 50%;
  background-size: cover;
  color: $black;
  box-sizing: border-box;
  .logo {
    text-align: center;
    svg {
      &, & .st0 {
        fill: $dark;
      }
    }
  }
  .login-block {
    width: 225px;
  }
}

.login-block {
  header {
    text-align: center;
    line-height: $line-height-login;
    h1 {
      margin: 0 0 30px;
      font-size: rem(2.4);
      font-weight: normal;
    }
    p {
      font-size: rem(1.2);
      color: $btn-link-color-default;
      margin: -16px 0 30px;
    }
  }
  md-input-container {
    display: block;
  }
  .submit-container {
    margin: 25px 3px 0;
  }
  .login-btn {
    font-size: rem(1.4);
    padding: 3px 6px;
    margin: 0;
  }
  .bottom-buttons {
    font-size: rem(1.4);
    margin-top: 9px;
    .md-button {
      font-size: inherit;
      color: $btn-link-color-default;
      line-height: 26px;
      min-height: inherit;
      min-width: inherit;
      padding: 0 10px;
      margin-bottom: 0;
    }
  }
}