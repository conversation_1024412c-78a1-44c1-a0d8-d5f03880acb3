.dtf-transcription-left {
  background: $input-blue-label;
  .logo {
    margin-top: 30px;
    margin-bottom: 5px;
    md-icon {
      width: 80px;
      svg {
        &, & .st0 {
          fill: $black;
        }
      }
    }
  }

  .image-messages {

    .message-inner {
      max-height: 100%;
    }

    .finish-icon {
      width: 170px;
      height: 170px;
      polyline {
        stroke: $blue;
      }
    }

    i.finish-icon {
      font-size: 170px;
      line-height: 170px;
      width: auto;
      height: auto;
      color: $input-error-grey;
    }

    .qr-icon {
      width: 146px;
      height: 146px;
      fill: $blue;
    }
    p {
      font-size: 24px;
      line-height: 1;
      color: $blue;
      margin: 20px 0 0;
    }
  }

  .remark {
    width: 90%;
    margin: 30px auto;
    padding: 20px;
    border: 1px solid #afafaf;
    font-size: 20px;
    max-height: 30%;
  }
  .giftset {
    width: 90%;
    margin: 0 auto;
    font-size: 40px;
    max-height: 10%;
    text-align: center;
    color: red;
  }

  .zoom-in-image {
    display: flex;
    width: 100%;
    min-height: 30%;
    overflow: hidden;
  }

}