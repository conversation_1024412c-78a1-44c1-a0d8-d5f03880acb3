.dtf-transcription-right {
  background: $default-dark-grey;

  > header {
    padding: 0 50px;
    height: 76px;
  }

  .content {
    background: inherit;
    color: $black;
  }

  .btn-back {
    min-width: initial;
    min-height: initial;
    line-height: 1;
    padding: 11px 13px;
    margin: 0 0 0 -20px;
    md-icon {
      width: 17px;
      height: 17px;
      min-width: 17px;
      min-height: 17px;
      display: inline-block;
      vertical-align: middle;
      margin-right: 13px;
      svg path {
        fill: $dark;
      }

    }
    span {
      color: $dark;
      display: inline-block;
      vertical-align: middle;
      line-height: 17px;
    }
  }

  h2 {
    font-size: rem(1.8);
    padding: 0;
    margin: 0 0 25px;
    font-weight: normal;
  }

  .btn-dtf-transcription {
    min-width: 250px;
    height: 50px;
    font-size: 18px;
    &[disabled] {
      background-color: #cfcfcf !important;
      opacity: 0.57 !important;
      &:hover {
        background-color: #cfcfcf !important;
      }
    }
  }

  .md-primary {
    background-color: $default-light-grey2 !important;
  }

  .buttons {
    .md-button {
      display: block;
      margin-left: auto;
      margin-right: auto;
      &:not(:last-child){
        margin-bottom: 15px;
      }
    }
    &.buttons-fail {
      margin-top: 30px;
    }
  }

  .scan-qr, .enter-qr {
    max-height: 100%;
  }

  .error-text {
    color: $red;
    margin: -5px 0px -25px;
    padding: 0;
    &.scanner-error {
      margin-bottom: 10px;
    }
  }

  .enter-qr {

    .form-group {
      position: relative;
      width: 250px;
      margin: 42px auto 20px;
      .simple-input {
        display: block;
        width: 100%;
      }
      .reset {
        @include simple-button();
        @include no-outline();
        padding: 2px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        margin-top: -2px;
        right: 2px;
        color: $grey4;
        i {
          display: block;
          line-height: 1;
          font-size: 18px;
          color: #888;
        }
        &:hover i {
          color: darken($grey4, 25%);
        }
      }
    }

  }

  .product-info {

    width: 100%;

    .info-form {
      padding-bottom: 10px;
    }

    .info-embroidery-form {
      padding-bottom: 10px;
    }

    .col-1 {
      margin-right: 7px;
    }

    .input-group {
      margin-bottom: 30px;
      .label {
        color: $dark2;
        margin-bottom: 12px;
      }
      .input {
        font-size: 18px;
      }
    }

    .types {
      max-width: 95%;
      margin: 70px auto 5px;
      color: $dark2;
      text-align: center;
      overflow-x: auto;

      .type {
        margin: 0 15px 5px;
      }

      .image {
        width: 60px;
        height: 60px;
        img {
          max-width: 100%;
          max-height: 100%;
          display: block;
        }
      }

      .out {
        opacity: 0.6;
      }

      .name {
        margin-top: 14px;
        margin-bottom: 15px;
      }
    }

  }

  .simple-input {
    border: none;
    border-bottom: 1px solid $grey4;
    background: transparent;
    padding: 4px 27px 6px 4px;
    color: $dark;
    box-sizing: border-box;
    font-size: rem(1.8);
    transition: border-color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    &:focus {
      outline: none;
      padding-bottom: 5px;
      border-bottom: 2px solid darken($grey4, 22%);
    }
  }

  .step-info {
    margin: 0 auto 20px;
    padding: 0 10px;
    border-collapse: collapse;

    width: 66%;

    td {
      border-top: 1px solid darken($default-input-border, 10%);
    }
    td:first-child:not(:only-child) {
      border-right: 1px solid darken($default-input-border, 10%);
    }

    td:not(:only-child) {
      width: 50%;
    }

    .label {
      color: $dark2;
      margin-bottom: 5px;
      font-size: 12px;
    }
    .input {
      font-size: 16px;
    }
    td {
      vertical-align: top;
      padding: 9px 7px 8px;
    }
    td:first-child {
      padding-left: 3px;
    }
    td:last-child {
      padding-right: 3px;
    }
  }

  .item-list {

    text-align: center;
    margin: 30px auto 0;
    width: 66%;
    overflow: auto;
    border-bottom: 1px solid lighten($default-input-border, 10%);

    max-height: calc(100vh - 394px);
    min-height: 120px;

    md-checkbox {
      margin: 10px 0 10px 0;
    }

    > div {
      display: table;
      margin: 0;
      width: 100%;
      text-align: left;
      border-collapse: collapse;
    }

    .cell {
      display: table-cell;
      vertical-align: middle;
      padding: 6px 7px 6px;
    }

    .cell:first-child {
      padding-left: 3px;
    }
    .cell:last-child {
      padding-right: 3px;
    }

    .heading {
      display: table-row;
      font-size: rem(1.2);
      color: $dark2;
      .cell {
        padding-top: 8px;
        padding-bottom: 8px;
      }
      .cell:first-child {
        padding-left: 7px;
      }
      .cell:last-child {
        padding-right: 7px;
      }
    }

    .default-row {
      display: table-row;
      font-size: rem(1.3);
    }

    .heading, .default-row {
      border-bottom: 1px solid lighten($default-input-border, 10%);
    }

    .default-row:last-child {
      border-bottom: none;
    }

    .image {
      margin: 0 12px 0 0;
      img {
        max-width: 32px;
        max-height: 32px;
        display: block;
      }
    }

  }

  .md-button.btn-more {
    width: 30px!important;
    min-width: 30px!important;
    padding: 6px 2px!important;
    margin-left: 10px;
  }

  .steps-container {

    background: #e0e0e0;
    text-align: center;
    padding-bottom: 2px;

    .steps {
      max-width: 95%;
      margin: 10px auto 3px;
      color: $dark2;
      overflow-x: auto;
      display: inline-block;

      .step {
        margin: 0 10px;
        padding: 5px 12px;

        &.current {
          background: #eee;

          border-radius: 5px;
        }

      }

      .image {
        width: 60px;
        height: 60px;
        margin: 0 auto 9px;
        img {
          max-width: 100%;
          max-height: 100%;
          display: block;
        }
      }

      .out {
        opacity: 0.6;
      }

      .name {
        margin-top: 2px;
        font-weight: 400;
        margin-bottom: 9px;
      }
    }
  }

  user-manage admin-global-search,.btn-search{
    display: none !important;
  }

}


.dtf-transcription-right.multiple {

  .buttons-actions {
    position: absolute;
    left: 50%;
    right: 0;
    margin-top: -72px;
  }

  .steps-container {
    margin-top: 73px;
  }

  .product-info .info-form {
    margin-top: 5px;
    padding-bottom: 0;
  }

  .steps-container .steps {
    margin-top: 10px;
  }

  .text-error {
    color: red;
    font-size: 12px;
    padding: 5px;
  }

}

.btn-embroidery-info {
  color: red !important;
  background-color: #cfcfcf !important;
}

.embroidery-info {
  margin: 0 auto 0;;
  padding: 0 10px;
  border-collapse: collapse;
  width: 95%;
}

.common-info {
  height: auto;
  overflow: hidden;
  margin-bottom: 25px;
}

.detail-info {
  margin-bottom: 30px;
}

.detail-value-info {
  height: 200px;
  overflow-y: auto;
  margin-bottom: 10px;
  word-break: break-word;
}

.detail-header-info {
  overflow: hidden;
  margin-bottom: 20px;
  font-weight: bold;
}

.size-detail-info {
  height: auto;
  overflow: hidden;
  margin-bottom: 20px;
}

.square-color {
  width: 20px;
  height: 20px;
}

.btn-copy-text {
  background: none;
  border: none;
  line-height: 1;
  padding: 4px;
  right: 6px;
  bottom: 6px;
  color: #bdbdbd;
  font-size: 16px;
}

.btn-after-copy {
  width: 15px;
  height: 15px;
  right: 6px;
  bottom: 6px;
  z-index: -1;
  visibility: hidden;
}
.rotate-degree {
  &:after {
    content: "\00b0"
  }
}

@include media(1100px) {
  .dtf-transcription-right .btn-dtf-transcription {
    min-width: 230px;
  }
}