.dtf-transcription-container {
  @extend .block-wide;
}

#scanner-field {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 1px;
  color: rgb(238, 238, 238);
  border: none;
  background: rgb(238, 238, 238);
  outline: none;
}
.dtf-transcription-qr-groups{
  visibility: hidden;
  position: absolute;
  bottom: 1000px;
  .page {
    transform-origin: 0 0;
    transform: rotate(90deg) translate(0, -100%);
    height: 197.3mm;
    width: 284.3mm;
    page-break-after: always;
  }

  .qr-codes {
    display: flex;
    flex-direction: row;
    justify-content: space-between
  }

  .qr-codes .qr-code .title {
    padding: 0;
    margin: 5px 0 0
  }

  .qr-code-dummy {
    width: 210px;
    height: 210px;
    background: #000
  }

  .table {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
  }

  .cell {
    margin-right: 1%;
    box-sizing: border-box;
    margin-bottom: 5px;
  }

  .cell p {
    margin: 0;
    padding: 0
  }

  .cell .detail {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    height: 17px;
    white-space: nowrap;
  }

  .cell img {
    display: block;
    max-width: 100%;
  }
}

.large-image {
  width: 100%;
}

