.component-loading {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  md-progress-circular {
    margin: auto;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: white;
  }
  
  &.full {
    display: block;
  }
  
  &.partial {
    background: rgba(255, 255, 255, 0.55);
    md-progress-circular {
      opacity: 0.95;
      background: rgba(255, 255, 255, 0.55);
    }
  }
  
  &.stopped {
    display: none;
  }
  
}

.order-detail-wrapper  {

  .component-loading {
    top: 75px;
    background: #fafafa;

    md-progress-circular {
      background: #fafafa;
    }

    &.partial {
      background: rgba(255, 255, 255, 0.55);
      md-progress-circular {
        opacity: 0.95;
        background: rgba(255, 255, 255, 0.55);
      }
    }
  }

  .nothing-found-msg {
    top: 75px;
  }
  
}