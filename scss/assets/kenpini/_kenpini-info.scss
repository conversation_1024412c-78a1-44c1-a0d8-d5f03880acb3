.kenpini-header {
  border-bottom: 1px solid $default-input-border;
  header {
    background: #ffffff;
  }
  h2 {
    font-size: 15px;
    margin: 0;
    padding: 0;
    font-weight: normal;
    color: #212121;
    padding-left: 20px;
  }
}

.kenpini-info {
  background: #acd0dd;

  .step-info {
    margin: 0 auto 20px;
    padding: 0 10px;
    border-collapse: collapse;
    width: 66%;
    margin-top: 20px;

    td {
      border-top: 1px solid darken($default-input-border, 10%);
    }

    td:first-child:not(:only-child) {
      border-right: 1px solid darken($default-input-border, 10%);
    }

    td:not(:only-child) {
      width: 50%;
    }

    td {
      vertical-align: top;
      padding: 9px 7px 8px;
      width: 1240px;
    }

    td:first-child {
      padding-left: 3px;
    }

    td:last-child {
      padding-right: 3px;
    }

  }
  .task-info {
    width: 100%;
    margin: auto;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    td {
      width: auto;
      //text-align: center;
      border: none;
    }
    .task-info-header {
      font-size: 26px;
      font-weight: bold;
      margin: 0 50px;
    }

  }
  .center{
    display: flex;
    align-items: center;
  }
  md-checkbox.good {
    margin-right: 0;
    margin-left: 0;
  }

  .step-info-div table tbody{
    display: block;
  }

  .step-info tbody {
    height: 280px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .oit-div{
    position: relative;
  }
  .oit-str{
    font-size: 25px;
    right: 80px;
    position: absolute;
    top: -12px;
  }
}

kenpini-info {
  width: 100%;
}

kenpini-failed {
  .box-delivery-late{
    margin-bottom: 0px !important;
  }
}

.kenpini-check-all {
  color: red;
  font-size: 30px;
}

.kenpini-embroidery-info {
  margin: 50px auto 0;;
  padding: 0 10px;
  border-collapse: collapse;
  width: 60%;
}