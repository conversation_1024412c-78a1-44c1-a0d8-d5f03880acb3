// General color
$black: #212121;
$dark: #424242;
$dark2: #757575;

$grey: #616161;
$grey2: #e0e0e0;
$grey3: #f5f5f5;
$grey4: #9e9e9e;
$dark-grey: #bdbdbd;

$blue: #2196f3;
$blue2: #0980df;
$red: #f44336;

$default-light-grey: #fafafa;
$default-light-grey2: #e0e0e0;
$default-dark-grey: #eee;

$default-input-border: #c8c8c8;

// special input (for login)
$si-color: $dark2;
$si-border-color: rgba(224, 224, 224, 0.6);
$si-background: $default-light-grey;

// Login
$btn-link-color-default: #9e9e9e;
$line-height-login: 1.2;

// buttons
$dark-button: $grey2;
$btn-icon: #9e9e9e;

// Management
$sidebar-back: $default-dark-grey;
$content-back: $default-light-grey;
$highlighted-column: $grey3;
$search-border-bottom: #bdbdbd;
$color-text-small: $grey;

$sidebar-link-color: #757575;
$sidebar-link-active-color: #fff;

$sidebar-secondary-link: $grey;
$sidebar-secondary-link-active: $black;

$input-border-bottom-color: #777;
$default-border-color: $dark-grey;

// Management: sidenavs
$mng-sidenav-back: $default-light-grey;

$input-special-dark-color: #757575;
$input-special-dark-border-color: #545454;
$input-special-dark-background: #333;

// inputs (angular material)
$input-dark-color: #fff;
$input-dark-label: #757575;
$input-dark-border-bottom: #757575;
$input-dark-label-focus: darken(#fff, 9%);
$input-dark-border-bottom-focus: #fff;

$input-grey-color: #fff;
$input-grey-label: #999;
$input-grey-border-bottom: #dbdbdb;
$input-grey-label-focus: #999;
$input-grey-border-bottom-focus: #dbdbdb;

$input-blue-color: #fff;
$input-blue-label: #a9d9ff;
$input-blue-border-bottom: #87bbe4;
$input-blue-label-focus: #a9d9ff;
$input-blue-border-bottom-focus: #fff;

$input-error: rgba(144, 69, 0, 0.8);
$input-error-blue: rgba(144, 69, 0, 0.8);
$input-error-grey: rgb(216, 45, 3);

// ----- Qr block -------
$qr-left-back: #2196f3;
$qr-right-back: #424242;

$qr-header-color: #9e9e9e;
$qr-footer-color: #dbdbdb;

$simple-input-border-active: #dbdbdb;

// easing functions (from angular materials)
$swift-ease-out-duration: 0.2s;
$swift-ease-out-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);

// from bootstrap
$btn-padding-vertical: 6px;
$btn-padding-horizontal: 12px;
$btn-font-size: 14px;
$btn-line-height: 1.2;
$btn-border-radius: 2px;

$btn-default-color: #424242;
$btn-default-bg: #eee;
$btn-default-border: darken($btn-default-bg, 15%);

$btn-primary-color: #fff;
$btn-primary-bg: #2196f3;
$btn-primary-border: darken($btn-primary-bg, 5%);


// ----- row update animation ------
$update-animation-background: rgba(255,255,255,0.2);
$update-animation-transition-start: all 0.35s ease-out;
$update-animation-transition-end: all 0.6s ease-out;;

// -------- tabs (sidenav)
$tab-header-border-color: #333333;
$tab-header-link-color: $sidebar-secondary-link;