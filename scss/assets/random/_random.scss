.random-content .pick-date-header .ng-binding{
  width: 50px;
}

.random-content .pick-date-header > md-datepicker .md-datepicker-input-container {
  width: 60px;
}

.random-content .record-title {
  font-weight: bold;
  font-size: 18px;
  padding: 10px;
  position:relative;
}

.random-content .record-title.inspector {
  font-size: 30px;
}

.random-content .layout-column{
  padding: 20px;
}

.random-content .cell {
  width: 33%;
}

.random-content .default-row {
  height: auto;
}

.random-content .record-detail {
  height: 25px;
  padding: 0 100px;
}

.random-content .record-detail.inspector {
  font-size: 25px;
  height: 35px;
}

.random-content .record-total {
  padding: 0 10px;
  font-size: 170px;
}

.random-content .record-total.total-garment {
  font-size: 100px;
}

.random-content .edit-quantity-btn{
  font-size: 20%;
}

.random-content .middle{
  margin: 80px 0;
}

.total-garment-detail{
  position: absolute;
  right: 84px;
  top: 38px;
  font-size: 30px;
}