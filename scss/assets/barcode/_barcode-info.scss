.barcode-header {
  border-bottom: 1px solid $default-input-border;
  header {
    background: #ffffff;
  }
  h2 {
    font-size: 15px;
    margin: 0;
    padding: 0;
    font-weight: normal;
    color: #212121;
    padding-left: 20px;
  }
}
.barcode-info {
  background: #acd0dd;
  height: 100%;
  .step-info {
    margin: 0 auto 20px;
    padding: 0 10px;
    border-collapse: collapse;
    width: 66%;
    margin-top: 20px;
    td {
      border-top: 1px solid darken($default-input-border, 10%);
    }
    td:first-child:not(:only-child) {
      border-right: 1px solid darken($default-input-border, 10%);
    }
    td:not(:only-child) {
      width: 50%;
    }
    td {
      vertical-align: top;
      padding: 9px 7px 8px;
    }
    td:first-child {
      padding-left: 3px;
    }
    td:last-child {
      padding-right: 3px;
    }
  }
  .task-info {
    width: 95%;
    margin: auto;
    td {
      width: 25%;
      text-align: center;
      border: none;
    }
    .task-info-header {
      font-size: 26px;
      font-weight: bold;
      margin-right: 100px;
    }
  }
  md-checkbox.good{
    margin-right: 0;
    margin-left: 0;
    margin-top: 15px;
  }

}

barcode-info {
  width: 100%;
}

barcode-failed {
  .box-delivery-late{
    margin-bottom: 0px !important;
  }
}
