seirendelivery-left {
  .place-detail-table{
    width:100%;
    height:90%;
    font-size:60px;
    padding:0 50px 0 50px;
    vertical-align: top !important;
    display: flex;
    td {
      vertical-align: top !important;
    }
  }

  .place-detail-title{
    font-size:30px;
  }

  .place-detail-customer{
    font-size: 18px;
    margin: 0 0 20px 0;
    width: 50%;
  }

  .remark {
    width: 90%;
    margin: 30px auto;
    padding: 20px;
    border: 1px solid #afafaf;
    font-size: 20px;
    max-height: 30%;
  }
  .giftset {
    width: 90%;
    margin: 0 auto;
    font-size: 40px;
    max-height: 10%;
    text-align: center;
    color: red;
  }
  .list-result {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    padding-top: 20px;
  }
}

.md-button.md-primary {
  color: #424242 !important;
  background-color: #cfcfcf !important;
}