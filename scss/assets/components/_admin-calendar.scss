.admin-calendar {

  .md-button.is-loading {
    color: transparent !important;
    pointer-events: none;
    md-icon{
      color: transparent !important;
    }
  }

  .md-button.is-loading:after {
    border-color: transparent transparent #d3d6db #d3d6db !important;
  }

  .md-button.md-warn.is-loading:after,.md-button.md-primary.is-loading:after {
    border-color: transparent transparent #fff #fff !important;
  }

  .md-button.is-loading:after {
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    top: 50%;
    position: absolute !important;
    animation: spin-around 500ms infinite linear;
    border: 2px solid #d3d6db;
    border-radius: 290486px;
    content: "";
    display: block;
    height: 16px;
    width: 16px;
  }


  @keyframes spin-around {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(359deg);
    }
  }

  .content-calendar {
    position: relative;
    .btn-save-rule, .btn-close-rule {
      margin-bottom: 0;
      margin-top: 3px;
      min-width: 0 !important;
      width: 40px;
      padding: 1px 0 !important;
      min-height: 25px !important;
      height: 25px;
    }
    .date-rule {
    }
    .btn-save-rule {
      margin-left: 0;
      &.is-active {
        md-icon {
          color: #212121 !important;
        }
      }
    }
    .btn-close-rule {
      margin-left: 4px;
    }
    .content-list {
      padding-left: 50px;
      padding-right: 0;
      md-list-item {
        padding-right: 0;
        min-height: 32px;
        height: 32px;
        .close-btn {
          min-height: 22px !important;
          height: 22px;
          padding: 0 6px !important;
          margin: 1px 10px 1px 15px;
          min-width: 30px !important;
        }
      }
    }
    .date-container {
      height: 42px;
    }
    .no-border-color {
      border-color: transparent;
    }
    .type-day {
      color: rgba(0, 0, 0, 0.54);
    }
    .form-content {
      padding-left: 50px;
      padding-right: 0;
    }
  }

  $border-color: #eee;

  $empty-day-background: #fafafa;

  $picker-day-today: #607d8b;

  $non-pickable-day-background: #eee;
  $non-pickable-day-color: #bbb;

  $pickable-day-non-picked-background: #fff;
  $pickable-day-non-picked-color: #212121;

  $pickable-day-picked-background: #52b3eb;
  $pickable-day-picked-color: #bdbdbd;
  .multiple-date-picker {
    user-select: none;
    .text-center {
      text-align: center;
    }

    .picker-top-row, .picker-days-week-row, .picker-days-row {
      width: 100%;
    }

    .picker-top-row {
      color: #78909c;
    }

    .picker-top-row > div {
      display: inline-block;
      font-size: 14px;
    }

    .picker-day:focus {
      outline: none;
    }

    .picker-navigate {
      width: 16.66%;
      color: #90a4ae;
      outline: none;
    }

    .picker-navigate:hover {
      cursor: pointer;
    }

    .picker-navigate.disabled, .picker-navigate.disabled:hover {
      color: #ddd;
      cursor: default;
    }

    .picker-month {
      width: 66.66%;
      font-size: 16px;
    }

    .picker-days-week-row > div {
      width: 14.28%;
      display: inline-block;
      color: #607d8b;
      font-size: 0.95em;
      margin-bottom: 10px;
    }

    .picker-days-row > div {
      background-color: transparent;
      width: 14.28%;
      display: inline-block;
    }

    .picker-top-row, .picker-day {
      padding: 10px 0;
    }

    .picker-day {
      box-sizing: border-box;
      color: $pickable-day-non-picked-color;
      display: inline-block;
      width: 30px;
      line-height: 30px;
      border: none;
      padding: 0;
    }

    .picker-day:not(.picker-off):not(.picker-empty):not(.picker-selected):hover {
      color: $pickable-day-picked-color;;
      cursor: pointer;
    }
    .picker-day.picker-selected:not(.picker-off):not(.picker-empty):hover {
      color: #e2e2e2;
      cursor: pointer;
    }

    .picker-day.picker-selected {
      color: $pickable-day-picked-color;
    }

    .picker-day.picker-off, .picker-day.picker-off:hover {
      background-color: inherit;
      color: lighten(#90a4ae, 10%);
      cursor: default;
    }

    .picker-day.picker-empty, .picker-day.picker-empty:hover {
      color: lighten(#90a4ae, 15%);
      cursor: default;
    }

    .picker-other-month.picker-day {
      color: #eeeeee;
    }
    .picker-other-month.picker-selected {
      color: #eeeeee;
    }

    .picker-day.today {
      background: radial-gradient(circle closest-side at center, $pickable-day-picked-background 14px, transparent 14px);
      color: #fff;
      &.picker-selected {
        background: radial-gradient(circle closest-side at center, #eeeeee 14px, transparent 14px);
        color: $pickable-day-non-picked-color;;
      }
    }

    .picker-day.is-loading {
      color: transparent !important;
      pointer-events: none;
      position: relative;
    }


    .picker-day.is-loading:after {
      border-color: transparent transparent #d3d6db #d3d6db !important;
    }

    .picker-day.is-loading:after {
      left: 50%;
      margin-left: -10px;
      margin-top: -10px;
      top: 50%;
      position: absolute !important;
      animation: spin-around 500ms infinite linear;
      border: 2px solid #d3d6db;
      border-radius: 290486px;
      content: "";
      display: block;
      height: 16px;
      width: 16px;
    }
  }
}