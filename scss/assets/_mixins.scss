@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover, &:focus {
    color: $color;
    background-color: darken($background, 15%);
    border-color: darken($border, 12%);
  }
  &:active,
  &.active,
  .open > &.dropdown-toggle {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12%);

    &:hover,
    &:focus,
    &.focus {
      color: $color;
      background-color: darken($background, 17%);
      border-color: darken($border, 16%);
    }
  }
  &:active,
  &.active,
  .open > &.dropdown-toggle {
    background-image: none;
  }
  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    &:hover,
    &:focus,
    &.focus {
      background-color: $background;
      border-color: $border;
    }
  }

  .badge {
    color: $background;
    background-color: $color;
  }
}

@mixin button-variant-material($color, $background) {
  color: $color !important;
  background-color: $background !important;

  &:hover {
    color: $color !important;
    background-color: darken($background, 12%) !important;
  }

  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    &,
    &:hover,
    &:focus,
    &.focus {
      background-color: $background !important;
      color: $color !important;
      cursor: not-allowed;
      opacity: 0.65;
    }
  }
  
}

@mixin button-size($padding-vertical, $padding-horizontal, $font-size, $line-height, $border-radius) {
  padding: $padding-vertical $padding-horizontal;
  font-size: $font-size;
  line-height: $line-height;
  border-radius: $border-radius;
}

@mixin tab-focus() {
  // Default
  outline: thin dotted;
  // WebKit
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

// button without border and background
@mixin simple-button() {
  background: none;
  border: none;
  line-height: 1;
}

@mixin simple-list() {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

@mixin no-outline() {
  &:focus {
    outline: none;
  }
}

@function rem($multiplier) {
  $font-size: 10px;
  @return $multiplier * $font-size;
}

/***** responsiveness ****/
@mixin media($break) {
  @media screen and (max-width: $break - 1px) {
    @content
  }
}

@mixin browser($browsers: Mozilla) {
  @each $browser in $browsers {
    html[data-browser*="#{$browser}"] & {
      @content;
    }
  }
}
