.management .matrix-container {

  .matrix-selects {
    
    > div {
      border-left: 1px solid $dark-grey;
      padding: 2px 12px;
      &:last-child {
        border-right: 1px solid $dark-grey;
      }
    }

    .md-select-icon {
      color: $dark2;
    }
    
    .select-type {
      
      .pointer {
        cursor: pointer;
        @include no-outline;
        font-size: 15px;
      }
      
      .image {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        img {
          display: block;
          max-width: 100%;
          max-height: 100%;
        }
      }

      md-input-container {
        label {
          display: none !important;
        }
        .md-select-placeholder {
          span:first-child {
            opacity: 0;
          }
        }
        .md-select-value {
          padding: 0;
          border-bottom: none;
          font-size: 15px;
        }
      }
      
      
      .md-menu {
        .md-select-icon {
          display: block;
          align-items: flex-end;
          text-align: end;
          width: 19px;
          margin: 0 4px;
          transform: translate3d(0, -2px, 0);
          font-size: 1.2rem;
          &:after {
            display: block;
            content: '\25BC';
            position: relative;
            top: 2px;
            speak: none;
            font-size: 13px;
            transform: scaleY(0.5) scaleX(1);
          }
        }
      }
      
    }

    .simple-select {
      md-input-container {
        margin-top: 12px;
        margin-bottom: 0;
      }
      .md-select-value {
        border-bottom: none !important;
        padding: 0 !important;
      }
    }
    
  }
  
  table {
    th {
      text-align: center;
      font-weight: bold;
    }
    
    .label {
      font-weight: bold;
    }
    
    td, th {
      padding: 20px 10px;
    }
    
    td:first-child {
      padding-left: 0;
    }
    td:last-child {
      padding-right: 0;
    }

    td, th {
      border-bottom: 1px solid $grey3;
    }
    
    tbody tr:last-child td {
      border-bottom: none;
    }
    
    width: 100%;
    padding: 0 40px;
  }
  
}

.selectdemoSelectHeader {
  .demo-header-searchbox {
    border: none;
    outline: none;
    height: 100%;
    width: 100%;
    padding: 0;
  }
  .demo-select-header {
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(0, 0, 0, 0.14), 0 0 0 0 rgba(0, 0, 0, 0.12);
    padding-left: 10.667px;
    height: 34px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    width: auto;
  }
  md-content._md {
    max-height: 240px;
  }
}