.storage-content {
  .circle-small {
    width: 30px !important;
    height: 30px !important;
  }
  .circle-two {
    display: flex;
  }
  .circle-red {
    background: #eb6d56;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .circle-orange {
    background: #FF8C00;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .circle-yellow {
    background: #fdd35d;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .storage-status {
    padding: 0 20px !important;
  }
}

storage-picking,storage-option-items-picking {
  header h2 {
    display: inline-block;
    width: 120px;
  }
  .pick-date-header {
    width: 130px;
    display: inline-block;
    vertical-align: top;
  }
  .record-detail.header {
    margin: 30px 0;
  }
  .record-detail.data {
    padding: 5px;
    border-bottom: 1px solid #c3c3c3;
    &:first-child {
      border-top: 1px solid #c3c3c3;
    }
    .quantity {
      padding-right: 7px;
    }
  }
  .picking-checkbox {
    margin-bottom: 0;
  }
  .quantity.change {
    color: #fe5ba7 !important;
  }
  md-checkbox.md-checked.change .md-icon {
    background-color: #fe5ba7 !important;
  }
  md-checkbox.change .md-icon {
    border: 2px solid #fe5ba7 !important;
  }
}

storage-picking-vakuum {
  .md-sidenav-left{
    width: calc(100% - 600px);
    max-width: initial;
  }

  .page-part-one .head {
    font-size: 20px;
    font-weight: 600;
    text-align: right;
  }

  .page-part-two {
    width: 150px;
  }

  .page-part-two .qr-code{
    margin: 15px;
  }
  .page-part-one {
    width: calc(100% - 150px);
  }
  .page-part-one, .page-part-two {
    float:left;
  }

  .page-part-one .cell .number, .page-part-one .cell .title {
    text-align: center
  }

  .page-part-one .table {
    height: 97.5%;
  }

  .page-part-one .cell {
    display: inline-block;
    text-align: center;
    width: 24.8%;
  }

  .page-part-one .cell img {
    max-width: 100%;
  }

  .page-part-one .cell .title {
    margin-bottom: 5px;
    font-size: 19px;
  }

  .page-part-one .cell .number {
    margin-top: 5px;
    font-size: 19px;
  }

  paging {
    ul {
      text-align: center;
    }
    ul li {
      display: inline-block;
      margin: 5px;
    }
    ul li a , ul li span{
      position: relative;
      float: left;
      padding: 6px 12px;
      margin-left: -1px;
      line-height: 1.42857143;
      color: #337ab7;
      text-decoration: none;
      background-color: #fff;
      border: 1px solid #ddd;
    }

    ul>.active>a, ul>.active>a:focus, ul>.active>a:hover, ul>.active>span, ul>.active>span:focus, ul>.active>span:hover {
      z-index: 3;
      color: #fff;
      cursor: default;
      background-color: #337ab7;
      border-color: #337ab7;
    }
  }
}

storage-current,storage-option-items {
  .pick-date-header .md-datepicker-button.md-button{
    padding: 2px !important;
  }
  .pick-date-header {
    width: 120px;
  }
  .garment-total {
    width: 700px;
  }
  .garment-total .garment-total-info {
    white-space: nowrap;
    padding: 0 16px;
  }
}

storage-current-detail {
  .form-buttons .buttons-container .md-button:last-child {
    margin-right: 8px;
  }
}