// general
.management-sidenav {
  width: 600px;
  max-width: 600px;
  background: $mng-sidenav-back;
  position: fixed;
  
  .esc-button {
    @include simple-button;
    @include no-outline;
    padding: 0;
    color: $btn-icon;
    line-height: 1;
    md-icon {
      color: inherit;
    }
  }
  
  header {
    color: $black;
    padding-left: 80px;
    padding-top: 25px;
    padding-bottom: 25px;
    position: relative;
    background: inherit;
    h2 {
      font-size: rem(1.8);
      font-weight: normal;
      margin: 0;
      padding: 0;
    }
    .esc-button {
      position: absolute;
      left: 22px;
      top: 50%;
      margin-top: -12px;
    }
    .go-back-button {

      @include simple-button;
      @include no-outline;
      padding: 0;
      color: $grey4;
      line-height: 1;
      
      path {
        fill: $grey4;
      }
      
      md-icon {
        height: 20px;
        width: 20px;
        min-height: 20px;
        min-width: 20px;
      }
      
      position: absolute;
      left: 22px;
      top: 50%;
      margin-top: -11px;
    }
    .sidenav-actions {
      position: absolute;
      top: 50%;
      right: 22px;
      margin-top: -15px;
      button {
        @include no-outline;
        display: inline-block;
        vertical-align: middle;
        padding: 2px;
        margin-left: 10px;
        color: $btn-icon;
        i {
          font-size: 19px;
        }
        md-icon {
          width: 19px;
          height: 19px;
          min-width: 19px;
          min-height: 19px;
          fill: $btn-icon;
        }
      }
      .btn-qr {
        md-icon {
          width: 18px;
          height: 18px;
          min-width: 18px;
          min-height: 18px;
        }
      }
    }
    .qr-popup {
      width: 130px;
      height: 130px;
      background: white;
      position: absolute;
      top: 55px;
      right: 20px;
      border-radius: 4px;
      padding: 13px;
      box-shadow: 0 0 13px -1px black;
      z-index: 1001;
    }
    .qr-popup-backdrop {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1000;
    }
    &.qr-visible {
      #qr-popup {
        display: block;
      }
      .btn-qr {
        color: #fff;
      }
    }
    
    &.with-buttons {
      h2 {
        line-height: 30px;
      }
      .sidenav-actions {
        margin-top: 1px;
        transform: translateY(-50%);
      }
    }
    
  }

  h3 {
    color: $sidebar-secondary-link;
    font-weight: normal;
    font-size: rem(1.3);
    margin: 20px 0 10px;
    padding: 0;
  }

  md-content {
    background: inherit;
    padding: 0 40px;
    color: $black;
    
    &.narrow {
      padding: 0 75px;
    }

    .flex-container {
      margin: 0 -13px -10px;
      &:last-child {
        margin-bottom: 0;
      }
      > div[flex], md-input-container {
        margin-left: 13px;
      }
      md-input-container {
        display: block;
        width: 100%;
      }
    }
    
    .form {
      
      header {
        padding: 0 0 15px;
        font-size: rem(1.6);
      }

      &.form-no-padding {
        margin: 0;
        > div {
          padding-left: 0;
          padding-right: 0;
        }
      }

      .edit-form-container {
        position: absolute;
        right: -50px;
        top: 0;
        button {
          color: #616161;
          font-size: 19px;
          @include no-outline;
        }
      }

      .extra-top-margin {
        margin-top: 15px;
      }
      &.form-small {
        input, textarea, label {
          font-size: 15px;
        }
      }
    }

    header.sidenav-header-section {
      margin-top: 34px;
      padding: 0 0 10px;
      font-size: rem(1.6);
    }

    .forms-container {
      .form {
        border-bottom: 1px solid $tab-header-border-color;
        margin-top: 18px;
      }
      .form:last-child {
        border-bottom: none;
      }
    }

    .people-block {
      > div:first-child {
        margin-top: 15px;
      }
      > div md-input-container {
        margin-bottom: 10px;
      }
    }
    
    // add people to contractor
    .add-people-container {
      margin-top: 10px;
      .add-person-button {
        @include simple-button;
        @include no-outline;
        padding: 0;
        margin: 0;
        font-size: rem(1.3);
        color: $sidebar-link-color;
        font-weight: bold;
      }
    }
    
    // order form
    .order-form {
      margin-bottom: 4px;
    }

    md-divider {
      background-color: $default-input-border;
      border-top-color: $default-input-border;
    }

    .order-history-wrapper {
      overflow: auto;
      margin: 30px -15px 20px 0;
      padding-right: 15px;
      min-height: 150px;
    }

    .order-history {
      > div {
        color: $dark;
        font-size: rem(1.5);
        margin-bottom: 12px;
      }
      .heading {
        font-size: rem(1.2);
        margin-bottom: 23px;
        color: $dark2;
      }
      .recent {
        color: white;
        margin-bottom: 23px;
      }
    }

  }

  .edit-block {
    > div {
      margin-bottom: -10px;
    }
    md-input-container {
      display: block;
    }
    margin-bottom: 30px;
  }

  footer {
    padding: 10px 30px 12px;
    .btn {
      padding: 11px 6px;
      min-width: 100px;
    }
    .md-button {
      padding: 2px 6px;
      min-width: 100px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .working-hours {
    h4 {
      font-size: rem(1.2);
      font-weight: normal;
      color: $sidebar-secondary-link;
      margin: 25px 0 11px;
    }
    .hours-block {
      display: flex;
      align-items: center;
      md-input-container {
        margin: 7px 0;
        .md-errors-spacer {
          display: none;
        }
      }
      input {
        width: 53px;
        padding-left: 5px;
        padding-right: 5px;
      }
      input[disabled] {
        border-bottom: none;
      }
      .divider {
        width: 10px;
        height: 1px;
        background: $black;
        margin: 0 10px;
      }
      button {
        @include simple-button;
        @include no-outline;
        margin-left: 70px;
        color: #646464;
        font-size: 19px;
        padding: 5px;
        margin-top: 10px;
        &:hover {
          color: lighten(#646464, 10%);
          transition: all 0.2s ease-out;
        }
      }
    }
    .add-hours {
      @include simple-button;
      @include no-outline;
      font-size: rem(1.2);
      padding: 4px 0;
      color: $black;
      &:hover {
        color: lighten($black, 10%);
        transition: all 0.2s ease-out;
      }
      margin: 20px 0 17px;
    }
  }

}

// sidenav detail
.sidenav-details {

  width: 1000px;
  max-width: 1000px;

  .form {
    margin-bottom: 15px;
  }

  .bottom-margin {
    margin-bottom: 30px;
  }
  .extra-bottom-margin {
    margin-bottom: 45px;
  }
  
  // todo: make universal styles (like in order-detail.scss)
  header.section-header {
    background: $content-back;
    border-bottom: 1px solid $default-border-color;
    height: 75px;
    padding: 25px 30px 25px 80px;
    h2 {
      font-size: rem(1.5);
      margin: 0;
      padding: 0;
      font-weight: normal;
    }
  }

  .sidebar {
    width: 300px;
    padding: 20px;
    border-right: 1px solid $default-border-color;
    background: $grey3;

    .form:last-child {
      margin-bottom: 0;
    }

  }

  .list-content {
    
    padding: 0;
    
    .cell {
      padding: 5px 15px;
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }
    }

    .highlighted-column-container {
      padding: 0 40px 0 35px;
      overflow: hidden;
    }

    .heading {
      height: 50px;
      border-bottom: 1px solid $grey3;
      .cell {
        color: $dark2;
        font-size: rem(1.2);
      }
    }

    .cell-images {
      .image {
        img {
          display: block;
          max-width: 32px;
          max-height: 32px;
          margin-right: 5px;
        }
        &:nth-last-child(2) {
          margin-right: 10px;
        }
      }
    }

    .cell-action {
      button {
        margin-left: 10px;
      }
      .buttons {
        margin-left: 10px;
      }
    }

    .md-button.btn-sm.btn-more {
      width: 30px !important;
      min-width: 30px !important;
      padding: 6px 2px !important;
      i {
        position: relative;
        top: -1px;
      }
    }

    .md-virtual-repeat-offsetter {
      padding: 0 40px 0 35px;
    }
  }
  
}

.sidenav-history {
  header.section-header {
    border-bottom: none;
    background: none;
    z-index: 1;
  }
  .highlighted-column-container {
    padding: 0 40px 0 35px;
    overflow-y: auto;
    z-index: 0;
  }
}

.management-sidenav {

  .product-list-narrow {

    .heading {
      font-size: rem(1.2);
      .subheading.text {
        color: $dark2;
      }
      .subheading {
        margin-bottom: 8px;
      }
      .search {
        height: 51px;
      }
      margin-bottom: 1px;
    }

    .default-row {
      border-bottom: none;
      height: auto;
      margin-bottom: 10px;
      .image {
        img {
          max-width: 24px;
          max-height: 24px;
          margin-right: 5px;
          display: block;
        }
        &:last-of-type {
          margin-right: 5px;
        }
      }
      md-checkbox {
        display: initial;
      }
    }

    .last-price {
      color: $grey4;
      margin-right: 40px;
    }

    .heading-btns {
      position: relative;
    }

    .change-price-discount {
      position: absolute;
      z-index: 10;
      width: 360px;
      box-sizing: border-box;
      font-size: 14px;
      line-height: 16px;
      padding: 10px 10px 8px;
      right: 0;
      top: -2px;
      border: none;
      outline: none;
      box-shadow: 0 0 8px rgba(220, 220, 220, 0.25);
    }
    
  }
  
  .printer-product-list {
    .images {
      margin-right: 15px;
    }
  }

  
}

// color block
.management-sidenav {

  .save-color-btn {
    &:hover, &.md-focused {
      background-color: rgba($grey4, 0.1);
    }
  }

  .colors-list {

    h3 {
      margin: 0 0 18px 1px;
      padding: 7px 0;
      font-size: 12px;
      color: #9e9e9e;
      font-weight: normal;
      border-bottom: 1px solid rgba($grey4, 0.45);
    }
    
    .default-row {
      border-bottom: none;
      height: auto;
      padding: 10px 0;
      font-size: 13px;
    }

    .image img {
      max-width: 24px;
      max-height: 24px;
    }

    .color-preview-container {
      margin-left: 10px;
      .color-preview {
        margin-left: 2px;
      }
    }

    .more-btn {
      margin-right: -4px;
      margin-left: 14px;
      padding: 2px 4px;
      i {
        display: block;
        font-size: 14px;
      }
    }
    
    .data-rowed {
      div:not(:last-child) {
        margin-right: 10px;
      }
    }
    
    &.bottom-border {
      margin-bottom: 20px;
    }

  }

  .color-divider {
    margin: 14px 0 29px;
  }
  
  .color-block.bottom-border {
    margin-bottom: 30px;
  }
  
}

// sidenav list block (just like color block)
.management-sidenav .sidenav-list-block {

  h3 {
    margin: 0 0 18px 1px;
    padding: 7px 0;
    font-size: 12px;
    color: $grey4;
    font-weight: normal;
    border-bottom: 1px solid rgba($grey4, 0.45);
    &.small-bottom {
      margin-bottom: 12px;
    }
  }

  hr.default-divider {
    margin: 0 0 18px 1px;
    border: none;
    border-top-width: 1px;
    border-top-style: solid;
    border-color: rgba($grey4, 0.45);
  }

  &.bottom-margin {
    margin-bottom: 20px;
  }

  .save-color-btn {
    &:hover, &.md-focused {
      background-color: rgba($grey4,0.1);
    }
  }

}

.management-sidenav .sidenav-list {

  margin-bottom: 10px;
  
  .heading {
    color: $dark2;
    font-size: 12px;
    margin-bottom: 8px;
  }

  .default-row {
    border-bottom: none;
    height: auto;
    padding: 6px 0;
    font-size: 13px;

    .btn-sm[md-button] {
      font-size: 11px;
      line-height: 1.1;
      padding: 6px 6px;
      min-height: 25px;
      min-width: 42px;
    }
    
  }

  .data-rowed {
    div:not(:last-child) {
      margin-right: 10px;
    }
  }
  
  md-menu {
    &.small {
      .more-btn {
        margin-right: -4px;
        margin-left: 14px;
        padding: 2px 4px;
        i {
          display: block;
          font-size: 14px;
        }
      }
    }
  }

}