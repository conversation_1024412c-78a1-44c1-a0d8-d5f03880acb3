.management {
  
  .new-order-wrapper {
    
    min-width: 1000px;
    @extend .block-window;
    background: $content-back;

    .btn-toggle-edit {
      display: none;
      position: absolute;
      right: 40px;
      top: 20px;
      text-transform: none;
      z-index: 10;
    }
    
    header.section-header {
      background: $content-back;
      border-bottom: 1px solid $default-border-color;
      min-height: 75px;
      padding: 0 30px 0 20px;
      .close-button {
        margin-right: 20px;
      }
      
      .sizes {
        .size {
          width: 105px;
          border-left: 1px solid $dark-grey;
          padding-left: 12px;
          &:last-child {
            border-right: 1px solid $dark-grey;
          }
        }
        .size-label {
          font-size: rem(1.1);
          color: $dark2;
          margin-bottom: 4px;
        }
        .size-value {
          font-size: rem(1.4);
          color: $dark;
          position: relative;
          input {
            border: none;
            background: inherit;
            padding: 0;
            outline: none;
            color: inherit;
            transition: width 0.1s ease-out;
            padding-right: 35px;
          }
          span {
            position: absolute;
            right: 7px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
      
      .buttons {
        .md-button {
          text-transform: none;
          font-size: rem(1.2);
          min-width: 70px !important;
        }
      }
    }

    .select-buttons {
      .btn-select {
        color: $dark;
        span {
          position: relative;
          padding-right: 13px;
          &:after {
            content: '';
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 4px 4px 0 4px;
            border-color: $dark transparent transparent transparent;
            position: absolute;
            right: 0;
            top: 50%;
            margin-top: -2px;
          }
        }
      }
    }
    
    .body {

      .controls {
        margin-top: 40px;
        margin-left: 1px;
        overflow: auto;
        .md-button {
          width: 40px;
          height: 40px;
          line-height: 40px;
          min-width: 40px;
          min-height: 40px;
          text-align: center;
          &:last-child {
            margin-bottom: 20px;
          }
          &.md-accent {
            md-icon {
              fill: $default-light-grey;
            }
            i {
              color: $default-light-grey;
            }
          }
          md-icon {
            min-width: 19px;
            min-height: 19px;
            width: 19px;
            height: 19px;
            fill: $grey4;
          }
          i {
            color: $grey4;
            font-size: rem(1.9);
            line-height: 41px;
          }
        }
      }

      .sides-container {
        margin-top: 70px;
        padding-right: 40px;
        padding-left: 10px;
        min-width: 116px;
        
        overflow: auto;
        
        .side {
          @include no-outline;
          padding: 3px;
          margin: 5px 5px 25px;
          opacity: 0.6;
          cursor: pointer;

          &:hover {
            opacity: 0.8;
          }

          &.active {
            opacity: 1;
          }

          .image {
            width: 50px;
            height: 50px;
            margin: 0 auto;
          }
          img {
            max-height: 100%;
            max-width:  100%;
          }

          .caption {
            margin-top: 15px;
            text-align: center;
            font-size: rem(1.2);
            color: $dark2;
          }
        }
        
      }
      
    }

    .order-edit-body {
      position: relative;
      display: block;
      //padding: 50px 0 25px 10px;
      padding: 0 0 0 10px;
      > div {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }
      .drawtool-container {
        height: 100%;
        width: 100%;
      }
    }

    
    .md-button {
      &.move-layer {
        .icon-layer {
          display: block;
        }
        .icon-canvas {
          display: none;
        }
      }
      &.move-canvas {
        .icon-layer {
          display: none;
        }
        .icon-canvas {
          display: block;
        }
      }
    }

    
    .sidenav-details {
      
      header.section-header {
        background: #fafafa;
        border-bottom: 1px solid #bdbdbd;
        height: 75px;
        padding: 25px 30px 25px 80px;
      }
      
      
      
      .sidebar {

        position: relative;
        
        .search-form {
          padding: 0 0 10px;
        }
        
        .type-block {
          header.active .label {
            color: $blue;
          }
          font-size: 14px;
          a {
            text-decoration: none;
          }
          header {
            padding: 10px 0 13px;
            height: auto;
            .label {
              color: $black;
            }
            .select-all {
              color: $dark-grey;
            }
          }
          .types {
            a {
              display: block;
              color: $dark;
              padding: 9px 20px 8px;
              margin: 5px 0;
              &:hover {
                color: lighten($dark, 20%);
              }
              &.active {
                color: $blue;
              }
            }
          }
        }
      }

      .search-failed {
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
      }
      
      .types-content {
        padding: 20px 30px;
        width: auto;
        height: auto;
        position: relative;

        .search-failed {
          font-size: 16px;
        }
        
      }
      .product-type-block {
        margin-bottom: 50px;
        &:last-child {
          margin-bottom: 20px;
        }
      }
      
    }
    .drawtool-preview {
      display: none;
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      img {
        display: block;
        margin: 0 auto;
      }
    }
  }
  
  .new-order-wrapper.show-mode {

    
    .section-header, .controls, .sides-container, .drawtool-container {
      display: none;
    }

    .drawtool-preview {
      display: block;
    }

    .btn-toggle-edit {
      display: block;
    }
    
  }
  
}