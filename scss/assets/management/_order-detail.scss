.management {
  
  .simple-icon-btn {
    @include simple-button;
    @include no-outline;
    color: $btn-icon;
    font-size: rem(2.2);
    padding: 2px;
    &:hover {
      color: lighten($btn-icon, 8%);
    }
    &.btn-add {
      font-size: rem(2.7);
    }
  }
  
  .order-detail-wrapper {
    
    @extend .block-window;
    background: $content-back;

    .form {
      margin-bottom: 15px;
    }

    .bottom-margin {
      margin-bottom: 30px;
    }
    .extra-bottom-margin {
      margin-bottom: 45px;
    }
    
    header.section-header {
      background: $content-back;
      border-bottom: 1px solid $default-border-color;
      height: 75px;
      padding: 0 30px 0 20px;
      h2 {
        font-size: rem(1.5);
        margin: 0;
        padding: 0;
        font-weight: normal;
      }
      .close-button {
        margin-right: 20px;
      }
      .buttons {
        .btn-add {
          font-size: rem(2.7);
          margin: 0 6px;
        }
        > div, .simple-icon-btn {
          margin: 0 8px;
        }
        input {
          width: 120px;
        }
      }
    }

    .sidebar {
      width: 300px;
      padding: 20px;
      border-right: 1px solid $default-border-color;
      background: $grey3;
      
      .form:last-child {
        margin-bottom: 0;
      }
      
    }

    md-checkbox {
      color: $dark2;
      
      &:not(.md-checked) .md-icon {
        border-color: #9e9e9e;
      }
    }

    .list-content {
      
      .cell {
        padding: 5px 15px;
        &:first-child {
          padding-left: 0;
        }
        &:last-child {
          padding-left: 0;
        }
      }

      .highlighted-column-container {
        padding: 0 40px 0 35px;
        overflow: hidden;
      }
      
      .heading {
        height: 50px;
        border-bottom: 1px solid $grey3;
        .cell {
          color: $dark2;
          font-size: rem(1.2);
        }
      }

      .cell-images {
        .image {
          img {
            display: block;
            max-width: 32px;
            max-height: 32px;
            margin-right: 5px;
          }
          &:nth-last-child(2) {
            margin-right: 10px;
          }
        }
      }

      .cell-action {
        button {
          margin-left: 10px;
        }
        .buttons {
          margin-left: 10px;
        }
      }
      
      .md-virtual-repeat-offsetter {
        padding: 0 40px 0 35px;
      }
    }
    
  }
  
}

.product-sides-images {

  margin-bottom: 20px;
  
  &.mode-min {
    .view-image {
      height: 180px;
    }
  }
  
  img {
    display: block;
  }
  
  .view-image {
    height: 350px;
    img {
      max-width: 100%;
      max-height: 100%;
    }
    margin-bottom: 20px;
  }

  .image-thumb {
    @include no-outline;
    width: 70px;
    padding: 3px;
    margin: 5px;
    opacity: 0.75;
    cursor: pointer;
    
    &:hover {
      opacity: 0.95;
    }
    
    &.active {
      opacity: 1;
    }
    
    .image {
      width: 40px;
      height: 40px;
      margin: 0 auto;
    }
    img {
      max-height: 100%;
      max-width:  100%;
    }

    .caption {
      margin-top: 15px;
      text-align: center;
      font-size: rem(1.2);
      color: $dark2;
    }
  }
  
}

.small > .product-sides-images {

  .view-image {
    height: 180px;
    width: 170px;
    img {
      max-width: 100%;
      max-height: 100%;
    }
    margin-bottom: 20px;
  }
  
}
.wrap-product-side-history {
  width: 100%;
}

product-sides-history {
    display: flex;
    margin-top: 15px;
  div:first-child {
    flex: 1;
  }
  div:last-child {
    flex: 3;
  }
}
