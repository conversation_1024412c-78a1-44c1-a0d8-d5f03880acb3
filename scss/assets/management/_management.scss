/******* ORDERS ********/
.management {
  color: $black;

  .bottom-margin {
    margin-bottom: 30px;
  }
  .extra-bottom-margin {
    margin-bottom: 45px;
  }

  .sidebar {
    background: $sidebar-back;
    padding-left: 33px;
    width: 200px;

    .logo {
      margin-top: 31px;
      md-icon {
        width: 95px;
        svg {
          &, & .st0 {
            fill: $black;
          }
        }
      }
    }

    .navbar {
      margin-top: 134px;
      ul {
        @include simple-list();
      }
      li {
        height: 30px;
        margin-bottom: 10px;
      }
      a {
        text-decoration: none;
        color: $grey;
        &:hover {
          color: lighten($grey, 15%);
        }
      }
      .btn-sidebar {
        display: none;
      }
      .btn-add, .btn-settings {
        padding: 0 !important;
        width: 30px;
        height: 30px;
        min-width: 30px !important;
        background: $dark-button !important;
        line-height: 1;
        margin-right: 10px;
        &:hover, &:active {
          background-color: darken($dark-button, 10%) !important;
        }
        &:active, &:focus {
          outline: none;
        }
      }
      .btn-add {
        i {
          display: block;
          line-height: 1;
          color: $grey4;
          font-size: 19px;
          position: relative;
          left: 0.5px;
        }
      }
      .btn-settings {
        i {
          display: block;
          line-height: 1;
          color: $grey4;
          font-size: 14px;
        }
        md-icon {
          width: 15px;
          height: 15px;
          min-width: 15px;
          min-height: 15px;
          fill: $grey4;
          line {
            stroke: $grey4;
          }
        }
      }
      li.active {
        a {
          color: $black;
        }
        .btn-sidebar {
          display: block;
        }
      }
    }

    .sidebar-secondary {
      margin-top: 80px;
      ul {
        @include simple-list();
      }
      li {
        padding: 1px 0;
        position: relative;
        margin-bottom: 16px;
      }
      a {
        color: $sidebar-secondary-link;
        text-decoration: none;
        &:hover {
          color: lighten($sidebar-secondary-link, 15%);
        }
      }
      li.active {
        a {
          color: $sidebar-secondary-link-active;
        }
        &:after {
          content: '';
          position: absolute;
          width: 3px;
          top: 0;
          bottom: 0;
          right: 0;
          background: #2196f3;
        }
      }
    }

  }
  .main-body {
    background: $content-back;
    position: relative;
    > md-content, > .component > md-content {
      position: relative;
      background: inherit;
    }
  }
  header {
    height: 80px;
    padding: 0 60px;
    background: $sidebar-back;
    color: $black;
    .cell {
      font-size: rem(1.2);
      padding: 0 20px;
    }

    &.with-subheader {
      height: auto;
      padding: 0;
      .subheader-th {
        background: $sidebar-back;
        color: $black;
        height: 80px;
        padding: 0 60px;
      }
      .subheader-nav {
        color: $black;
        background: $content-back;
        padding: 0 40px;
        border-bottom: 1px solid $default-dark-grey;
        float:left;
        .manage-block-nav {
          margin: 0 0 -1px;
          padding: 0;
          list-style-type: none;
          li {
            display: inline-block;

          }
          li.active a {
            color: $dark;
            border-color: $blue;
          }
          a {
            color: $dark2;
            text-decoration: none;
            display: block;
            padding: 19px 20px 17px;
            border-bottom: 2px solid transparent;
            span {
              position: relative;
            }
          }
          a.has-notification span:after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background: $blue;
            border-radius: 50%;
            top: -4px;
            right: -11px;
          }
        }
        md-nav-bar .md-nav-bar {
          border: none;
          .has-notification {
            position: relative;
          }
          .has-notification:after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background: $blue;
            border-radius: 50%;
            top: -4px;
            right: -11px;
          }
          .md-button.md-accent {
            color: $dark !important;
            background: $content-back !important;
          }
        }
      }
      .garment-total{
        float:left;
        padding: 16px 40px;
        .garment-total-info{
          white-space: nowrap;
          padding: 0 16px;
        }
      }
    }

    .progress-container {
      .progress-bar {
        margin: 0 15px;
        position: relative;
        max-width: 350px;
      }
      .number {
        position: absolute;
        color: $blue;
        left: 0;
        right: 0;
        text-align: center;
        bottom: 13px;
      }
      .progress {
        width: 100%;
        background: $default-light-grey2;
        .progress-current {
          padding: 4px 0;
          background: $blue;
        }
      }
    }
  }
  .content {
    color: $black;
    position: relative;
    background: $content-back;
    height: 100%;
    width: 100%;

    .md-virtual-repeat-offsetter {
      padding: 0 47px 0 60px;
      z-index: 2;
    }
    .md-virtual-repeat-scroller {
      z-index: 2;
    }

    &.no-virtual-repeat {
      padding: 0 47px 0 60px;
    }

    md-divider {
      border-top-color: #f5f5f5;
    }
    .cell {
      padding: 10px 20px;
      md-checkbox {
        margin-bottom: 3px;
      }
    }
  }

  .heading-btns {
    .md-button {
      padding: 6px 15px !important;
      margin: 0;
    }

    .md-button:not(.last) {
      margin-right: 10px;
    }

    .btn-more {
      width: 30px !important;
      min-width: 30px !important;
      padding: 6px 2px !important;
    }

  }

  .red {
    color: $red;
  }

  .default-row {
    position: relative;
    z-index: 10;
    height: 51px;
    border-bottom: 1px solid #f5f5f5;
    &.default-row-new {
      height: auto;
    }
    .trend {
      margin-left: 5px;
      md-icon {
        min-width: 22px;
        min-height: 22px;
        width: 22px;
        height: 22px;
      }
      &.up {
        md-icon {
          fill: #219e5e;
        }
      }
      &.down {
        transform: rotate(180deg);
        md-icon {
          fill: #cb5234;
        }
      }
    }

    &:before, &:after {
      content: '';
      position: absolute;
      top: 0;
      left: -15px;
      height: 100%;
      width: 15px;
      background: none;
    }
    &:after {
      left: auto;
      right: -15px;
    }

    .cell-info {
      position: relative;
      .main {
        font-size: rem(1.5);
        color: $black;
      }
      .divider {
        color: $grey4;
        margin: 2px 8px 0;
      }
      .secondary {
        font-size: rem(1.5);
        color: $grey4;
      }

      &.simple {
        > div {
          font-size: rem(1.4);
        }
        > div:not(:last-child) {
          margin-right: 15px;
        }
        .secondary {
          font-size: rem(1.4);
        }
      }

    }

    .cell-data {

      &.indented {
        padding-left: 45px;
      }

      md-checkbox {
        margin-bottom: 0;
      }
      .checkbox {
        margin-right: 10px;
      }
      .images .image:last-child {
        margin-right: 14px;
      }
    }

    .order-row {
      height: 51px;
    }

    .image {
      img {
        display: block;
        max-width: 32px;
        max-height: 32px;
        margin-right: 5px;
      }
    }

    .data-rowed {

      .divider {
        width: 4px;
        height: 4px;
        background: $dark;
        border-radius: 100%;
        margin: 0 7px
      }
      .addition {
        color: $grey4;
      }
      .divider.addition {
        background: $grey4;
      }

      &.rowed-inline {
        > div {
          display: inline-block;
          vertical-align: middle;
        }
      }

    }

    .addition {
      color: $grey4;
      &.reversed.have-margin {
        margin-right: 10px;
      }
    }

    &.row-update-start {
      background: $update-animation-background;
      transition: $update-animation-transition-start;

      &:before, &:after {
        background: $update-animation-background;
        transition: $update-animation-transition-start;
      }

      .highlighted-column {
        background: $update-animation-background;
        transition: $update-animation-transition-start;
      }

      &.row-update-end {
        background: none;
        transition: $update-animation-transition-end;
        .highlighted-column {
          background: $highlighted-column;
          transition: $update-animation-transition-end;
        }
        &:before, &:after {
          background: none;
          transition: $update-animation-transition-end;
        }
      }

    }
  }

  .cell-colors {
    .color-dot:not(:last-child) {
      margin-right: 10px;
    }
  }

  .cell-progress {
    .progress-text {
      margin-right: 19px;
    }
    .divider {
      width: 4px;
      height: 4px;
      background: $dark;
      border-radius: 100%;
      margin: 0 7px
    }
  }

  .user-block {

    height: 51px;
    border-bottom: 1px solid $grey3;

    .user-info {
      position: relative;
      .user-name {
        font-size: rem(1.5);
        color: $black;
      }
      .user-divider {
        color: $grey4;
        margin: 2px 8px 0;
      }
      .user-email {
        font-size: rem(1.5);
        color: $grey4;
      }
      .user-company {
        margin-left: 20px;
      }
      .user-self-icon {
        position: absolute;
        left: -30px;
        color: #fff;
        display: none;
        font-size: 20px;
        top: 50%;
        margin-top: -12px;
      }
      &.user-self {
        .user-self-icon {
          display: block;
        }
      }
    }

  }

  .select-role {
    .md-button {
      min-width: 110px !important;
    }

    button {
      span {
        display: inline-block;
        vertical-align: middle;
        line-height: 1;
      }
      md-icon {
        margin-left: 6px;
        min-width: 11px;
        min-height: 11px;
        width: 11px;
        height: 11px;
        polyline {
          stroke: $dark;
        }
      }
    }

  }

  .user-self-icon {
    position: absolute;
    left: -28px;
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    color: $dark;
  }

  .btn-accept {
    width: 30px !important;
    min-width: 30px !important;
    padding: 6px 2px !important;
    md-icon {
      height: 17px;
      width: 17px;
      min-height: 17px;
      min-width: 17px;
      polyline {
        stroke: $dark2;
      }
    }
  }
  .btn-reject {
    width: 30px !important;
    min-width: 30px !important;
    padding: 6px 2px !important;
    i {
      color: $dark2;
      font-size: 16px;
      line-height: 1;
    }
  }

  .text-small {
    font-size: rem(1.2);
    color: $color-text-small;
  }

  .cell {
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      padding-right: 0;
    }
  }

  .cell-action {
    button {
      margin: 0 0 0 25px;
    }
    button + button {
      margin: 0 0 0 10px;
    }
    .md-button.btn-sm.btn-more {
      width: 30px !important;
      min-width: 30px !important;
      padding: 6px 2px !important;
      margin-left: 10px;
      i {
        display: block;
      }
    }
  }

  .cell-company {
    > div:first-child {
      font-size: rem(1.8);
      margin-right: 26px;
      font-weight: bold;
    }
    .text-small {
      span:last-child {
        margin-left: 2px;
      }
    }
  }

  .highlighted-column {
    background-color: $highlighted-column;
  }

  .highlighted-column-container {
    position: absolute;
    top: 0;
    bottom: 0;
    padding: 0 47px 0 60px;
    width: 100%;
    box-sizing: border-box;
    z-index: 1;
  }

  .search {
    position: relative;
    z-index: 10;
    height: 70px;
  }
  .search-form {
    padding: 13px 0;
    margin-left: -2px;
    input, i {
      display: inline-block;
      vertical-align: middle;
    }
    i {
      color: $btn-icon;
      font-size: 20px;
      line-height: 1;
      padding: 0 2px;
      cursor: pointer;
      transition: color $swift-ease-out-duration $swift-ease-out-timing-function;
    }
    i:first-child {
      margin-right: 8px;
    }
    i:last-child {
      margin-left: 8px;
    }
    input {
      border-color: transparent;
      border-style: solid;
      border-width: 0 0 2px 0;
      background: transparent;
      transition: border-color $swift-ease-out-duration $swift-ease-out-timing-function;
      color: $black;
      &:focus {
        outline: none;
      }
    }

    &.has-focus input {
      border-bottom-color: $search-border-bottom;
    }

    .reset {
      @include simple-button();
      @include no-outline();
      padding: 1px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      margin-top: -1px;
      right: -20px;
      color: $grey4;
      i {
        display: block;
        line-height: 1;
        font-size: 15px;
        color: #888;
        margin: 0;
        padding: 0;
      }
      &:hover i {
        color: darken($grey4, 25%);
      }
    }
    &.with-reset {
      position: relative;

      .reset {
        display: none;
      }

      &.has-focus {
        .reset {
          display: block;
        }
      }
    }

    &.movable {
      transform: translate(152px, 0);
      transition: transform 0.2s ease-out;
      i:hover {
        color: lighten($btn-icon, 8%);
      }
      input {
        width: 150px;
        position: relative;
        z-index: -20;
        opacity: 0;
        transition: opacity 0.2s ease-out;
      }
      &.has-focus {
        transform: translate(0, 0);
        i:hover {
          color: $btn-icon;
        }
        input {
          transition: border-color 0.5s $swift-ease-out-timing-function;
          z-index: 0;
          opacity: 1;
        }
      }
    }

  }

}

.global-search-wrapper {

  @extend .block-window;
  background: $content-back;

  .form {
    margin-bottom: 15px;
  }

  .pagination-loading{
    top: calc(50% - 20px);
    left: calc(50% - 20px);
    position: absolute;
  }

  .bottom-margin {
    margin-bottom: 30px;
  }
  .extra-bottom-margin {
    margin-bottom: 45px;
  }

  header.section-header {
    background: $content-back;
    border-bottom: 1px solid $default-border-color;
    height: 75px;
    padding: 0 30px 0 20px;
    h2 {
      font-size: rem(1.5);
      margin: 0;
      padding: 0;
      font-weight: normal;
    }
    .close-button {
      margin-right: 20px;
    }
    .buttons {
      .btn-add {
        font-size: rem(2.7);
        margin: 0 6px;
      }
      > div, .simple-icon-btn {
        margin: 0 8px;
      }
      input {
        width: 120px;
      }
    }
  }

  .sidebar {
    width: 300px;
    padding: 20px;
    border-right: 1px solid $default-border-color;
    background: $grey3;

    .form:last-child {
      margin-bottom: 0;
    }

  }

  md-checkbox {
    color: $dark2;

    &:not(.md-checked) .md-icon {
      border-color: #9e9e9e;
    }
  }

  .list-content {

    .cell {
      padding: 5px 15px;
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-left: 0;
      }
    }

    .highlighted-column-container {
      padding: 0 40px 0 35px;
      overflow: hidden;
    }

    .heading {
      height: 50px;
      border-bottom: 1px solid $grey3;
      .cell {
        color: $dark2;
        font-size: rem(1.2);
      }
    }

    .cell-images {
      .image {
        img {
          display: block;
          max-width: 32px;
          max-height: 32px;
          margin-right: 5px;
        }
        &:nth-last-child(2) {
          margin-right: 10px;
        }
      }
    }

    .cell-action {
      button {
        margin-left: 10px;
      }
      .buttons {
        margin-left: 10px;
      }
    }

    .md-virtual-repeat-offsetter {
      padding: 0 40px 0 35px;
    }
  }

}
.global-search-content {
  .global-search-content-search {
    background-color: #fafafa;
    padding: 0 60px;
  }
  .section-line {
    border: 1px solid #bdbdbd;
    border-bottom: 0;
  }
  .sticky-header {
    z-index: 30 !important;
  }
  md-list-item.md-no-proxy{
    padding: 0;
  }
  .md-subheader .md-subheader-inner {
    padding-left: 0;
    padding-right: 0;
  }
  .collapse-btn{
    transition: all 0.3s linear;
    transform: rotate(0deg);
    min-height: 16px;
    height: 16px;
  }
  .animate-show-hide.ng-hide {
    height: 0;
  }
  .animate-show-hide.ng-hide-add,
  .animate-show-hide.ng-hide-remove {
    transition: all linear 0.3s;
  }
  .collapse-btn.close{
    transform: rotate(180deg);
  }
  .section-text {
    font-weight: 500;
    font-size: 16px;
  }
  .default-row {
    a {
      color: inherit;
    }
    .cell{
      font-size: 14px;
    }
  }
  .block-wide-scroll {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
  }
}

.pagination-loading {
  background: inherit;
  position: relative;
  z-index: 10;
  top: 21px;
  md-progress-circular {
    margin: 0 auto;
  }
}

.loading-list {
  position: fixed;
  top: 80px;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 77;
  display: none;
  margin-left: 200px;
  md-progress-circular {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto !important;
  }
}

.socket-error {
  .loading-list {
    top: 124px;
  }
}

body.list-loads {
  .management {
    .loading-list {
      display: block;
    }
    .main-body {
      .content {
        opacity: 0.5
      }
    }
    .pagination-loading {
      display: none !important;
    }
  }

  &.blocking {
    .management {
      .main-body {
        .content {
          opacity: 0;
        }
      }
    }
    .highlighted-column-container {
      display: none !important;
    }
  }

  .nothing-found-msg {
    display: none !important;
  }
}

.nothing-found-msg {
  position: absolute;
  left: 0;
  right: 0;
  top: 52px;
  bottom: 0;
  color: $dark;
  z-index: 10;
  div {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    transform: translateY(-50%);
    font-size: 26px;
  }
}

.management .scroll-top-btn {
  position: fixed;
  top: 80px;
  transition: all 0.2s ease;
  margin-left: 59px;
  z-index: 55;

  border: none;
  background: $dark-button;
  color: $dark;
  width: 150px;
  height: 40px;

  opacity: 0;
  visibility: hidden;

  &:focus {
    outline: none;
  }

  &:hover {
    background-color: darken($dark-button, 5%);
  }

  &.shown {
    opacity: 1;
    visibility: visible;
  }

}

.socket-error {
  .scroll-top-btn {
    top: 124px;
  }
}

.date-pick {
  position: relative;
  md-datepicker {
    position: absolute;
    visibility: hidden;
    left: -50px; // for proper alignment of datepicker modal
  }
}

/**** statistics ***/
.management {
  .progress {
    font-size: rem(1.4);
    width: 300px;
    background-color: $default-light-grey2;
    border-radius: 10px;
    .progress-current {
      text-align: center;
      background-color: $dark-grey;
      padding: 2px 0;
      border-radius: inherit;
    }
  }
  .date-statistics {
    width: 140px;
  }
}

.management-sidenav md-content .unit-history > div:not(.heading) {
  color: #fff;
}

.place-used-current {
  padding-top: 25px;
  position: absolute;
  top: 0;
  right: 45%;
  z-index: 9;
}

@include media(1300px) {
  .management {
    header {
      padding: 0 20px;
      .date-end {
        max-width: 10%;
      }
    }
    header .user-info {
      .divider{
        display: none;
      }
      .search-global {
        margin-left: 5px;
      }
    }
    header.with-subheader .subheader-th {
      padding: 0 15px;
    }
    .content .md-virtual-repeat-offsetter {
      padding: 0 20px 0 20px;
    }
    .content.no-virtual-repeat {
      padding: 0 15px 0 20px;
    }
  }
  .management .highlighted-column-container {
    padding: 0 15px 0 20px;
  }
  .management .content .cell {
    padding: 6px 0;
    font-size: 13px;
  }
  .management header.with-subheader .subheader-nav {
    padding: 0 15px 0 15px;
  }
  .user-info .divider {
    margin: 0 0 0 10px;
  }
  .management header .user-info {
    font-size: 13px;
  }
  .user-info .switch-app-block {
    margin-right: 15px;
  }
  .user-info .user-name {
   //display: none;
  }
  .management header .cell {
    padding: 0 0;
  }
}