.kenpinuv-header {
    border-bottom: 1px solid $default-input-border;

header {
    background: #ffffff;
}

h2 {
    font-size: 15px;
    margin: 0;
    padding: 0;
    font-weight: normal;
    color: #212121;
    padding-left: 20px;
}

}
.kenpinuv-info {
    background: #acd0dd;
    height: 100%;

.step-info {
    margin: 0 auto 20px;
    padding: 0 10px;
    border-collapse: collapse;
    width: 66%;
    margin-top: 20px;

td {
    border-top: 1px solid darken($default-input-border, 10%);
}

td:first-child:not(:only-child) {
    border-right: 1px solid darken($default-input-border, 10%);
}

td:not(:only-child) {
    width: 50%;
}

td {
    vertical-align: top;
    padding: 9px 7px 8px;
    width: 1240px;
}

td:first-child {
    padding-left: 3px;
}

td:last-child {
    padding-right: 3px;
}

}
.task-info {
    width: 100%;
    margin: auto;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    td {
        width: auto;
        text-align: center;
        border: none;
    }
  .task-info-header {
    font-weight: bold;
    padding: 10px;
    font-size: 18px;
  }

}
md-checkbox.good {
    margin-right: 0;
    margin-left: 0;
}

  .step-info-div table tbody{
    display: block;
  }

  .step-info tbody {
    height: 280px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .oit-div{
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }
  .oit-str{
    font-size: 25px;
    right: 80px;
    top: -12px;
  }
}

kenpinuv-info {
    width: 100%;
}

.kenpinuv-check-all {
  color: red;
  font-size: 30px;
}