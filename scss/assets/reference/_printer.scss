.option-customers-container {
  width:100%;
}

.option-customers {
  width:49%;
  display:inline-block;
}

.option-scroll-container {
  width: 99%;
  height: 400px;
}

.option-scroll {
  height: 370px;
  overflow: auto;
}

.option-customers-container label, .option-scroll-container label {
  position: relative !important;
}

.option-label {
  margin-bottom: 10px;
}

.option-nowrap {
  white-space: nowrap;
}

.custom-form {
  .custom-label {
    display: flex;
    align-items: center;
    &:before {
      content: '※';
      color: red;
      font-size: 15px;
      margin-right: 4px;
    }
  }
  .m-top-0 {
    margin-top: 0px !important;
  }
  .error-text {
    color: red;
    font-size: 12px;
  }
  form {
    input {
      margin-bottom: 6px;
    }
  }
  .checkbox-container {
    flex-direction: column;
    padding-bottom: 10px;
  }
}