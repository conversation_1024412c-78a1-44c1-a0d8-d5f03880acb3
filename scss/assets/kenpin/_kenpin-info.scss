.kenpin-header {
  border-bottom: 1px solid $default-input-border;
  header {
    background: #ffffff;
  }
  h2 {
    font-size: 15px;
    margin: 0;
    padding: 0;
    font-weight: normal;
    color: #212121;
    padding-left: 20px;
  }
}
.kenpin-info {
  background: #acd0dd;
  height: 100%;
  .step-info {
    margin: 0 auto 20px;
    padding: 0 10px;
    border-collapse: collapse;
    width: 66%;
    margin-top: 20px;
    td {
      border-top: 1px solid darken($default-input-border, 10%);
    }
    td:first-child:not(:only-child) {
      border-right: 1px solid darken($default-input-border, 10%);
    }
    td:not(:only-child) {
      width: 50%;
    }
    td {
      vertical-align: top;
      padding: 9px 7px 8px;
    }
    td:first-child {
      padding-left: 3px;
    }
    td:last-child {
      padding-right: 3px;
    }
  }
  .task-info {
    width: 95%;
    margin: auto;
    td {
      width: 25%;
      text-align: center;
      border: none;
    }
    .task-info-header {
      font-size: 26px;
      font-weight: bold;
      margin-right: 100px;
    }
  }
  md-checkbox.good{
    margin-right: 0;
    margin-left: 0;
    margin-top: 15px;
  }

}

.kenpin-inline-flex {
  display: inline-flex;
  flex-direction: column;
}
kenpin-info {
  width: 100%;
}

kenpin-failed {
  .box-delivery-late{
    margin-bottom: 0px !important;
  }
}

.wrap-check-box {
  width: max-content;
  margin: auto;
  text-align: left;
}

.wrap-ok-box {
  gap: 16px;
  margin-top: 16px;
}

.custom-form {
  .custom-label {
    display: flex;
    align-items: center;
    &:before {
      content: '※';
      color: red;
      font-size: 15px;
      margin-right: 4px;
    }
  }
  .item-box {
    display: flex;
    align-items: center;
    .m-bottom-0 {
      margin-bottom: 0;
    }
  }
  .m-top-0 {
    margin-top: 0 !important;
  }
  .error-text {
    color: red;
    font-size: 12px;
  }
  form {
    input {
      margin-bottom: 6px;
    }
  }
  .checkbox-container {
    flex-direction: column;
    padding-bottom: 10px;
  }
}
.include-option {
  background: #acd0dd;
  width: 300px;
  height: auto;
}
