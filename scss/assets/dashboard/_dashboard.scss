.dashboard {
  
  background: $default-light-grey;
  
  > header.header {
    min-height: 75px;
    padding: 0 50px;
    border-bottom: 1px solid $dark-grey;
  }

  .logo {
    md-icon {
      width: 95px;
      svg {
        &, & .st0 {
          fill: $black;
        }
      }
    }
  }
  
  .main-chart {
    display: block;
    padding: 20px 40px;
  }

  .pie-charts {
    padding: 0 60px;
  }
  
  .pie-block {
    padding: 40px;
    .pie {
      position: relative;
      display: block;
      .pie-text {
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        text-align: center;
        transform: translateY(-43%);
        font-size: 18px;
        font-weight: bold;
      }
    }

    .pie-description {
      margin-left: 35px;
      > div:first-child {
        font-size: 18px;
        margin-bottom: 5px;
      }
      > div:last-child {
        
      }
    }
    
  }

  .dashboard-statistics {
    margin-bottom: 10px;
    padding: 0 40px;
    table {
      width: 100%;
    }
    .width-auto {
      width: 1px;
      white-space: nowrap;
    }
    
    td, th {
      padding: 20px;
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }
      border-bottom: 1px solid $grey3;
    }

    th {
      text-align: left;
      font-weight: normal;
      font-size: rem(1.2);
      color: $dark2;
      padding-bottom: 30px;
    }
    
  }
  
}

.weekpicker-container {

  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  
  .weekpicker-datepicker .md-button {

    display: inline-block;
    width: 30px;
    height: 30px;
    min-width: 30px;
    min-height: 30px;
    line-height: 1;
    padding: 2px;

    .md-datepicker-calendar-icon {
      display: block;
      height: 16px;
      width: 16px;
      min-height: 16px;
      min-width: 16px;
      fill: $grey4;
      margin: auto;
      background-repeat: no-repeat no-repeat;
      vertical-align: middle;
    }

  }

  .weekpicker-daterange {

    font-size: 15px;

    .md-button {

      display: inline-block;
      width: 30px;
      height: 30px;
      min-width: 30px;
      min-height: 30px;
      line-height: 1;
      padding: 2px;

      md-icon, md-icon path {
        display: block;
        height: 16px;
        width: 16px;
        min-height: 16px;
        min-width: 16px;
        fill: $grey4;
        margin: auto;
        background-repeat: no-repeat no-repeat;
        vertical-align: middle;
      }
    }

    .go-next {
      md-icon {
        transform: rotate(180deg);
      }
    }

  }
  
}