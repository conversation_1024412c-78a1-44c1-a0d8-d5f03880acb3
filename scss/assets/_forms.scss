.default-form {
  
  h3 {
    margin: 0 0 8px 1px;
    padding: 0;
    font-size: rem(1.2);
    color: $grey4;
    font-weight: normal;
  }
  
  .input-container {
    border-top: 1px solid $default-input-border;
    
    &.bottom-border {
      border-bottom: 1px solid $default-input-border;
    }

    md-input-container {
      display: block;
      margin: 17px 0 1px;
      .md-errors-spacer {
        display: none;
      }
      &.md-input-focused .md-input {
        padding-bottom: 1px;
      }
      &.md-input-has-value label:not(.md-no-float),
      &.md-input-focused label:not(.md-no-float),
      &.md-input-has-placeholder label:not(.md-no-float),
      &.md-input-has-value label:not(.md-no-float) {
        transform: translate3d(0, 9px, 0) scale(0.75);
      }
    }

    label,
    md-input-container:not(.md-input-invalid).md-input-has-value label {
      font-size: 14px;
      color: lighten($dark2, 3%);
    }

    md-input-container:not(.md-input-invalid).md-input-has-value.md-input-focused label {
      color: $dark2;
    }

    input, textarea, .md-select-value {
      border-bottom: none !important;
      font-size: rem(1.4);
      color: $dark;
    }

    md-select:focus .md-select-value {
      border-bottom: none;
    }

    md-select[disabled] .md-select-value {
      background: none;
      .md-select-icon {
        display: none;
      }
    }

    .accent-negative {
      color: $red;
    }
    .accent-total {
      color: $blue2;
      font-weight: bold;
    }
    
  }
  
  .checkbox-container {
    border-top: 1px solid $default-input-border;
    padding-top: 16px;
    .md-label {
      margin-top: 3px;
    }
  }

  .form-buttons {
    margin-top: 20px;
    .md-button:first-child {
      margin-left: 0;
    }
  }

  .flex-50 {
    border-right: 1px solid $default-input-border;
    margin-right: -1px;
  }
  .flex-50 + .flex-50 {
    border-right: none;
    padding-left: 10px;
  }
  .flex-33, .flex-66 {
    border-right: 1px solid $default-input-border;
    margin-right: -1px;
  }
  .flex-33 + .flex-33, .flex-66 + .flex-33 {
    padding-left: 10px;
  }
  .flex-33.last {
    border-right: none;
  }
}

.form-buttons {

  .input-container {
    
    md-input-container {
      display: block;
      margin: 16px 0 1px;
      .md-errors-spacer {
        display: none;
      }
      &.md-input-focused .md-input {
        padding-bottom: 1px;
      }
      &.md-input-has-value label:not(.md-no-float),
      &.md-input-focused label:not(.md-no-float),
      &.md-input-has-placeholder label:not(.md-no-float),
      &.md-input-has-value label:not(.md-no-float) {
        transform: translate3d(0, 9px, 0) scale(0.75);
      }
    }

    label,
    md-input-container:not(.md-input-invalid).md-input-has-value label {
      font-size: 14px;
      color: lighten($dark2, 3%);
    }

    md-input-container:not(.md-input-invalid).md-input-has-value.md-input-focused label {
      color: $dark2;
    }

    input, textarea {
      border-bottom: none;
      font-size: rem(1.4);
      color: $dark;
    }

  }
  
  .buttons-container {
    margin-top: 13px;
    .md-button:last-child {
      margin-right: 0;
    }
  }
  
}