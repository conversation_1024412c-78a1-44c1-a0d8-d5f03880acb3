.input-date-picker {
  display: flex;
  align-items: center;
  padding-top: 10px;
}
.width-date-picker {
  width: 83px;
}

.register-box-info {
  max-width: 500px;
  margin: 30px auto;
  align-items: center;
  justify-content: flex-start;
}

.info-box-number {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 30px;
  margin-bottom: 30px;
}

.info-order-number {
  text-align: center;
  height: 500px;
  overflow-y: auto;
  line-height: 28px;
  padding: 0 50px;
  font-size: 20px;
}

.font-size-24 {
  font-size: 24px;
}

.barcode-icon {
  width: 150px;
  height: 150px;
  fill: #eee;
}
.scan-barcode {
  margin-top: -100px;
}
.cancel-button {
  position: absolute;
  left: 52%;
  top: 25px;
  z-index: 2;
  &:after {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 5px 5px 5px;
    border-color: transparent #424242 transparent transparent;
    position: absolute;
    left: 2px;
    top: 40%;
    margin-top: -2px;
  }
}

.finish-button {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: #5b9bd5;
  color: #fff;
  min-height: 30px !important;
  min-width: 50px !important;
  font-size: 12px;
  z-index: 2;
  &:hover {
    background-color: darken(#5b9bd5, 12%) !important;
  }
}

.button-sort {
  margin-left: 20px;
  button {
    border: 0;
    background: transparent;
  }
}

.button-number {
  border: 0;
  background: transparent;
}