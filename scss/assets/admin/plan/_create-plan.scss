.create-plan-content {
  
  .products-list {

    md-checkbox {
      margin-bottom: 0;
    }
    
    .header-row {
      font-size: 15px;
      min-height: 51px;
      padding: 0 35px;
      margin-top: 7px;
      margin-bottom: 7px;
      font-weight: bold;
      position: relative;
      z-index: 15;
    }
    
    .md-menu {
      .select-status {
        @include no-outline;
        cursor: pointer;
        position: relative;
        padding-right: 15px;
        &:after {
          content: '';
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 4px 4px 0 4px;
          border-color: $black transparent transparent transparent;
          position: absolute;
          top: 50%;
          transform: translateY(-49%);
          right: 0;
        }
      }
    }

    .image {
      img {
        display: block;
        max-width: 32px;
        max-height: 32px;
        margin-right: 5px;
      }
    }
    
    .default-row {
      padding: 0 35px;
      line-height: 1.35;
      
      &::before, &::after {
        content: none;
      }
      &.row-active {
        background: $grey3;
      }
      
      .divider {
        display: inline-block;
        vertical-align: middle;
      }
      
      .data-rowed {
        padding-right: 10px;
      }
      
    }
    
    .order-row {
      min-height: 51px;
      border-top: 1px solid $grey3;
      &:last-child {
        border-bottom: 1px solid $grey3;
      }
    }

    .cell-data {

      &.indented {
        padding-left: 45px;
      }
      .checkbox {
        margin-right: 10px;
      }
      .images .image:last-child {
        margin-right: 14px;
      }
    }
    
    .divider {
      width: 4px;
      height: 4px;
      background: #424242;
      border-radius: 100%;
      margin: 0 7px;
    }
    
  }
  
  .nothing-found-msg {
    margin-top: -52px;
  }
  
  .printers-list {
    background: $grey3;
    padding: 15px 0 0;
    
    .pick-date-header {
      width: 115px;
      margin-left: auto;
      overflow: hidden;
    }
    
    .header, .heading {
      font-size: 15px;
      padding: 0 35px;
      font-weight: bold;
    }
    .header {
      margin-bottom: 25px;
      position: relative;
      z-index: 15;
    }
    .heading {
      > div:not(:last-child) {
        margin-right: 12px;
      }
    }
    
    .other .heading {
      margin-bottom: 5px;
    }

    .other .printer-row {
      min-height: 58px;
    }
    
    .printer-row {
      padding: 8px 35px;
      min-height: 42px;
      
      .printer-name {
        min-width: 90px;
        margin-right: 10px;
      }

      .printer-progress {
        @extend .inline-progress;
        .progress {
          max-width: 200px;
          &.has-note {
            width: calc(100% - 38px);
          }
        }
        .progress-note {
          margin-left: 10px;
          span {
            display: block;
            width: 12px;
            height: 12px;
            background: url('../img/icons/attention.png');
            background-size: cover;
          }

        }
      }
      
      .image {
        @extend .order-image-thumb;
      }

      .printer-colors {
        margin-right: 15px;
        .color-dot:not(:last-child) {
          margin-right: 10px;
        }
      }

      .printer-size {
        margin-right: 15px;
      }
      
    }

    .suggested {
      margin-bottom: 15px;
    }
    
  }

  .btn-accept {
    width: 30px !important;
    min-width: 30px !important;
    padding: 6px 2px !important;
    md-icon {
      height: 17px;
      width: 17px;
      min-height: 17px;
      min-width: 17px;
      polyline {
        stroke: $dark2;
      }
    }
  }
  
}


.menu-small.custom-checkbox {
  padding: 8px;
}

.auto-plan {
  padding: 0px 30px;
}

.close-filter-button {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background: #000;
  position: absolute;
  top: -2px;
  right: -20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-filter-icon {
  font-size: 12px;
  color: #fff;
  line-height: 14px;
}