.management .chart {
  
  opacity: 1;
  margin-left: -60px;
  margin-right: -47px;
  padding: 15px 47px 15px 60px;
  margin-bottom: 15px;
  background: $grey3;

  &.ng-hide-add, &.ng-hide-remove {
    transition: all ease-out 0.2s;
  }
  &.ng-hide {
    opacity: 0;
  }

  .chart-header {
    margin-bottom: 20px;
  }
  
  .chart-component {
    display: block;
    width: 100%;
  }

  .chart-datepicker .md-button {

    display: inline-block;
    width: 30px;
    height: 30px;
    min-width: 30px;
    min-height: 30px;
    line-height: 1;
    padding: 2px;
    
    .md-datepicker-calendar-icon {
      display: block;
      height: 16px;
      width: 16px;
      min-height: 16px;
      min-width: 16px;
      fill: $grey4;
      margin: auto;
      background-repeat: no-repeat no-repeat;
      vertical-align: middle;
    }

  }

  .chart-daterange {

    font-size: 15px;
    
    .md-button {

      display: inline-block;
      width: 30px;
      height: 30px;
      min-width: 30px;
      min-height: 30px;
      line-height: 1;
      padding: 2px;
      
      md-icon, md-icon path {
        display: block;
        height: 16px;
        width: 16px;
        min-height: 16px;
        min-width: 16px;
        fill: $grey4;
        margin: auto;
        background-repeat: no-repeat no-repeat;
        vertical-align: middle;
      }
    }
    
    .go-next {
      md-icon {
        transform: rotate(180deg);
      }
    }
    
  }
  
  .chart-menu {
    margin-left: 4px;
  }

  .chart-compare {
    margin-left: 12px;
    md-checkbox {
      vertical-align: middle;
      margin-bottom: 0;
    }
    > div {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      top: 1px;
      color: darken($grey4, 9%);
    }
    md-checkbox.md-default-theme:not(.md-checked) .md-icon, md-checkbox:not(.md-checked) .md-icon {
      border-color: $grey4;
    }
  }
  
}