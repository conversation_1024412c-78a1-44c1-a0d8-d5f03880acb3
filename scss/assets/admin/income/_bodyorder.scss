.condition-title {
  margin-bottom: 15px;
  background-color: #eeeeee;
  padding: 10px;
}

.btn-download-csv {
  color: blue;
  &:hover {
    color: black;
    text-decoration: none;
  }
}

.history-table {
  border-collapse: collapse;
  width: 75%;
  margin-bottom: 30px;
  th, td {
    border: 1px solid black;
  }
  th {
    background-color: dodgerblue;
    white-space: nowrap;
    padding: 10px;
  }
  td {
    text-align: center;
    padding: 5px;
    a:hover {
      color: black;
      text-decoration: none;
    }
  }

}

.select-time {
  width: 100px;
}

.time-d-flex {
  display: flex;
}

.select-date {
  width: 150px;
}

.select-hour {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.select-minute {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.start-time-area {
  display: flex;
  align-items: center;
}

.border-order-date {
  font-size: 14px !important;
  width: 105px;
}
