/*** Major styles ***/
body {
  line-height: 1.2;
  min-width: 1000px;
}

.logo {
  md-icon {
    width: 130px;
    color: #fff;
  }
}

/*****------Helper classes -----*****/
.block-wide {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.absolute-wide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.block-wide-scroll {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
}

.block-min-width {
  width: 100%;
  height: 100%;
  &.min-1400 {
    min-width: 1400px;
  }
}

.overflow-hidden {
  overflow: hidden;
}

.root {
  @extend .block-wide;
}

.not-scrollable-x {
  overflow-x: hidden;
}

.socket-error-msg {
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: #ef5350;
  padding: 13px 5px;
  height: 44px;
}

md-input-container.fullwidth {
  width: 100%;
}

// styles for datepicker in input container
md-input-container {
  background: inherit;
  &._md-datepicker-floating-label {
    padding-left: 0 !important;
  }
  .md-datepicker-input {
    width: 100%;
  }
  md-datepicker {
    padding-right: 0 !important;
    background: inherit;
    display: block;
    width: 100%;
    margin-bottom: -6px;
  }
  .md-icon-button {
    position: absolute;
    right: -15px;
    top: -1px;
    z-index: 10;
    margin-top: 0 !important;
    md-icon {
      width: 20px;
      height: 20px;
    }
  }
  .md-datepicker-input-container {
    margin-left: 0 !important;
    width: 100%;
    padding-bottom: 0 !important;
  }
  .md-datepicker-triangle-button {
    display: none !important;
  }
  &.disabled {
    .md-datepicker-button {
      opacity: 0;
    }
  }
}

.md-datepicker-input-mask {
  display: none;
}

// color preview

.color-preview {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  display: inline-block;
  vertical-align: top;
  &:not(:last-child) {
    margin-right: 4px;
  }
}

md-input-container {

  .color-preview-container {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

}

.md-menu-color-menu {
  span {
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
  }
  .color-preview {
    vertical-align: middle;
  }

  .btn-select span {
    margin-right: 10px;
  }

}

// styles for clipboard button in input container
md-input-container.with-clipboard {
  .clipboard-button {
    @include simple-button;
    @include no-outline;
    padding: 4px;
    position: absolute;
    right: 6px;
    bottom: 6px;
    color: $dark-grey;
    font-size: 16px;
  }
  .btn-dummy {
    position: absolute;
    width: 24px;
    height: 25px;
    right: 6px;
    bottom: 6px;
    z-index: -1;
    visibility: hidden;
  }
  input {
    padding-right: 28px;
  }
}

// styles for time picker
mdp-time-picker {
  .md-icon-button {
    display: none;
  }
  md-input-container {
    width: 100%;
  }
}

// special input fields (like in bootstrap)
.input-special {
  color: $si-color;
  margin-bottom: -21px;
  label {
    color: inherit;
    font-size: 12px;
  }
  input {
    border: 1px solid $si-border-color !important;
    background-color: $si-background;
    color: lighten($si-color, 15%);
    padding: 5px 10px !important;
    font-size: 12px;
    border-radius: 4px;
    height: 40px;
    line-height: 12px;
  }
  .md-select-placeholder, .md-select-value {
    border: 1px solid $si-border-color;
    background-color: $si-background;
    color: lighten($si-color, 15%) !important;
    font-size: 12px;
    border-radius: 4px;
    padding: 6px 8px;
  }
  // input behaviour on focus
  &:not(.md-input-invalid).md-input-focused, md-select:focus {
    label {
      color: inherit;
    }
    input {
      border: 1px solid $si-border-color;
      padding: 19px 10px;
    }
    .md-select-placeholder, .md-select-value {
      color: lighten($si-color, 15%) !important;
      border: 1px solid $si-border-color !important;
      padding-bottom: 6px;
    }
  }
  &:not(.md-input-invalid).md-input-has-value {
    label {
      color: inherit;
    }
    input {
      padding: 19px 10px;
    }
  }
  label:not(.md-no-float):not(.md-container-ignore), .md-placeholder {
    padding-left: 10px;
    transform: translate3d(0, 31px, 0) scale(1);
  }
  &.md-input-focused label:not(.md-no-float),
  &.md-input-has-placeholder label:not(.md-no-float),
  &.md-input-has-value label:not(.md-no-float) {
    transform: translate3d(-6px, 3px, 0) scale(0.9);
  }
  input.ng-invalid.ng-dirty {
    border-color: $input-error !important
  }

  &.dark {
    color: $input-special-dark-color;
    input {
      border-color: $input-special-dark-border-color;
      background-color: $input-special-dark-background;
      color: #fff;
    }
    &:not(.md-input-invalid).md-input-focused, md-select:focus {
      input {
        border-color: $input-special-dark-border-color;
      }
      .md-select-placeholder, .md-select-value {
        border-color: $input-special-dark-border-color !important;
      }
    }
    .md-select-placeholder, .md-select-value {
      border-color: $input-special-dark-border-color;
      background-color: $input-special-dark-background;
    }
  }

}

// input form hits and errors styling
md-input-container {
  .hint {
    position: absolute;
    left: 1px;
    right: 35px;
    top: 37px;
    font-size: 12px;
    line-height: 14px;
    transition: all 0.3s cubic-bezier(0.55, 0, 0.55, 0.2);
    &.ng-hide, &.ng-enter, &.ng-leave.ng-leave-active {
      top: 0;
      opacity: 0;
    }
    &.ng-leave, &.ng-enter.ng-enter-active {
      top: 37px;
      opacity: 1
    }
  }
  label.md-required:after, md-select.md-required .md-select-placeholder > span:first-child:after {
    content: ' *';
    font-size: 13px;
    vertical-align: top;
  }
  label.md-no-required:after {
    display: none !important;
  }
  &.md-input-invalid {
    label, .md-select-placeholder, label.md-required:after {
      color: $input-error !important;
    }
    .md-input, .md-select-value, .md-select-placeholder {
      border-bottom-color: $input-error !important;
    }
  }
  &.disabled {
    .md-errors-spacer {
      display: none !important;
    }
    label.md-required:after, md-select.md-required .md-select-placeholder > span:first-child:after {
      content: none;
    }
    .hint {
      display: none;
    }
  }
  &.no-errorspacer {
    .md-errors-spacer {
      display: none !important;
    }
  }
  .select-error-spacer {
    min-height: 24px;
    float: right;
    width: 1px;
  }
}

form.ng-submitted {
  md-select.ng-invalid {
    .md-select-placeholder, .md-select-value {
      color: $input-error !important;
      border-bottom-color: $input-error !important;
    }
    & ~ div [ng-message] {
      margin-top: 0 !important;
      opacity: 1 !important;
    }
  }

  .input-blue md-select.ng-invalid {
    .md-select-placeholder, .md-select-value {
      color: $input-error-blue !important;
      border-bottom-color: $input-error-blue !important;
    }
  }

}

// angular materials input styling
.input-blue {

  &.md-input-invalid {

    .md-icon-button {
      md-icon {
        color: $input-error-blue !important;
      }
    }

  }

  .md-input, .md-select-value {
    color: $input-blue-color !important;
  }
  label, .md-select-placeholder, .md-icon-button md-icon {
    color: $input-blue-label !important;
  }
  .md-input, .md-select-value {
    border-bottom-color: $input-blue-border-bottom !important;
  }
  &.md-input-focused {
    label, .md-select-placeholder {
      color: $input-blue-label-focus !important;
    }
    .md-input, .md-select-value {
      border-bottom-color: $input-blue-border-bottom-focus !important;
    }
  }
  .md-input, .md-select-placeholder, .md-select-value {
    transition: border-color 0.2s ease-out;
  }
  &.md-input-focused, &.md-input-has-placeholder, &.md-input-has-value {
    label {
      transform: translate3d(0, 6px, 0) scale(0.9) !important;
    }
  }
  &.disabled {
    .md-input, .md-select-placeholder, .md-select-value {
      border-bottom-color: transparent !important;
      background: none !important;
    }
    .md-select-icon {
      display: none !important;
    }
  }

  &.md-input-invalid {
    label, .md-select-placeholder, label.md-required:after {
      color: $input-error-blue !important;
    }
    .md-input, .md-select-value, .md-select-placeholder {
      border-bottom-color: $input-error-blue !important;
    }
  }
  &.md-input-invalid .md-char-counter, [ng-messages], [ng-message-exp] {
    color: $input-error-blue !important;
  }
  .hint, .md-char-counter {
    color: $input-blue-label !important;
  }

}

.input-grey {

  &.md-input-invalid {

    .md-icon-button {
      md-icon {
        color: $input-error-grey !important;
      }
    }

  }

  .md-input, .md-select-value {
    color: $input-grey-color !important;
  }
  label, .md-select-placeholder, .md-icon-button md-icon {
    color: $input-grey-label !important;
  }
  .md-input, .md-select-value {
    border-bottom-color: $input-grey-border-bottom !important;
  }
  &.md-input-focused {
    label, .md-select-placeholder {
      color: $input-grey-label-focus !important;
    }
    .md-input, .md-select-value {
      border-bottom-color: $input-grey-border-bottom-focus !important;
    }
  }
  .md-input, .md-select-placeholder, .md-select-value {
    transition: border-color 0.2s ease-out;
  }
  &.md-input-focused, &.md-input-has-placeholder, &.md-input-has-value {
    label {
      transform: translate3d(0, 6px, 0) scale(0.9) !important;
    }
  }
  &.disabled {
    .md-input, .md-select-placeholder, .md-select-value {
      border-bottom-color: transparent !important;
      background: none !important;
    }
    .md-select-icon {
      display: none !important;
    }
  }

  &.md-input-invalid {
    label, .md-select-placeholder, label.md-required:after {
      color: $input-error-grey !important;
    }
    .md-input, .md-select-value, .md-select-placeholder {
      border-bottom-color: $input-error-grey !important;
    }
  }
  &.md-input-invalid .md-char-counter, [ng-messages], [ng-message-exp] {
    color: $input-error-grey !important;
  }
  .hint, .md-char-counter {
    color: $input-grey-label !important;
  }

}

.input-dark, .input-dark-container md-input-container {

  &.md-input-invalid {

    .md-icon-button {
      md-icon {
        color: $input-error !important;
      }
    }

  }

  .md-input, .md-select-value {
    color: $input-dark-color !important;
  }
  label, .md-select-placeholder, .md-icon-button md-icon {
    color: $input-dark-label !important;
  }
  .md-input, .md-select-value {
    border-bottom-color: $input-dark-border-bottom !important;
  }
  &.md-input-focused {
    label, .md-select-placeholder, .md-icon-button md-icon {
      color: $input-dark-label-focus !important;
    }
    .md-input, .md-select-value {
      border-bottom-color: $input-dark-border-bottom-focus !important;
    }
  }
  .md-input, .md-select-placeholder, .md-select-value {
    transition: border-color 0.2s ease-out;
  }
  &.md-input-focused, &.md-input-has-placeholder, &.md-input-has-value {
    label {
      transform: translate3d(0, 6px, 0) scale(0.9) !important;
    }
  }

  &.disabled {
    .md-input, .md-select-placeholder, .md-select-value {
      border-bottom-color: transparent !important;
      background: none !important;
    }
    .md-select-icon {
      display: none !important;
    }
  }
  .input-readonly {
    color: #666666 !important;
  }
}

//------ Typography ------
.small {
  font-size: rem(1.2);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-transform-none {
  text-transform: initial;
}

// --------- Buttons ---------------
.simple-btn {
  @include simple-button;
}

.md-open-menu-container {
  .simple-btn {
    display: inline-block;
    vertical-align: middle;
    padding: 5px;
    margin-left: 5px;
    color: $grey;
    font-size: 20px !important;
    &:focus {
      outline: none;
    }
  }
}

// button like in bootstrap
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  @include button-size($btn-padding-vertical, $btn-padding-horizontal, $btn-font-size, $btn-line-height, $btn-border-radius);
  user-select: none;
  transition: background-color $swift-ease-out-duration $swift-ease-out-timing-function,
  border-color $swift-ease-out-duration $swift-ease-out-timing-function;

  &,
  &:active,
  &.active {
    &:focus,
    &.focus {
      //@include tab-focus;
      outline: none;
    }
  }

  &:hover,
  &:focus,
  &.focus {
    color: $btn-default-color;
    text-decoration: none;
  }

  &:active,
  &.active {
    outline: 0;
    background-image: none;
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
  }

  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    cursor: not-allowed;
    opacity: 0.65;
    box-shadow: none;
  }
}

.btn-default {
  @include button-variant($btn-default-color, $btn-default-bg, $btn-default-border);
}

.btn-primary {
  @include button-variant($btn-primary-color, $btn-primary-bg, $btn-primary-border);
}

.btn-white {
  @include button-variant(#2196f3, #fff, #fff);
  &:focus {
    color: #2196f3;
  }
}

.btn-white-grey {
  @include button-variant(#2196f3, #bae1f8, #bae1f8);
  &:hover {
    background-color: darken(#bae1f8, 10%);
    border-color: darken(#bae1f8, 10%);
  }
  &:focus {
    color: #2196f3;
  }
}

.btn-grey-cancel {
  @include button-variant(#fff, #626262, #626262);
  &:hover {
    background-color: darken(#626262, 7%);
    border-color: darken(#626262, 7%);
  }
}

.btn-block {
  display: block;
  width: 100%;
}

// ----- Misc -------
// dropdown menu 
md-menu-item, md-option {
  height: 35px;
  min-height: 35px;
}

.delete-confirm {
  background: $content-back;
  border-radius: 0;
  h2 {
    color: $black;
    padding: 15px 15px 5px;
    margin-bottom: 10px;
  }
  md-dialog-actions {
    padding: 0;
    margin-bottom: 6px;
    > div {
      margin: 0 15px;
    }
  }
}

.hide {
  display: none !important;
}

.show {
  display: block !important;
}

.invisible {
  visibility: hidden !important;
  .md-ripple-container {
    display: none !important;
  }
}

.inb-vam {
  display: inline-block;
  vertical-align: middle;
}

#loading-block {
  display: none;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  &.shown {
    display: block;
  }
  md-progress-circular {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto !important;
  }
}

/**** User info block *****/
.md-open-menu-container {
  .calendar {
    height: 31px;
    md-datepicker {
      margin-right: 0;
      margin-bottom: 1px;
      .md-datepicker-button {
        background-color: transparent;
        height: 20px;
        width: 30px;
        .md-datepicker-calendar-icon {
          fill: #616161;
          height: 20px;
          width: 20px;
          margin-bottom: 6px;
        }
      }
      .md-datepicker-input-container {
        display: none;
      }
      .md-datepicker-calendar-pane{
        display: none;
      }
    }
  }
}

.user-info {
  position: relative;
  user-manage {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }
  .md-button.btn-more {
    margin: 6px 0;
    padding: 6px 0 !important;
    min-width: 30px !important;
  }
  .search-global {
    position: relative;
    z-index: 10;
    display: inline-block;
    height: 21px;
    margin-left: 10px;
    margin-right: 3px;
    .search-form-global {
      form {
        display: flex;
        align-items: center;
        justify-items: center;
        height: 21px;
        position: relative;
      }
      input, i {
        display: inline-block;
        vertical-align: middle;
      }
      i {
        @include simple-button();
        font-size: 20px;
        line-height: 1;
        padding: 0 2px;
        cursor: pointer;
        transition: color $swift-ease-out-duration $swift-ease-out-timing-function;
      }
      i:last-child {
        margin-left: 8px;
      }
      .absolute-content {
        display: flex;
        visibility: hidden;
        align-items: center;
        height: 40px;
        i {
          margin-left: 60px;
        }
        right: -57px;
        position: absolute;
        background-color: #eeeeee;
        width: calc(100vw - 273px);
        min-width: 680px;
        @include browser(Mac) {
          top: -10px;
          width: calc(100vw - 278px);
        }
      }
      input {
        width: 0;
        border-style: solid;
        border-width: 0 0 2px 0;
        border-bottom-color: $search-border-bottom;
        background: transparent;
        transition: width $swift-ease-out-duration $swift-ease-out-timing-function;
        font-size: 14px;
        color: $black;
        &:focus {
          outline: none;
        }
      }

      &.has-focus {
        input {
          width: 100%;
        }
        .absolute-content {
          visibility: visible;
          display: flex;
        }
      }

      .reset {
        @include simple-button();
        @include no-outline();
        background-color: #eeeeee;
        padding: 1px;
        position: absolute;
        transform: translateY(-50%);
        top: 18px;
        right: -15px;
        height: 40px;
        i {
          display: block;
          line-height: 1;
          font-size: 15px;
          margin: 0;
          padding: 0;
        }
        &:hover i {
          color: darken($grey4, 25%);
        }
      }
      &.with-reset {
        position: relative;

        .reset {
          display: none;
        }

        &.has-focus {
          .reset {
            display: block;
          }
        }
      }
    }
  }
  .user-name {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    a {
      display: block;
      text-decoration: none;
      color: $dark;
      &:hover {
        color: darken($dark, 10%);
      }
      &:focus {
        outline: none;
      }
    }
  }
  .divider {
    display: inline-block;
    vertical-align: middle;
    width: 1px;
    background: #666;
    height: 15px;
    margin: 0 5px 0 15px;
  }
  .simple-btn {
    display: inline-block;
    vertical-align: middle;
    padding: 5px;
    margin-left: 5px;
    color: $grey;
    line-height: 1;
    font-size: 20px;
    &:focus {
      outline: none;
    }
  }

  .switch-app-block {
    display: inline-block;
    vertical-align: middle;
    margin-right: 25px;
    font-size: 14px;
    white-space: nowrap;
    a {
      text-decoration: none;
      color: $dark;
      position: relative;
      padding-right: 14px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 4px 4px 0 4px;
        border-color: $dark transparent transparent transparent;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -2px;
      }
    }

  }
}

.sub-menu {
  text-decoration: none;
  color: $dark;
  position: relative;
  padding-right: 14px;
  &:after {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 4px 4px 0 4px;
    border-color: $dark transparent transparent transparent;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -2px;
  }
}

.date-pick {
  md-input-container {
    [ng-messages], [ng-messages-exp] {
      transition: none !important;
    }
  }
}

/** angular buttons **/
.md-button {

  box-shadow: none !important;
  border-radius: 4px !important;
  vertical-align: middle;

  &.md-fab {
    border-radius: 50% !important;
  }

  &.md-primary {
    @include button-variant-material($btn-default-color, $btn-default-bg);
  }

  &.md-accent {
    @include button-variant-material($btn-primary-color, $btn-primary-bg);
  }

  &.btn-sm {
    padding: 6px 16px !important;
    line-height: 1.1 !important;
    min-height: 30px !important;
    min-width: 50px !important;
    font-size: 12px;
  }

  &.btn-xs {
    padding: 5px 10px !important;
    line-height: 1.1 !important;
    min-height: 26px !important;
    min-width: 36px !important;
    font-size: 11px;
  }

  &.btn-white {
    @include button-variant-material(#2196f3, #fff);
  }

  &.btn-white-grey {
    @include button-variant-material(#2196f3, #bae1f8);
    &:hover {
      background-color: darken(#bae1f8, 10%) !important;
    }
  }

  &.with-arrow {
    span {
      position: relative;
      padding-right: 15px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 4px 4px 0 4px;
        border-color: $dark2 transparent transparent transparent;
        position: absolute;
        top: 50%;
        transform: translateY(-49%);
        right: 0;
      }
    }
  }

}

md-menu-item > .md-button {
  border-radius: 0 !important;
}

/*** scrollbar styles ***/
::-webkit-scrollbar {
  width: 8px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.25);
}

body {
  scrollbar-base-color: rgb(96, 96, 96);
  scrollbar-track-color: lighten(rgb(96, 96, 96), 15%);
  scrollbar-arrow-color: black;
  scrollbar-shadow-color: lighten(rgb(96, 96, 96), 18%);
}

/*** message that browser is not supported ***/
#not-supported-msg {
  display: none;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10000;
  background: rgb(88, 88, 88);
  text-align: center;
  div {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin-top: -14px;
    color: white;
    font-size: 24px;
  }
  &.shown {
    display: block;
  }
}

md-input-container.reserve-space:not(.disabled) {
  margin-bottom: 42px;
}

/**** block page window *****/
.block-window {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -10;
  opacity: 0;
  overflow: auto;

  > section {
    min-width: 1000px;
  }

  &.shown {
    z-index: 78;
    opacity: 1;
    transition: opacity 0.3s ease-out;
  }

  &.hiding {
    z-index: 95;
    opacity: 0;
    transition: opacity 0.3s ease-out;
  }

  background: $content-back;

  .form {
    margin-bottom: 15px;
  }

  .bottom-margin {
    margin-bottom: 30px;
  }
  .extra-bottom-margin {
    margin-bottom: 45px;
  }

  .simple-icon-btn {
    @include simple-button;
    @include no-outline;
    color: $btn-icon;
    font-size: rem(2.2);
    padding: 2px;
    &:hover {
      color: lighten($btn-icon, 8%);
    }
    &.btn-add {
      font-size: rem(2.7);
    }
  }

  .search {
    position: relative;
    z-index: 10;
  }
  .search-form {
    padding: 13px 0;
    margin-left: -2px;

    input, i {
      display: inline-block;
      vertical-align: middle;
    }
    i {
      color: $btn-icon;
      font-size: 20px;
      line-height: 1;
      padding: 0 2px;
      cursor: pointer;
      transition: color $swift-ease-out-duration $swift-ease-out-timing-function;
    }
    i:first-child {
      margin-right: 8px;
    }
    i:last-child {
      margin-left: 8px;
    }
    input {
      border-color: transparent;
      border-style: solid;
      border-width: 0 0 2px 0;
      background: transparent;
      transition: border-color $swift-ease-out-duration $swift-ease-out-timing-function;
      color: $black;
      &:focus {
        outline: none;
        border-bottom-color: $search-border-bottom;
      }
    }
  }

  header.section-header {
    background: $content-back;
    border-bottom: 1px solid $default-border-color;
    min-height: 75px;
    padding: 0 30px 0 20px;
    h2 {
      font-size: rem(1.5);
      margin: 0;
      padding: 0;
      font-weight: normal;
    }
    .close-button {
      margin-right: 20px;
    }
    .buttons {
      .btn-add {
        font-size: rem(2.7);
        margin: 0 6px;
      }
      > div, .simple-icon-btn {
        margin: 0 8px;
      }
      input {
        width: 120px;
      }
    }
  }

}

.menu-small {
  padding: 4px 0;
  md-menu-item {
    height: 30px;
    min-height: 30px;
    > .md-button {
      font-size: 12px;
      min-height: 30px;
      line-height: 30px;
    }
  }
}

.pick-date-header {
  position: relative;
  > div {
    position: absolute;
    top: 50%;
    transform: translate(0, -46%);
    left: 45px;
    font-size: 12px;
    color: $black;
    cursor: pointer;
    padding-right: 20px;
    width: max-content;
    &:after {
      content: '';
      position: absolute;
      right: 4px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 5px 5px 0 5px;
      border-color: $black transparent transparent transparent;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
  > md-datepicker {
    &.no-padding {
      padding: 0;
    }
    &.no-events {
      pointer-events: none;
    }
    .md-datepicker-input-container {
      visibility: hidden;
      width: 1px;
    }
    .md-datepicker-button {
      background: $grey2;
      width: 30px;
      height: 30px;
      min-height: 30px;
      line-height: 1;
      padding: 2px;
      &:hover {
        background-color: darken($grey2, 5%) !important;
      }
      .md-datepicker-calendar-icon {
        height: 16px;
        width: 16px;
        min-height: 16px;
        min-width: 16px;
        fill: $grey4;
      }
    }
  }
}

.order-image-thumb {
  img {
    display: block;
    max-width: 32px;
    max-height: 32px;
    margin-right: 5px;
  }
  &:last-child img {
    margin-right: 0;
  }
}

.inline-progress {
  .progress-text {
    margin-right: 19px;
  }
  .progress {
    font-size: rem(1.4);
    max-width: 300px;
    width: 100%;
    background-color: $default-light-grey2;
    border-radius: 10px;
    .progress-current {
      text-align: center;
      background-color: $dark-grey;
      padding: 2px 0;
      border-radius: inherit;
    }
  }
  .divider {
    width: 4px;
    height: 4px;
    background: $dark;
    border-radius: 100%;
    margin: 0 7px
  }
}

.color-dot {
  width: 8px;
  height: 8px;
  border: 1px solid black;
  border-radius: 50%;
  background: black;
  &.white {
    border-color: #c4c4c4;
    background: #fff;
  }
  &.black {
    border-color: #494949;
    background: #494949;
  }
}

// todo: is there a better way ?
.scroll-no-jump, .management .scroll-no-jump {
  overflow-x: hidden;
  .content:not(.md-virtual-repeat-container), .md-virtual-repeat-offsetter {
    width: calc(100vw - 200px);
    min-width: 100%;
  }
  .management-sidenav, .order-detail-wrapper {
    .content:not(.md-virtual-repeat-container), .md-virtual-repeat-offsetter {
      width: 100%;
      min-width: calc(100% - 300px);
    }
  }
}

@include media(1000px) {
  .scroll-no-jump, .management .scroll-no-jump {
    overflow-x: auto;
    .content {
      width: auto;
    }
  }
}

/*** toast ***/
.printty-toast {
  .md-toast-content {
    background: #fff !important;
    color: $dark !important;
  }
}

.global-loading {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
  background: rgba(255, 255, 255, 0.6);

  md-progress-circular {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto !important;
  }
}

.global-loading-progress {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
  background: rgba(255, 255, 255, 0.6);
  md-progress-circular {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto !important;
  }
}

// datepicker hidden in menu
md-datepicker.in-menu-hidden {

  width: 0;
  padding: 0;

  .md-datepicker-button {
    width: 0px;
    height: 0px;
    padding: 0;
    margin: 0;
    visibility: hidden;
  }

  .md-datepicker-input-container {
    width: 0;
    height: 0;
    display: block;
    border: none;
    visibility: hidden;
  }

}

// vision
.float-left {
  float: left;
}

.float-right {
  float: right;
}

.text-xl {
  font-size: 1.25rem;
}

.text-center{
  text-align: center;
}

.box-not-margin-bottom{
  margin-bottom: 0px !important;
}

.h-full{
  height: 100%;
}

.font-sm{
  font-size: 12px;
}

.pb-12{
  padding-bottom: 3rem;
}
.pb-20{
  padding-bottom: 5rem;
}