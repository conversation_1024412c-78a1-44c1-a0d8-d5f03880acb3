body {
  font-family: "Noto Sans", "Noto Sans CJK JP", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "メイリオ", sans-serif;
  font-size: 14px;
  line-height: 1;
  padding: 0;
  margin: 0;
}

table {

  border-collapse: collapse;
  width: 100%;

  img {
    display: block;
    max-width: 100%;
  }

  td {
    height: 0;
    position: relative;
    > div {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: 2.5%;
    }

    &.empty > div {
      background-color: #f5f5f5;
    }

    img, p {
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      margin: 0;
      padding: 0;
    }

    p {
      color: #424242;
    }

  }
  
}

.ng-hide {
  display: none !important; 
}

@media print {
  body {
    font-family: "Noto Sans", "Noto Sans CJK JP", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "メイリオ", sans-serif;
    font-size: 14px;
    line-height: 1;
    padding: 0;
    margin: 0;
  }

  table {

    border-collapse: collapse;
    width: 100%;

    img {
      display: block;
      max-width: 100%;
    }

    td {
      height: 0;
      position: relative;
      > div {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: 2.5%;
      }

      &.empty > div {
        background-color: #f5f5f5;
      }

      img, p {
        display: block;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        margin: 0;
        padding: 0;
      }

      p {
        color: #424242;
      }

    }

  }

  .ng-hide {
    display: none !important;
  }
}