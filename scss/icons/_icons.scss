// Icons
// -------------------------


[data-icons8]:before,
.icons8-checked-checkbox:before,
.icons8-copy:before,
.icons8-delete:before,
.icons8-delete-2:before,
.icons8-edit:before,
.icons8-exit:before,
.icons8-image-file:before,
.icons8-more:before,
.icons8-plus:before,
.icons8-resize-four-directions:before,
.icons8-rotate-left:before,
.icons8-rotate-right:before,
.icons8-save:before,
.icons8-search:before,
.icons8-settings:before,
.icons8-sort-down:before,
.icons8-sort-up:before,
.icons8-unchecked-checkbox:before
{ @include icons8-font; }

[data-icons8]:before { content: attr(data-icons8); }

.icons8-checked-checkbox:before { content: $icons8-var-checked-checkbox; }
.icons8-copy:before { content: $icons8-var-copy; }
.icons8-delete:before { content: $icons8-var-delete; }
.icons8-delete-2:before { content: $icons8-var-delete-2; }
.icons8-edit:before { content: $icons8-var-edit; }
.icons8-exit:before { content: $icons8-var-exit; }
.icons8-image-file:before { content: $icons8-var-image-file; }
.icons8-more:before { content: $icons8-var-more; }
.icons8-plus:before { content: $icons8-var-plus; }
.icons8-resize-four-directions:before { content: $icons8-var-resize-four-directions; }
.icons8-rotate-left:before { content: $icons8-var-rotate-left; }
.icons8-rotate-right:before { content: $icons8-var-rotate-right; }
.icons8-save:before { content: $icons8-var-save; }
.icons8-search:before { content: $icons8-var-search; }
.icons8-settings:before { content: $icons8-var-settings; }
.icons8-sort-down:before { content: $icons8-var-sort-down; }
.icons8-sort-up:before { content: $icons8-var-sort-up; }
.icons8-unchecked-checkbox:before { content: $icons8-var-unchecked-checkbox; }

