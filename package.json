{"name": "printty", "description": "printty web app", "version": "2.0.0", "scripts": {"start": "electron index.js", "deploy:build": "node ghDeploy.js", "build": "gulp build", "prebuild": "npm run clean:build", "clean:build": "npm run rimraf -- build", "rimraf": "<PERSON><PERSON><PERSON>", "build_osx": "electron-packager ./ --platform=darwin --app-version=2.0.0 --out=app --arch=x64 --overwrite --icon=logo/Printty.icns", "build_win": "electron-packager ./ --platform=win32 --app-version=2.0.0 --out=app --arch=all --overwrite --icon=logo/Printty.ico"}, "devDependencies": {"electron-packager": "^8.5.2", "electron-prebuilt": "^1.4.13", "electron-rebuild": "^1.5.7", "gh-pages": "^0.12.0", "gulp": "^3.9.1", "gulp-autoprefixer": "^3.1.1", "gulp-changed": "^1.3.2", "gulp-clean-css": "^2.3.0", "gulp-connect": "^5.0.0", "gulp-if": "^2.0.2", "gulp-ignore": "^2.0.2", "gulp-ng-annotate": "^2.0.0", "gulp-pug": "^3.1.0", "gulp-sass": "^2.3.2", "gulp-uglify": "^2.0.0", "gulp-useref": "^3.1.2", "merge-stream": "^1.0.0", "gulp-awspublish": "^3.3.0"}, "dependencies": {"angular-material-time-picker": "^1.0.8", "angular-paging": "^2.2.2", "electron-pdf-window": "^1.0.9", "jquery-ui-dist": "^1.12.1"}}